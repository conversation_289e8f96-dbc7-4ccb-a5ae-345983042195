/**
 * 表示当前属性或变量的值必然不为undefined
 * * 如果当前字段类型不含null并且字段非可选，则无需进行空值检测
 * * 配合TS的strickNullCheck使用
 */
declare type Exist<T> = Exclude<T, undefined>;

/**
 * 表示当前属性或变量的值必然不能为undefined，但可以为bull
 * * 空值检查只需要针对null进行
 * * 配合TS的strickNullCheck使用
 */
declare type Nullable<T> = Exist<T> | null;

/** 明确表示当前目标可能为undefined */
declare type Unsure<T> = Exist<T> | undefined;

/** 取两个类型的公共属性 */
declare type CommonAttrs<A, B> = {
  [k in keyof A & keyof B]: A[k] | B[k];
};

/**
 * @example type a = Xor<'a'|'b'|'c', 'b'|'c'|'d'> // 'a' | 'd'
 */
declare type Xor<A, B> = Exclude<A, B> | Exclude<B, A>;

/** 获取函数的第一个参数的类型, NonNullable 用来排除函数可能不存在的情况 */
declare type FirstFuncArg<F> = NonNullable<F> extends () => void
  ? void
  : NonNullable<F> extends (arg1: infer R, ...rest: any[]) => any
  ? R
  : void;

declare type ArrayElement<T> = T extends Array<infer R> ? R : never;

/** 构造函数描述 */
declare interface Constructor<T> {
  new (...args: any[]): T;
}

/** 明确表示当前接口中所有字段都为严格状态 */
declare type StrictType<T> = { [key in keyof T]: Exist<T[key]> };

/** 可自定义扩展的Object类型 */
declare type PureObject<V = any> = Record<string, V>;

declare type PartialRequired<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

declare type Values<O> = O[keyof O];

type KeyValueArr<T extends Record<PropertyKey, PropertyKey>> = {
  [P in keyof T]: { key: P; value: T[P] };
}[keyof T];

type InvertResult<T extends Record<PropertyKey, PropertyKey>> = {
  [P in KeyValueArr<T>['value']]: Extract<KeyValueArr<T>, { value: P }>['key'];
};
