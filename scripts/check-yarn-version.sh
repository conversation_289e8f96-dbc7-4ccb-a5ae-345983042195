#!/bin/bash

RED='\033[0;31m'
GREEN='\033[0;32m'
WHITE='\033[0;37m'
required_yarn_version='1.10.1'

fail () {
  printf "${RED}$@\n${WHITE}"
  exit -1
}

info () {
  printf "${GREEN}$@\n${WHITE}"
}

command_exists () {
  type "$1" >/dev/null 2>&1
}

version_gte () {
  test "$(printf '%s\n%s\n' $1 $2 | sort -V | head -n 1)" == "$1";
}

if ! command_exists yarn ; then
  fail 'yarn not found. Install with `brew install yarn`'
fi

if version_gte "$required_yarn_version" $(yarn --version) ; then
  info "yarn version ok"
else
  fail "Requires yarn >= $required_yarn_version"
fi

