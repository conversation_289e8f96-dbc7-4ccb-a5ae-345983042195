#!/bin/sh

# MIT © Sindre Sorhus - sindresorhus.com

# git hook to run a command after `git pull` if a specified file was changed

changed_files="$(git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD)"

check_run() {
	echo "$changed_files" | grep -E --quiet "$1" && eval "$2"
}

check_run package.json "make yarn-install"
check_run .gitsubmodules "make update-submodules"
check_run "client/shared|client/webpack" "make update-submodules"

exit 0
