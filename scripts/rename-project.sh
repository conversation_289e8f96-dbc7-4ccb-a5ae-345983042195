#!/bin/sh

RED='\033[0;31m'
GREEN='\033[0;32m'
WHITE='\033[0;37m'

fail () {
  printf "${RED}$@\n${WHITE}"
  exit -1
}

info () {
  printf "${GREEN}$@\n${WHITE}"
}

read -p "当前项目名称(默认 wsc-pc-name): " old_project_name
read -p "新项目名称: " new_project_name

old_project_name=${old_project_name:-wsc-pc-name}

if [ -z "$new_project_name" ]
then
  fail "请输入新项目名称"
fi

grep -Rl \
  --exclude-dir node_modules \
  --exclude-dir dist \
  --exclude-dir local \
  "$old_project_name" \
  wap package.json | \
  xargs -t -I % sed -i '' "s/$old_project_name/$new_project_name/g" %
