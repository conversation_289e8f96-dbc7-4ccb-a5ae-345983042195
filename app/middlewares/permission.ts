import { matchKdtIdWithPercent } from '../common';

module.exports = _ => {
  return async (ctx, next) => {
    const isAdmin = ctx.userId === 1570;
    const whiteList = ctx.apolloClient.getConfig({
      appId: 'jarvis-front',
      namespace: 'feature-switch',
      key: 'assistant-feature-switch',
    });
    if (matchKdtIdWithPercent(ctx.kdtId, whiteList) || isAdmin) {
      await next();
    } else {
      await ctx.render('permission/index.njk');
    }
  };
};
