/**
 * 群发统计
 *
 * <AUTHOR>
 */
import BaseService from '../base/BaseService';

// 消息类型
type MsgChannel = 'sms' | 'wechat' | 'wechatMiniProgram' | 'wechatTemplate';

interface IStatistic {
  failed: number; // 发送失败数
  charge: number; // 发送计费数
  success: number; // 发送成功数
  total: number; // 发送总数
  visit?: number; // 访问数
}

interface IStatisticResponse extends IStatistic {
  channelType: MsgChannel; // 消息类型
  kdtId: number; // kdtId
}

/**
 * 短信群发统计接口
 * https://doc.qima-inc.com/pages/viewpage.action?pageId=85918433
 * be: luyanliang
 *
 * @class PushStatisticService
 * @extends {BaseService}
 */
class PushStatisticService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.platform.push.api.statistic.PushStatisticService';
  }

  /**
   * 实时统计接口
   *
   * @param {MsgChannel} channelType
   *
   * @return {IStatisticResponse} 短信群发特殊日合集
   * @memberof PushStatisticService
   */
  async getRealTimeStatistic(channelType: MsgChannel = 'sms'): Promise<IStatisticResponse> {
    return this.invoke('getRealTimeStatistic', [
      {
        kdtId: this.ctx.kdtId,
        channelType,
      },
    ]);
  }
}

export default PushStatisticService;
