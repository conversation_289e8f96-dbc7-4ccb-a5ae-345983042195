import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.flow.FlowDataService -  */ class FlowDataService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.flow.FlowDataService';
  }

  /**
   *  查询工作流运行数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458507
   *
   *  @param {Object} toggleFlowDTO -
   *  @param {number} toggleFlowDTO.kdtId - 店铺id
   *  @param {number} toggleFlowDTO.flowId - 工作流id
   *  @param {number} toggleFlowDTO.operatorId - 操作人ID
   *  @param {string} toggleFlowDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async getFlowRuntimeData(toggleFlowDTO) {
    return this.invoke('getFlowRuntimeData', [toggleFlowDTO]);
  }

  /**
   *  查询工作流运行记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458574
   *
   *  @param {Object} listFlowRuntimeRecordsDTO -
   *  @param {number} listFlowRuntimeRecordsDTO.kdtId - 店铺id
   *  @param {number} listFlowRuntimeRecordsDTO.pageSize - 页大小
   *  @param {number} listFlowRuntimeRecordsDTO.page - 页码，从1开始
   *  @param {string} listFlowRuntimeRecordsDTO.sort - 排序
   *  @param {number} listFlowRuntimeRecordsDTO.flowId - 工作流id
   *  @return {Promise}
   */
  async listFlowRuntimeRecords(listFlowRuntimeRecordsDTO) {
    return this.invoke('listFlowRuntimeRecords', [listFlowRuntimeRecordsDTO]);
  }
}

export default FlowDataService;
