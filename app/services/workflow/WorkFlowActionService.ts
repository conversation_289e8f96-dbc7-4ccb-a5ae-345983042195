/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.action.ActionService -  */

class ActionService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.action.ActionService';
  }

  /**
   *  查询执行器列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458055
   *
   *  @param {Object} listActionsDTO -
   *  @param {number} listActionsDTO.kdtId - 店铺id
   *  @param {number} listActionsDTO.triggerId - triggerId
   *  @param {number} listActionsDTO.pageSize - 页大小
   *  @param {number} listActionsDTO.page - 页码，从1开始
   *  @param {string} listActionsDTO.sort - 排序
   *  @return {Promise}
   */
  async listActions(listActionsDTO) {
    return this.invoke('listActions', [listActionsDTO]);
  }

  /**
   *  消息内容校验
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1495093
   *
   *  @param {string} content -
   *  @return {Promise}
   */
  async checkMsgContent(content) {
    return this.invoke('checkMsgContent', [content]);
  }

  /**
   *  查询action可串联的action列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1499120
   *
   *  @param {Object} listConcatenateActionDTO -
   *  @param {number} listConcatenateActionDTO.kdtId - 店铺id
   *  @param {number} listConcatenateActionDTO.actionId - 触发器id
   *  @return {Promise}
   */
  async listCanBeConcatenateActions(listConcatenateActionDTO) {
    return this.invoke('listCanBeConcatenateActions', [listConcatenateActionDTO]);
  }

  /**
   *  校验发消息action的各个消息渠道的权限(result中key为channel的值，value是权限描述)
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1608781
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async checkSendMsgActionChannelPurview(kdtId) {
    return this.invoke('checkSendMsgActionChannelPurview', [kdtId]);
  }
}

export default ActionService;
