/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.flow.FLowService -  */ class FLowService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.flow.FLowService';
  }

  /**
   *  创建工作流
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458126
   *
   *  @param {Object} flowConfigDTO -
   *  @param {number} flowConfigDTO.kdtId - 店铺ID
   *  @param {Array.<Object>} flowConfigDTO.chainRules[] - 条件+action组
   *  @param {[object Object]} flowConfigDTO.chainRules[].actionDTO - 执行器
   *  @param {[object Object]} flowConfigDTO.chainRules[].conditions - 条件
   *  @param {number} flowConfigDTO.triggerId - triggerId
   *  @param {string} flowConfigDTO.name - 名字
   *  @param {number} flowConfigDTO.operatorId - 操作人ID
   *  @param {string} flowConfigDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async createFlow(flowConfigDTO) {
    return this.invoke('createFlow', [flowConfigDTO]);
  }

  /**
   *  查询工作流列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458266
   *
   *  @param {Object} listFlowsDTO -
   *  @param {number} listFlowsDTO.kdtId - 店铺id
   *  @param {string} listFlowsDTO.name - 工作流名称
   *  @param {number} listFlowsDTO.pageSize - 页大小
   *  @param {number} listFlowsDTO.page - 页码，从1开始
   *  @param {string} listFlowsDTO.sort - 排序
   *  @param {number} listFlowsDTO.status - 工作流状态
   *  @return {Promise}
   */
  async listFlows(listFlowsDTO) {
    return this.invoke('listFlows', [listFlowsDTO]);
  }

  /**
   *  启用工作流
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458519
   *
   *  @param {Object} flowIdDTO -
   *  @param {number} flowIdDTO.kdtId - 店铺id
   *  @param {number} flowIdDTO.flowId - 工作流id
   *  @param {number} flowIdDTO.operatorId - 操作人ID
   *  @param {string} flowIdDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async enable(flowIdDTO) {
    return this.invoke('enableV2', [flowIdDTO]);
  }

  /**
   *  停用工作流
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458520
   *
   *  @param {Object} flowIdDTO -
   *  @param {number} flowIdDTO.kdtId - 店铺id
   *  @param {number} flowIdDTO.flowId - 工作流id
   *  @param {number} flowIdDTO.operatorId - 操作人ID
   *  @param {string} flowIdDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async disable(flowIdDTO) {
    return this.invoke('disable', [flowIdDTO]);
  }

  /**
   *  删除工作流
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458521
   *
   *  @param {Object} flowIdDTO -
   *  @param {number} flowIdDTO.kdtId - 店铺id
   *  @param {number} flowIdDTO.flowId - 工作流id
   *  @param {number} flowIdDTO.operatorId - 操作人ID
   *  @param {string} flowIdDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async delete(flowIdDTO) {
    return this.invoke('delete', [flowIdDTO]);
  }

  /**
   *  查询工作流详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458524
   *
   *  @param {Object} flowIdDTO -
   *  @param {number} flowIdDTO.kdtId - 店铺id
   *  @param {number} flowIdDTO.flowId - 工作流id
   *  @param {number} flowIdDTO.operatorId - 操作人ID
   *  @param {string} flowIdDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async getFlowDetailById(flowIdDTO) {
    return this.invoke('getFlowDetailByIdV2', [flowIdDTO]);
  }

  /**
   *  修改工作流
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458271
   *
   *  @param {Object} flowConfigDTO -
   *  @param {number} flowConfigDTO.kdtId - 店铺ID
   *  @param {Array.<Object>} flowConfigDTO.chainRules[] - 条件+action组
   *  @param {number} flowConfigDTO.triggerId - triggerId
   *  @param {string} flowConfigDTO.name - 名字
   *  @param {number} flowConfigDTO.id - 主键ID
   *  @param {number} flowConfigDTO.operatorId - 操作人ID
   *  @param {string} flowConfigDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async edit(flowConfigDTO) {
    return this.invoke('editV2', [flowConfigDTO]);
  }

  /**
   *  创建工作流会话关系
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1463520
   *
   *  @param {Object} flowSessionRelationDTO -
   *  @param {string} flowSessionRelationDTO.createdAt - 创建时间
   *  @param {number} flowSessionRelationDTO.isDeleted - 逻辑删除标志
   *  @param {string} flowSessionRelationDTO.flowMode - flow模型
   *  @param {number} flowSessionRelationDTO.sessionId - 关联的session id
   *  @param {number} flowSessionRelationDTO.id - 主键id
   *  @param {number} flowSessionRelationDTO.status - 状态
   *  @param {string} flowSessionRelationDTO.updatedAt - 修改时间
   *  @return {Promise}
   */
  async createFlowSessionRelation(flowSessionRelationDTO) {
    return this.invoke('createFlowSessionRelation', [flowSessionRelationDTO]);
  }

  /**
   *  保存任务草稿
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1482342
   *
   *  @param {Object} flowConfigDTO -
   *  @param {Object} flowConfigDTO.triggerDefine - 工作流触发器
   *  @param {number} flowConfigDTO.kdtId - 店铺ID
   *  @param {Array.<Object>} flowConfigDTO.chainRules[] - 条件+action组
   *  @param {number} flowConfigDTO.triggerId - triggerId
   *  @param {string} flowConfigDTO.name - 名字
   *  @param {string} flowConfigDTO.canvasCells - 画布节点坐标及节点关联数据
   *  @param {number} flowConfigDTO.id - flowId
   *  @param {number} flowConfigDTO.operatorId - 操作人ID
   *  @param {string} flowConfigDTO.operatorName - 操作人名称
   *  @param {string} flowConfigDTO.canvasComponents - 画布各节点数据结构
   *  @return {Promise}
   */
  async saveFlowDraft(flowConfigDTO) {
    return this.invoke('saveFlowDraft', [flowConfigDTO]);
  }

  async saveFlowDraftV2(flowConfigDTO) {
    return this.invoke('saveFlowDraftV2', [flowConfigDTO]);
  }
}

export default FLowService;
