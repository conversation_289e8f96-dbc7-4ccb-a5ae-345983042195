/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.operator.OperatorService -  */ class OperatorService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.operator.OperatorService';
  }

  /**
   *  查询变量适用的操作符
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458093
   *
   *  @param {Object} listOperatorsByVariableDTO -
   *  @param {number} listOperatorsByVariableDTO.kdtId - 店铺ID
   *  @param {number} listOperatorsByVariableDTO.variableId - 变量id
   *  @return {Promise}
   */
  async listOperatorsByVariable(listOperatorsByVariableDTO) {
    return this.invoke('listOperatorsByVariable', [listOperatorsByVariableDTO]);
  }
}

export default OperatorService;
