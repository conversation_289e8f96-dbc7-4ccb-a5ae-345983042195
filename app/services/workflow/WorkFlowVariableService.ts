/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.variable.VariableService -  */ class VariableService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.variable.VariableService';
  }

  /**
   *  查询trigger对应的变量列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458005
   *
   *  @param {Object} listVariablesByTriggerDTO -
   *  @param {number} listVariablesByTriggerDTO.kdtId - 店铺ID
   *  @param {number} listVariablesByTriggerDTO.triggerId - trigger id
   *  @param {number} listVariablesByTriggerDTO.pageSize - 页大小
   *  @param {number} listVariablesByTriggerDTO.page - 页码，从1开始
   *  @param {string} listVariablesByTriggerDTO.sort - 排序
   *  @return {Promise}
   */
  async listVariablesByTrigger(listVariablesByTriggerDTO) {
    return this.invoke('listVariablesByTrigger', [listVariablesByTriggerDTO]);
  }

  /**
   *  校验变量导入文件是否合法
   *  zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1551360
   *
   *  @param {Object} variableFileUploadDTO -
   *  @param {string} variableFileUploadDTO.fileName - 文件名
   *  @param {Object} variableFileUploadDTO.fileUrl - 源文件地址
   *  @param {number} variableFileUploadDTO.kdtId - 店铺id
   *  @param {number} variableFileUploadDTO.variableId - 变量id
   *  @return {Promise}
   */
  async checkVariableFile(variableFileUploadDTO) {
    return this.invoke('checkVariableFile', [variableFileUploadDTO]);
  }

  /**
   *  校验变量的使用权限
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1549734
   *
   *  @param {Object} variablePurviewCheckDTO -
   *  @param {number} variablePurviewCheckDTO.kdtId - 店铺id
   *  @param {Object} variablePurviewCheckDTO.relatedDataParam - 关联变量的数据
   *  @param {number} variablePurviewCheckDTO.variableId - 变量id
   *  @return {Promise}
   */
  async checkVariablePurview(variablePurviewCheckDTO) {
    return this.invoke('checkVariablePurview', [variablePurviewCheckDTO]);
  }
}

export default VariableService;
