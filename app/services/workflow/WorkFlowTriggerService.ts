/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.trigger.TriggerService -  */
class TriggerService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.trigger.TriggerService';
  }

  /**
   *  查询trigger列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458004
   *
   *  @param {Object} listTriggerDTO -
   *  @param {number} listTriggerDTO.kdtId - 店铺id
   *  @param {string} listTriggerDTO.name - trigger名称
   *  @param {number} listTriggerDTO.pageSize - 页大小
   *  @param {number} listTriggerDTO.page - 页码，从1开始
   *  @param {string} listTriggerDTO.sort - 排序
   *  @return {Promise}
   */
  async listTriggers(listTriggerDTO) {
    return this.invoke('listTriggers', [listTriggerDTO]);
  }

  /**
   *  查询所有的触发器(带分组)
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1489521
   *
   *  @param {Object} queryAllTriggerDTO -
   *  @param {number} queryAllTriggerDTO.kdtId - 店铺id
   *  @param {string} queryAllTriggerDTO.name - trigger名称
   *  @return {Promise}
   */
  async queryAllTriggerWithGroup(queryAllTriggerDTO) {
    return this.invoke('queryAllTriggerWithGroup', [queryAllTriggerDTO]);
  }
}

export default TriggerService;
