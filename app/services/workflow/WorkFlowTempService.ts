/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.flow.FlowTempService -  */

class FlowTempService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.flow.FlowTempService';
  }

  /**
   *  查询工作流模版详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458148
   *
   *  @param {number} id - 模版id
   *  @return {Promise}
   */
  async getFlowTempById(id, kdtId) {
    return this.invoke('getFlowTempByIdV2', [id, kdtId]);
  }

  /**
   *  查询工作流模版列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458147
   *
   *  @param {Object} listFlowTempDTO -
   *  @param {number} listFlowTempDTO.kdtId - 店铺ID
   *  @param {string} listFlowTempDTO.name - 工作流模版名称
   *  @param {number} listFlowTempDTO.pageSize - 页大小
   *  @param {number} listFlowTempDTO.page - 页码，从1开始
   *  @param {string} listFlowTempDTO.sort - 排序
   *  @return {Promise}
   */
  async listFlowTemps(listFlowTempDTO) {
    return this.invoke('listFlowTemps', [listFlowTempDTO]);
  }

  /**
   *  创建工作流
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458126
   *
   *  @param {Object} flowConfigDTO -
   *  @param {number} flowConfigDTO.kdtId - 店铺ID
   *  @param {Array.<Object>} flowConfigDTO.chainRules[] - 条件+action组
   *  @param {[object Object]} flowConfigDTO.chainRules[].actionDTO - 执行器
   *  @param {[object Object]} flowConfigDTO.chainRules[].conditions - 条件
   *  @param {number} flowConfigDTO.triggerId - triggerId
   *  @param {string} flowConfigDTO.name - 名字
   *  @param {number} flowConfigDTO.operatorId - 操作人ID
   *  @param {string} flowConfigDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async createFlow(flowConfigDTO) {
    return this.invoke('createFlow', [flowConfigDTO]);
  }

  /**
   *  查询工作流列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458266
   *
   *  @param {Object} listFlowsDTO -
   *  @param {number} listFlowsDTO.kdtId - 店铺id
   *  @param {string} listFlowsDTO.name - 工作流名称
   *  @param {number} listFlowsDTO.pageSize - 页大小
   *  @param {number} listFlowsDTO.page - 页码，从1开始
   *  @param {string} listFlowsDTO.sort - 排序
   *  @param {number} listFlowsDTO.status - 工作流状态
   *  @return {Promise}
   */
  async listFlows(listFlowsDTO) {
    return this.invoke('listFlows', [listFlowsDTO]);
  }

  /**
   *  查询trigger列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458004
   *
   *  @param {Object} listTriggerDTO -
   *  @param {number} listTriggerDTO.kdtId - 店铺id
   *  @param {string} listTriggerDTO.name - trigger名称
   *  @param {number} listTriggerDTO.pageSize - 页大小
   *  @param {number} listTriggerDTO.page - 页码，从1开始
   *  @param {string} listTriggerDTO.sort - 排序
   *  @return {Promise}
   */
  async listTriggers(listTriggerDTO) {
    return this.invoke('listTriggers', [listTriggerDTO]);
  }

  /**
   *  查询trigger对应的变量列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458005
   *
   *  @param {Object} listVariablesByTriggerDTO -
   *  @param {number} listVariablesByTriggerDTO.kdtId - 店铺ID
   *  @param {number} listVariablesByTriggerDTO.triggerId - trigger id
   *  @param {number} listVariablesByTriggerDTO.pageSize - 页大小
   *  @param {number} listVariablesByTriggerDTO.page - 页码，从1开始
   *  @param {string} listVariablesByTriggerDTO.sort - 排序
   *  @return {Promise}
   */
  async listVariablesByTrigger(listVariablesByTriggerDTO) {
    return this.invoke('listVariablesByTrigger', [listVariablesByTriggerDTO]);
  }

  /**
   *  查询变量适用的操作符
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458093
   *
   *  @param {Object} listOperatorsByVariableDTO -
   *  @param {number} listOperatorsByVariableDTO.kdtId - 店铺ID
   *  @param {number} listOperatorsByVariableDTO.variableId - 变量id
   *  @return {Promise}
   */
  async listOperatorsByVariable(listOperatorsByVariableDTO) {
    return this.invoke('listOperatorsByVariable', [listOperatorsByVariableDTO]);
  }

  /**
   *  查询变量的可使用的值
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458012
   *
   *  @param {Object} listFieldsByTriggerAndVariableDTO -
   *  @param {number} listFieldsByTriggerAndVariableDTO.kdtId - 店铺id
   *  @param {number} listFieldsByTriggerAndVariableDTO.triggerId - triggerId
   *  @param {number} listFieldsByTriggerAndVariableDTO.pageSize - 页大小
   *  @param {number} listFieldsByTriggerAndVariableDTO.page - 页码，从1开始
   *  @param {string} listFieldsByTriggerAndVariableDTO.sort - 排序
   *  @param {number} listFieldsByTriggerAndVariableDTO.variableId - 变量ID
   *  @return {Promise}
   */
  async listFieldsByTriggerAndVariable(listFieldsByTriggerAndVariableDTO) {
    return this.invoke('listFieldsByTriggerAndVariable', [listFieldsByTriggerAndVariableDTO]);
  }

  /**
   *  查询工作流运行记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458574
   *
   *  @param {Object} listFlowRuntimeRecordsDTO -
   *  @param {number} listFlowRuntimeRecordsDTO.kdtId - 店铺id
   *  @param {number} listFlowRuntimeRecordsDTO.pageSize - 页大小
   *  @param {number} listFlowRuntimeRecordsDTO.page - 页码，从1开始
   *  @param {string} listFlowRuntimeRecordsDTO.sort - 排序
   *  @param {number} listFlowRuntimeRecordsDTO.flowId - 工作流id
   *  @return {Promise}
   */
  async listFlowRuntimeRecords(listFlowRuntimeRecordsDTO) {
    return this.invoke('listFlowRuntimeRecords', [listFlowRuntimeRecordsDTO]);
  }

  /**
   *  查询工作流运行数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458507
   *
   *  @param {Object} toggleFlowDTO -
   *  @param {number} toggleFlowDTO.kdtId - 店铺id
   *  @param {number} toggleFlowDTO.flowId - 工作流id
   *  @param {number} toggleFlowDTO.operatorId - 操作人ID
   *  @param {string} toggleFlowDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async getFlowRuntimeData(toggleFlowDTO) {
    return this.invoke('getFlowRuntimeData', [toggleFlowDTO]);
  }

  /**
   *  查询工作流详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458524
   *
   *  @param {Object} flowIdDTO -
   *  @param {number} flowIdDTO.kdtId - 店铺id
   *  @param {number} flowIdDTO.flowId - 工作流id
   *  @param {number} flowIdDTO.operatorId - 操作人ID
   *  @param {string} flowIdDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async getFlowRuntimeDataById(flowIdDTO) {
    return this.invoke('getFlowRuntimeDataById', [flowIdDTO]);
  }

  /**
   *  查询工作流详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458524
   *
   *  @param {Object} flowIdDTO -
   *  @param {number} flowIdDTO.kdtId - 店铺id
   *  @param {number} flowIdDTO.flowId - 工作流id
   *  @param {number} flowIdDTO.operatorId - 操作人ID
   *  @param {string} flowIdDTO.operatorName - 操作人名称
   *  @return {Promise}
   */
  async getFlowDetailById(flowIdDTO) {
    return this.invoke('getFlowDetailById', [flowIdDTO]);
  }

  /**
   *  查询模版分类数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1482321
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async listTemplateTag(kdtId) {
    return this.invoke('listTemplateTag', [kdtId]);
  }

  /**
   *  查询自动任务模板
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1489393
   *
   *  @param {Object} queryFlowTempsDTO -
   *  @param {boolean} queryFlowTempsDTO.isRecommend - 是否仅查询推荐模版
   *  @param {number} queryFlowTempsDTO.kdtId - 店铺id
   *  @param {number} queryFlowTempsDTO.pageSize - 页大小
   *  @param {number} queryFlowTempsDTO.page - 页码，从1开始
   *  @param {string} queryFlowTempsDTO.sort - 排序
   *  @param {string} queryFlowTempsDTO.groupCode - 分组code
   *  @return {Promise}
   */
  async queryFlowTemps(queryFlowTempsDTO) {
    return this.invoke('queryFlowTemps', [queryFlowTempsDTO]);
  }
}

export default FlowTempService;
