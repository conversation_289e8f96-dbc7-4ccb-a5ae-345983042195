import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.permission.CheckPermissionService -  */
class CheckPermissionService extends BaseService {
    get SERVICE_NAME() {
        return "com.youzan.bos.workflow.service.api.permission.CheckPermissionService";
    }

    /**
     *  校验店铺是否有自动任务的使用权限
     *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1548366
     *
     *  @param {number} kdtId -
     *  @return {Promise}
     */
    async checkShopFLowPermission(kdtId) {
        return this.invoke("checkShopFLowPermission", [kdtId]);
    }
}

export default CheckPermissionService;