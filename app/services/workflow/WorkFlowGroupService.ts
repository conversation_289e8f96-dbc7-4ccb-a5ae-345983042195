import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.flow.FlowGroupService -  */ class FlowGroupService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.flow.FlowGroupService';
  }

  /**
   *  查询所有任务模版分类
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1489394
   *
   *  @param {number} kdtId - 店铺ID
   *  @return {Promise}
   */
  async queryAllFlowTempGroup(kdtId) {
    return this.invoke('queryAllFlowTempGroup', [kdtId]);
  }
}

export default FlowGroupService;
