/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.field.FieldService -  */ class FieldService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.field.FieldService';
  }

  /**
   *  查询变量的可使用的值
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1458012
   *
   *  @param {Object} listFieldsByTriggerAndVariableDTO -
   *  @param {number} listFieldsByTriggerAndVariableDTO.kdtId - 店铺id
   *  @param {number} listFieldsByTriggerAndVariableDTO.triggerId - triggerId
   *  @param {number} listFieldsByTriggerAndVariableDTO.pageSize - 页大小
   *  @param {number} listFieldsByTriggerAndVariableDTO.page - 页码，从1开始
   *  @param {string} listFieldsByTriggerAndVariableDTO.sort - 排序
   *  @param {number} listFieldsByTriggerAndVariableDTO.variableId - 变量ID
   *  @return {Promise}
   */
  async listFieldsByTriggerAndVariable(listFieldsByTriggerAndVariableDTO) {
    return this.invoke('listFieldsByTriggerAndVariable', [listFieldsByTriggerAndVariableDTO]);
  }
}

export default FieldService;
