/* eslint-disable max-len */
import BaseService from '../base/BaseService';

class FlowService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.WorkOrderService';
  }

  /**
             *  获取工单数据
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1542468 
*
             *  @param {Object} request - 请求参数
             *  @param {number} request.kdtId - 店铺id
             *  @param {string} request.category - 分类
 <p>
 taskFlow: 经营托管
 aigc: 创意生成
             *  @param {Object} request.operator - 操作人信息
             *  @param {string} request.operator.operatorId - 
             *  @param {string} request.operator.operatorName - 
             *  @return {Promise}
             */
  async getWorkOrderData(request) {
    return this.invoke('getWorkOrderData', [request]);
  }

  /**
             *  分页查询工单运行明细
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1542469 
*
             *  @param {Object} request - 请求参数
             *  @param {number} request.kdtId - 店铺id
             *  @param {number} request.pageSize - 每页大小
             *  @param {number} request.page - 页码
             *  @param {string} request.category - 分类
 <p>
 taskFlow: 经营托管
 aigc: 创意生成
             *  @param {Object} request.operator - 操作人信息
             *  @param {string} request.operator.operatorId - 
             *  @param {string} request.operator.operatorName - 
             *  @return {Promise}
             */
  async pageExecuteRecords(request) {
    return this.invoke('pageExecuteRecords', [request]);
  }
}

export default FlowService;
