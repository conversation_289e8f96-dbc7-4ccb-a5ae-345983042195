/* eslint-disable max-len */
import BaseService from '../base/BaseService';

class AgentBuildService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.AgentBuildService';
  }

  /**
   *  是否已完成初始化
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541766
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId - 店铺id
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async isFinished(request) {
    return this.invoke('isFinished', [request]);
  }

  /**
   *  提交智能体构建任务
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541767
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺id
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @param {string} request.objective - 希望通过智能体达成的目标
   *  @return {Promise}
   */
  async submitBuildTask(request) {
    return this.invoke('submitBuildTask', [request]);
  }

  /**
   *  获取智能体构建结果
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541768
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺id
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async getBuildResult(request) {
    return this.invoke('getBuildResult', [request]);
  }

  /**
   *  检查并初始化智能体
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1613667
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺id
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async checkAndInit(request) {
    return this.invoke('checkAndInit', [request]);
  }
}

export default AgentBuildService;
