/* eslint-disable max-len */
import BaseService from '../base/BaseService';

class DashboardService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.DashboardService';
  }

  /**
   *  智能体工作台 - 获取工作成果数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541769
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺id
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async agentData(request) {
    return this.invoke('agentData', [request]);
  }

  /**
   *  智能体工作台 - 获取我的智能体
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541770
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺id
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async myAgents(request) {
    return this.invoke('myAgents', [request]);
  }
}

export default DashboardService;
