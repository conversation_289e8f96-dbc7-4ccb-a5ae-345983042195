/* eslint-disable max-len */
import BaseService from '../base/BaseService';

class CollectService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.CollectService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1543066
   *
   *  @param {Object} addCollectRequest -
   *  @param {number} addCollectRequest.kdtId -
   *  @param {boolean} addCollectRequest.collected - 是否收藏
   *  @param {number} addCollectRequest.id -
   *  @param {string} addCollectRequest.userName -
   *  @param {number} addCollectRequest.type - 1: 图文 2：图片 3：报告
   *  @param {number} addCollectRequest.userId -
   *  @param {string} addCollectRequest.content -
   *  @param {Object} addCollectRequest.operator - 操作人信息
   *  @return {Promise}
   */
  async addCollect(addCollectRequest) {
    return this.invoke('addCollect', [addCollectRequest]);
  }

  /**
   *
   *  zan<PERSON>I文档地址: http://zanapi.qima-inc.com/site/service/view/1543067
   *
   *  @param {Object} query -
   *  @param {number} query.kdtId -
   *  @param {number} query.pageIndex -
   *  @param {number} query.pageSize -
   *  @param {Object} query.operator - 操作人信息
   *  @param {string} query.operator.operatorId -
   *  @param {string} query.operator.operatorName -
   *  @return {Promise}
   */
  async getCollections(query) {
    return this.invoke('getCollections', [query]);
  }
}

export default CollectService;
