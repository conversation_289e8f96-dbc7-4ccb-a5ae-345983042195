// @ts-nocheck
import { PCBaseService } from '@youzan/wsc-pc-base';

/**
 * Project Base Service
 */
class BaseService extends PCBaseService {
  private _SERVICE_NAME?: string | undefined;

  public get SERVICE_NAME(): string | undefined {
    return this._SERVICE_NAME;
  }

  public set SERVICE_NAME(value: string | undefined) {
    this._SERVICE_NAME = value;
  }
  // /**
  //  * 调用 Dubbo 服务
  //  * @param {String} methodName  方法名
  //  * @param {Object} args        请求参数
  //  * @param {Object} options     额外参数
  //  * @param {String} options.key
  //  * @param {Boolean} options.allowBigNumberInJSON
  //  * @return {Promise<any>}
  //  */
  // async invoke(methodName: string, args: any, options: any = {}) {
  //   const serviceName = this.SERVICE_NAME || '';
  //   const customHeaders: any = {
  //    timeout: options.timeout || DUBBOCLIENT_TIMEOUT,
  //   };

  //   Object.assign(customHeaders, this.ctx.getDubboSamHeaders());

  //   let result;
  //   let dubboOptions;
  //   try {
  //     dubboOptions = {
  //       allowBigNumberInJSON: options.allowBigNumberInJSON || false,
  //       // @ts-ignore
  //       headers: Object.assign(customHeaders, options.headers || {}),
  //       timeout:  options.timeout || DUBBOCLIENT_TIMEOUT,
  //     };
  //     console.log(serviceName, methodName,args)
  //     result = await this.dubboCall(serviceName, methodName, args, dubboOptions);
  //    // if (!(+result.code === 200 || +result.code === 0)) {
  //    //   this.ctx.logger.warn(result.message || result.msg, new Error(), result);
  //    // }
  //   } catch (e) {
  //     this.ctx.logger.error(`Error calling dubbo: [${serviceName}][${methodName}]`, e, { args, dubboOptions });
  //    // return { code: -1, message: '接口调用出错，请重试！' };
  //      throw e;
  //   }

  //   return result;
  // }
}

export = BaseService;
