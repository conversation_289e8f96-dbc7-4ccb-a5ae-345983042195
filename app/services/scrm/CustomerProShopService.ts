import BaseService from '../base/BaseService';

/* com.youzan.scrm.api.customer.service.customerpro.CustomerProShopService -  */ class CustomerProShopService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.scrm.api.customer.service.customerpro.CustomerProShopService';
  }

  /**
   *  查询店铺是否有客户PRO能力
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1482952
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId - 店铺id
   *  @return {Promise}
   */
  async isCustomerPro(request) {
    return this.invoke('isCustomerPro', [request]);
  }
}

export default CustomerProShopService;
