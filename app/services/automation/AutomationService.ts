import BaseService from '../base/BaseService';

const SUCCESS_CODE = 200;

class AutomationService extends BaseService {
  async getPluginLifeCycle() {
    let result;
    const { ctx } = this;
    // @ts-ignore
    const host = ctx.getConfig('CDP_HOST');
    const url = host + '/ma/api/v1/plugin/lifecycle/get';
    try {
      result = await this.httpCall({
        url,
        headers: {
          cookie: this.ctx.headers.cookie || '',
          'system-name': 'wsc-pc-jiawo',
        },
      });
      // success fetch
      const { code, message, data } = result || {};
      if (code === SUCCESS_CODE) {
        return data;
      }
      if (code === 141800002 || code === 144100000) {
        return {
          crmPluginLifecycle: null,
          customerProPluginLifecycle: null,
          maPluginLifecycle: null,
        };
      }
      throw new Error(message || '系统异常');
    } catch (err) {
      console.log(err);
      this.ctx.logger.error(
        `查询crm&cdp服务期 httpCall错误 kdtId: ${this.ctx.kdtId} url: ${url}, }`,
        err,
      );
    }
  }
}

export default AutomationService;
