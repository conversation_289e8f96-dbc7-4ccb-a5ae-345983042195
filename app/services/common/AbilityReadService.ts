/* eslint-disable max-len */
import BaseService from '../base/BaseService';

/* com.youzan.shopcenter.shopprod.api.service.ability.AbilityReadService -  */

class AbilityReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopprod.api.service.ability.AbilityReadService';
  }

  /**
   *  查询店铺能力信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/127865
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @param {string} abilityCode - 能力编码
   *  @return {Promise}
   */
  async queryShopAbilityInfo(kdtId, abilityCode) {
    return this.invoke('queryShopAbilityInfo', [kdtId, abilityCode]);
  }
}

export default AbilityReadService;
