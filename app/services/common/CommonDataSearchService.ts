import BaseService from '../base/BaseService';

/* com.youzan.bos.workflow.service.api.search.CommonDataSearchService -  */

class CommonDataSearchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bos.workflow.service.api.search.CommonDataSearchService';
  }

  /**
   *  通用数据搜索接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1462414
   *
   *  @param {Object} searchDataQueryDTO -
   *  @param {number} searchDataQueryDTO.kdtId - 当前店铺kdtId
   *  @param {string} searchDataQueryDTO.keyWork - 数据搜索关键词
   *  @param {number} searchDataQueryDTO.pageSize - 页大小
   *  @param {number} searchDataQueryDTO.page - 页码，从1开始
   *  @param {string} searchDataQueryDTO.sort - 排序
   *  @param {string} searchDataQueryDTO.apiId - 查询数据的api
   *  @return {Promise}
   */
  async searchData(searchDataQueryDTO) {
    return this.invoke('searchData', [searchDataQueryDTO]);
  }

  /**
   *  通用数据搜索接口(通过id进行数据搜索)
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1484190
   *
   *  @param {Object} searchDataByIdsDTO -
   *  @param {number} searchDataByIdsDTO.rootKdtId - 总部kdtId
   *  @param {number} searchDataByIdsDTO.kdtId - 当前店铺kdtId
   *  @param {string} searchDataByIdsDTO.searchDataType - 数据筛选类型（goods：商品，customerLevel：会员等级，memberCard：权益卡）
   *  @param {Array.<Array>} searchDataByIdsDTO.ids[] - id
   *  @param {Object} searchDataByIdsDTO.extParam - 扩展参数
   *  @param {Array} searchDataByIdsDTO.ids[] -
   *  @return {Promise}
   */
  async querySelectedData(searchDataByIdsDTO) {
    return this.invoke('querySelectedData', [searchDataByIdsDTO]);
  }
}

export default CommonDataSearchService;
