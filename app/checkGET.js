/* 检查Get接口 */
const fs = require('fs');
const path = require('path');

const options = {
  flags: 'w', //
  encoding: 'utf8', // utf8编码
};

const stderr = fs.createWriteStream('./checkGet.log', options);

const logger = new console.Console(stderr);

// 根据ctrl的前缀判断是否需要核实
const ctrlExclude = /^get|query|list|fetch|find|\*/;

let totalCheck = 0;
const warnList = [];
const errorList = [];

function walkSync(currentDirPath, callback) {
  fs.readdirSync(currentDirPath, { withFileTypes: true }).forEach(function(dirent) {
    const filePath = path.join(currentDirPath, dirent.name);
    if (dirent.isFile()) {
      callback(filePath, dirent);
    } else if (dirent.isDirectory()) {
      walkSync(filePath, callback);
    }
  });
}

function checkGetJson(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    content = content.replace(/\/\/(.*)/g, ''); // 删注释
    content = content.replace(/\r\n/g, ''); // 删换行
    content = content.replace(/\s/g, ''); // 删空格
    content = content.replace(/`/g, "'"); // 替换` => '符号(方便匹配检查)
    const reg = /(?<=\[)('GET'|'POST'|'PUT'|'DELETE')(.+?)(?=\])/gi;
    // console.log('content:', content);
    const getJson = content.match(reg);
    // console.log(getJson);
    getJson.forEach(json => {
      const jsonSplit = json.split(',');
      // console.log(jsonSplit);
      const method = jsonSplit[0];
      if (/'GET'/i.test(method)) {
        totalCheck += 1;
        // const pathSplit = jsonSplit[1].split('/');
        // let jsonName = pathSplit[pathSplit.length - 1];
        // jsonName = jsonName.replace(/\$\{apiPath\}/, '');
        // jsonName = jsonName.replace(/'|`/, '');
        // if (!jsonName) throw new Error('jsonName is empty');
        // // 根据path前缀排除部分接口
        // if (!/^get|query|list|\*/.test(jsonName)) {
        //   warnList.push({ jsonName, filePath });
        // }
        let ctrlName = jsonSplit[3];
        ctrlName = ctrlName.replace(/'|`/g, '');

        if (ctrlName) {
          // 根据ctrl前缀排除部分接口
          if (!ctrlExclude.test(ctrlName)) {
            warnList.push({ name: ctrlName, filePath });
          }
        } else {
          errorList.push({ filePath });
        }
      }
    });
  } catch (e) {
    errorList.push({ filePath });
  }
}

function printResult() {
  logger.log(`共检查 Get 接口: ${totalCheck}`);
  logger.log(`以下接口需要核实是否是适用Get方法, 共${warnList.length}个`);
  warnList.forEach(function(warn) {
    logger.log(`fn: ${warn.name}, path: ${warn.filePath}`);
  });
  logger.log(`以下文件需要手动校对, 共${errorList.length}个`);
  errorList.forEach(function(error) {
    logger.log(`path: ${error.filePath}`);
  });
}

walkSync('./routers', function(filePath) {
  checkGetJson(filePath);
});

printResult();
