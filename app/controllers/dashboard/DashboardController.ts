import { GET, Index, Inject, Metadata, POST, Router } from '@youzan/assets-route-plugin';
import { PCBaseController } from '@youzan/wsc-pc-base';
import AgentBuildService from '../../services/agent/AgentBuildService';
import DashboardService from '../../services/agent/DashboardService';

@Router('dashboard')
class DashboardController extends PCBaseController {
  @Inject()
  private readonly AgentBuildService!: AgentBuildService;

  @Inject()
  private readonly DashboardService!: DashboardService;

  public async init() {
    super.init();
  }

  private getOperator() {
    const { userId } = this.ctx.getState('userInfo');
    const { nickName } = this.ctx.getLocalSession('userInfo');
    return {
      operatorId: String(userId),
      operatorName: nickName,
    };
  }

  private async hasInit() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const operator = this.getOperator();
    const result = await new AgentBuildService(ctx).isFinished({ kdtId, operator });
    console.log('hasInit', result);
    return result;
  }

  @Index(['index'])
  async getIndexHtml() {
    const { ctx } = this;
    const hasInit = await this.hasInit();
    if (hasInit) {
      return ctx.render('dashboard/index.html');
    }
    return ctx.redirect(`/v4/jiawo/guide/index`);
  }

  @Metadata('获取工作成果')
  @GET('getAgentData')
  async getAgentData() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const operator = this.getOperator();
    const result = await this.DashboardService.agentData({
      kdtId,
      operator,
    });
    this.ctx.successRes(result);
  }

  @Metadata('获取我的智能体')
  @GET('getMyAgentList')
  async getMyAgentList() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const operator = this.getOperator();
    const result = await this.DashboardService.myAgents({
      kdtId,
      operator,
    });
    this.ctx.successRes(result);
  }

  @Metadata('根据 code 跳转智能体首页')
  @GET('redirectAgent')
  async redirectAgent() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { code } = ctx.query;

    const operator = this.getOperator();
    const result = await this.DashboardService.myAgents({
      kdtId,
      operator,
    });
    const agent = result.find(item => item.code === code);
    if (agent) {
      return ctx.redirect(`/v4/jiawo/task-flow/${agent.id}`);
    }
    return ctx.redirect(`/v4/jiawo/dashboard/index`);
  }

  @Metadata('检查并初始化智能体')
  @GET('checkAndInitAgent')
  async checkAndInitAgent() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const operator = this.getOperator();
    const result = await this.AgentBuildService.checkAndInit({
      kdtId,
      operator,
    });
    this.ctx.successRes(result);
  }
}

export = DashboardController;
