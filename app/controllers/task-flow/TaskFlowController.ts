import { Router, Index, Inject, GET, POST } from '@youzan/assets-route-plugin';
import BaseController from '../base/BaseController';
import { PCBaseController } from '@youzan/wsc-pc-base';
import MyAgentSkillService from '../../services/agent/MyAgentSkillService';
import MyAgentService from '../../services/agent/MyAgentService';

@Router('task-flow')
class AutomationController extends PCBaseController {


  @Inject()
  private readonly myAgentSkillService!: MyAgentSkillService;

  @Inject()
  private readonly myAgentService!: MyAgentService;

  public async init() {
    super.init();
  }

  @Index(['index', '*', ''])
  async getIndexHtml() {
    const { ctx } = this;
    // 只放开微商城单店
    return ctx.render('task-flow/index.html');
  }


  getBaseBizParams(postAction = true) {
    const { userId } = this.ctx.getState('userInfo');
    const { nickName } = this.ctx.getLocalSession('userInfo');
    const { rootKdtId } = this.ctx.state.shopInfo;
    const params: any = {
      kdtId: this.ctx.kdtId,
      hqKdtId: rootKdtId || this.ctx.kdtId,
      rootKdtId: rootKdtId || this.ctx.kdtId,
      fromApp: 'wsc-pc-jiawo',
    };

    if (postAction) {
      params.operatorId = '' + userId;
      params.operatorName = nickName;
    }
    return params;
  }


  @GET('querySkills')
  async querySkills() {
    const { agentId } = this.ctx.getQueryData();
    const query = {
      agentId: Number(agentId),
      operator: this.getBaseBizParams(true),
      kdtId: this.ctx.kdtId,
    };
    const result = await this.myAgentSkillService.querySkills(query);
    return this.ctx.successRes(result);
  }

  @GET('getMyAgentDetail')
  async getMyAgentDetail() {
    const { myAgentId } = this.ctx.getQueryData();;
    const request = {
      myAgentId: Number(myAgentId),
      kdtId: this.ctx.kdtId,
      operator: this.getBaseBizParams(true)
    };
    const result = await this.myAgentService.getMyAgentDetail(request);
    return this.ctx.successRes(result);
  }

  @GET('pageExecuteRecords')
  async pageExecuteRecords() {
    const { myAgentId, pageSize, page } = this.ctx.getQueryData();
    const request = {
      myAgentId: Number(myAgentId),
      pageSize: Number(pageSize),
      page: Number(page),
      operator: this.getBaseBizParams(true),
      kdtId: this.ctx.kdtId,
    };
    const result = await this.myAgentService.pageExecuteRecords(request);
    return this.ctx.successRes(result);
  }

  @POST('saveSkill')
  async saveSkill() {
    const { skillId, agentId, enable, config } = this.ctx.request.body;
    const operate = {
      skillId: Number(skillId),
      agentId: Number(agentId),
      enable,
      config: JSON.parse(config),
      kdtId: this.ctx.kdtId,
      operator: this.getBaseBizParams(true)
    };

    const result = await this.myAgentSkillService.save(operate);
    return this.ctx.successRes(result);
  }

}

export = AutomationController;
