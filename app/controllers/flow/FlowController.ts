import { Router, Index, Inject, Metadata, GET } from '@youzan/assets-route-plugin';
import { PCBaseController } from '@youzan/wsc-pc-base';
import FlowService from '../../services/flow/FlowService';

@Router('flow')
class FlowController extends PCBaseController {
  @Inject()
  private readonly FlowService!: FlowService;

  public async init() {
    super.init();
  }

  private getOperator() {
    const { userId } = this.ctx.getState('userInfo');
    const { nickName } = this.ctx.getLocalSession('userInfo');
    return {
      operatorId: String(userId),
      operatorName: nickName,
    };
  }

  @Index(['index'])
  async getIndexHtml() {
    const { ctx } = this;
    // 只放开微商城单店
    return ctx.render('flow/index.html');
  }

  @Metadata('获取概览数据')
  @GET('getDashboardData')
  async getDashboardData() {
    const { kdtId } = this.ctx;
    const params = this.ctx.getQueryData();
    const operator = this.getOperator();
    const result = await this.FlowService.getWorkOrderData({
      kdtId,
      operator,
      category: params.category,
    });
    this.ctx.successRes(result);
  }

  @Metadata('获取运行列表数据')
  @GET('getExecResultList')
  async getExecResultList() {
    const { kdtId } = this.ctx;
    const params = this.ctx.getQueryData();
    const operator = this.getOperator();
    const result = await this.FlowService.pageExecuteRecords({
      kdtId,
      operator,
      category: params.category,
      page: params.current,
      pageSize: params.pageSize,
    });
    this.ctx.successRes(result);
  }
}

export = FlowController;
