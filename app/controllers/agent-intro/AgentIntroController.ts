import { Router, Index, Inject, Metadata, GET, POST } from '@youzan/assets-route-plugin';
import { PCBaseController } from '@youzan/wsc-pc-base';

@Router('agent-intro')
class AgentIntroController extends PCBaseController {
  public async init() {
    super.init();
  }

  @Index(['index'])
  async getIndexHtml() {
    console.log('AgentIntroController Jinlaile ');
    const { ctx } = this;
    // 只放开微商城单店
    return ctx.render('agent-intro/index.html');
  }
}

export = AgentIntroController;
