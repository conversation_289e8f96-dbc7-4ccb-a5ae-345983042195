import { Router, Index, Inject, GET } from '@youzan/assets-route-plugin';
import BaseController from '../base/BaseController';
import { PCBaseController } from '@youzan/wsc-pc-base';
import CustomerProShopService from '../../services/scrm/CustomerProShopService';
import AutomationService from '../../services/automation/AutomationService';

@Router('work-flow-upgrade')
class WorkFlowUpgradeController extends PCBaseController {

  public async init() {
    super.init();
  }

  @Index(['index', '*'])
  async getIndexHtml() {
    const { ctx } = this;
    // 只放开微商城单店
    return ctx.render('work-flow-upgrade/index.html');
  }
}

export = WorkFlowUpgradeController;
