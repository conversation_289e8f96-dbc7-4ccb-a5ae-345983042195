import { Router, Index, Inject, Metadata, GET, POST } from '@youzan/assets-route-plugin';
import { PCBaseController } from '@youzan/wsc-pc-base';
import CollectService from '../../services/collect/CollectService';

@Router('collect')
class CollectController extends PCBaseController {
  @Inject()
  private readonly CollectService!: CollectService;

  public async init() {
    super.init();
  }

  private getOperator() {
    const { userId } = this.ctx.getState('userInfo');
    const { nickName } = this.ctx.getLocalSession('userInfo');
    return {
      operatorId: String(userId),
      operatorName: nickName,
    };
  }

  @Index(['index'])
  async getIndexHtml() {
    const { ctx } = this;
    // 只放开微商城单店
    return ctx.render('collect/index.html');
  }

  @Metadata('获取收藏列表')
  @GET('getCollectList')
  async getAgentList() {
    const { kdtId } = this.ctx;
    const params = this.ctx.getQueryData();
    const operator = this.getOperator();
    const result = await this.CollectService.getCollections({
      kdtId,
      operator,
      pageIndex: params.current || 1,
      pageSize: params.pageSize || 20,
    });

    this.ctx.successRes(result);
  }

  @Metadata('收藏/取消收藏')
  @POST('updateCollectState')
  async createAgent() {
    const { kdtId } = this.ctx;
    const params = this.ctx.request.body;
    const operator = this.getOperator();
    const result = await this.CollectService.addCollect({
      collected: params.collected,
      content: params.content,
      id: +params.id,
      skillName: params.skillName,
      itemId: +params.itemId,
      type: +params.type,
      operator,
      userId: operator.operatorId,
      userName: operator.operatorName,
      kdtId,
    });
    this.ctx.successRes(result);
  }
}

export = CollectController;
