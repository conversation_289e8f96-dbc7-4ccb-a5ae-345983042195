import { Router, Index, Inject, GET } from '@youzan/assets-route-plugin';
import BaseController from '../base/BaseController';
import { PCBaseController } from '@youzan/wsc-pc-base';
import CustomerProShopService from '../../services/scrm/CustomerProShopService';
import AutomationService from '../../services/automation/AutomationService';
import CheckPermissionService from '../../services/workflow/CheckPermissionService';

@Router('automation')
class AutomationController extends PCBaseController {
  @Inject()
  private readonly CustomerProShopService!: CustomerProShopService;

  @Inject()
  private readonly AutomationService!: AutomationService;

  @Inject()
  private readonly CheckPermissionService!: CheckPermissionService;

  public async init() {
    super.init();
  }

  @Index(['index'])
  async getIndexHtml() {
    const { ctx } = this;
    // 只放开微商城单店
    // FIXME: 商业化项目 被误发 暂时去掉
    // const { permission } = await this.CheckPermissionService.checkShopFLowPermission(this.ctx.kdtId);
    // console.log('permission', permission);
    // if (permission) {
      return ctx.render('automation/index.html');
    // }
    // return ctx.render('work-flow-upgrade/index.html');
  }

  @GET('checkShopFlowPermission')
  public async checkShopFlowPermission() {
    const result = await this.CheckPermissionService.checkShopFLowPermission(this.ctx.kdtId);
    this.ctx.successRes(result);
  }

  // 判断是否有客户pro能力
  @GET('isCustomerPro')
  async isCustomerPro() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const result = await this.CustomerProShopService.isCustomerPro({ kdtId });
    ctx.successRes(result);
  }

  @GET('getPluginLifeCycle')
  async getPluginLifeCycle() {
    const { ctx } = this;
    const result = await this.AutomationService.getPluginLifeCycle();
    ctx.successRes(result);
  }
}

export = AutomationController;
