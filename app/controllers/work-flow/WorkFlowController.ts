import { GET, Index, Inject, Metadata, POST, Router } from '@youzan/assets-route-plugin';
import AbilityReadService from '../../services/common/AbilityReadService';
import CommonDataSearchService from '../../services/common/CommonDataSearchService';
import pushStatisticService from '../../services/messagepush/PushStatisticService';
import ActionService from '../../services/workflow/WorkFlowActionService';
import WorkFlowDataService from '../../services/workflow/WorkFlowDataService';
import FieldService from '../../services/workflow/WorkFlowFieldsService';
import FlowService from '../../services/workflow/WorkFlowFlowService';
import OperatorService from '../../services/workflow/WorkFlowOperatorService';
import FlowTempService from '../../services/workflow/WorkFlowTempService';
import TriggerService from '../../services/workflow/WorkFlowTriggerService';
import VariableService from '../../services/workflow/WorkFlowVariableService';
import BaseController from '../base/BaseController';
import FlowGroupService from '../../services/workflow/WorkFlowGroupService';
import CheckPermissionService from '../../services/workflow/CheckPermissionService';

@Router('work-flow')
class WorkFlowController extends BaseController {
  public async init() {
    super.init();
  }

  @Inject()
  private readonly FlowTempService!: FlowTempService;

  @Inject()
  private readonly TriggerService!: TriggerService;

  @Inject()
  private readonly FlowService!: FlowService;

  @Inject()
  private readonly VariableService!: VariableService;

  @Inject()
  private readonly OperatorService!: OperatorService;

  @Inject()
  private readonly FieldService!: FieldService;

  @Inject()
  private readonly ActionService!: ActionService;

  @Inject()
  private readonly WorkFlowDataService!: WorkFlowDataService;

  @Inject()
  private readonly CommonDataSearchService!: CommonDataSearchService;

  @Inject()
  private readonly PushStatisticService!: pushStatisticService;

  @Inject()
  private readonly AbilityReadService!: AbilityReadService;

  @Inject()
  private readonly FlowGroupService!: FlowGroupService;

  @Inject()
  private readonly CheckPermissionService!: CheckPermissionService;

  constructor(ctx) {
    super(ctx);
    this.ctx.biz = 'work-flow';
  }

  @Index(['index', '', '*'])
  async getDemoHtml() {
    const { ctx } = this;
    return ctx.render('work-flow/index.html');
  }

  getFlowId() {
    const { flowId } = this.ctx.getQueryData();
    return flowId;
  }

  getBaseBizParams(postAction = true) {
    const { userId } = this.ctx.getState('userInfo');
    const { nickName } = this.ctx.getLocalSession('userInfo');
    const { rootKdtId } = this.ctx.state.shopInfo;
    const params: any = {
      kdtId: this.ctx.kdtId,
      hqKdtId: rootKdtId || this.ctx.kdtId,
      rootKdtId: rootKdtId || this.ctx.kdtId,
      fromApp: 'wsc-pc-jiawo',
    };

    if (postAction) {
      params.operatorId = userId;
      params.operatorName = nickName;
    }
    return params;
  }

  @Metadata('获取flow运行数据详情')
  @GET('getFlowRuntimeData')
  async getFlowRuntimeData() {
    const flowId = this.getFlowId();
    const result = await this.WorkFlowDataService.getFlowRuntimeData({
      ...this.getBaseBizParams(),
      flowId,
    });
    this.ctx.successRes(result);
  }

  @Metadata('获取flow运行记录详情')
  @GET('getFlowRuntimeRecord')
  async getFlowRuntimeRecord() {
    const flowId = this.getFlowId();
    const { page, pageSize } = this.ctx.query;

    const result = await this.WorkFlowDataService.listFlowRuntimeRecords({
      ...this.getBaseBizParams(),
      flowId,
      page,
      pageSize,
    });
    this.ctx.successRes(result);
  }

  @Metadata('获取flow列表')
  @GET('getFlowList')
  async getFlowList(ctx) {
    const params = this.ctx.getQueryData();
    const { page, pageSize, name, status, startTime, endTime } = params;
    const result = await this.FlowService.listFlows({
      ...this.getBaseBizParams(),
      page,
      pageSize,
      name: name || null,
      status: status || null,
      startTime: startTime || null,
      endTime: endTime || null,
      isNewVersion: true,
    });
    this.ctx.successRes(result);
  }

  @Metadata('查询trigger列表')
  @GET('listTriggers')
  async listTriggers() {
    const params = this.ctx.getQueryData();
    const result = await this.TriggerService.listTriggers({
      ...this.getBaseBizParams(),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // 查询变量列表 listVariablesByTrigger
  @Metadata('查询变量列表')
  @GET('listVariablesByTrigger')
  async listVariablesByTrigger() {
    const params = this.ctx.getQueryData();
    const result = await this.VariableService.listVariablesByTrigger({
      ...this.getBaseBizParams(),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // 查询操作符列表 listOperatorsByVariable
  @Metadata('查询操作符列表')
  @GET('listOperatorsByVariable')
  async listOperatorsByVariable() {
    const params = this.ctx.getQueryData();
    const result = await this.OperatorService.listOperatorsByVariable({
      ...this.getBaseBizParams(),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // 查询变量可用值列表 listFieldsByTriggerAndVariable
  @Metadata('查询变量可用值列表')
  @GET('listFieldsByTriggerAndVariable')
  async listFieldsByTriggerAndVariable() {
    const params = this.ctx.getQueryData();
    const result = await this.FieldService.listFieldsByTriggerAndVariable({
      ...this.getBaseBizParams(),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // 查询执行器列表 listActions
  @Metadata('查询执行器列表')
  @GET('listActions')
  async listActions() {
    const params = this.ctx.getQueryData();
    const result = await this.ActionService.listActions({ ...this.getBaseBizParams(), ...params });
    this.ctx.successRes(result);
  }

  // action串联时，根据actionId查询可串联的action列表 listCanBeConcatenateActions
  @Metadata('查询action可串联的action列表')
  @GET('listCanBeConcatenateActions')
  async listCanBeConcatenateActions() {
    const params = this.ctx.getQueryData();
    const result = await this.ActionService.listCanBeConcatenateActions({
      ...this.getBaseBizParams(),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // action串联时，根据actionId查询可串联的action列表 listCanBeConcatenateActions
  @Metadata('验证发消息action的各个消息渠道的权限')
  @GET('checkSendMsgActionChannelPurview')
  async checkSendMsgActionChannelPurview() {
    const { kdtId } = this.ctx;
    const result = await this.ActionService.checkSendMsgActionChannelPurview(kdtId);
    this.ctx.successRes(result);
  }

  // 创建任务流 createFlow
  @Metadata('创建任务流')
  @POST('createFlow')
  async createFlow() {
    const params = this.ctx.request.body;
    const result = await this.FlowService.createFlow({
      ...this.getBaseBizParams(true),
      ...params,
      canvasComponents: JSON.stringify(params.canvasComponents),
    });
    this.ctx.successRes(result);
  }

  // 查询工作流模版列表 listFlowTemps
  // eslint-disable-next-line youzan/youzan-standard-words
  @Metadata('查询工作流模版列表')
  @GET('listFlowTemps')
  async listFlowTemps() {
    const params = this.ctx.getQueryData();
    const result = await this.FlowTempService.listFlowTemps({
      ...this.getBaseBizParams(),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // searchData 通用数据搜索接口
  @Metadata('通用数据搜索接口')
  @GET('searchData')
  async searchData() {
    const params = this.ctx.getQueryData();
    const { kdtId } = this.ctx;
    const { filterParams, extParam, ...rest } = params;
    const result = await this.CommonDataSearchService.searchData({
      ...this.getBaseBizParams(),
      ...rest,
      extParam: extParam ? JSON.parse(extParam) : null,
      filterParams: filterParams ? JSON.parse(filterParams) : null,
      kdtId,
    });
    this.ctx.successRes(result);
  }

  // getFlowTempById 查询工作流模版详情
  // eslint-disable-next-line youzan/youzan-standard-words
  @Metadata('查询工作流模版详情')
  @GET('getFlowTempById')
  async getFlowTempById() {
    const { id } = this.ctx.query;
    const { kdtId } = this.ctx;
    const result = await this.FlowTempService.getFlowTempById(id, kdtId);
    this.ctx.successRes(result);
  }

  @Metadata('获取flow详情')
  @GET('getFlowDetail')
  async getFlowDetail() {
    const flowId = this.getFlowId();
    const result = await this.FlowService.getFlowDetailById({ flowId, ...this.getBaseBizParams() });
    this.ctx.successRes(result);
  }

  @Metadata('启用flow')
  @POST('enable')
  async enable() {
    const { flowId } = this.ctx.request.body;
    const result = await this.FlowService.enable({ flowId, ...this.getBaseBizParams(true) });
    this.ctx.successRes(result);
  }

  @Metadata('停用flow')
  @POST('disable')
  async disable(ctx) {
    const { flowId } = this.ctx.request.body;
    const result = await this.FlowService.disable({ flowId, ...this.getBaseBizParams(true) });
    this.ctx.successRes(result);
  }

  @Metadata('删除flow')
  @POST('delete')
  async delete(ctx) {
    const { flowId } = this.ctx.request.body;
    const result = await this.FlowService.delete({ flowId, ...this.getBaseBizParams(true) });
    this.ctx.successRes(result);
  }

  @Metadata('复制flow')
  @POST('copy')
  async copy(ctx) {
    const { flowId } = this.ctx.request.body;
    // 获取详情
    const flowDetail = await this.FlowService.getFlowDetailById({
      flowId,
      ...this.getBaseBizParams(true),
    });

    // 创建一个草稿
    const result = await this.FlowService.saveFlowDraftV2({
      ...this.getBaseBizParams(true),
      ...flowDetail,
      triggerId: flowDetail?.triggerDefine?.id,
      id: undefined,
    });
    this.ctx.successRes(result);
  }

  // edit
  @Metadata('修改flow')
  @POST('edit')
  async edit(ctx) {
    const params = this.ctx.request.body;
    const result = await this.FlowService.edit({
      ...this.getBaseBizParams(true),
      ...params,
      canvasComponents: JSON.stringify(params.canvasComponents),
    });
    this.ctx.successRes(result);
  }

  // createFlowSessionRelation 创建工作流会话关系
  @Metadata('创建工作流会话关系')
  @POST('createFlowSessionRelation')
  async createFlowSessionRelation(ctx) {
    const params = this.ctx.request.body;
    const result = await this.FlowService.createFlowSessionRelation({
      ...this.getBaseBizParams(true),
      ...params,
    });
    this.ctx.successRes(result);
  }

  // 获取实时推送统计
  @Metadata('获取实时短信条数')
  @GET('getRealTimeStatistic')
  public async getRealTimeStatistic() {
    const result = await this.PushStatisticService.getRealTimeStatistic();
    this.ctx.json(0, 'success', result);
  }

  // eslint-disable-next-line youzan/youzan-standard-words
  @Metadata('获取模版数据')
  @GET('getTemplates')
  public async getTemplates() {
    const { kdtId } = this.ctx;
    const result = await this.FlowTempService.listTemplateTag(kdtId);
    this.ctx.successRes(result);
  }

  @Metadata('保存草稿')
  @POST('saveDraft')
  public async saveDraft() {
    const params = this.ctx.request.body;
    // console.log('params is ', params, JSON.stringify(params.canvasComponents));
    const finalParams = {
      ...this.getBaseBizParams(true),
      ...params,
      canvasComponents: JSON.stringify(params.canvasComponents),
    };
    const result = await this.FlowService.saveFlowDraftV2(finalParams);
    this.ctx.successRes(result);
  }

  @Metadata('检查是否有workflow权限')
  @GET('checkWorkFlowPermission')
  public async checkWorkFlowPermission() {
    const result = await this.AbilityReadService.queryShopAbilityInfo(
      this.ctx.kdtId,
      'jiawo_work_flow_ability',
    );
    const { valid = false, validDetail } = result || {};
    const boo = valid && !!validDetail;
    this.ctx.successRes(boo);
  }

  @Metadata('通过ids查询详情')
  @GET('querySelectedData')
  public async querySelectedData() {
    const params = this.ctx.getQueryData();
    const { ids, searchDataType, extParam } = params;
    const result = await this.CommonDataSearchService.querySelectedData({
      ...this.getBaseBizParams(),
      ids: ids.split(',').map(Number),
      searchDataType,
      extParam: extParam ? JSON.parse(extParam) : null,
    });
    console.log('debug deubbo result is ', result);
    // result 回来的数组顺序可能会乱，需要按照ids的顺序重新排序
    const map = {};
    result.forEach(item => {
      map[item.id] = item;
    });
    const res = ids
      .split(',')
      .map(id => map[id])
      .filter(Boolean);
    this.ctx.successRes(res);
  }

  @Metadata('通过ids查询详情')
  @GET('querySelectedDataByTag')
  public async querySelectedDataByTag() {
    const params = this.ctx.getQueryData();
    const { ids, searchDataType, extParam } = params;
    const result = await this.CommonDataSearchService.querySelectedData({
      ...this.getBaseBizParams(),
      ids: ids.split(',').map(Number),
      searchDataType,
      extParam: extParam ? JSON.parse(extParam) : null,
    });
    this.ctx.successRes(result);
  }

  @Metadata('查询所有flow模板分组')
  @GET('queryAllFlowTempGroup')
  public async queryAllFlowTempGroup() {
    const result = await this.FlowGroupService.queryAllFlowTempGroup(this.ctx.kdtId);
    this.ctx.successRes(result);
  }

  @Metadata('查询自动任务模板（新）')
  @GET('queryFlowTemps')
  public async queryFlowTemps() {
    const { kdtId } = this.ctx;
    const { groupCode, page, pageSize, isRecommend } = this.ctx.getQueryData();
    const result = await this.FlowTempService.queryFlowTemps({
      kdtId,
      groupCode,
      page,
      pageSize,
      isRecommend,
    });
    this.ctx.successRes(result);
  }

  @Metadata('查询所有triggers（带分组')
  @GET('queryAllTriggerWithGroup')
  public async queryAllTriggers() {
    const { name } = this.ctx.getQueryData();
    const result = await this.TriggerService.queryAllTriggerWithGroup({
      kdtId: this.ctx.kdtId,
      name,
    });
    this.ctx.successRes(result);
  }

  @Metadata('消息内容验证')
  @GET('checkMsgContent')
  public async checkMsgContent() {
    const { content } = this.ctx.getQueryData();
    const result = await this.ActionService.checkMsgContent(content);
    this.ctx.successRes(result);
  }

  @Metadata('根据模板创建草稿')
  @POST('createDraftByTemplate')
  public async createDraftByTemplate() {
    const { templateId } = this.ctx.request.body;
    // 查询模板详情
    const templateDetail = await this.FlowTempService.getFlowTempById(templateId, this.ctx.kdtId);
    // 创建草稿
    const finalParams = {
      ...this.getBaseBizParams(true),
      ...templateDetail,
      triggerId: templateDetail?.triggerDefine?.id,
      id: undefined,
    };
    const result = await this.FlowService.saveFlowDraftV2(finalParams);
    this.ctx.successRes(result);
  }

  @Metadata('验证变量导入文件是否合法')
  @POST('checkVariableFile')
  async checkVariableFile() {
    const { variableId, fileName, fileUrl } = this.ctx.request.body || {};
    const result = await this.VariableService.checkVariableFile({
      ...this.getBaseBizParams(),
      variableId: +variableId,
      fileName,
      fileUrl,
    });
    this.ctx.successRes(result);
  }

  @Metadata('验证变量的使用权限')
  @GET('checkVariablePurview')
  async checkVariablePurview() {
    const { variableId, relatedDataParam } = this.ctx.getQueryData() || {};
    const result = await this.VariableService.checkVariablePurview({
      ...this.getBaseBizParams(),
      variableId: +variableId,
      relatedDataParam: relatedDataParam ? JSON.parse(relatedDataParam) : null,
    });
    this.ctx.successRes(result);
  }

  @Metadata('验证店铺是否有自动任务的使用权限')
  @GET('checkShopFlowPermission')
  public async checkShopFlowPermission() {
    const result = await this.CheckPermissionService.checkShopFLowPermission(this.ctx.kdtId);
    this.ctx.successRes(result);
  }
}

export = WorkFlowController;
