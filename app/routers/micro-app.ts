const { registerApp } = require('@youzan/micro-app-plugin');
const path = require('path');

const PREFIX = '/v4/jiawo';

function routes(router: string, pathx: string[]) {
  return pathx.map(p => path.join(PREFIX, router, p));
}

// 工作台 导航 工单 收藏
registerApp('@wsc-pc-jiawo/dashboard', ['/v4/jiawo/dashboard/index']);
registerApp('@wsc-pc-jiawo/collect', ['/v4/jiawo/collect/index']);
registerApp('@wsc-pc-jiawo/flow', ['/v4/jiawo/flow/index']);
registerApp('@wsc-pc-jiawo/guide', ['/v4/jiawo/guide/index']);

// aigc
registerApp('@wsc-pc-jiawo/aigc', routes('aigc', ['index', '*', '']));

// 自动任务
registerApp('@wsc-pc-jiawo/task-flow', routes('task-flow', ['index', '*', '']));
registerApp('@wsc-pc-jiawo/work-flow', routes('work-flow', ['index', '', '*']));
registerApp('@wsc-pc-jiawo/automation', ['/v4/jiawo/automation/index']);

export = [];
