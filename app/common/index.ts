/**
 * 数组匹配kdtId, 第一个值可以为百分比
 * [63077, 63088], ["50%", 63077]
 * @param kdtId
 * @param config
 * @returns boolean
 */
export function matchKdtIdWithPercent(kdtId: number, config: Array<string | number> = []): boolean {
  if (typeof kdtId !== 'number' || config.length === 0) {
    return false;
  }

  const percent = config[0];

  const excludeList = config.filter(item => +item < 0).map(item => Math.abs(+item));

  // 黑名单优先级最高
  if (excludeList.length > 0) {
    if (excludeList.includes(kdtId)) {
      return false;
    }
  }

  if (typeof percent === 'string' && percent.indexOf('%') > -1) {
    if (kdtId % 100 < parseInt(percent, 10)) {
      return true;
    }
  }

  return config.includes(kdtId);
}
