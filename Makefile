usage = "\
Usage: make [target] \n\n\
Available targets:\n\
dev [no-dll=true] [entry=\"a b c\"]           开发环境打包\n\
pre [entry=\"a b c\"]                         生产环境打包\n\
install                                     安装/更新依赖\n\
update-submodule-version                    更新所有 submodule 到最新的 commit\n\
update-lint-rules                           同步所有 lint 规则文件\n\
sync path=client/.eslintrc.js               从模版仓库同步文件或者文件夹到本地\n\
clean                                       清理打包结果\n\
clean-cache                                 清理打包缓存\n\
purge                                       清理打包缓存和打包结果\n\
wap-dev [no-dll=true] [entry=\"a\"]           wap 开发模式\n\
wap-pre                                     wap 打包预发\n\
wap-install                                 wap 安装/更新依赖\n\
wap-clean                                   wap 清理打包结果\n\
rename-project                              修改项目名称\n"

# Build PC dev arguments
dev_args =
ifeq ($(no-dll), true)
	dev_args := $(dev_args) --no-dll
endif
ifdef entry
	dev_args := $(dev_args) $(foreach item,$(entry),-e $(item))
endif

# Build PC prod arguments
prod_args =
ifdef entry
	prod_args := $(prod_args) $(foreach item,$(entry),-e $(item))
endif

# Build wap dev arguments
wap_dev_args =
ifneq ($(no-dll), true)
	wap_dev_args := $(wap_dev_args) --dll
endif
ifdef entry
	wap_dev_args := $(wap_dev_args) --entry $(entry)
endif

yyarn = yarn --registry=http://registry.npm.qima-inc.com --disturl=https://npm.taobao.org/dist

.PHONY: usage pre dev clean-cache clean clean-pre install update-submodule-version \
build yarn-install update-submodules sync check-yarn-version wap-install \
wap-dev wap-pre wap-build wap-hash wap-cdn wap-clean wap-clean-dev wap-clean-pre \
check-wap-exists rename-project update-lint-rules purge

# Must be the first target!
usage:
	@echo $(wap_dev_args)
	@echo $(usage)

pre: install build

dev: check-yarn-version
	cd client && yarn dev $(dev_args)

clean-cache:
	cd client && yarn clean-cache

clean: clean-dev clean-pre

purge: clean clean-cache

clean-dev:
	rm -rf static/local

clean-pre:
	rm -rf static/build

install: yarn-install update-submodules

update-submodule-version:
	git submodule update --remote

build:
	cd client && yarn build $(prod_args)

build-node:
	rm -rf dist
	npx tsc --project .
	cp -R app/views dist/app
	cp config/*.json dist/config || true

yarn-install:
	$(yyarn)
	cd client && $(yyarn)

update-submodules:
	git submodule update --init

sync:
	./scripts/update-path.sh $(path)

check-yarn-version:
	./scripts/check-yarn-version.sh

rename-project:
	./scripts/rename-project.sh

# wap
check-wap-exists:
ifeq ($(wildcard wap),)
	@printf '\033[0;31mwap directory not found\n'
	@exit 1
endif

wap-install: check-wap-exists
	cd wap && $(yyarn)

wap-dev: check-wap-exists wap-clean-dev
	cd wap && yarn dev $(wap_dev_args)

wap-pre: wap-build wap-hash wap-cdn

wap-build: check-wap-exists wap-clean-pre
	cd wap && yarn build

wap-hash: check-wap-exists
	cd wap && yarn hash

wap-cdn: check-wap-exists
	cd wap && yarn cdn

wap-clean-dev: check-wap-exists
	rm -rf ./wap/local

wap-clean-pre: check-wap-exists
	rm -rf ./wap/dist

wap-clean: wap-clean-dev wap-clean-pre
