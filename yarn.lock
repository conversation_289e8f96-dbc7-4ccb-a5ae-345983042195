# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz#f4ad435aa263db935b8f10f2c552d23fb716a63f"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.25.9", "@babel/code-frame@^7.26.0":
  version "7.26.2"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.26.2.tgz#4b5fab97d33338eff916235055f0ebc21e573a85"
  integrity sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.25.9":
  version "7.26.2"
  resolved "http://registry.npm.qima-inc.com/@babel/compat-data/download/@babel/compat-data-7.26.2.tgz#278b6b13664557de95b8f35b90d96785850bb56e"
  integrity sha1-J4trE2ZFV96VuPNbkNlnhYULtW4=

"@babel/core@^7.17.9":
  version "7.26.0"
  resolved "http://registry.npm.qima-inc.com/@babel/core/download/@babel/core-7.26.0.tgz#d78b6023cc8f3114ccf049eb219613f74a747b40"
  integrity sha1-14tgI8yPMRTM8EnrIZYT90p0e0A=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.0"
    "@babel/generator" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.0"
    "@babel/parser" "^7.26.0"
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.26.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.25.9", "@babel/generator@^7.26.0":
  version "7.26.2"
  resolved "http://registry.npm.qima-inc.com/@babel/generator/download/@babel/generator-7.26.2.tgz#87b75813bec87916210e5e01939a4c823d6bb74f"
  integrity sha1-h7dYE77IeRYhDl4Bk5pMgj1rt08=
  dependencies:
    "@babel/parser" "^7.26.2"
    "@babel/types" "^7.26.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.25.9.tgz#55af025ce365be3cdc0c1c1e56c6af617ce88875"
  integrity sha1-Va8CXONlvjzcDBweVsavYXzoiHU=
  dependencies:
    "@babel/compat-data" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.25.9.tgz#e7f8d20602ebdbf9ebbea0a0751fb0f2a4141715"
  integrity sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.26.0.tgz#8ce54ec9d592695e58d84cd884b7b5c6a2fdeeae"
  integrity sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz#1aabb72ee72ed35789b4bbcad3ca2862ce614e8c"
  integrity sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz#24b64e2c3ec7cd3b3c547729b8d16871f22cbdc7"
  integrity sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.25.9.tgz#86e45bd8a49ab7e03f276577f96179653d41da72"
  integrity sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=

"@babel/helpers@^7.26.0":
  version "7.26.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helpers/download/@babel/helpers-7.26.0.tgz#30e621f1eba5aa45fe6f4868d2e9154d884119a4"
  integrity sha1-MOYh8eulqkX+b0ho0ukVTYhBGaQ=
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/highlight@^7.10.4":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/highlight/download/@babel/highlight-7.25.9.tgz#8141ce68fc73757946f983b343f1231f4691acc6"
  integrity sha1-gUHOaPxzdXlG+YOzQ/EjH0aRrMY=
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.25.9", "@babel/parser@^7.26.0", "@babel/parser@^7.26.2", "@babel/parser@^7.7.0":
  version "7.26.2"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.26.2.tgz#fd7b6f487cfea09889557ef5d4eeb9ff9a5abd11"
  integrity sha1-/XtvSHz+oJiJVX711O65/5pavRE=
  dependencies:
    "@babel/types" "^7.26.0"

"@babel/template@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/template/download/@babel/template-7.25.9.tgz#ecb62d81a8a6f5dc5fe8abfc3901fc52ddf15016"
  integrity sha1-7LYtgaim9dxf6Kv8OQH8Ut3xUBY=
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/traverse@^7.25.9", "@babel/traverse@^7.7.0":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/traverse/download/@babel/traverse-7.25.9.tgz#a50f8fe49e7f69f53de5bea7e413cd35c5e13c84"
  integrity sha1-pQ+P5J5/afU95b6n5BPNNcXhPIQ=
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/generator" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.25.9"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.9", "@babel/types@^7.26.0", "@babel/types@^7.7.0":
  version "7.26.0"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.26.0.tgz#deabd08d6b753bc8e0f198f8709fb575e31774ff"
  integrity sha1-3qvQjWt1O8jg8Zj4cJ+1deMXdP8=
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@commitlint/cli@^8.3.5":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/cli/download/@commitlint/cli-8.3.6.tgz#4375dc52616ab974201e03df66adeb0e22ae42fc"
  integrity sha1-Q3XcUmFquXQgHgPfZq3rDiKuQvw=
  dependencies:
    "@commitlint/format" "^8.3.6"
    "@commitlint/lint" "^8.3.6"
    "@commitlint/load" "^8.3.6"
    "@commitlint/read" "^8.3.6"
    babel-polyfill "6.26.0"
    chalk "2.4.2"
    get-stdin "7.0.0"
    lodash "4.17.21"
    meow "5.0.0"
    resolve-from "5.0.0"
    resolve-global "1.0.0"

"@commitlint/config-conventional@^8.3.4":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/config-conventional/download/@commitlint/config-conventional-8.3.6.tgz#9199ab11fbc63ca20039b3da6566b10c4dd77f3b"
  integrity sha1-kZmrEfvGPKIAObPaZWaxDE3Xfzs=
  dependencies:
    conventional-changelog-conventionalcommits "4.2.1"

"@commitlint/ensure@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/ensure/download/@commitlint/ensure-8.3.6.tgz#cb9b6d88f197a77153505b43e76b20e1515335c6"
  integrity sha1-y5ttiPGXp3FTUFtD52sg4VFTNcY=
  dependencies:
    lodash "4.17.21"

"@commitlint/execute-rule@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/execute-rule/download/@commitlint/execute-rule-8.3.6.tgz#8ecdc0dd448ba4795c7a93dcf724707941c6634a"
  integrity sha1-js3A3USLpHlcepPc9yRweUHGY0o=

"@commitlint/format@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/format/download/@commitlint/format-8.3.6.tgz#7bc9910d42dbda04d136964deb3b9191d7246781"
  integrity sha1-e8mRDULb2gTRNpZN6zuRkdckZ4E=
  dependencies:
    chalk "^2.0.1"

"@commitlint/is-ignored@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/is-ignored/download/@commitlint/is-ignored-8.3.6.tgz#c254b48cbc57a103eb4bfb546c3de8a51529b47c"
  integrity sha1-wlS0jLxXoQPrS/tUbD3opRUptHw=
  dependencies:
    semver "6.3.0"

"@commitlint/lint@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/lint/download/@commitlint/lint-8.3.6.tgz#2fddea4ab1d197e430a1927669d1e74bac6384f8"
  integrity sha1-L93qSrHRl+QwoZJ2adHnS6xjhPg=
  dependencies:
    "@commitlint/is-ignored" "^8.3.6"
    "@commitlint/parse" "^8.3.6"
    "@commitlint/rules" "^8.3.6"
    babel-runtime "^6.23.0"
    lodash "4.17.21"

"@commitlint/load@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/load/download/@commitlint/load-8.3.6.tgz#a26dc64c0655d9f4932ecabd81ebe7c2af5259a8"
  integrity sha1-om3GTAZV2fSTLsq9gevnwq9SWag=
  dependencies:
    "@commitlint/execute-rule" "^8.3.6"
    "@commitlint/resolve-extends" "^8.3.6"
    babel-runtime "^6.23.0"
    chalk "2.4.2"
    cosmiconfig "^5.2.0"
    lodash "4.17.21"
    resolve-from "^5.0.0"

"@commitlint/message@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/message/download/@commitlint/message-8.3.6.tgz#c97ab348aa92a2dd6ee1d9de8bb44f78c470c36d"
  integrity sha1-yXqzSKqSot1u4dnei7RPeMRww20=

"@commitlint/parse@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/parse/download/@commitlint/parse-8.3.6.tgz#e7d2fd7d79fde7ba49784f8bb1c5b71b336a3781"
  integrity sha1-59L9fXn957pJeE+LscW3GzNqN4E=
  dependencies:
    conventional-changelog-angular "^1.3.3"
    conventional-commits-parser "^3.0.0"
    lodash "^4.17.11"

"@commitlint/read@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/read/download/@commitlint/read-8.3.6.tgz#48c881dabbf5c50f668fad30aaee709142d3fcb3"
  integrity sha1-SMiB2rv1xQ9mj60wqu5wkULT/LM=
  dependencies:
    "@commitlint/top-level" "^8.3.6"
    "@marionebl/sander" "^0.6.0"
    babel-runtime "^6.23.0"
    git-raw-commits "^2.0.0"

"@commitlint/resolve-extends@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/resolve-extends/download/@commitlint/resolve-extends-8.3.6.tgz#b3cfb042f262152ad3f53b708fc657713bb21f34"
  integrity sha1-s8+wQvJiFSrT9Ttwj8ZXcTuyHzQ=
  dependencies:
    import-fresh "^3.0.0"
    lodash "4.17.21"
    resolve-from "^5.0.0"
    resolve-global "^1.0.0"

"@commitlint/rules@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/rules/download/@commitlint/rules-8.3.6.tgz#e9e23fc10dda8844cefcd6b40d4e4c3ead721383"
  integrity sha1-6eI/wQ3aiETO/Na0DU5MPq1yE4M=
  dependencies:
    "@commitlint/ensure" "^8.3.6"
    "@commitlint/message" "^8.3.6"
    "@commitlint/to-lines" "^8.3.6"
    babel-runtime "^6.23.0"

"@commitlint/to-lines@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/to-lines/download/@commitlint/to-lines-8.3.6.tgz#0db98a36ee1d952d6fab371b926c38b8eb8ecbde"
  integrity sha1-DbmKNu4dlS1vqzcbkmw4uOuOy94=

"@commitlint/top-level@^8.3.6":
  version "8.3.6"
  resolved "http://registry.npm.qima-inc.com/@commitlint/top-level/download/@commitlint/top-level-8.3.6.tgz#b5e35ed08c461aed68410e743be5ea4535c6dadf"
  integrity sha1-teNe0IxGGu1oQQ50O+XqRTXG2t8=
  dependencies:
    find-up "^4.0.0"

"@discoveryjs/json-ext@0.5.7":
  version "0.5.7"
  resolved "http://registry.npm.qima-inc.com/@discoveryjs/json-ext/download/@discoveryjs/json-ext-0.5.7.tgz#1d572bfbbe14b7704e0ba0f39b74815b84870d70"
  integrity sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=

"@eggjs/yauzl@^2.11.0":
  version "2.11.0"
  resolved "http://registry.npm.qima-inc.com/@eggjs/yauzl/download/@eggjs/yauzl-2.11.0.tgz#b8e4413f50fc7c51451f770f152de4c1137aa99b"
  integrity sha1-uORBP1D8fFFFH3cPFS3kwRN6qZs=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer2 "^1.2.0"

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "http://registry.npm.qima-inc.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz#9e42981ef035beb3dd49add17acb96e8ff6f394c"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@grpc/grpc-js@^1.1.5":
  version "1.12.2"
  resolved "http://registry.npm.qima-inc.com/@grpc/grpc-js/download/@grpc/grpc-js-1.12.2.tgz#97eda82dd49bb9c24eaf6434ea8d7de446e95aac"
  integrity sha1-l+2oLdSbucJOr2Q06o195EbpWqw=
  dependencies:
    "@grpc/proto-loader" "^0.7.13"
    "@js-sdsl/ordered-map" "^4.4.2"

"@grpc/proto-loader@0.5.4":
  version "0.5.4"
  resolved "http://registry.npm.qima-inc.com/@grpc/proto-loader/download/@grpc/proto-loader-0.5.4.tgz#038a3820540f621eeb1b05d81fbedfb045e14de0"
  integrity sha1-A4o4IFQPYh7rGwXYH77fsEXhTeA=
  dependencies:
    lodash.camelcase "^4.3.0"
    protobufjs "^6.8.6"

"@grpc/proto-loader@^0.7.13":
  version "0.7.13"
  resolved "http://registry.npm.qima-inc.com/@grpc/proto-loader/download/@grpc/proto-loader-0.7.13.tgz#f6a44b2b7c9f7b609f5748c6eac2d420e37670cf"
  integrity sha1-9qRLK3yfe2CfV0jG6sLUION2cM8=
  dependencies:
    lodash.camelcase "^4.3.0"
    long "^5.0.0"
    protobufjs "^7.2.5"
    yargs "^17.7.2"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/@hapi/address/download/@hapi/address-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/bourne@^2.0.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@hapi/bourne/download/@hapi/bourne-2.1.0.tgz#66aff77094dc3080bd5df44ec63881f2676eb020"
  integrity sha1-Zq/3cJTcMIC9XfROxjiB8mdusCA=

"@hapi/bourne@^3.0.0":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@hapi/bourne/download/@hapi/bourne-3.0.0.tgz#f11fdf7dda62fe8e336fa7c6642d9041f30356d7"
  integrity sha1-8R/ffdpi/o4zb6fGZC2QQfMDVtc=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz#fde96064ca446dec8c55a8c2f130957b070c6e06"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@15.1.1":
  version "15.1.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "http://registry.npm.qima-inc.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz#1407967d4c6eecd7388f83acf1eaf4d0c6e58ef9"
  integrity sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz#b520529ec21d8e5945a1851dfd1c32e94e39ff45"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.5.tgz#dcce6aff74bdf6dad1a95802b69b04a2fcb1fb36"
  integrity sha1-3M5q/3S99trRqVgCtpsEovyx+zY=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@js-sdsl/ordered-map@^4.4.2":
  version "4.4.2"
  resolved "http://registry.npm.qima-inc.com/@js-sdsl/ordered-map/download/@js-sdsl/ordered-map-4.4.2.tgz#9299f82874bab9e4c7f9c48d865becbfe8d6907c"
  integrity sha1-kpn4KHS6ueTH+cSNhlvsv+jWkHw=

"@koa/router@^9.4.0":
  version "9.4.0"
  resolved "http://registry.npm.qima-inc.com/@koa/router/download/@koa/router-9.4.0.tgz#734b64c0ae566eb5af752df71e4143edc4748e48"
  integrity sha1-c0tkwK5WbrWvdS33HkFD7cR0jkg=
  dependencies:
    debug "^4.1.1"
    http-errors "^1.7.3"
    koa-compose "^4.1.0"
    methods "^1.1.2"
    path-to-regexp "^6.1.0"

"@kokojs/cli@^1.2.5":
  version "1.5.14"
  resolved "http://registry.npm.qima-inc.com/@kokojs/cli/download/@kokojs/cli-1.5.14.tgz#620ada51f46aad3ef81d425bf6e936e79b1a80fe"
  integrity sha1-YgraUfRqrT74HUJb9uk255sagP4=
  dependencies:
    axios "^1.2.1"
    fs-extra "^10.0.0"
    ora "^5.4.1"
    puppeteer-core "13.7.0"

"@kokojs/core@^3.19.0":
  version "3.33.14"
  resolved "http://registry.npm.qima-inc.com/@kokojs/core/download/@kokojs/core-3.33.14.tgz#746a187a70d0aaf138ac1f29aa77aa4b1485e5ec"
  integrity sha1-dGoYenDQqvE4rB8pqneqSxSF5ew=
  dependencies:
    "@kokojs/shared" "3.33.14"
    "@kokojs/sockjs-client" "1.0.0"
    "@nuxt/friendly-errors-webpack-plugin" "2.5.2"
    get-port "^5.1.1"
    minimist "^1.2.5"
    webpack "4.x"
    webpack-bundle-analyzer "^4.4.2"
    webpack-dev-server "^3.11.2"
    webpack-merge "^5.8.0"
    webpackbar "^4.0.0"

"@kokojs/shared@3.19.2":
  version "3.19.2"
  resolved "http://registry.npm.qima-inc.com/@kokojs/shared/download/@kokojs/shared-3.19.2.tgz#dd4bd7003358627c4fd3515ab84e75fb86cd1ae3"
  integrity sha1-3UvXADNYYnxP01FauE51+4bNGuM=
  dependencies:
    "@types/fs-extra" "^9.0.8"
    "@types/webpack" "^4.41.26"
    "@types/webpack-dev-server" "^3.11.1"
    axios "^0.21.1"
    cache-loader "^4.1.0"
    chalk "^4.1.0"
    chokidar "^3.5.1"
    consola "^2.15.3"
    deepmerge "^4.2.2"
    execa "^5.0.0"
    fast-glob "^3.2.5"
    fast-memoize "^2.5.2"
    fs-extra "^9.1.0"
    ora "^5.3.0"
    webpack "^4.46.0"
    webpack-chain "^6.5.1"

"@kokojs/shared@3.33.14", "@kokojs/shared@^3.17.1":
  version "3.33.14"
  resolved "http://registry.npm.qima-inc.com/@kokojs/shared/download/@kokojs/shared-3.33.14.tgz#374dcc8fa66043ed937a876af4c8dc6d8d1ba21b"
  integrity sha1-N03Mj6ZgQ+2Teodq9MjcbY0bohs=
  dependencies:
    "@types/fs-extra" "^9.0.12"
    "@types/webpack" "^4.41.26"
    "@types/webpack-dev-server" "^3.11.5"
    axios "^0.21.1"
    cache-loader "^4.1.0"
    chalk "^4.1.1"
    chokidar "^3.5.2"
    consola "^2.15.3"
    deepmerge "^4.2.2"
    execa "^5.1.1"
    fast-glob "^3.2.7"
    fast-memoize "^2.5.2"
    fs-extra "^10.0.0"
    ora "^5.4.1"
    webpack "^4.46.0"
    webpack-chain "^6.5.1"

"@kokojs/sockjs-client@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@kokojs/sockjs-client/download/@kokojs/sockjs-client-1.0.0.tgz#1ac68cd2f13a8a874ac589bf93bcded9d65e2b1d"
  integrity sha1-GsaM0vE6iodKxYm/k7ze2dZeKx0=

"@marionebl/sander@^0.6.0":
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/@marionebl/sander/download/@marionebl/sander-0.6.1.tgz#1958965874f24bc51be48875feb50d642fc41f7b"
  integrity sha1-GViWWHTyS8Ub5Ih1/rUNZC/EH3s=
  dependencies:
    graceful-fs "^4.1.3"
    mkdirp "^0.5.1"
    rimraf "^2.5.2"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nuxt/friendly-errors-webpack-plugin@2.5.2":
  version "2.5.2"
  resolved "http://registry.npm.qima-inc.com/@nuxt/friendly-errors-webpack-plugin/download/@nuxt/friendly-errors-webpack-plugin-2.5.2.tgz#982a43ee2da61611f7396439e57038392d3944d5"
  integrity sha1-mCpD7i2mFhH3OWQ55XA4OS05RNU=
  dependencies:
    chalk "^2.3.2"
    consola "^2.6.0"
    error-stack-parser "^2.0.0"
    string-width "^4.2.3"

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.25"
  resolved "http://registry.npm.qima-inc.com/@polka/url/download/@polka/url-1.0.0-next.25.tgz#f077fdc0b5d0078d30893396ff4827a13f99e817"
  integrity sha1-8Hf9wLXQB40wiTOW/0gnoT+Z6Bc=

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/aspromise/download/@protobufjs/aspromise-1.1.2.tgz#9b8b0cc663d669a7d8f6f5d0893a14d348f30fbf"
  integrity sha1-m4sMxmPWaafY9vXQiToU00jzD78=

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/base64/download/@protobufjs/base64-1.1.2.tgz#4c85730e59b9a1f1f349047dbf24296034bb2735"
  integrity sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/codegen/download/@protobufjs/codegen-2.0.4.tgz#7ef37f0d010fb028ad1ad59722e506d9262815cb"
  integrity sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/eventemitter/download/@protobufjs/eventemitter-1.1.0.tgz#355cbc98bafad5978f9ed095f397621f1d066b70"
  integrity sha1-NVy8mLr61ZePntCV85diHx0Ga3A=

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/fetch/download/@protobufjs/fetch-1.1.0.tgz#ba99fb598614af65700c1619ff06d454b0d84c45"
  integrity sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/float/download/@protobufjs/float-1.0.2.tgz#5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1"
  integrity sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/inquire/download/@protobufjs/inquire-1.1.0.tgz#ff200e3e7cf2429e2dcafc1140828e8cc638f089"
  integrity sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/path/download/@protobufjs/path-1.1.2.tgz#6cc2b20c5c9ad6ad0dccfd21ca7673d8d7fbf68d"
  integrity sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/pool/download/@protobufjs/pool-1.1.0.tgz#09fd15f2d6d3abfa9b65bc366506d6ad7846ff54"
  integrity sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/utf8/download/@protobufjs/utf8-1.1.0.tgz#a777360b5b39a1a2e5106f8e858f2fd2d060c570"
  integrity sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=

"@rtsao/scc@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@rtsao/scc/download/@rtsao/scc-1.1.0.tgz#927dd2fae9bc3361403ac2c7a00c32ddce9ad7e8"
  integrity sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=

"@stylelint/postcss-css-in-js@^0.37.2":
  version "0.37.3"
  resolved "http://registry.npm.qima-inc.com/@stylelint/postcss-css-in-js/download/@stylelint/postcss-css-in-js-0.37.3.tgz#d149a385e07ae365b0107314c084cb6c11adbf49"
  integrity sha1-0UmjheB642WwEHMUwITLbBGtv0k=
  dependencies:
    "@babel/core" "^7.17.9"

"@stylelint/postcss-markdown@^0.36.2":
  version "0.36.2"
  resolved "http://registry.npm.qima-inc.com/@stylelint/postcss-markdown/download/@stylelint/postcss-markdown-0.36.2.tgz#0a540c4692f8dcdfc13c8e352c17e7bfee2bb391"
  integrity sha1-ClQMRpL43N/BPI41LBfnv+4rs5E=
  dependencies:
    remark "^13.0.0"
    unist-util-find-all-after "^3.0.2"

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz#e4386316284f00b98435bf40f72f75a09dabf6c1"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@types/accepts@*":
  version "1.3.7"
  resolved "http://registry.npm.qima-inc.com/@types/accepts/download/@types/accepts-1.3.7.tgz#3b98b1889d2b2386604c2bbbe62e4fb51e95b265"
  integrity sha1-O5ixiJ0rI4ZgTCu75i5PtR6VsmU=
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  version "1.19.5"
  resolved "http://registry.npm.qima-inc.com/@types/body-parser/download/@types/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/commander@^2.12.2":
  version "2.12.2"
  resolved "http://registry.npm.qima-inc.com/@types/commander/download/@types/commander-2.12.2.tgz#183041a23842d4281478fa5d23c5ca78e6fd08ae"
  integrity sha1-GDBBojhC1CgUePpdI8XKeOb9CK4=
  dependencies:
    commander "*"

"@types/connect-history-api-fallback@*":
  version "1.5.4"
  resolved "http://registry.npm.qima-inc.com/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.5.4.tgz#7de71645a103056b48ac3ce07b3520b819c1d5b3"
  integrity sha1-fecWRaEDBWtIrDzgezUguBnB1bM=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "http://registry.npm.qima-inc.com/@types/connect/download/@types/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.8"
  resolved "http://registry.npm.qima-inc.com/@types/content-disposition/download/@types/content-disposition-0.5.8.tgz#6742a5971f490dc41e59d277eee71361fea0b537"
  integrity sha1-Z0Kllx9JDcQeWdJ37ucTYf6gtTc=

"@types/cookies@*":
  version "0.9.0"
  resolved "http://registry.npm.qima-inc.com/@types/cookies/download/@types/cookies-0.9.0.tgz#a2290cfb325f75f0f28720939bee854d4142aee2"
  integrity sha1-oikM+zJfdfDyhyCTm+6FTUFCruI=
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/cookies@^0.7.1":
  version "0.7.10"
  resolved "http://registry.npm.qima-inc.com/@types/cookies/download/@types/cookies-0.7.10.tgz#c4881dca4dd913420c488508d192496c46eb4fd0"
  integrity sha1-xIgdyk3ZE0IMSIUI0ZJJbEbrT9A=
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/debug@^4.1.4":
  version "4.1.12"
  resolved "http://registry.npm.qima-inc.com/@types/debug/download/@types/debug-4.1.12.tgz#a155f21690871953410df4b6b6f53187f0500917"
  integrity sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=
  dependencies:
    "@types/ms" "*"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^5.0.0":
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.0.tgz#91f06cda1049e8f17eeab364798ed79c97488a1c"
  integrity sha1-kfBs2hBJ6PF+6rNkeY7XnJdIihw=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/express/download/@types/express-5.0.0.tgz#13a7d1f75295e90d19ed6e74cab3678488eaa96c"
  integrity sha1-E6fR91KV6Q0Z7W50yrNnhIjqqWw=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/formidable@^1.0.31":
  version "1.2.8"
  resolved "http://registry.npm.qima-inc.com/@types/formidable/download/@types/formidable-1.2.8.tgz#78a95c12606920aeb1165ab2670943d726a79325"
  integrity sha1-eKlcEmBpIK6xFlqyZwlD1yankyU=
  dependencies:
    "@types/node" "*"

"@types/fs-extra@^9.0.1", "@types/fs-extra@^9.0.12", "@types/fs-extra@^9.0.8":
  version "9.0.13"
  resolved "http://registry.npm.qima-inc.com/@types/fs-extra/download/@types/fs-extra-9.0.13.tgz#7594fbae04fe7f1918ce8b3d213f74ff44ac1f45"
  integrity sha1-dZT7rgT+fxkYzos9IT90/0SsH0U=
  dependencies:
    "@types/node" "*"

"@types/glob@*":
  version "8.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/glob/download/@types/glob-8.1.0.tgz#b63e70155391b0584dce44e7ea25190bbc38f2fc"
  integrity sha1-tj5wFVORsFhNzkTn6iUZC7w48vw=
  dependencies:
    "@types/minimatch" "^5.1.2"
    "@types/node" "*"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@types/glob/download/@types/glob-7.2.0.tgz#bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/hapi__joi@*":
  version "17.1.14"
  resolved "http://registry.npm.qima-inc.com/@types/hapi__joi/download/@types/hapi__joi-17.1.14.tgz#937843b8a5d49fbfa8a87cc3527f6575e2140ca8"
  integrity sha1-k3hDuKXUn7+oqHzDUn9ldeIUDKg=

"@types/hapi__joi@^15.0.3":
  version "15.0.4"
  resolved "http://registry.npm.qima-inc.com/@types/hapi__joi/download/@types/hapi__joi-15.0.4.tgz#49e2e1e6da15ade0fdd6db4daf94aecb07bb391b"
  integrity sha1-SeLh5toVreD91ttNr5Suywe7ORs=
  dependencies:
    "@types/hapi__joi" "*"

"@types/http-assert@*":
  version "1.5.5"
  resolved "http://registry.npm.qima-inc.com/@types/http-assert/download/@types/http-assert-1.5.5.tgz#dfb1063eb7c240ee3d3fe213dac5671cfb6a8dbf"
  integrity sha1-37EGPrfCQO49P+IT2sVnHPtqjb8=

"@types/http-errors@*":
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/@types/http-errors/download/@types/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha1-frR3JsORtzRabsNa1/TeRpz1uk8=

"@types/http-proxy@^1.17.5":
  version "1.17.15"
  resolved "http://registry.npm.qima-inc.com/@types/http-proxy/download/@types/http-proxy-1.17.15.tgz#12118141ce9775a6499ecb4c01d02f90fc839d36"
  integrity sha1-EhGBQc6XdaZJnstMAdAvkPyDnTY=
  dependencies:
    "@types/node" "*"

"@types/ioredis@^4.0.10":
  version "4.28.10"
  resolved "http://registry.npm.qima-inc.com/@types/ioredis/download/@types/ioredis-4.28.10.tgz#40ceb157a4141088d1394bb87c98ed09a75a06ff"
  integrity sha1-QM6xV6QUEIjROUu4fJjtCadaBv8=
  dependencies:
    "@types/node" "*"

"@types/ip@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/ip/download/@types/ip-1.1.0.tgz#aec4f5bfd49e4a4c53b590d88c36eb078827a7c0"
  integrity sha1-rsT1v9SeSkxTtZDYjDbrB4gnp8A=
  dependencies:
    "@types/node" "*"

"@types/js-yaml@^3.12.5":
  version "3.12.10"
  resolved "http://registry.npm.qima-inc.com/@types/js-yaml/download/@types/js-yaml-3.12.10.tgz#4d80d0c7dfc570eb4f0be280cb2d67789f977ba5"
  integrity sha1-TYDQx9/FcOtPC+KAyy1neJ+Xe6U=

"@types/json-schema@^7.0.5", "@types/json-schema@^7.0.7", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "http://registry.npm.qima-inc.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://registry.npm.qima-inc.com/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/keygrip@*":
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/@types/keygrip/download/@types/keygrip-1.0.6.tgz#1749535181a2a9b02ac04a797550a8787345b740"
  integrity sha1-F0lTUYGiqbAqwEp5dVCoeHNFt0A=

"@types/koa-bodyparser@^4.2.2", "@types/koa-bodyparser@^4.3.0":
  version "4.3.10"
  resolved "http://registry.npm.qima-inc.com/@types/koa-bodyparser/download/@types/koa-bodyparser-4.3.10.tgz#02b8d3d57579aa7d491d553f1f4058088bfe127f"
  integrity sha1-ArjT1XV5qn1JHVU/H0BYCIv+En8=
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*", "@types/koa-compose@^3.2.5":
  version "3.2.8"
  resolved "http://registry.npm.qima-inc.com/@types/koa-compose/download/@types/koa-compose-3.2.8.tgz#dec48de1f6b3d87f87320097686a915f1e954b57"
  integrity sha1-3sSN4faz2H+HMgCXaGqRXx6VS1c=
  dependencies:
    "@types/koa" "*"

"@types/koa-router@^7.0.31", "@types/koa-router@^7.0.40":
  version "7.4.8"
  resolved "http://registry.npm.qima-inc.com/@types/koa-router/download/@types/koa-router-7.4.8.tgz#471d0c9c20d4caa05721b50b0ae4d08c22a7cb03"
  integrity sha1-Rx0MnCDUyqBXIbULCuTQjCKnywM=
  dependencies:
    "@types/koa" "*"

"@types/koa@*", "@types/koa@^2.0.46", "@types/koa@^2.11.4", "@types/koa@^2.13.0", "@types/koa@^2.13.1":
  version "2.15.0"
  resolved "http://registry.npm.qima-inc.com/@types/koa/download/@types/koa-2.15.0.tgz#eca43d76f527c803b491731f95df575636e7b6f2"
  integrity sha1-7KQ9dvUnyAO0kXMfld9XVjbntvI=
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/koa__router@^8.0.4":
  version "8.0.11"
  resolved "http://registry.npm.qima-inc.com/@types/koa__router/download/@types/koa__router-8.0.11.tgz#d7b37e6db934fc072ea1baa2ab92bc8ac4564f3e"
  integrity sha1-17N+bbk0/Acuobqiq5K8isRWTz4=
  dependencies:
    "@types/koa" "*"

"@types/lodash@^4.14.123", "@types/lodash@^4.14.149", "@types/lodash@^4.14.168":
  version "4.17.7"
  resolved "http://registry.npm.qima-inc.com/@types/lodash/download/@types/lodash-4.17.7.tgz#2f776bcb53adc9e13b2c0dfd493dfcbd7de43612"
  integrity sha1-L3dry1OtyeE7LA39ST38vX3kNhI=

"@types/long@^4.0.1":
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/long/download/@types/long-4.0.2.tgz#b74129719fc8d11c01868010082d483b7545591a"
  integrity sha1-t0EpcZ/I0RwBhoAQCC1IO3VFWRo=

"@types/mdast@^3.0.0":
  version "3.0.15"
  resolved "http://registry.npm.qima-inc.com/@types/mdast/download/@types/mdast-3.0.15.tgz#49c524a263f30ffa28b71ae282f813ed000ab9f5"
  integrity sha1-ScUkomPzD/ootxrigvgT7QAKufU=
  dependencies:
    "@types/unist" "^2"

"@types/microtime@^2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/microtime/download/@types/microtime-2.1.0.tgz#adbd99f501a85c88695eb1efd3515810f2563932"
  integrity sha1-rb2Z9QGoXIhpXrHv01FYEPJWOTI=

"@types/mime@^1":
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/@types/mime/download/@types/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/mime@^2.0.3":
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/mime/download/@types/mime-2.0.3.tgz#c893b73721db73699943bfc3653b1deb7faa4a3a"
  integrity sha1-yJO3NyHbc2mZQ7/DZTsd63+qSjo=

"@types/minimatch@*", "@types/minimatch@^5.1.2":
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz#07508b45797cb81ec3f273011b054cd0755eddca"
  integrity sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=

"@types/minimist@^1.2.0":
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/@types/minimist/download/@types/minimist-1.2.5.tgz#ec10755e871497bcd83efe927e43ec46e8c0747e"
  integrity sha1-7BB1XocUl7zYPv6SfkPsRujAdH4=

"@types/ms@*":
  version "0.7.34"
  resolved "http://registry.npm.qima-inc.com/@types/ms/download/@types/ms-0.7.34.tgz#10964ba0dee6ac4cd462e2795b6bebd407303433"
  integrity sha1-EJZLoN7mrEzUYuJ5W2vr1AcwNDM=

"@types/node@*", "@types/node@>=13.7.0":
  version "22.9.0"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-22.9.0.tgz#b7f16e5c3384788542c72dc3d561a7ceae2c0365"
  integrity sha1-t/FuXDOEeIVCxy3D1WGnzq4sA2U=
  dependencies:
    undici-types "~6.19.8"

"@types/node@^10.3.2":
  version "10.17.60"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-10.17.60.tgz#35f3d6213daed95da7f0f73e75bcc6980e90597b"
  integrity sha1-NfPWIT2u2V2n8Pc+dbzGmA6QWXs=

"@types/node@^14.14.22":
  version "14.18.63"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-14.18.63.tgz#1788fa8da838dbb5f9ea994b834278205db6ca2b"
  integrity sha1-F4j6jag427X56plLg0J4IF22yis=

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"
  resolved "http://registry.npm.qima-inc.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.4.tgz#56e2cc26c397c038fab0e3a917a12d5c5909e901"
  integrity sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=

"@types/nunjucks@^3.0.0":
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/@types/nunjucks/download/@types/nunjucks-3.2.1.tgz#02a3ade3dc4d3950029c6466a4034565dba7cf8c"
  integrity sha1-AqOt49xNOVACnGRmpANFZdunz4w=

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/parse-json/download/@types/parse-json-4.0.2.tgz#5950e50960793055845e956c427fc2b0d70c5239"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/pino-pretty@*":
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/pino-pretty/download/@types/pino-pretty-5.0.0.tgz#aa7a61cfd553b051764acfa0a49872f7a09a1722"
  integrity sha1-qnphz9VTsFF2Ss+gpJhy96CaFyI=
  dependencies:
    pino-pretty "*"

"@types/pino-std-serializers@*":
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/pino-std-serializers/download/@types/pino-std-serializers-4.0.0.tgz#1e28b80b554c8222858e99a4e0fc77fd070e10e8"
  integrity sha1-Hii4C1VMgiKFjpmk4Px3/QcOEOg=
  dependencies:
    pino-std-serializers "*"

"@types/pino@^6.3.0", "@types/pino@^6.3.1", "@types/pino@^6.3.7":
  version "6.3.12"
  resolved "http://registry.npm.qima-inc.com/@types/pino/download/@types/pino-6.3.12.tgz#4425db6ced806109c3df957100cba9dfcd73c228"
  integrity sha1-RCXbbO2AYQnD35VxAMup381zwig=
  dependencies:
    "@types/node" "*"
    "@types/pino-pretty" "*"
    "@types/pino-std-serializers" "*"
    sonic-boom "^2.1.0"

"@types/qs@*":
  version "6.9.16"
  resolved "http://registry.npm.qima-inc.com/@types/qs/download/@types/qs-6.9.16.tgz#52bba125a07c0482d26747d5d4947a64daf8f794"
  integrity sha1-UruhJaB8BILSZ0fV1JR6ZNr495Q=

"@types/range-parser@*":
  version "1.2.7"
  resolved "http://registry.npm.qima-inc.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/rimraf@^3.0.0":
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/rimraf/download/@types/rimraf-3.0.2.tgz#a63d175b331748e5220ad48c901d7bbf1f44eef8"
  integrity sha1-pj0XWzMXSOUiCtSMkB17vx9E7vg=
  dependencies:
    "@types/glob" "*"
    "@types/node" "*"

"@types/semver@^7.3.12":
  version "7.5.8"
  resolved "http://registry.npm.qima-inc.com/@types/semver/download/@types/semver-7.5.8.tgz#8268a8c57a3e4abd25c165ecd36237db7948a55e"
  integrity sha1-gmioxXo+Sr0lwWXs02I323lIpV4=

"@types/send@*":
  version "0.17.4"
  resolved "http://registry.npm.qima-inc.com/@types/send/download/@types/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.7"
  resolved "http://registry.npm.qima-inc.com/@types/serve-static/download/@types/serve-static-1.15.7.tgz#22174bbd74fb97fe303109738e9b5c2f3064f714"
  integrity sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/source-list-map@*":
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/@types/source-list-map/download/@types/source-list-map-0.1.6.tgz#164e169dd061795b50b83c19e4d3be09f8d3a454"
  integrity sha1-Fk4WndBheVtQuDwZ5NO+CfjTpFQ=

"@types/sqlstring@^2.2.1":
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/@types/sqlstring/download/@types/sqlstring-2.3.0.tgz#5960ade0166cfaaa4673fc74ec8157a08d06c89e"
  integrity sha1-WWCt4BZs+qpGc/x07IFXoI0GyJ4=

"@types/tapable@^1":
  version "1.0.12"
  resolved "http://registry.npm.qima-inc.com/@types/tapable/download/@types/tapable-1.0.12.tgz#bc2cab12e87978eee89fb21576b670350d6d86ab"
  integrity sha1-vCyrEuh5eO7on7IVdrZwNQ1thqs=

"@types/uglify-js@*":
  version "3.17.5"
  resolved "http://registry.npm.qima-inc.com/@types/uglify-js/download/@types/uglify-js-3.17.5.tgz#905ce03a3cbbf2e31cbefcbc68d15497ee2e17df"
  integrity sha1-kFzgOjy78uMcvvy8aNFUl+4uF98=
  dependencies:
    source-map "^0.6.1"

"@types/unist@^2", "@types/unist@^2.0.0", "@types/unist@^2.0.2":
  version "2.0.11"
  resolved "http://registry.npm.qima-inc.com/@types/unist/download/@types/unist-2.0.11.tgz#11af57b127e32487774841f7a4e54eab166d03c4"
  integrity sha1-Ea9XsSfjJId3SEH3pOVOqxZtA8Q=

"@types/url-parse@^1.4.3":
  version "1.4.11"
  resolved "http://registry.npm.qima-inc.com/@types/url-parse/download/@types/url-parse-1.4.11.tgz#01f8faa7b8bfd438e5f5efb8337a74513a15602b"
  integrity sha1-Afj6p7i/1Djl9e+4M3p0UToVYCs=

"@types/webpack-dev-server@^3.11.1", "@types/webpack-dev-server@^3.11.5":
  version "3.11.6"
  resolved "http://registry.npm.qima-inc.com/@types/webpack-dev-server/download/@types/webpack-dev-server-3.11.6.tgz#d8888cfd2f0630203e13d3ed7833a4d11b8a34dc"
  integrity sha1-2IiM/S8GMCA+E9PteDOk0RuKNNw=
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-static" "*"
    "@types/webpack" "^4"
    http-proxy-middleware "^1.0.0"

"@types/webpack-sources@*":
  version "3.2.3"
  resolved "http://registry.npm.qima-inc.com/@types/webpack-sources/download/@types/webpack-sources-3.2.3.tgz#b667bd13e9fa15a9c26603dce502c7985418c3d8"
  integrity sha1-tme9E+n6FanCZgPc5QLHmFQYw9g=
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    source-map "^0.7.3"

"@types/webpack@^4", "@types/webpack@^4.41.26":
  version "4.41.40"
  resolved "http://registry.npm.qima-inc.com/@types/webpack/download/@types/webpack-4.41.40.tgz#41ea11cfafe08de24c3ef410c58976350667e2d1"
  integrity sha1-QeoRz6/gjeJMPvQQxYl2NQZn4tE=
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    anymatch "^3.0.0"
    source-map "^0.6.0"

"@types/yauzl@^2.9.1":
  version "2.10.3"
  resolved "http://registry.npm.qima-inc.com/@types/yauzl/download/@types/yauzl-2.10.3.tgz#e9b2808b4f109504a03cda958259876f61017999"
  integrity sha1-6bKAi08QlQSgPNqVglmHb2EBeZk=
  dependencies:
    "@types/node" "*"

"@typescript-eslint/eslint-plugin@5.50.0", "@typescript-eslint/eslint-plugin@^4.22.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.50.0.tgz#fb48c31cadc853ffc1dc35373f56b5e2a8908fe9"
  integrity sha1-+0jDHK3IU//B3DU3P1a14qiQj+k=
  dependencies:
    "@typescript-eslint/scope-manager" "5.50.0"
    "@typescript-eslint/type-utils" "5.50.0"
    "@typescript-eslint/utils" "5.50.0"
    debug "^4.3.4"
    grapheme-splitter "^1.0.4"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    regexpp "^3.2.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@^4.24.0":
  version "4.33.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-4.33.0.tgz#6f2a786a4209fa2222989e9380b5331b2810f7fd"
  integrity sha1-byp4akIJ+iIimJ6TgLUzGygQ9/0=
  dependencies:
    "@types/json-schema" "^7.0.7"
    "@typescript-eslint/scope-manager" "4.33.0"
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/typescript-estree" "4.33.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/parser@5.50.0", "@typescript-eslint/parser@^4.22.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.50.0.tgz#a33f44b2cc83d1b7176ec854fbecd55605b0b032"
  integrity sha1-oz9EssyD0bcXbshU++zVVgWwsDI=
  dependencies:
    "@typescript-eslint/scope-manager" "5.50.0"
    "@typescript-eslint/types" "5.50.0"
    "@typescript-eslint/typescript-estree" "5.50.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@4.33.0":
  version "4.33.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-4.33.0.tgz#d38e49280d983e8772e29121cf8c6e9221f280a3"
  integrity sha1-045JKA2YPody4pEhz4xukiHygKM=
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"

"@typescript-eslint/scope-manager@5.50.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.50.0.tgz#90b8a3b337ad2c52bbfe4eac38f9164614e40584"
  integrity sha1-kLijszetLFK7/k6sOPkWRhTkBYQ=
  dependencies:
    "@typescript-eslint/types" "5.50.0"
    "@typescript-eslint/visitor-keys" "5.50.0"

"@typescript-eslint/type-utils@5.50.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.50.0.tgz#509d5cc9728d520008f7157b116a42c5460e7341"
  integrity sha1-UJ1cyXKNUgAI9xV7EWpCxUYOc0E=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.50.0"
    "@typescript-eslint/utils" "5.50.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@4.33.0":
  version "4.33.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/types/download/@typescript-eslint/types-4.33.0.tgz#a1e59036a3b53ae8430ceebf2a919dc7f9af6d72"
  integrity sha1-oeWQNqO1OuhDDO6/KpGdx/mvbXI=

"@typescript-eslint/types@5.50.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/types/download/@typescript-eslint/types-5.50.0.tgz#c461d3671a6bec6c2f41f38ed60bd87aa8a30093"
  integrity sha1-xGHTZxpr7GwvQfOO1gvYeqijAJM=

"@typescript-eslint/typescript-estree@4.33.0":
  version "4.33.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-4.33.0.tgz#0dfb51c2908f68c5c08d82aefeaf166a17c24609"
  integrity sha1-DftRwpCPaMXAjYKu/q8WahfCRgk=
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"
    debug "^4.3.1"
    globby "^11.0.3"
    is-glob "^4.0.1"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/typescript-estree@5.50.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.50.0.tgz#0b9b82975bdfa40db9a81fdabc7f93396867ea97"
  integrity sha1-C5uCl1vfpA25qB/avH+TOWhn6pc=
  dependencies:
    "@typescript-eslint/types" "5.50.0"
    "@typescript-eslint/visitor-keys" "5.50.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.50.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.50.0.tgz#807105f5ffb860644d30d201eefad7017b020816"
  integrity sha1-gHEF9f+4YGRNMNIB7vrXAXsCCBY=
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.50.0"
    "@typescript-eslint/types" "5.50.0"
    "@typescript-eslint/typescript-estree" "5.50.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@4.33.0":
  version "4.33.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-4.33.0.tgz#2a22f77a41604289b7a186586e9ec48ca92ef1dd"
  integrity sha1-KiL3ekFgQom3oYZYbp7EjKku8d0=
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    eslint-visitor-keys "^2.0.0"

"@typescript-eslint/visitor-keys@5.50.0":
  version "5.50.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.50.0.tgz#b752ffc143841f3d7bc57d6dd01ac5c40f8c4903"
  integrity sha1-t1L/wUOEHz17xX1t0BrFxA+MSQM=
  dependencies:
    "@typescript-eslint/types" "5.50.0"
    eslint-visitor-keys "^3.3.0"

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz#bd850604b4042459a5a41cd7d338cbed695ed964"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz#3c3d3b271bddfc84deb00f71344438311d52ffb4"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz#203f676e333b96c9da2eeab3ccef33c45928b6a2"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz#a1442d269c5feb23fcbc9ef759dac3547f29de00"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz#647f8892cd2043a82ac0c8c5e75c36f1d9159f27"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz#c05256b71244214671f4b08ec108ad63b70eddb8"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz#25d8884b76839871a08a6c6f806c3979ef712f07"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz#4fed8beac9b8c14f8c58b70d124d549dd1fe5790"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz#5a4138d5a6292ba18b04c5ae49717e4167965346"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz#15c7a0fbaae83fb26143bbacf6d6df1702ad39e4"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz#f19ca0b76a6dc55623a09cffa769e838fa1e1c95"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz#04d33b636f78e6a6813227e82402f7637b6229ab"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz#3fe6d79d3f0f922183aa86002c42dd256cfee9cf"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz#50bc70ec68ded8e2763b01a1418bf43491a7a49c"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz#2211181e5b31326443cc8112eb9f0b9028721a61"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz#9d48e44826df4a6598294aa6c87469d642fff65e"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz#3031115d79ac5bd261556cecc3fa90a3ef451914"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz#4935d54c85fef637b00ce9f52377451d00d47899"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

"@youzan-cloud/utils@3.2.6":
  version "3.2.6"
  resolved "http://registry.npm.qima-inc.com/@youzan-cloud/utils/download/@youzan-cloud/utils-3.2.6.tgz#40fb15fd5c6697aba1edcd32ef1cf11656b936c9"
  integrity sha1-QPsV/Vxml6uh7c0y7xzxFla5Nsk=
  dependencies:
    tslib "^2.5.3"

"@youzan/access-core@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/access-core/download/@youzan/access-core-1.0.0.tgz#83739a2bf8b45db370f6cd4e2d444f61fbbe008d"
  integrity sha1-g3OaK/i0XbNw9s1OLURPYfu+AI0=

"@youzan/ae-cli@^1.0.5":
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/@youzan/ae-cli/download/@youzan/ae-cli-1.0.5.tgz#bcee91d17a30f3dda19edb0ae770f3b4c68966f4"
  integrity sha1-vO6R0Xow892hntsK53DztMaJZvQ=
  dependencies:
    "@tsconfig/node14" "^1.0.0"
    "@types/commander" "^2.12.2"
    "@types/koa" "^2.13.1"
    "@youzan/box" "^1.0.4"
    "@youzan/youzan-env" "^1.0.2"
    chalk "4.1.0"
    commander "^5.1.0"
    koa "^2.13.1"
    source-map-support "^0.5.19"

"@youzan/apollox@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/apollox/download/@youzan/apollox-1.0.0.tgz#25ef358a271fe4b3405c23f86b076a3471513cc1"
  integrity sha1-Je81iicf5LNAXCP4awdqNHFRPME=
  dependencies:
    "@youzan/youzan-env" "1.0.0"
    debug "4.1.1"
    lightning-request "1.0.0"
    properties-parser "0.3.1"

"@youzan/apollox@2.2.4":
  version "2.2.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/apollox/download/@youzan/apollox-2.2.4.tgz#a00a113bc3348f7b0c93cce24d37aad2b4dcf334"
  integrity sha1-oAoRO8M0j3sMk8ziTTeq0rTc8zQ=
  dependencies:
    "@youzan/lightning-request" "^1.0.3"
    "@youzan/youzan-env" "1.0.2"
    debug "4.1.1"
    properties-parser "0.3.1"

"@youzan/assets-route-plugin@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/assets-route-plugin/download/@youzan/assets-route-plugin-2.1.0.tgz#6403d53fe0fabf93efc531e9c52055f6fb1b4352"
  integrity sha1-ZAPVP+D6v5PvxTHpxSBV9vsbQ1I=
  dependencies:
    astroboy-router "2.3.0"
    reflect-metadata "^0.1.12"
    tslib "^2.1.0"

"@youzan/biz-plugin-logger@1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/biz-plugin-logger/download/@youzan/biz-plugin-logger-1.1.1.tgz#f8b4c35cf324afa0394cd08bf31e5e7ce8b0db07"
  integrity sha1-+LTDXPMkr6A5TNCL8x5efOiw2wc=

"@youzan/biz-plugin-rig@1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/biz-plugin-rig/download/@youzan/biz-plugin-rig-1.1.0.tgz#8cbc1200d5a5b2cb1f2ae5b34d1d5f945ebfc0fb"
  integrity sha1-jLwSANWlsssfKuWzTR1flF6/wPs=
  dependencies:
    "@youzan/rig-sdk" "2.1.1"

"@youzan/biz-plugin-session@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/biz-plugin-session/download/@youzan/biz-plugin-session-2.1.0.tgz#1357d4d17558074b761a5168c4660bbbf95256dd"
  integrity sha1-E1fU0XVYB0t2GlFoxGYLu/lSVt0=
  dependencies:
    "@youzan/utils" "^3.0.5"
    "@youzan/youzan-skynet-logger" "1.0.0"
    cookies "^0.8.0"
    lodash "^4.17.21"

"@youzan/box@^1.0.4":
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/box/download/@youzan/box-1.0.4.tgz#75df1bae90ef9d324682f04596a2d44a122602a3"
  integrity sha1-dd8brpDvnTJGgvBFlqLUShImAqM=
  dependencies:
    "@tsconfig/node14" "^1.0.0"
    "@types/koa" "^2.13.0"
    "@types/koa-compose" "^3.2.5"
    "@types/lodash" "^4.14.168"
    "@types/pino" "^6.3.7"
    "@youzan/dubbo-resource" "^1.0.1"
    "@youzan/future" "^1.0.2"
    "@youzan/youzan-env" "^1.0.2"
    "@youzan/youzan-oss" "^1.0.0"
    "@youzan/youzan-skynet-logger" "^1.0.0"
    debug "^4.3.1"
    globby "^11.0.2"
    js-yaml "^4.0.0"
    koa "^2.13.1"
    koa-body "^4.2.0"
    koa-compose "^4.1.0"
    lodash "^4.17.21"
    pino "^6.11.2"
    pino-pretty "^4.7.1"
    typescript "^4.2.2"

"@youzan/cdn-fallback@1.2.0":
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/cdn-fallback/download/@youzan/cdn-fallback-1.2.0.tgz#cefee65790ed6430bbe83067cf370ac9f4254a2e"
  integrity sha1-zv7mV5DtZDC76DBnzzcKyfQlSi4=

"@youzan/dubbo-client-node@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-client-node/download/@youzan/dubbo-client-node-1.0.0.tgz#38b0c3ca343c8d19770d1da43f0e49dcf6a068bd"
  integrity sha1-OLDDyjQ8jRl3DR2kPw5J3PagaL0=
  dependencies:
    byte "^2.0.0"
    hessian.js "^2.9.0"
    ip "^1.1.5"

"@youzan/dubbo-invoke@1.3.3", "@youzan/dubbo-invoke@^1.2.1", "@youzan/dubbo-invoke@^1.2.3":
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-invoke/download/@youzan/dubbo-invoke-1.3.3.tgz#19194e880aefdd5f3801c7ca37ca2d0f822f3fbc"
  integrity sha1-GRlOiArv3V84AcfKN8otD4IvP7w=
  dependencies:
    "@youzan/dubbo-client-node" "1.0.0"
    "@youzan/rontgen" "2.4.1"
    "@youzan/youzan-env" "1.1.0"
    graceful-error "^1.1.5"
    zan-json-parse "1.0.2"

"@youzan/dubbo-resource@^1.0.1":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-resource/download/@youzan/dubbo-resource-1.0.3.tgz#bb69cd21cf09e1040d8d33294ddb94cdb2e40418"
  integrity sha1-u2nNIc8J4QQNjTMpTduUzbLkBBg=
  dependencies:
    "@youzan/dubbo-invoke" "^1.2.3"

"@youzan/dynamic-domain-plugin-wsc-pc@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/dynamic-domain-plugin-wsc-pc/download/@youzan/dynamic-domain-plugin-wsc-pc-2.1.0.tgz#97de7c0d95deb2d358a24d1f670f7beb403dc764"
  integrity sha1-l958DZXestNYok0fZw9760A9x2Q=

"@youzan/engine-service@^3.0.2":
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/engine-service/download/@youzan/engine-service-3.0.3.tgz#261c8801d2e5ff3d5db338002cf030c00af76e2e"
  integrity sha1-JhyIAdLl/z1dszgALPAwwAr3bi4=
  dependencies:
    "@koa/router" "^9.4.0"
    "@tsconfig/node14" "^1.0.0"
    "@types/js-yaml" "^3.12.5"
    "@types/koa" "^2.11.4"
    "@types/koa-bodyparser" "^4.3.0"
    "@types/koa__router" "^8.0.4"
    "@types/node" "^14.14.22"
    "@types/pino" "^6.3.1"
    "@types/rimraf" "^3.0.0"
    "@types/sqlstring" "^2.2.1"
    "@youzan/future" "^1.0.2"
    "@youzan/monitor-core" "^1.0.1"
    "@youzan/wire-proto" "^2.0.1"
    "@youzan/youzan-env" "^1.0.0"
    "@youzan/youzan-oss" "^1.0.0"
    "@youzan/youzan-skynet-logger" "^1.0.0"
    axios "^0.20.0"
    compressing "^1.5.1"
    koa "^2.13.0"
    koa-bodyparser "^4.3.0"
    mysql2 "^2.2.5"
    nanoid "^3.1.16"
    pino "^6.11.0"
    pino-pretty "^4.2.1"
    rimraf "^3.0.2"
    source-map-support "^0.5.19"
    sqlstring "^2.3.2"
    typescript "^4.1.3"

"@youzan/eslint-config-koko@3.19.2":
  version "3.19.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/eslint-config-koko/download/@youzan/eslint-config-koko-3.19.2.tgz#1e287d81c75a558380ddf4f496b8e5c0572463ef"
  integrity sha1-Hih9gcdaVYOA3fT0lrjlwFckY+8=
  dependencies:
    "@kokojs/shared" "3.19.2"
    "@typescript-eslint/eslint-plugin" "^4.22.0"
    "@typescript-eslint/parser" "^4.22.0"
    "@youzan/eslint-plugin-koko" "3.19.2"
    "@youzan/eslint-plugin-location-check" "^1.0.0"
    babel-eslint "^10.1.0"
    confusing-browser-globals-fresh "^1.0.2"
    eslint-config-airbnb-base "^14.2.1"
    eslint-config-prettier "^6.11.0"
    eslint-plugin-import "^2.22.1"
    eslint-plugin-prettier "^3.4.0"
    eslint-plugin-react "^7.23.2"
    eslint-plugin-react-hooks "^4.2.0"
    eslint-plugin-vue "^7.9.0"
    eslint-plugin-youzan "^1.0.0"
    vue-eslint-parser "^7.6.0"

"@youzan/eslint-plugin-koko@3.19.2":
  version "3.19.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/eslint-plugin-koko/download/@youzan/eslint-plugin-koko-3.19.2.tgz#b4d307bd11d645c4997ebdbe2d114c3a85ea4253"
  integrity sha1-tNMHvRHWRcSZfr2+LRFMOoXqQlM=
  dependencies:
    "@typescript-eslint/experimental-utils" "^4.24.0"

"@youzan/eslint-plugin-location-check@^1.0.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/eslint-plugin-location-check/download/@youzan/eslint-plugin-location-check-1.1.0.tgz#a5950579df9f3a1092f01faf996c04f5ee48f4c4"
  integrity sha1-pZUFed+fOhCS8B+vmWwE9e5I9MQ=
  dependencies:
    requireindex "~1.1.0"

"@youzan/future@^1.0.2":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/future/download/@youzan/future-1.1.0.tgz#729076c2e7cdf1fe202b9ff455f957ab4f7b7aef"
  integrity sha1-cpB2wufN8f4gK5/0VflXq097eu8=

"@youzan/koko-plugin-ae@^1.0.4":
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/@youzan/koko-plugin-ae/download/@youzan/koko-plugin-ae-1.0.7.tgz#141d9250bf307f619a012fec3cdb2757c278a13f"
  integrity sha1-FB2SUL8wf2GaAS/sPNsnV8J4oT8=
  dependencies:
    "@kokojs/shared" "^3.17.1"
    "@tsconfig/node14" "^1.0.0"
    "@youzan/ae-cli" "^1.0.5"
    "@youzan/youzan-oss" "^1.0.0"
    compressing "^1.5.1"
    shelljs "^0.8.4"
    zan-ajax "^3.0.0"

"@youzan/lightning-request@^1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/lightning-request/download/@youzan/lightning-request-1.0.3.tgz#330a6fd775148b1c1d0ba9fedd699fef6b1f59c9"
  integrity sha1-Mwpv13UUixwdC6n+3Wmf72sfWck=
  dependencies:
    "@youzan/sync-rpc" "^1.3.8"

"@youzan/matrix-cli@^1.0.11":
  version "1.0.14"
  resolved "http://registry.npm.qima-inc.com/@youzan/matrix-cli/download/@youzan/matrix-cli-1.0.14.tgz#4a273368cd88f0ca42cd28be1887286de66b5d86"
  integrity sha1-SiczaM2I8MpCzSi+GIcobeZrXYY=
  dependencies:
    "@youzan/apollox" "1.0.0"
    "@youzan/engine-service" "^3.0.2"
    "@youzan/monitor-core" "1.0.1"
    "@youzan/youzan-env" "1.0.0"
    "@youzan/youzan-oss" "1.0.0"
    axios "^0.21.1"
    chalk "4.1.0"
    cli-table "0.3.1"
    commander "5.1.0"
    compressing "^1.5.1"
    graceful-config "1.0.0"
    lodash "4.17.19"
    minimist "1.2.5"
    nanoid "^3.1.16"
    open "7.1.0"
    require-from-string "2.0.2"
    socket.io "^2.3.0"
    source-map-support "^0.5.19"
    write-file-atomic "3.0.3"

"@youzan/micro-app-plugin@^2.4.0":
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/micro-app-plugin/download/@youzan/micro-app-plugin-2.4.1.tgz#5de0c69be0f583091d9735df00257e34b37f702d"
  integrity sha1-XeDGm+D1gwkdlzXfACV+NLN/cC0=
  dependencies:
    "@youzan/micro-app-shared" "2.5.1"
    "@youzan/utils" "^3"
    "@youzan/utils-shop" "^1.3.0"
    path-to-regexp "^1.1.1"
    tslib "^2"

"@youzan/micro-app-shared@2.5.1":
  version "2.5.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/micro-app-shared/download/@youzan/micro-app-shared-2.5.1.tgz#c7e9451a6b91eda89f86d43208ae98126588975a"
  integrity sha1-x+lFGmuR7aifhtQyCK6YEmWIl1o=
  dependencies:
    tslib "^2"

"@youzan/monitor-core@1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/monitor-core/download/@youzan/monitor-core-1.0.1.tgz#7c18fad672e4c6ac4a4a226e5859f19d418a982c"
  integrity sha1-fBj61nLkxqxKSiJuWFnxnUGKmCw=
  dependencies:
    "@grpc/grpc-js" "^1.1.5"
    "@grpc/proto-loader" "0.5.4"
    "@youzan/youzan-skynet-logger" "^0.0.3"
    debug "^4.1.1"
    split2 "^3.1.1"

"@youzan/monitor-core@^1.0.1":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/monitor-core/download/@youzan/monitor-core-1.0.2.tgz#d82efb347db6a6be66daaeb381eace90b1d25081"
  integrity sha1-2C77NH22pr5m2q6zgerOkLHSUIE=
  dependencies:
    "@grpc/grpc-js" "^1.1.5"
    "@grpc/proto-loader" "0.5.4"
    "@youzan/youzan-skynet-logger" "^1.0.0"
    debug "^4.1.1"
    split2 "^3.1.1"

"@youzan/monitor@1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/monitor/download/@youzan/monitor-1.0.3.tgz#5917d8cbcc4651820ef75dce336ad3224bb715b5"
  integrity sha1-WRfYy8xGUYIO913OM2rTIku3FbU=
  dependencies:
    "@youzan/youzan-env" "^1.0.0"
    "@youzan/youzan-skynet-logger" "^1.0.0"
    debug "^4.1.1"
    fast-json-stringify "^2.2.3"
    split2 "^3.1.1"

"@youzan/node-qiniu@1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/node-qiniu/download/@youzan/node-qiniu-1.0.2.tgz#5bbb2f752a613cf036a540ee2dbf1b9ad1f7862f"
  integrity sha1-W7svdSphPPA2pUDuLb8bmtH3hi8=
  dependencies:
    agentkeepalive "3.3.0"
    crc32 "0.2.2"
    encodeurl "^1.0.1"
    formstream "1.1.0"
    mime "2.3.1"
    tunnel-agent "0.6.0"
    urllib "2.22.0"

"@youzan/pay-gateway-plugin@^0.1.5":
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/@youzan/pay-gateway-plugin/download/@youzan/pay-gateway-plugin-0.1.5.tgz#a73cb77582e0832aafea4b52d3563b788e1abfe3"
  integrity sha1-pzy3dYLggyqv6ktS01Y7eI4av+M=

"@youzan/plugin-shop-ability@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-shop-ability/download/@youzan/plugin-shop-ability-1.0.0.tgz#5f80a32f43e6c52570c67541e853a38531e798a4"
  integrity sha1-X4CjL0PmxSVwxnVB6FOjhTHnmKQ=
  dependencies:
    lodash "^4.17.21"

"@youzan/rig-sdk@2.1.1":
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/rig-sdk/download/@youzan/rig-sdk-2.1.1.tgz#e00c9cbbf9707ff5ef9e56f732cfe546e0abed89"
  integrity sha1-4Aycu/lwf/Xvnlb3Ms/lRuCr7Yk=
  dependencies:
    "@youzan/dubbo-invoke" "^1.2.1"
    "@youzan/youzan-env" "^1.0.2"
    "@youzan/youzan-skynet-logger" "^1.0.0"
    lodash.get "^4.4.2"
    path-to-regexp "^3.1.0"

"@youzan/rontgen-core@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen-core/download/@youzan/rontgen-core-2.1.0.tgz#1f0a5f14c4a92800f913c87b3a787baf5d6759d0"
  integrity sha1-HwpfFMSpKAD5E8h7Onh7r11nWdA=

"@youzan/rontgen@2.4.0":
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen/download/@youzan/rontgen-2.4.0.tgz#08edc9d7a725c3e85b4ded28149b7d6a8bf8ca36"
  integrity sha1-CO3J16clw+hbTe0oFJt9aov4yjY=
  dependencies:
    "@youzan/rontgen-core" "2.1.0"
    "@youzan/youzan-env" "^1.0.1"
    "@youzan/youzan-skynet-logger" "1.0.0"
    debug "^4.3.1"

"@youzan/rontgen@2.4.1":
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen/download/@youzan/rontgen-2.4.1.tgz#1f9f5f8cfa41a78bbd804579116375ee7068fc28"
  integrity sha1-H59fjPpBp4u9gEV5EWN17nBo/Cg=
  dependencies:
    "@youzan/rontgen-core" "2.1.0"
    "@youzan/youzan-env" "^1.0.1"
    "@youzan/youzan-skynet-logger" "1.0.0"
    debug "^4.3.1"

"@youzan/runtime-core@^1.0.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/runtime-core/download/@youzan/runtime-core-1.1.1.tgz#204d38f06baf40fc7e79029dc1184fbc16193936"
  integrity sha1-IE048GuvQPx+eQKdwRhPvBYZOTY=
  dependencies:
    tslib "^2.1.0"

"@youzan/runtime-plugin@2.1.3":
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/runtime-plugin/download/@youzan/runtime-plugin-2.1.3.tgz#1f64b87f0970f1140fe2efe1721c453c61468320"
  integrity sha1-H2S4fwlw8RQP4u/hchxFPGFGgyA=
  dependencies:
    "@youzan/access-core" "^1.0.0"
    "@youzan/runtime-core" "^1.0.1"
    tslib "^2.1.0"

"@youzan/runtime@3.1.3":
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/runtime/download/@youzan/runtime-3.1.3.tgz#d6f58dcb823124a0db46c08ab0312fe2ee926f7a"
  integrity sha1-1vWNy4IxJKDbRsCKsDEv4u6Sb3o=
  dependencies:
    "@youzan/apollox" "2.2.4"
    "@youzan/monitor" "1.0.3"
    "@youzan/youzan-env" "1.0.2"
    "@youzan/youzan-skynet-logger" "3.2.2"
    dnscache "1.0.2"
    graceful-error "^1.1.5"
    lightning-request "1.0.0"
    lru-cache "5.1.1"
    tslib "1.10.0"

"@youzan/skynet-logger-plugin@1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/skynet-logger-plugin/download/@youzan/skynet-logger-plugin-1.1.1.tgz#e20d9b56ac04c062035f3045b69b4723f4014caf"
  integrity sha1-4g2bVqwEwGIDXzBFtptHI/QBTK8=
  dependencies:
    util "^0.12.2"

"@youzan/stylelint-config-koko@^3.1.2-beta.1":
  version "3.33.14"
  resolved "http://registry.npm.qima-inc.com/@youzan/stylelint-config-koko/download/@youzan/stylelint-config-koko-3.33.14.tgz#5542d8b397a06e04e9085d74c27b2387f333525f"
  integrity sha1-VULYs5egbgTpCF10wnsjh/MzUl8=
  dependencies:
    "@youzan/stylelint-plugin-koko" "3.33.14"
    stylelint-config-prettier "^8.0.2"
    stylelint-config-standard "^20.0.0"

"@youzan/stylelint-plugin-koko@3.33.14":
  version "3.33.14"
  resolved "http://registry.npm.qima-inc.com/@youzan/stylelint-plugin-koko/download/@youzan/stylelint-plugin-koko-3.33.14.tgz#4a75ea5c366f4dbbbfcf8269eaeb4c858bac5d21"
  integrity sha1-SnXqXDZvTbu/z4Jp6utMhYusXSE=

"@youzan/sync-rpc@^1.3.8":
  version "1.3.8"
  resolved "http://registry.npm.qima-inc.com/@youzan/sync-rpc/download/@youzan/sync-rpc-1.3.8.tgz#a552dd1ecce15f1cae7a6e12a2373f3a6a0013f0"
  integrity sha1-pVLdHszhXxyuem4Sojc/OmoAE/A=
  dependencies:
    get-port "^3.1.0"

"@youzan/utils-shop@1.3.0", "@youzan/utils-shop@^1.1.1", "@youzan/utils-shop@^1.3.0":
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils-shop/download/@youzan/utils-shop-1.3.0.tgz#e75df528b8cbd7d9b7dac77f1784ceeae2b9573d"
  integrity sha1-5131KLjL19m32sd/F4TO6uK5Vz0=

"@youzan/utils@3.0.2":
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-3.0.2.tgz#0725fc94a05db236bca1e0054917da50eb248aae"
  integrity sha1-ByX8lKBdsja8oeAFSRfaUOskiq4=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^6.0.2"
    exif-js "^2.3.0"
    fecha "^4.2.0"
    query-string "^6.13.7"
    raf "^3.4.1"
    tslib "^2.0.3"
    url-parse "^1.4.7"

"@youzan/utils@^2.4.0":
  version "2.4.9"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-2.4.9.tgz#99e24d09967917c4f9868908fd76c8df2a0a8be9"
  integrity sha1-meJNCZZ5F8T5hokI/XbI3yoKi+k=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^5.2.2"
    compare-versions "^3.4.0"
    exif-js "^2.3.0"
    fecha "^3.0.2"
    lodash "^4.17.11"
    query-string "5"
    raf "^3.4.1"
    tslib "^1.9.3"
    url-parse "^1.4.4"
    zan-jquery "^1.0.2"

"@youzan/utils@^3", "@youzan/utils@^3.0.5":
  version "3.2.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-3.2.6.tgz#5062c3eae7eb3ec2fba0f5b0329b3e81f5bfd90b"
  integrity sha1-UGLD6ufrPsL7oPWwMps+gfW/2Qs=
  dependencies:
    "@youzan-cloud/utils" "3.2.6"
    big.js "^6.0.2"
    exif-js "^2.3.0"
    fecha "^4.2.0"
    query-string "5"
    raf "^3.4.1"
    tslib "^2.5.3"
    url-parse "^1.4.7"

"@youzan/wire-pack@^1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/wire-pack/download/@youzan/wire-pack-1.0.1.tgz#edb3baebaed6926389e9b45d970e813f516e7f74"
  integrity sha1-7bO6667WkmOJ6bRdlw6BP1Fuf3Q=

"@youzan/wire-proto@^2.0.1":
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/wire-proto/download/@youzan/wire-proto-2.0.1.tgz#433425f4894705f782f5d6f63554d3aed207048e"
  integrity sha1-QzQl9IlHBfeC9db2NVTTrtIHBI4=
  dependencies:
    "@youzan/future" "^1.0.2"
    "@youzan/wire-pack" "^1.0.1"

"@youzan/wsc-pc-base@^5.3.17":
  version "5.4.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/wsc-pc-base/download/@youzan/wsc-pc-base-5.4.2.tgz#dbf8a6d9bb94af4fdd8b90671d98c718bb9fb466"
  integrity sha1-2/im2buUr0/di5BnHZjHGLuftGY=
  dependencies:
    "@youzan/biz-plugin-logger" "1.1.1"
    "@youzan/biz-plugin-rig" "1.1.0"
    "@youzan/biz-plugin-session" "2.1.0"
    "@youzan/cdn-fallback" "1.2.0"
    "@youzan/dynamic-domain-plugin-wsc-pc" "2.1.0"
    "@youzan/micro-app-plugin" "^2.4.0"
    "@youzan/runtime-plugin" "2.1.3"
    "@youzan/skynet-logger-plugin" "1.1.1"
    "@youzan/utils" "3.0.2"
    "@youzan/utils-shop" "1.3.0"
    "@youzan/youzan-framework" "5.1.13"
    "@youzan/youzan-skynet-logger" "1.0.0"
    bigint-node "1.0.4"
    bowser "^2.9.0"
    date-fns "1.29.0"
    debug "3.1.0"
    koa-bunyan-logger "^2.1.0"
    koa-compose "4.1.0"
    locutus "2.0.10"
    lodash "4.17.5"
    microtime "3.0.0"
    prettyjson "1.2.1"

"@youzan/youzan-env@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-1.0.0.tgz#80d95db7b50076599dc697984235c4de19d39195"
  integrity sha1-gNldt7UAdlmdxpeYQjXE3hnTkZU=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-env@1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-1.0.2.tgz#b07fc222ced1f2b7dc1ff70a4cdce513765f9723"
  integrity sha1-sH/CIs7R8rfcH/cKTNzlE3ZflyM=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-env@1.1.0", "@youzan/youzan-env@^1.0.0", "@youzan/youzan-env@^1.0.1", "@youzan/youzan-env@^1.0.2":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-1.1.0.tgz#82e58f862789dfb36245f34967a99558e9e6b4e3"
  integrity sha1-guWPhieJ37NiRfNJZ6mVWOnmtOM=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-framework@5.1.13":
  version "5.1.13"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-framework/download/@youzan/youzan-framework-5.1.13.tgz#7d2f1f5fe47b57e340d5ab5917ec4713787c30e3"
  integrity sha1-fS8fX+R7V+NA1atZF+xHE3h8MOM=
  dependencies:
    "@hapi/joi" "15.1.1"
    "@types/cookies" "^0.7.1"
    "@types/debug" "^4.1.4"
    "@types/hapi__joi" "^15.0.3"
    "@types/ioredis" "^4.0.10"
    "@types/ip" "^1.1.0"
    "@types/koa-bodyparser" "^4.2.2"
    "@types/koa-router" "^7.0.40"
    "@types/microtime" "^2.1.0"
    "@types/nunjucks" "^3.0.0"
    "@types/pino" "^6.3.0"
    "@youzan/dubbo-invoke" "1.3.3"
    "@youzan/node-qiniu" "1.0.2"
    "@youzan/runtime" "3.1.3"
    "@youzan/youzan-rontgen" "3.1.0"
    astroboy "3.1.5"
    debug "3.1.0"
    graceful-error "^1.1.5"
    ioredis "4.3.0"
    ip "1.1.5"
    jsonp-body "1.0.0"
    lightning-request "^1.0.0"
    lodash "4.17.10"
    microtime "3.0.0"
    nunjucks "3.1.6"
    pino "6.3.2"
    pino-http "5.2.0"
    pino-pretty "4.0.0"
    tslib "^1.10.0"
    utility-types "^3.7.0"
    zan-ajax "2.1.0"

"@youzan/youzan-oss@1.0.0", "@youzan/youzan-oss@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-oss/download/@youzan/youzan-oss-1.0.0.tgz#47fec927f0142779b3deee0d1db42457dc213e5e"
  integrity sha1-R/7JJ/AUJ3mz3u4NHbQkV9whPl4=
  dependencies:
    "@types/mime" "^2.0.3"
    "@youzan/youzan-env" "1.0.0"
    chalk "^4.1.0"
    graceful-error "^1.1.5"
    is-stream "^2.0.0"
    mime "^2.4.6"
    urllib "^2.36.1"
    xml2js "^0.4.23"

"@youzan/youzan-rontgen@3.1.0":
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-rontgen/download/@youzan/youzan-rontgen-3.1.0.tgz#1216cf7315e55f5a91ee9940e5eacf67a3dc0d3e"
  integrity sha1-EhbPcxXlX1qR7plA5erPZ6PcDT4=
  dependencies:
    "@youzan/rontgen" "2.4.0"
    "@youzan/youzan-env" "^1.0.2"
    lodash.merge "^4.6.2"

"@youzan/youzan-skynet-logger@1.0.0", "@youzan/youzan-skynet-logger@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-1.0.0.tgz#1a13495bd237824a045abeaee977b2b2ac5994e9"
  integrity sha1-GhNJW9I3gkoEWr6u6XeysqxZlOk=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"

"@youzan/youzan-skynet-logger@3.2.2":
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-3.2.2.tgz#60163d904fff616934edbdac4d57a2cc53a6cd8e"
  integrity sha1-YBY9kE//YWk07b2sTVeizFOmzY4=
  dependencies:
    "@youzan/youzan-env" "^1.0.2"
    ip "^1.1.5"

"@youzan/youzan-skynet-logger@^0.0.3":
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-0.0.3.tgz#bad3be8aea24597389d6e2b985ff4b273370127c"
  integrity sha1-utO+iuokWXOJ1uK5hf9LJzNwEnw=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"

JSONStream@^1.0.4:
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/JSONStream/download/JSONStream-1.3.5.tgz#3208c1f08d3a4d99261ab64f92302bc15e111ca0"
  integrity sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

a-sync-waterfall@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/a-sync-waterfall/download/a-sync-waterfall-1.0.1.tgz#75b6b6aa72598b497a125e7a2770f14f4c8a1fa7"
  integrity sha1-dba2qnJZi0l6El56J3DxT0yKH6c=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/abort-controller/download/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

accepts@^1.2.2, accepts@^1.3.5, accepts@~1.3.4, accepts@~1.3.8:
  version "1.3.8"
  resolved "http://registry.npm.qima-inc.com/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-jsx@^5.2.0, acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "http://registry.npm.qima-inc.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.0.0:
  version "8.3.4"
  resolved "http://registry.npm.qima-inc.com/acorn-walk/download/acorn-walk-8.3.4.tgz#794dd169c3977edf4ba4ea47583587c5866236b7"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^6.4.1:
  version "6.4.2"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-6.4.2.tgz#35866fd710528e92de10cf06016498e47e39e1e6"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.0.4, acorn@^8.11.0:
  version "8.14.0"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-8.14.0.tgz#063e2c70cac5fb4f6467f0b11152e04c682795b0"
  integrity sha1-Bj4scMrF+09kZ/CxEVLgTGgnlbA=

address@>=0.0.1:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/address/download/address-2.0.3.tgz#e910900615db3d8a20c040d4c710631062fc4ba8"
  integrity sha1-6RCQBhXbPYogwEDUxxBjEGL8S6g=

after@0.8.2:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/after/download/after-0.8.2.tgz#fedb394f9f0e02aa9768e702bda23b505fae7e1f"
  integrity sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8=

agent-base@6:
  version "6.0.2"
  resolved "http://registry.npm.qima-inc.com/agent-base/download/agent-base-6.0.2.tgz#49fff58577cfee3f37176feab4c22e00f86d7f77"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

agentkeepalive@3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/agentkeepalive/download/agentkeepalive-3.3.0.tgz#6d5de5829afd3be2712201a39275fd11c651857c"
  integrity sha1-bV3lgpr9O+JxIgGjknX9EcZRhXw=
  dependencies:
    humanize-ms "^1.2.1"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/aggregate-error/download/aggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/ajv-errors/download/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "http://registry.npm.qima-inc.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.11.0, ajv@^6.12.4, ajv@^6.12.6:
  version "6.12.6"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.17.1"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-8.17.1.tgz#37d9a5c776af6bc92d7f4f9510eba4c0a60d11a6"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/ansi-colors/download/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/ansi-colors/download/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz#69fbc4d6ccbe383f9736934ae34c3f8290f1bf41"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"
  integrity sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-promise@^1.0.0, any-promise@^1.1.0, any-promise@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.0, anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

args@^5.0.1:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/args/download/args-5.0.3.tgz#943256db85021a85684be2f0882f25d796278702"
  integrity sha1-lDJW24UCGoVoS+LwiC8l15YnhwI=
  dependencies:
    camelcase "5.0.0"
    chalk "2.4.2"
    leven "2.1.0"
    mri "1.1.4"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-buffer-byte-length@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.1.tgz#1e5583ec16763540a27ae52eed99ff899223568f"
  integrity sha1-HlWD7BZ2NUCieuUu7Zn/iZIjVo8=
  dependencies:
    call-bind "^1.0.5"
    is-array-buffer "^3.0.4"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/array-flatten/download/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-ify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/array-ify/download/array-ify-1.0.0.tgz#9e528762b4a9066ad163a6962a364418e9626ece"
  integrity sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.8"
  resolved "http://registry.npm.qima-inc.com/array-includes/download/array-includes-3.1.8.tgz#5e370cbe172fdd5dd6530c1d4aadda25281ba97d"
  integrity sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-union@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz#3e4fbcb30a15a7f5bf64cf2faae22d139c2e4904"
  integrity sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.5.tgz#8c35a755c72908719453f87145ca011e39334d0d"
  integrity sha1-jDWnVccpCHGUU/hxRcoBHjkzTQ0=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/array.prototype.flat/download/array.prototype.flat-1.3.2.tgz#1476217df8cff17d72ee8f3ba06738db5b387d18"
  integrity sha1-FHYhffjP8X1y7o87oGc421s4fRg=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.2.tgz#c9a7c6831db8e719d6ce639190146c24bbd3e527"
  integrity sha1-yafGgx245xnWzmORkBRsJLvT5Sc=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz#fe954678ff53034e717ea3352a03f0b0b86f7ffc"
  integrity sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.3.tgz#097972f4255e41bc3425e37dc3f6421cf9aefde6"
  integrity sha1-CXly9CVeQbw0JeN9w/ZCHPmu/eY=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.2.1"
    get-intrinsic "^1.2.3"
    is-array-buffer "^3.0.4"
    is-shared-array-buffer "^1.0.2"

arraybuffer.slice@~0.0.7:
  version "0.0.7"
  resolved "http://registry.npm.qima-inc.com/arraybuffer.slice/download/arraybuffer.slice-0.0.7.tgz#3bbc4275dd584cc1b10809b89d4e8b63a69e7675"
  integrity sha1-O7xCdd1YTMGxCAm4nU6LY6aednU=

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asap@^2.0.3, asap@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "http://registry.npm.qima-inc.com/asn1.js/download/asn1.js-5.4.1.tgz#11a980b84ebb91781ce35b0fdc2ee294e3783f07"
  integrity sha1-EamAuE67kXgc41sP3C7ilON4Pwc=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

assert@^1.1.1:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/assert/download/assert-1.5.1.tgz#038ab248e4ff078e7bc2485ba6e6388466c78f76"
  integrity sha1-A4qySOT/B457wkhbpuY4hGbHj3Y=
  dependencies:
    object.assign "^4.1.4"
    util "^0.10.4"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

astroboy-router@2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/astroboy-router/download/astroboy-router-2.3.0.tgz#751bac900cfe707270f1b314ea1f471a01e7abca"
  integrity sha1-dRuskAz+cHJw8bMU6h9HGgHnq8o=
  dependencies:
    "@types/koa" "^2.0.46"
    "@types/koa-router" "^7.0.31"
    "@types/node" "^10.3.2"
    lodash "^4.17.11"
    reflect-metadata "^0.1.12"
    tslib "^2.1.0"

astroboy@3.1.5:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/astroboy/download/astroboy-3.1.5.tgz#f06541d495c24b3f90b91d36e87ca338ea9e8ea2"
  integrity sha1-8GVB1JXCSz+QuR026HyjOOqejqI=
  dependencies:
    "@types/fs-extra" "^9.0.1"
    "@types/koa" "^2.0.46"
    "@types/koa-compose" "^3.2.5"
    "@types/lodash" "^4.14.123"
    ajv "^6.12.6"
    complete-assign "0.0.2"
    fast-glob "2.2.6"
    fs-extra "9.0.1"
    koa "2.5.0"
    koa-body "2.5.0"
    koa-bodyparser "4.2.1"
    koa-compose "4.1.0"
    koa-router "7.4.0"
    koa-static "4.0.2"
    lodash "4.17.11"
    path-matching "0.0.2"
    tslib "^2.2.0"
    xss "0.3.7"

async-each@^1.0.1:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/async-each/download/async-each-1.0.6.tgz#52f1d9403818c179b7561e11a5d1b77eb2160e77"
  integrity sha1-UvHZQDgYwXm3Vh4RpdG3frIWDnc=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async@^2.6.4:
  version "2.6.4"
  resolved "http://registry.npm.qima-inc.com/async/download/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/at-least-node/download/at-least-node-1.0.0.tgz#602cd4b46e844ad4effc92a8011a3c46e0238dc2"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

atomic-sleep@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/atomic-sleep/download/atomic-sleep-1.0.0.tgz#eb85b77a601fc932cfe432c5acd364a9e2c9075b"
  integrity sha1-64W3emAfyTLP5DLFrNNkqeLJB1s=

autoprefixer@^9.8.6:
  version "9.8.8"
  resolved "http://registry.npm.qima-inc.com/autoprefixer/download/autoprefixer-9.8.8.tgz#fd4bd4595385fa6f06599de749a4d5f7a474957a"
  integrity sha1-/UvUWVOF+m8GWZ3nSaTV96R0lXo=
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001109"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    picocolors "^0.2.1"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@0.18.0:
  version "0.18.0"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.18.0.tgz#32d53e4851efdc0a11993b6cd000789d70c05102"
  integrity sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=
  dependencies:
    follow-redirects "^1.3.0"
    is-buffer "^1.1.5"

axios@0.19.0:
  version "0.19.0"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.19.0.tgz#8e09bff3d9122e133f7b8101c8fbdd00ed3d2ab8"
  integrity sha1-jgm/89kSLhM/e4EByPvdAO09Krg=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

axios@^0.20.0:
  version "0.20.0"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.20.0.tgz#057ba30f04884694993a8cd07fa394cff11c50bd"
  integrity sha1-BXujDwSIRpSZOozQf6OUz/EcUL0=
  dependencies:
    follow-redirects "^1.10.0"

axios@^0.21.1:
  version "0.21.4"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.21.4.tgz#c67b90dc0568e5c1cf2b0b858c43ba28e2eda575"
  integrity sha1-xnuQ3AVo5cHPKwuFjEO6KOLtpXU=
  dependencies:
    follow-redirects "^1.14.0"

axios@^1.2.1:
  version "1.7.7"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-1.7.7.tgz#2f554296f9892a72ac8d8e4c5b79c14a91d0a47f"
  integrity sha1-L1VClvmJKnKsjY5MW3nBSpHQpH8=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "http://registry.npm.qima-inc.com/babel-eslint/download/babel-eslint-10.1.0.tgz#6968e568a910b78fb3779cdd8b6ac2f479943232"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-polyfill@6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-polyfill/download/babel-polyfill-6.26.0.tgz#379937abc67d7895970adc621f284cd966cf2153"
  integrity sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=
  dependencies:
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    regenerator-runtime "^0.10.5"

babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

backo2@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/backo2/download/backo2-1.0.2.tgz#31ab1ac8b129363463e35b3ebb69f4dfcfba7947"
  integrity sha1-MasayLEpNjRj41s+u2n038+6eUc=

bail@^1.0.0:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/bail/download/bail-1.0.5.tgz#b6fa133404a392cbc1f8c4bf63f5953351e7a776"
  integrity sha1-tvoTNASjksvB+MS/Y/WVM1Hnp3Y=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/balanced-match/download/balanced-match-2.0.0.tgz#dc70f920d78db8b858535795867bf48f820633d9"
  integrity sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=

base64-arraybuffer@0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/base64-arraybuffer/download/base64-arraybuffer-0.1.4.tgz#9818c79e059b1355f97e0428a017c838e90ba812"
  integrity sha1-mBjHngWbE1X5fgQooBfIOOkLqBI=

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base64id@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/base64id/download/base64id-2.0.0.tgz#2770ac6bc47d312af97a8bf9a634342e0cd25cb6"
  integrity sha1-J3Csa8R9MSr5eov5pjQ0LgzSXLY=

base@^0.11.1:
  version "0.11.2"
  resolved "http://registry.npm.qima-inc.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

big.js@^6.0.2:
  version "6.2.2"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-6.2.2.tgz#be3bb9ac834558b53b099deef2a1d06ac6368e1a"
  integrity sha1-vju5rINFWLU7CZ3u8qHQasY2jho=

bigint-node@1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/bigint-node/download/bigint-node-1.0.4.tgz#ef5650e4638e7593322e8cac32755a4de5f4de8a"
  integrity sha1-71ZQ5GOOdZMyLoysMnVaTeX03oo=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

bindings@^1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bl@^1.0.0:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/bl/download/bl-1.2.3.tgz#1e8dd80142eac80d7158c9dccc047fb620e035e7"
  integrity sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^4.0.3, bl@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

blob@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.qima-inc.com/blob/download/blob-0.0.5.tgz#d680eeef25f8cd91ad533f5b01eed48e64caf683"
  integrity sha1-1oDu7yX4zZGtUz9bAe7UjmTK9oM=

bluebird@^3.5.5:
  version "3.7.2"
  resolved "http://registry.npm.qima-inc.com/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.0"
  resolved "http://registry.npm.qima-inc.com/bn.js/download/bn.js-4.12.0.tgz#775b3f278efbb9718eec7361f483fb36fbbfea88"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^5.0.0, bn.js@^5.2.1:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/bn.js/download/bn.js-5.2.1.tgz#0bc527a6a0d18d0aa8d5b0538ce4a77dccfa7b70"
  integrity sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A=

body-parser@1.20.3:
  version "1.20.3"
  resolved "http://registry.npm.qima-inc.com/body-parser/download/body-parser-1.20.3.tgz#1953431221c6fb5cd63c4b36d53fab0928e548c6"
  integrity sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "http://registry.npm.qima-inc.com/bonjour/download/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

bowser@^2.9.0:
  version "2.11.0"
  resolved "http://registry.npm.qima-inc.com/bowser/download/bowser-2.11.0.tgz#5ca3c35757a7aa5771500c70a73a9f91ef420a8f"
  integrity sha1-XKPDV1enqldxUAxwpzqfke9CCo8=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.qima-inc.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/browserify-rsa/download/browserify-rsa-4.1.0.tgz#b2fd06b5b75ae297f7ce2dc651f918f5be158c8d"
  integrity sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/browserify-sign/download/browserify-sign-4.2.2.tgz#e78d4b69816d6e3dd1c747e64e9947f9ad79bc7e"
  integrity sha1-541LaYFtbj3Rx0fmTplH+a15vH4=
  dependencies:
    bn.js "^5.2.1"
    browserify-rsa "^4.1.0"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.4"
    inherits "^2.0.4"
    parse-asn1 "^5.1.6"
    readable-stream "^3.6.2"
    safe-buffer "^5.2.1"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.12.0, browserslist@^4.24.0:
  version "4.24.2"
  resolved "http://registry.npm.qima-inc.com/browserslist/download/browserslist-4.24.2.tgz#f5845bc91069dbd55ee89faf9822e1d885d16580"
  integrity sha1-9YRbyRBp29Ve6J+vmCLh2IXRZYA=
  dependencies:
    caniuse-lite "^1.0.30001669"
    electron-to-chromium "^1.5.41"
    node-releases "^2.0.18"
    update-browserslist-db "^1.1.1"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"
  integrity sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/buffer-alloc/download/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  integrity sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "http://registry.npm.qima-inc.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/buffer-fill/download/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"
  integrity sha1-+PeLdniYiO858gXNY39o5wISKyw=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-json@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/buffer-json/download/buffer-json-2.0.0.tgz#f73e13b1e42f196fe2fd67d001c7d7107edd7c23"
  integrity sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "http://registry.npm.qima-inc.com/buffer/download/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.2.1, buffer@^5.5.0:
  version "5.7.1"
  resolved "http://registry.npm.qima-inc.com/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "http://registry.npm.qima-inc.com/buffer/download/buffer-6.0.3.tgz#2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bunyan@~1.8.12:
  version "1.8.15"
  resolved "http://registry.npm.qima-inc.com/bunyan/download/bunyan-1.8.15.tgz#8ce34ca908a17d0776576ca1b2f6cbd916e93b46"
  integrity sha1-jONMqQihfQd2V2yhsvbL2RbpO0Y=
  optionalDependencies:
    dtrace-provider "~0.8"
    moment "^2.19.3"
    mv "~2"
    safe-json-stringify "~1"

byte@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/byte/download/byte-2.0.0.tgz#c6188cf7e4be92daac22f47312f5a1f64091b18a"
  integrity sha1-xhiM9+S+ktqsIvRzEvWh9kCRsYo=
  dependencies:
    debug "^3.1.0"
    long "^4.0.0"
    utility "^1.13.1"

bytes@3.1.2:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

cacache@^12.0.2:
  version "12.0.4"
  resolved "http://registry.npm.qima-inc.com/cacache/download/cacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-content-type/download/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c"
  integrity sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

cache-loader@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/cache-loader/download/cache-loader-4.1.0.tgz#9948cae353aec0a1fcb1eafda2300816ec85387e"
  integrity sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=
  dependencies:
    buffer-json "^2.0.0"
    find-cache-dir "^3.0.0"
    loader-utils "^1.2.3"
    mkdirp "^0.5.1"
    neo-async "^2.6.1"
    schema-utils "^2.0.0"

call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/call-bind/download/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha1-BgFlmcQMVkmMGHadJzC+JCtvo7k=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

call-me-maybe@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/call-me-maybe/download/call-me-maybe-1.0.2.tgz#03f964f19522ba643b1b0693acb9152fe2074baa"
  integrity sha1-A/lk8ZUiumQ7GwaTrLkVL+IHS6o=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-keys@^4.0.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/camelcase-keys/download/camelcase-keys-4.2.0.tgz#a2aa5fb1af688758259c32c141426d78923b9b77"
  integrity sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=
  dependencies:
    camelcase "^4.1.0"
    map-obj "^2.0.0"
    quick-lru "^1.0.0"

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "http://registry.npm.qima-inc.com/camelcase-keys/download/camelcase-keys-6.2.2.tgz#5e755d6ba51aa223ec7d3d52f25778210f9dc3c0"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-5.0.0.tgz#03295527d58bd3cd4aa75363f35b2e8d97be2f42"
  integrity sha1-AylVJ9WL081Kp1Nj81sujZe+L0I=

camelcase@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001669:
  version "1.0.30001680"
  resolved "http://registry.npm.qima-inc.com/caniuse-lite/download/caniuse-lite-1.0.30001680.tgz#5380ede637a33b9f9f1fc6045ea99bd142f3da5e"
  integrity sha1-U4Dt5jejO5+fH8YEXqmb0ULz2l4=

chalk@2.4.2, chalk@^2.0.1, chalk@^2.3.2, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-4.1.0.tgz#4e14870a618d9e2edd97dd8345fd9d9dc315646a"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/character-entities-legacy/download/character-entities-legacy-1.1.4.tgz#94bc1845dce70a5bb9d2ecc748725661293d8fc1"
  integrity sha1-lLwYRdznClu50uzHSHJWYSk9j8E=

character-entities@^1.0.0:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/character-entities/download/character-entities-1.2.4.tgz#e12c3939b7eaf4e5b15e7ad4c5e28e1d48c5b16b"
  integrity sha1-4Sw5Obfq9OWxXnrUxeKOHUjFsWs=

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/character-reference-invalid/download/character-reference-invalid-1.1.4.tgz#083329cda0eae272ab3dbbf37e9a382c13af1560"
  integrity sha1-CDMpzaDq4nKrPbvzfpo4LBOvFWA=

chokidar@^2.0.0, chokidar@^2.1.8:
  version "2.1.8"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.1, chokidar@^3.5.1, chokidar@^3.5.2:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.1.1:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/chownr/download/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz#05bffd7ff928465093314708c93bdfa9bd1f0f5b"
  integrity sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

ci-info@^3.1.1:
  version "3.9.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-3.9.0.tgz#4279a62028a7b1f262f3473fc9605f5e218c59b4"
  integrity sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://registry.npm.qima-inc.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "http://registry.npm.qima-inc.com/cli-spinners/download/cli-spinners-2.9.2.tgz#1773a8f4b9c4d6ac31563df53b3fc1d79462fe41"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-table@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/cli-table/download/cli-table-0.3.1.tgz#f53b05266a8b1a0b934b3d0821e6e2dc5914ae23"
  integrity sha1-9TsFJmqLGguTSz0IIebi3FkUriM=
  dependencies:
    colors "1.0.3"

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-truncate/download/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cliui@^3.0.3:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/clone-deep/download/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone-regexp@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/clone-regexp/download/clone-regexp-2.2.0.tgz#7d65e00885cd8796405c35a737e7a86b7429e36f"
  integrity sha1-fWXgCIXNh5ZAXDWnN+eoa3Qp428=
  dependencies:
    is-regexp "^2.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

cluster-key-slot@^1.0.6:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/cluster-key-slot/download/cluster-key-slot-1.1.2.tgz#88ddaa46906e303b5de30d3153b7d9fe0a0c19ac"
  integrity sha1-iN2qRpBuMDtd4w0xU7fZ/goMGaw=

co-body@^5.1.1:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/co-body/download/co-body-5.2.0.tgz#5a0a658c46029131e0e3a306f67647302f71c124"
  integrity sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ=
  dependencies:
    inflation "^2.0.0"
    qs "^6.4.0"
    raw-body "^2.2.0"
    type-is "^1.6.14"

co-body@^6.0.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/co-body/download/co-body-6.2.0.tgz#afd776d60e5659f4eee862df83499698eb1aea1b"
  integrity sha1-r9d21g5WWfTu6GLfg0mWmOsa6hs=
  dependencies:
    "@hapi/bourne" "^3.0.0"
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.qima-inc.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.16, colorette@^2.0.7:
  version "2.0.20"
  resolved "http://registry.npm.qima-inc.com/colorette/download/colorette-2.0.20.tgz#9eb793e6833067f7235902fcd3b09917a000a95a"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

colors@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/colors/download/colors-1.0.3.tgz#0433f44d809680fdeb60ed260f1b0c262e82a40b"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

colors@^1.1.2:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/colors/download/colors-1.4.0.tgz#c50491479d4c1bdaed2c9ced32cf7c7dc2360f78"
  integrity sha1-xQSRR51MG9rtLJztMs98fcI2D3g=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@*, commander@5.1.0, commander@^2.20.0, commander@^2.20.3, commander@^2.9.0, commander@^5.1.0, commander@^6.2.0, commander@^7.2.0:
  version "2.20.3"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compare-func@^1.3.1:
  version "1.3.4"
  resolved "http://registry.npm.qima-inc.com/compare-func/download/compare-func-1.3.4.tgz#6b07c4c5e8341119baf44578085bda0f4a823516"
  integrity sha1-awfExeg0ERm69EV4CFvaD0qCNRY=
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^3.0.0"

compare-versions@^3.4.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/compare-versions/download/compare-versions-3.6.0.tgz#1a5689913685e5a87637b8d3ffca75514ec41d62"
  integrity sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI=

complete-assign@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/complete-assign/download/complete-assign-0.0.2.tgz#e35d14be0e9f087f050b2b72c8e87cd7f91691e5"
  integrity sha1-410Uvg6fCH8FCytyyOh81/kWkeU=

component-bind@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/component-bind/download/component-bind-1.0.0.tgz#00c608ab7dcd93897c0009651b1d3a8e1e73bbd1"
  integrity sha1-AMYIq33Nk4l8AAllGx06jh5zu9E=

component-emitter@1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/component-emitter/download/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"
  integrity sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=

component-emitter@^1.2.1, component-emitter@~1.3.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/component-emitter/download/component-emitter-1.3.1.tgz#ef1d5796f7d93f135ee6fb684340b26403c97d17"
  integrity sha1-7x1XlvfZPxNe5vtoQ0CyZAPJfRc=

component-inherit@0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/component-inherit/download/component-inherit-0.0.3.tgz#645fc4adf58b72b649d5cae65135619db26ff143"
  integrity sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM=

compressible@~2.0.18:
  version "2.0.18"
  resolved "http://registry.npm.qima-inc.com/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compressing@^1.5.1:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/compressing/download/compressing-1.10.0.tgz#bca0f734736b5bb05c3a176fef8d60d7f5f322da"
  integrity sha1-vKD3NHNrW7BcOhdv741g1/XzIto=
  dependencies:
    "@eggjs/yauzl" "^2.11.0"
    flushwritable "^1.0.0"
    get-ready "^1.0.0"
    iconv-lite "^0.5.0"
    mkdirp "^0.5.1"
    pump "^3.0.0"
    streamifier "^0.1.1"
    tar-stream "^1.5.2"
    yazl "^2.4.2"

compression@^1.7.4:
  version "1.7.5"
  resolved "http://registry.npm.qima-inc.com/compression/download/compression-1.7.5.tgz#fdd256c0a642e39e314c478f6c2cd654edd74c93"
  integrity sha1-/dJWwKZC454xTEePbCzWVO3XTJM=
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.0.2"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "http://registry.npm.qima-inc.com/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

confusing-browser-globals-fresh@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/confusing-browser-globals-fresh/download/confusing-browser-globals-fresh-1.0.2.tgz#e220ebbf639e0fa7019d0c235969b04f1042ca28"
  integrity sha1-4iDrv2OeD6cBnQwjWWmwTxBCyig=

confusing-browser-globals@^1.0.10:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/confusing-browser-globals/download/confusing-browser-globals-1.0.11.tgz#ae40e9b57cdd3915408a2805ebd3a5585608dc81"
  integrity sha1-rkDptXzdORVAiigF69OlWFYI3IE=

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

consola@^2.10.0, consola@^2.15.3, consola@^2.6.0:
  version "2.15.3"
  resolved "http://registry.npm.qima-inc.com/consola/download/consola-2.15.3.tgz#2e11f98d6a4be71ff72e0bdf07bd23e12cb61550"
  integrity sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/console-browserify/download/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.4, content-disposition@~0.5.0, content-disposition@~0.5.2:
  version "0.5.4"
  resolved "http://registry.npm.qima-inc.com/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.0, content-type@^1.0.2, content-type@^1.0.4, content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

conventional-changelog-angular@^1.3.3:
  version "1.6.6"
  resolved "http://registry.npm.qima-inc.com/conventional-changelog-angular/download/conventional-changelog-angular-1.6.6.tgz#b27f2b315c16d0a1f23eb181309d0e6a4698ea0f"
  integrity sha1-sn8rMVwW0KHyPrGBMJ0OakaY6g8=
  dependencies:
    compare-func "^1.3.1"
    q "^1.5.1"

conventional-changelog-conventionalcommits@4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/conventional-changelog-conventionalcommits/download/conventional-changelog-conventionalcommits-4.2.1.tgz#d6cb2e2c5d7bfca044a08b9dba84b4082e1a1bd9"
  integrity sha1-1ssuLF17/KBEoIuduoS0CC4aG9k=
  dependencies:
    compare-func "^1.3.1"
    lodash "^4.2.1"
    q "^1.5.1"

conventional-commits-parser@^3.0.0:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/conventional-commits-parser/download/conventional-commits-parser-3.2.4.tgz#a7d3b77758a202a9b2293d2112a8d8052c740972"
  integrity sha1-p9O3d1iiAqmyKT0hEqjYBSx0CXI=
  dependencies:
    JSONStream "^1.0.4"
    is-text-path "^1.0.1"
    lodash "^4.17.15"
    meow "^8.0.0"
    split2 "^3.0.0"
    through2 "^4.0.0"

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/convert-source-map/download/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.7.1:
  version "0.7.1"
  resolved "http://registry.npm.qima-inc.com/cookie/download/cookie-0.7.1.tgz#2f73c42142d5d5cf71310a74fc4ae61670e5dbc9"
  integrity sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=

cookie@~0.4.1:
  version "0.4.2"
  resolved "http://registry.npm.qima-inc.com/cookie/download/cookie-0.4.2.tgz#0e41f24de5ecf317947c82fc789e06a884824432"
  integrity sha1-DkHyTeXs8xeUfIL8eJ4GqISCRDI=

cookies@^0.8.0:
  version "0.8.0"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.8.0.tgz#1293ce4b391740a8406e3c9870e828c4b54f3f90"
  integrity sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

cookies@~0.7.0:
  version "0.7.3"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.7.3.tgz#7912ce21fbf2e8c2da70cf1c3f351aecf59dadfa"
  integrity sha1-eRLOIfvy6MLacM8cPzUa7PWdrfo=
  dependencies:
    depd "~1.1.2"
    keygrip "~1.0.3"

cookies@~0.9.0:
  version "0.9.1"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.9.1.tgz#3ffed6f60bb4fb5f146feeedba50acc418af67e3"
  integrity sha1-P/7W9gu0+18Ub+7tulCsxBivZ+M=
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/copy-to/download/copy-to-2.0.1.tgz#2680fbb8068a48d08656b6098092bdafc906f4a5"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

core-js@^2.4.0, core-js@^2.5.0:
  version "2.6.12"
  resolved "http://registry.npm.qima-inc.com/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^5.0.7, cosmiconfig@^5.2.0:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

crc32@0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/crc32/download/crc32-0.2.2.tgz#7ad220d6ffdcd119f9fc127a7772cacea390a4ba"
  integrity sha1-etIg1v/c0Rn5/BJ6d3LKzqOQpLo=

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "http://registry.npm.qima-inc.com/create-ecdh/download/create-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-fetch@3.1.5:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/cross-fetch/download/cross-fetch-3.1.5.tgz#e1389f44d9e7ba767907f7af8454787952ab534f"
  integrity sha1-4TifRNnnunZ5B/evhFR4eVKrU08=
  dependencies:
    node-fetch "2.6.7"

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.5"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-7.0.5.tgz#910aac880ff5243da96b728bc6521a5f6c2f2f82"
  integrity sha1-kQqsiA/1JD2pa3KLxlIaX2wvL4I=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "http://registry.npm.qima-inc.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssfilter@0.0.10:
  version "0.0.10"
  resolved "http://registry.npm.qima-inc.com/cssfilter/download/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/cyclist/download/cyclist-1.0.2.tgz#673b5f233bf34d8e602b949429f8171d9121bea3"
  integrity sha1-ZztfIzvzTY5gK5SUKfgXHZEhvqM=

dargs@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/dargs/download/dargs-7.0.0.tgz#04015c41de0bcb69ec84050f3d9be0caf8d6d5cc"
  integrity sha1-BAFcQd4Ly2nshAUPPZvgyvjW1cw=

data-view-buffer@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/data-view-buffer/download/data-view-buffer-1.0.1.tgz#8ea6326efec17a2e42620696e671d7d5a8bc66b2"
  integrity sha1-jqYybv7Bei5CYgaW5nHX1ai8ZrI=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-length@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/data-view-byte-length/download/data-view-byte-length-1.0.1.tgz#90721ca95ff280677eb793749fce1011347669e2"
  integrity sha1-kHIcqV/ygGd+t5N0n84QETR2aeI=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/data-view-byte-offset/download/data-view-byte-offset-1.0.0.tgz#5e0bbfb4828ed2d1b9b400cd8a7d119bca0ff18a"
  integrity sha1-Xgu/tIKO0tG5tADNin0Rm8oP8Yo=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@1.29.0:
  version "1.29.0"
  resolved "http://registry.npm.qima-inc.com/date-fns/download/date-fns-1.29.0.tgz#12e609cdcb935127311d04d33334e2960a2a54e6"
  integrity sha1-EuYJzcuTUScxHQTTMzTilgoqVOY=

dateformat@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/dateformat/download/dateformat-3.0.3.tgz#a6e37499a4d9a9cf85ef5872044d62901c9889ae"
  integrity sha1-puN0maTZqc+F71hyBE1ikByYia4=

dateformat@^4.5.1, dateformat@^4.6.3:
  version "4.6.3"
  resolved "http://registry.npm.qima-inc.com/dateformat/download/dateformat-4.6.3.tgz#556fa6497e5217fedb78821424f8a1c22fa3f4b5"
  integrity sha1-VW+mSX5SF/7beIIUJPihwi+j9LU=

debounce@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/debounce/download/debounce-1.2.1.tgz#38881d8f4166a5c5848020c11827b834bcb3e0a5"
  integrity sha1-OIgdj0FmpcWEgCDBGCe4NLyz4KU=

debug@*, debug@4, debug@^4.0.0, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.2.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.7"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.3.7.tgz#87945b4151a011d76d95a198d7111c865c360a52"
  integrity sha1-h5RbQVGgEddtlaGY1xEchlw2ClI=
  dependencies:
    ms "^2.1.3"

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.0, debug@^2.6.3, debug@^2.6.8:
  version "2.6.9"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@3.1.0, debug@=3.1.0, debug@~3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@4.1.1, debug@~4.1.0:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

debug@4.3.4:
  version "4.3.4"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

debug@^3.1.0, debug@^3.2.7:
  version "3.2.7"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

decamelize-keys@^1.0.0, decamelize-keys@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/decamelize-keys/download/decamelize-keys-1.1.1.tgz#04a2d523b2f18d80d0158a43b895d56dff8d19d8"
  integrity sha1-BKLVI7LxjYDQFYpDuJXVbf+NGdg=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz#e69dbe25d37941171dd540e024c444cd5188e1e9"
  integrity sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@^1.0.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/deep-equal/download/deep-equal-1.1.2.tgz#78a561b7830eef3134c7f6f3a3d6af272a678761"
  integrity sha1-eKVht4MO7zE0x/bzo9avJypnh2E=
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^1.5.2:
  version "1.5.2"
  resolved "http://registry.npm.qima-inc.com/deepmerge/download/deepmerge-1.5.2.tgz#10499d868844cdad4fee0842df8c7f6f0c95a753"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "http://registry.npm.qima-inc.com/deepmerge/download/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/default-gateway/download/default-gateway-4.2.0.tgz#167104c7500c2115f6dd69b0a536bb8ed720552b"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-user-agent@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/default-user-agent/download/default-user-agent-1.0.0.tgz#16c46efdcaba3edc45f24f2bd4868b01b7c2adc6"
  integrity sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY=
  dependencies:
    os-name "~1.0.3"

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/define-data-property/download/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/define-properties/download/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/del/download/del-4.1.1.tgz#9e8f117222ea44a31ff3a156c049b99052a9f0b4"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denque@^1.1.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/denque/download/denque-1.5.1.tgz#07f670e29c9a78f8faecb2566a1e2c11929c5cbf"
  integrity sha1-B/Zw4pyaePj67LJWah4sEZKcXL8=

denque@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/denque/download/denque-2.1.0.tgz#e93e1a6569fb5e66f16a3c2a2964617d349d6ab1"
  integrity sha1-6T4aZWn7XmbxajwqKWRhfTSdarE=

depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@^1.1.0, depd@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/des.js/download/des.js-1.1.0.tgz#1d37f5766f3bbff4ee9638e871a8768c173b81da"
  integrity sha1-HTf1dm87v/Tuljjocah2jBc7gdo=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0, destroy@^1.0.3, destroy@^1.0.4:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/detect-node/download/detect-node-2.1.0.tgz#c9c70775a49c3d03bc2c06d9a73be550f978f8b1"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

devtools-protocol@0.0.981744:
  version "0.0.981744"
  resolved "http://registry.npm.qima-inc.com/devtools-protocol/download/devtools-protocol-0.0.981744.tgz#9960da0370284577d46c28979a0b32651022bacf"
  integrity sha1-mWDaA3AoRXfUbCiXmgsyZRAius8=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

digest-header@^0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/digest-header/download/digest-header-0.0.1.tgz#11ccf6deec5766ac379744d901c12cba49514be6"
  integrity sha1-Ecz23uxXZqw3l0TZAcEsuklRS+Y=
  dependencies:
    utility "0.1.11"

digest-header@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/digest-header/download/digest-header-1.1.0.tgz#e16ab6cf4545bc4eea878c8c35acd1b89664d800"
  integrity sha1-4Wq2z0VFvE7qh4yMNazRuJZk2AA=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "http://registry.npm.qima-inc.com/dns-packet/download/dns-packet-1.3.4.tgz#e3455065824a2507ba886c55a89963bb107dec6f"
  integrity sha1-40VQZYJKJQe6iGxVqJljuxB97G8=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/dns-txt/download/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

dnscache@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/dnscache/download/dnscache-1.0.2.tgz#fd3c24d66c141625f594c77be7a8dafee2a66c8a"
  integrity sha1-/Twk1mwUFiX1lMd756ja/uKmbIo=
  dependencies:
    asap "^2.0.6"
    lodash.clone "^4.5.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-serializer@0:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/dom-serializer/download/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/domain-browser/download/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/domelementtype/download/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^2.3.0:
  version "2.4.2"
  resolved "http://registry.npm.qima-inc.com/domhandler/download/domhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domutils@^1.5.1:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/domutils/download/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-prop@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/dot-prop/download/dot-prop-3.0.0.tgz#1b708af094a49c9a0e7dbcad790aba539dac1177"
  integrity sha1-G3CK8JSknJoOfbyteQq6U52sEXc=
  dependencies:
    is-obj "^1.0.0"

dtrace-provider@~0.8:
  version "0.8.8"
  resolved "http://registry.npm.qima-inc.com/dtrace-provider/download/dtrace-provider-0.8.8.tgz#2996d5490c37e1347be263b423ed7b297fb0d97e"
  integrity sha1-KZbVSQw34TR74mO0I+17KX+w2X4=
  dependencies:
    nan "^2.14.0"

duplexer@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/duplexer/download/duplexer-0.1.2.tgz#3abe43aef3835f8ae077d136ddce0f276b0400e6"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "http://registry.npm.qima-inc.com/duplexify/download/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ee-first@1.1.1, ee-first@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.5.41:
  version "1.5.43"
  resolved "http://registry.npm.qima-inc.com/electron-to-chromium/download/electron-to-chromium-1.5.43.tgz#d9e69fc709ddebd521416de9d17cdef81d2d4718"
  integrity sha1-2eafxwnd69UhQW3p0Xze+B0tRxg=

elliptic@^6.5.3, elliptic@^6.5.4:
  version "6.5.7"
  resolved "http://registry.npm.qima-inc.com/elliptic/download/elliptic-6.5.7.tgz#8ec4da2cb2939926a1b9a73619d768207e647c8b"
  integrity sha1-jsTaLLKTmSahuac2GddoIH5kfIs=
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@^1.0.1, encodeurl@^1.0.2, encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encodeurl@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/encodeurl/download/encodeurl-2.0.0.tgz#7b8ea898077d7e409d3ac45474ea38eaf0857a58"
  integrity sha1-e46omAd9fkCdOsRUdOo46vCFelg=

end-of-stream@^1.0.0, end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "http://registry.npm.qima-inc.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

engine.io-client@~3.5.0:
  version "3.5.4"
  resolved "http://registry.npm.qima-inc.com/engine.io-client/download/engine.io-client-3.5.4.tgz#5b40d7381772ba05c0881be5735a318424a07fc6"
  integrity sha1-W0DXOBdyugXAiBvlc1oxhCSgf8Y=
  dependencies:
    component-emitter "~1.3.0"
    component-inherit "0.0.3"
    debug "~3.1.0"
    engine.io-parser "~2.2.0"
    has-cors "1.1.0"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    ws "~7.5.10"
    xmlhttprequest-ssl "~1.6.2"
    yeast "0.1.2"

engine.io-parser@~2.2.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/engine.io-parser/download/engine.io-parser-2.2.1.tgz#57ce5611d9370ee94f99641b589f94c97e4f5da7"
  integrity sha1-V85WEdk3DulPmWQbWJ+UyX5PXac=
  dependencies:
    after "0.8.2"
    arraybuffer.slice "~0.0.7"
    base64-arraybuffer "0.1.4"
    blob "0.0.5"
    has-binary2 "~1.0.2"

engine.io@~3.6.0:
  version "3.6.2"
  resolved "http://registry.npm.qima-inc.com/engine.io/download/engine.io-3.6.2.tgz#b5d0fffafdd8525dbcd10b3ab5d8337271c3c36b"
  integrity sha1-tdD/+v3YUl280Qs6tdgzcnHDw2s=
  dependencies:
    accepts "~1.3.4"
    base64id "2.0.0"
    cookie "~0.4.1"
    debug "~4.1.0"
    engine.io-parser "~2.2.0"
    ws "~7.5.10"

enhanced-resolve@^4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.qima-inc.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz#2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enquirer@^2.3.5, enquirer@^2.3.6:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/enquirer/download/enquirer-2.4.1.tgz#93334b3fbd74fc7097b224ab4a8fb7e40bf4ae56"
  integrity sha1-kzNLP710/HCXsiSrSo+35Av0rlY=
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

entities@^1.1.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/entities/download/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/entities/download/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "http://registry.npm.qima-inc.com/errno/download/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-inject@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/error-inject/download/error-inject-1.0.0.tgz#e2b3d91b54aed672f309d950d154850fa11d4f37"
  integrity sha1-4rPZG1Su1nLzCdlQ0VSFD6EdTzc=

error-stack-parser@^2.0.0:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/error-stack-parser/download/error-stack-parser-2.1.4.tgz#229cb01cdbfa84440bfa91876285b94680188286"
  integrity sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.17.5, es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.2, es-abstract@^1.23.3:
  version "1.23.3"
  resolved "http://registry.npm.qima-inc.com/es-abstract/download/es-abstract-1.23.3.tgz#8f0c5a35cd215312573c5a27c87dfd6c881a0aa0"
  integrity sha1-jwxaNc0hUxJXPFonyH39bIgaCqA=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    arraybuffer.prototype.slice "^1.0.3"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    data-view-buffer "^1.0.1"
    data-view-byte-length "^1.0.1"
    data-view-byte-offset "^1.0.0"
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.0.3"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.4"
    get-symbol-description "^1.0.2"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    hasown "^2.0.2"
    internal-slot "^1.0.7"
    is-array-buffer "^3.0.4"
    is-callable "^1.2.7"
    is-data-view "^1.0.1"
    is-negative-zero "^2.0.3"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.3"
    is-string "^1.0.7"
    is-typed-array "^1.1.13"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.5"
    regexp.prototype.flags "^1.5.2"
    safe-array-concat "^1.1.2"
    safe-regex-test "^1.0.3"
    string.prototype.trim "^1.2.9"
    string.prototype.trimend "^1.0.8"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.2"
    typed-array-byte-length "^1.0.1"
    typed-array-byte-offset "^1.0.2"
    typed-array-length "^1.0.6"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.15"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/es-define-property/download/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha1-x/rvvf+LJpbPX0aSHt+3fMS6OEU=
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.2.1, es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-iterator-helpers@^1.0.19:
  version "1.0.19"
  resolved "http://registry.npm.qima-inc.com/es-iterator-helpers/download/es-iterator-helpers-1.0.19.tgz#117003d0e5fec237b4b5c08aded722e0c6d50ca8"
  integrity sha1-EXAD0OX+wje0tcCK3tci4MbVDKg=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    globalthis "^1.0.3"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    iterator.prototype "^1.1.2"
    safe-array-concat "^1.1.2"

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/es-object-atoms/download/es-object-atoms-1.0.0.tgz#ddb55cd47ac2e240701260bc2a8e31ecb643d941"
  integrity sha1-3bVc1HrC4kBwEmC8Ko4x7LZD2UE=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/es-set-tostringtag/download/es-set-tostringtag-2.0.3.tgz#8bb60f0a440c2e4281962428438d58545af39777"
  integrity sha1-i7YPCkQMLkKBliQoQ41YVFrzl3c=
  dependencies:
    get-intrinsic "^1.2.4"
    has-tostringtag "^1.0.2"
    hasown "^2.0.1"

es-shim-unscopables@^1.0.0, es-shim-unscopables@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz#1f6942e71ecc7835ed1c8a83006d8771a63a3763"
  integrity sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M=
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/escalade/download/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-html@^1.0.3, escape-html@~1.0.1, escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-airbnb-base@^14.2.1:
  version "14.2.1"
  resolved "http://registry.npm.qima-inc.com/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz#8a2eb38455dc5a312550193b319cdaeef042cd1e"
  integrity sha1-ii6zhFXcWjElUBk7MZza7vBCzR4=
  dependencies:
    confusing-browser-globals "^1.0.10"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-prettier@^6.11.0:
  version "6.15.0"
  resolved "http://registry.npm.qima-inc.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz#7f93f6cb7d45a92f1537a70ecc06366e1ac6fed9"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "http://registry.npm.qima-inc.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz#d4eaac52b8a2e7c3cd1903eb00f7e053356118ac"
  integrity sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.12.0:
  version "2.12.0"
  resolved "http://registry.npm.qima-inc.com/eslint-module-utils/download/eslint-module-utils-2.12.0.tgz#fe4cfb948d61f49203d7b08871982b65b9af0b0b"
  integrity sha1-/kz7lI1h9JID17CIcZgrZbmvCws=
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@^2.22.1:
  version "2.31.0"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-import/download/eslint-plugin-import-2.31.0.tgz#310ce7e720ca1d9c0bb3f69adfd1c6bdd7d9e0e7"
  integrity sha1-MQzn5yDKHZwLs/aa39HGvdfZ4Oc=
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.8"
    array.prototype.findlastindex "^1.2.5"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.0"
    hasown "^2.0.2"
    is-core-module "^2.15.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.0"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.8"
    tsconfig-paths "^3.15.0"

eslint-plugin-prettier@^3.4.0:
  version "3.4.1"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz#e9ddb200efb6f3d05ffe83b1665a716af4a387e5"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^4.2.0:
  version "4.6.2"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.2.tgz#c829eb06c0e6f484b3fbb85a97e57784f328c596"
  integrity sha1-yCnrBsDm9ISz+7hal+V3hPMoxZY=

eslint-plugin-react@^7.23.2:
  version "7.36.1"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-react/download/eslint-plugin-react-7.36.1.tgz#f1dabbb11f3d4ebe8b0cf4e54aff4aee81144ee5"
  integrity sha1-8dq7sR89Tr6LDPTlSv9K7oEUTuU=
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.2"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.0.19"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.8"
    object.fromentries "^2.0.8"
    object.values "^1.2.0"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.11"
    string.prototype.repeat "^1.0.0"

eslint-plugin-vue@^7.9.0:
  version "7.20.0"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-vue/download/eslint-plugin-vue-7.20.0.tgz#98c21885a6bfdf0713c3a92957a5afeaaeed9253"
  integrity sha1-mMIYhaa/3wcTw6kpV6Wv6q7tklM=
  dependencies:
    eslint-utils "^2.1.0"
    natural-compare "^1.4.0"
    semver "^6.3.0"
    vue-eslint-parser "^7.10.0"

eslint-plugin-youzan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-youzan/download/eslint-plugin-youzan-1.0.1.tgz#044cd875670e494afe8ab1c951075cb8ec7a30ac"
  integrity sha1-BEzYdWcOSUr+irHJUQdcuOx6MKw=
  dependencies:
    requireindex "~1.1.0"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-3.0.0.tgz#8aebaface7345bb33559db0a1f13a1d2d48c3672"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.3.0:
  version "3.4.3"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@7.32.0:
  version "7.32.0"
  resolved "http://registry.npm.qima-inc.com/eslint/download/eslint-7.32.0.tgz#c6d328a14be3fb08c8d1d21e12c02fdb7a2a812d"
  integrity sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.2.1:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-6.2.1.tgz#77fc72e1fd744a2052c20f38a5b575832e82734a"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-7.3.1.tgz#f2df330b752c6f55019f8bd89b7660039c1bbbb6"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/esquery/download/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://registry.npm.qima-inc.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/event-target-shim/download/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "http://registry.npm.qima-inc.com/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0, events@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

eventsource@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/eventsource/download/eventsource-2.0.2.tgz#76dfcc02930fb2ff339520b6d290da573a9e8508"
  integrity sha1-dt/MApMPsv8zlSC20pDaVzqehQg=

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-4.1.0.tgz#4e5491ad1572f2f17a77d388c6c857135b22847a"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.0.0, execa@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

execall@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/execall/download/execall-2.0.0.tgz#16a06b5fe5099df7d00be5d9c06eecded1663b45"
  integrity sha1-FqBrX+UJnffQC+XZwG7s3tFmO0U=
  dependencies:
    clone-regexp "^2.1.0"

exif-js@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/exif-js/download/exif-js-2.3.0.tgz#9d10819bf571f873813e7640241255ab9ce1a814"
  integrity sha1-nRCBm/Vx+HOBPnZAJBJVq5zhqBQ=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express@^4.17.1:
  version "4.21.1"
  resolved "http://registry.npm.qima-inc.com/express/download/express-4.21.1.tgz#9dae5dda832f16b4eec941a4e44aa89ec481b281"
  integrity sha1-na5d2oMvFrTuyUGk5EqonsSBsoE=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.3"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.7.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.3.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.3"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.10"
    proxy-addr "~2.0.7"
    qs "6.13.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.19.0"
    serve-static "1.16.2"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-zip@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/extract-zip/download/extract-zip-2.0.1.tgz#663dca56fe46df890d5f131ef4a06d22bb8ba13a"
  integrity sha1-Zj3KVv5G34kNXxMe9KBtIruLoTo=
  dependencies:
    debug "^4.1.1"
    get-stream "^5.1.0"
    yauzl "^2.10.0"
  optionalDependencies:
    "@types/yauzl" "^2.9.1"

fast-copy@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/fast-copy/download/fast-copy-3.0.2.tgz#59c68f59ccbcac82050ba992e0d5c389097c9d35"
  integrity sha1-WcaPWcy8rIIFC6mS4NXDiQl8nTU=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/fast-diff/download/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@2.2.6:
  version "2.2.6"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-2.2.6.tgz#a5d5b697ec8deda468d85a74035290a025a95295"
  integrity sha1-pdW2l+yN7aRo2Fp0A1KQoCWpUpU=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.2.5, fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.3.2"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha1-qQRQHlfP3S/83tRemaVP71XkYSk=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-json-stringify@^2.2.3:
  version "2.7.13"
  resolved "http://registry.npm.qima-inc.com/fast-json-stringify/download/fast-json-stringify-2.7.13.tgz#277aa86c2acba4d9851bd6108ed657aa327ed8c0"
  integrity sha1-J3qobCrLpNmFG9YQjtZXqjJ+2MA=
  dependencies:
    ajv "^6.11.0"
    deepmerge "^4.2.2"
    rfdc "^1.2.0"
    string-similarity "^4.0.1"

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-memoize@^2.5.2:
  version "2.5.2"
  resolved "http://registry.npm.qima-inc.com/fast-memoize/download/fast-memoize-2.5.2.tgz#79e3bb6a4ec867ea40ba0e7146816f6cdce9b57e"
  integrity sha1-eeO7ak7IZ+pAug5xRoFvbNzptX4=

fast-redact@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/fast-redact/download/fast-redact-2.1.0.tgz#dfe3c1ca69367fb226f110aa4ec10ec85462ffdf"
  integrity sha1-3+PBymk2f7Im8RCqTsEOyFRi/98=

fast-redact@^3.0.0:
  version "3.5.0"
  resolved "http://registry.npm.qima-inc.com/fast-redact/download/fast-redact-3.5.0.tgz#e9ea02f7e57d0cd8438180083e93077e496285e4"
  integrity sha1-6eoC9+V9DNhDgYAIPpMHfkliheQ=

fast-safe-stringify@^2.0.7, fast-safe-stringify@^2.0.8, fast-safe-stringify@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/fast-safe-stringify/download/fast-safe-stringify-2.1.1.tgz#c406a83b6e70d9e35ce3b30a81141df30aeba884"
  integrity sha1-xAaoO25w2eNc47MKgRQd8wrrqIQ=

fast-uri@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/fast-uri/download/fast-uri-3.0.1.tgz#cddd2eecfc83a71c1be2cc2ef2061331be8a7134"
  integrity sha1-zd0u7PyDpxwb4swu8gYTMb6KcTQ=

fast-url-parser@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/fast-url-parser/download/fast-url-parser-1.1.3.tgz#f4af3ea9f34d8a271cf58ad2b3759f431f0b318d"
  integrity sha1-9K8+qfNNiicc9YrSs3WfQx8LMY0=
  dependencies:
    punycode "^1.3.2"

fastest-levenshtein@^1.0.12:
  version "1.0.16"
  resolved "http://registry.npm.qima-inc.com/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz#210e61b6ff181de91ea9b3d1b84fdedd47e034e5"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastq@^1.6.0:
  version "1.17.1"
  resolved "http://registry.npm.qima-inc.com/fastq/download/fastq-1.17.1.tgz#2a523f07a4e7b1e81a42b91b8bf2254107753b47"
  integrity sha1-KlI/B6TnsegaQrkbi/IlQQd1O0c=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3, faye-websocket@^0.11.4:
  version "0.11.4"
  resolved "http://registry.npm.qima-inc.com/faye-websocket/download/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

fd-slicer2@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/fd-slicer2/download/fd-slicer2-1.2.0.tgz#a2c54a540639bbcd4702480821771341277ca20e"
  integrity sha1-osVKVAY5u81HAkgIIXcTQSd8og4=
  dependencies:
    pend "^1.2.0"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/fd-slicer/download/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

fecha@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/fecha/download/fecha-3.0.3.tgz#fabbd416497649a42c24d34bfa726b579203a1e2"
  integrity sha1-+rvUFkl2SaQsJNNL+nJrV5IDoeI=

fecha@^4.2.0:
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/fecha/download/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "http://registry.npm.qima-inc.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/filter-obj/download/filter-obj-1.1.0.tgz#9b311112bc6c6127a16e016c6c5d7f19e0805c5b"
  integrity sha1-mzERErxsYSehbgFsbF1/GeCAXFs=

finalhandler@1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/finalhandler/download/finalhandler-1.3.1.tgz#0c575f1d1d324ddd1da35ad7ece3df7d19088019"
  integrity sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=
  dependencies:
    debug "2.6.9"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0:
  version "3.3.2"
  resolved "http://registry.npm.qima-inc.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz#b30c5b6eff0730731aea9bbd9dbecbd80256d64b"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/flat-cache/download/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flat@^5.0.2:
  version "5.0.2"
  resolved "http://registry.npm.qima-inc.com/flat/download/flat-5.0.2.tgz#8ca6fe332069ffa9d324c327198c598259ceb241"
  integrity sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=

flatstr@^1.0.12:
  version "1.0.12"
  resolved "http://registry.npm.qima-inc.com/flatstr/download/flatstr-1.0.12.tgz#c2ba6a08173edbb6c9640e3055b95e287ceb5931"
  integrity sha1-wrpqCBc+27bJZA4wVbleKHzrWTE=

flatted@^3.2.9:
  version "3.3.1"
  resolved "http://registry.npm.qima-inc.com/flatted/download/flatted-3.3.1.tgz#21db470729a6734d4997002f439cb308987f567a"
  integrity sha1-IdtHBymmc01JlwAvQ5yzCJh/Vno=

flexbuffer@0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/flexbuffer/download/flexbuffer-0.0.6.tgz#039fdf23f8823e440c38f3277e6fef1174215b30"
  integrity sha1-A5/fI/iCPkQMOPMnfm/vEXQhWzA=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

flushwritable@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/flushwritable/download/flushwritable-1.0.0.tgz#3e328d8fde412ad47e738e3be750b4d290043498"
  integrity sha1-PjKNj95BKtR+c44751C00pAENJg=

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.0.0, follow-redirects@^1.10.0, follow-redirects@^1.14.0, follow-redirects@^1.15.6, follow-redirects@^1.3.0:
  version "1.15.6"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha1-f4FcDNpCScdP8J6V75fCO1/QOZs=

for-each@^0.3.3:
  version "0.3.3"
  resolved "http://registry.npm.qima-inc.com/for-each/download/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

form-data@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/form-data/download/form-data-4.0.1.tgz#ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48"
  integrity sha1-uhB22qqlv9fpnBpssCqgpc/5DUg=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formidable@^1.1.1:
  version "1.2.6"
  resolved "http://registry.npm.qima-inc.com/formidable/download/formidable-1.2.6.tgz#d2a51d60162bbc9b4a055d8457a7c75315d1a168"
  integrity sha1-0qUdYBYrvJtKBV2EV6fHUxXRoWg=

formstream@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/formstream/download/formstream-1.1.0.tgz#51f3970f26136eb0ad44304de4cebb50207b4479"
  integrity sha1-UfOXDyYTbrCtRDBN5M67UCB7RHk=
  dependencies:
    destroy "^1.0.4"
    mime "^1.3.4"
    pause-stream "~0.0.11"

formstream@^1.1.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/formstream/download/formstream-1.5.1.tgz#b25f8121aa434cc82e8b36cdd765338b7b8df4de"
  integrity sha1-sl+BIapDTMguizbN12Uzi3uN9N4=
  dependencies:
    destroy "^1.0.4"
    mime "^2.5.2"
    node-hex "^1.0.1"
    pause-stream "~0.0.11"

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2, fresh@^0.5.2, fresh@~0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs-constants/download/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"
  integrity sha1-a+Dem+mYzhavivwkSXue6bfM2a0=

fs-extra@9.0.1:
  version "9.0.1"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-9.0.1.tgz#910da0062437ba4c39fedd863f1675ccfefcb9fc"
  integrity sha1-kQ2gBiQ3ukw5/t2GPxZ1zP78ufw=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^1.0.0"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^9.1.0:
  version "9.1.0"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-9.1.0.tgz#5954460c764a8da2094ba3554bf839e6b9a7c86d"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.2:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.5, function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/function.prototype.name/download/function.prototype.name-1.1.6.tgz#cdf315b7d90ee77a4c6ee216c3c3362da07533fd"
  integrity sha1-zfMVt9kO53pMbuIWw8M2LaB1M/0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

generate-function@^2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/generate-function/download/generate-function-2.3.1.tgz#f069617690c10c868e73b8465746764f97c3479f"
  integrity sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=
  dependencies:
    is-property "^1.0.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://registry.npm.qima-inc.com/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/get-intrinsic/download/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha1-44X1pLUifUScPqu60FSU7wq76t0=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz#b5fde77f22cbe35f390b4e089922c50bce6ef664"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-port@^3.1.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/get-port/download/get-port-3.2.0.tgz#dd7ce7de187c06c8bf353796ac71e099f0980ebc"
  integrity sha1-3Xzn3hh8Bsi/NTeWrHHgmfCYDrw=

get-port@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/get-port/download/get-port-5.1.1.tgz#0469ed07563479de6efb986baf053dcd7d4e3193"
  integrity sha1-BGntB1Y0ed5u+5hrrwU9zX1OMZM=

get-ready@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/get-ready/download/get-ready-1.0.0.tgz#f91817f1e9adecfea13a562adfc8de883ab34782"
  integrity sha1-+RgX8emt7P6hOlYq38jeiDqzR4I=

get-stdin@7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-7.0.0.tgz#8d5de98f15171a125c5e516643c7a6d0ea8a96f6"
  integrity sha1-jV3pjxUXGhJcXlFmQ8em0OqKlvY=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stdin@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-8.0.0.tgz#cbad6a73feb75f6eeb22ba9e01f89aa28aa97a53"
  integrity sha1-y61qc/63X27rIrqeAfiaooqpelM=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0, get-stream@^5.1.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/get-symbol-description/download/get-symbol-description-1.0.2.tgz#533744d5aa20aca4e079c8e5daf7fd44202821f5"
  integrity sha1-UzdE1aogrKTgecjl2vf9RCAoIfU=
  dependencies:
    call-bind "^1.0.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

git-raw-commits@^2.0.0:
  version "2.0.11"
  resolved "http://registry.npm.qima-inc.com/git-raw-commits/download/git-raw-commits-2.0.11.tgz#bc3576638071d18655e1cc60d7f524920008d723"
  integrity sha1-vDV2Y4Bx0YZV4cxg1/UkkgAI1yM=
  dependencies:
    dargs "^7.0.0"
    lodash "^4.17.15"
    meow "^8.0.0"
    split2 "^3.0.0"
    through2 "^4.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^6.0.1:
  version "6.0.4"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-6.0.4.tgz#0f08860f6a155127b2fadd4f9ce24b1aab6e4d22"
  integrity sha1-DwiGD2oVUSey+t1PnOJLGqtuTSI=
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/global-dirs/download/global-dirs-0.1.1.tgz#b319c0dd4607f353f3be9cca4c72fc148c49f445"
  integrity sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=
  dependencies:
    ini "^1.3.4"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/global-modules/download/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/global-prefix/download/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.24.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/globalthis/download/globalthis-1.0.4.tgz#7430ed3a975d97bfb59bcce41f5cabbafa651236"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.0.2, globby@^11.0.3, globby@^11.1.0:
  version "11.1.0"
  resolved "http://registry.npm.qima-inc.com/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globby@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/globjoin/download/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

gonzales-pe@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/gonzales-pe/download/gonzales-pe-4.3.0.tgz#fe9dec5f3c557eead09ff868c65826be54d067b3"
  integrity sha1-/p3sXzxVfurQn/hoxlgmvlTQZ7M=
  dependencies:
    minimist "^1.2.5"

gopd@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

graceful-config@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/graceful-config/download/graceful-config-1.0.0.tgz#2755b82d3010aa7c0b31577c81a2be5ab656a1f4"
  integrity sha1-J1W4LTAQqnwLMVd8gaK+WrZWofQ=
  dependencies:
    debug "^4.1.1"
    lodash "^4.17.19"

graceful-error@^1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.qima-inc.com/graceful-error/download/graceful-error-1.1.5.tgz#f6ee29f1f278fdf92e1e881b176aeab035282cb0"
  integrity sha1-9u4p8fJ4/fkuHogbF2rqsDUoLLA=

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "http://registry.npm.qima-inc.com/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

grapheme-splitter@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/grapheme-splitter/download/grapheme-splitter-1.0.4.tgz#9cf3a665c6247479896834af35cf1dbb4400767e"
  integrity sha1-nPOmZcYkdHmJaDSvNc8du0QAdn4=

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/gzip-size/download/gzip-size-6.0.0.tgz#065367fd50c239c0671cbcbad5be3e2eeb10e462"
  integrity sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI=
  dependencies:
    duplexer "^0.1.2"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/handle-thing/download/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/hard-rejection/download/hard-rejection-2.1.0.tgz#1c6eda5c1685c63942766d79bb40ae773cecd883"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-binary2@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has-binary2/download/has-binary2-1.0.3.tgz#7776ac627f3ea77250cfc332dab7ddf5e4f5d11d"
  integrity sha1-d3asYn8+p3JQz8My2rfd9eT10R0=
  dependencies:
    isarray "2.0.1"

has-cors@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/has-cors/download/has-cors-1.1.0.tgz#5e474793f7ea9843d1bb99c23eef49ff126fff39"
  integrity sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1, has-proto@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has-proto/download/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha1-sx3f6bDm6ZFFNqarKGQm0CFPd/0=

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/hash-base/download/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/hash.js/download/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

help-me@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/help-me/download/help-me-5.0.0.tgz#b1ebe63b967b74060027c2ac61f9be12d354a6f6"
  integrity sha1-sevmO5Z7dAYAJ8KsYfm+EtNUpvY=

hessian.js@^2.9.0:
  version "2.11.0"
  resolved "http://registry.npm.qima-inc.com/hessian.js/download/hessian.js-2.11.0.tgz#36d1599a4db6bccaccf1accfa73e0cdc4efd024c"
  integrity sha1-NtFZmk22vMrM8azPpz4M3E79Akw=
  dependencies:
    byte "^2.0.0"
    is-type-of "^1.2.1"
    long "^4.0.0"
    utility "^1.16.1"

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://registry.npm.qima-inc.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/hosted-git-info/download/hosted-git-info-4.1.0.tgz#827b82867e9ff1c8d0c4d9d53880397d2c86d224"
  integrity sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=
  dependencies:
    lru-cache "^6.0.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "http://registry.npm.qima-inc.com/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^1.3.1:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/html-entities/download/html-entities-1.4.0.tgz#cfbd1b01d2afaf9adca1b10ae7dffab98c71d2dc"
  integrity sha1-z70bAdKvr5rcobEK59/6uYxx0tw=

html-escaper@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/html-escaper/download/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-tags@^3.1.0:
  version "3.3.1"
  resolved "http://registry.npm.qima-inc.com/html-tags/download/html-tags-3.3.1.tgz#a04026a18c882e4bba8a01a3d39cfe465d40b5ce"
  integrity sha1-oEAmoYyILku6igGj05z+Rl1Atc4=

htmlparser2@^3.10.0:
  version "3.10.1"
  resolved "http://registry.npm.qima-inc.com/htmlparser2/download/htmlparser2-3.10.1.tgz#bd679dc3f59897b6a34bb10749c855bb53a9392f"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-assert@^1.1.0, http-assert@^1.3.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/http-assert/download/http-assert-1.5.0.tgz#c389ccd87ac16ed2dfa6246fd73b926aa00e6b8f"
  integrity sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.8.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "http://registry.npm.qima-inc.com/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@^1.2.8, http-errors@^1.3.1, http-errors@^1.6.1, http-errors@^1.6.3, http-errors@^1.7.3, http-errors@~1.8.0:
  version "1.8.1"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.8.1.tgz#7c3f28577cbc8a207388455dbd62295ed07bd68c"
  integrity sha1-fD8oV3y8iiBziEVdvWIpXtB71ow=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "http://registry.npm.qima-inc.com/http-parser-js/download/http-parser-js-0.5.8.tgz#af23090d9ac4e24573de6f6aecc9d84a48bf20e3"
  integrity sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "http://registry.npm.qima-inc.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz#183c7dc4aa1479150306498c210cdaf96080a43a"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy-middleware@^1.0.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/http-proxy-middleware/download/http-proxy-middleware-1.3.1.tgz#43700d6d9eecb7419bf086a128d0f7205d9eb665"
  integrity sha1-Q3ANbZ7st0Gb8IahKND3IF2etmU=
  dependencies:
    "@types/http-proxy" "^1.17.5"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.17.0, http-proxy@^1.18.1:
  version "1.18.1"
  resolved "http://registry.npm.qima-inc.com/http-proxy/download/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

https-proxy-agent@5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz#c59ef224a04fe8b754f3db0063a25ea30d0005d6"
  integrity sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

humanize-ms@^1.2.0, humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/humanize-ms/download/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

husky@^1.3.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/husky/download/husky-1.3.1.tgz#26823e399300388ca2afff11cfa8a86b0033fae0"
  integrity sha1-JoI+OZMAOIyir/8Rz6ioawAz+uA=
  dependencies:
    cosmiconfig "^5.0.7"
    execa "^1.0.0"
    find-up "^3.0.0"
    get-stdin "^6.0.0"
    is-ci "^2.0.0"
    pkg-dir "^3.0.0"
    please-upgrade-node "^3.1.1"
    read-pkg "^4.0.1"
    run-node "^1.0.0"
    slash "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.15:
  version "0.4.24"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.5.0:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.5.2.tgz#af6d628dccfb463b7364d97f715e4b74b8c8c2b8"
  integrity sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13, ieee754@^1.1.4, ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.8, ignore@^5.2.0:
  version "5.3.2"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/import-lazy/download/import-lazy-4.0.0.tgz#e8eb627483a0a43da3c03f3e35548be5cb0cc153"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

import-local@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/import-local/download/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

indexof@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/indexof/download/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"
  integrity sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/infer-owner/download/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflation@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/inflation/download/inflation-2.1.0.tgz#9214db11a47e6f756d111c4f9df96971c60f886c"
  integrity sha1-khTbEaR+b3VtERxPnflpccYPiGw=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@^1.3.5:
  version "1.3.8"
  resolved "http://registry.npm.qima-inc.com/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/internal-ip/download/internal-ip-4.3.0.tgz#845452baad9d2ca3b69c635a137acb9a0dad0907"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

internal-slot@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/internal-slot/download/internal-slot-1.0.7.tgz#c06dcca3ed874249881007b0a5523b172a190802"
  integrity sha1-wG3Mo+2HQkmIEAewpVI7FyoZCAI=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.0"
    side-channel "^1.0.4"

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/invert-kv/download/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

ioredis@4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/ioredis/download/ioredis-4.3.0.tgz#a92850dd8794eaee4f38a265c830ca823a09d345"
  integrity sha1-qShQ3YeU6u5POKJlyDDKgjoJ00U=
  dependencies:
    cluster-key-slot "^1.0.6"
    debug "^3.1.0"
    denque "^1.1.0"
    flexbuffer "0.0.6"
    lodash.defaults "^4.2.0"
    lodash.flatten "^4.4.0"
    redis-commands "1.4.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^1.0.0"

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/ip-regex/download/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.qima-inc.com/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.9"
  resolved "http://registry.npm.qima-inc.com/ip/download/ip-1.1.9.tgz#8dfbcc99a754d07f425310b86a99546b1151e396"
  integrity sha1-jfvMmadU0H9CUxC4aplUaxFR45Y=

ipaddr.js@1.9.1, ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "http://registry.npm.qima-inc.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz#96c6a22b6a23929b11ea0afb1836c36ad4a5d698"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.1.tgz#3223b10628354644b86260db29b3e693f5ceedd4"
  integrity sha1-MiOxBig1RkS4YmDbKbPmk/XO7dQ=
  dependencies:
    hasown "^2.0.0"

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-alphabetical/download/is-alphabetical-1.0.4.tgz#9e7d6b94916be22153745d184c298cbf986a686d"
  integrity sha1-nn1rlJFr4iFTdF0YTCmMv5hqaG0=

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-alphanumerical/download/is-alphanumerical-1.0.4.tgz#7eb9a2431f855f6b1ef1a78e326df515696c4dbf"
  integrity sha1-frmiQx+FX2se8aeOMm31FWlsTb8=
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-arguments@^1.0.4, is-arguments@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/is-arguments/download/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-array-buffer@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/is-array-buffer/download/is-array-buffer-3.0.4.tgz#7a1f92b3d61edd2bc65d24f130530ea93d7fae98"
  integrity sha1-eh+Ss9Ye3SvGXSTxMFMOqT1/rpg=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-async-function@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-async-function/download/is-async-function-2.0.0.tgz#8e4418efd3e5d3a6ebb0164c05ef5afb69aa9646"
  integrity sha1-jkQY79Pl06brsBZMBe9a+2mqlkY=
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.0, is-buffer@^2.0.2:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-2.0.5.tgz#ebc252e400d22ff8d77fa09888821a24a658c191"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://registry.npm.qima-inc.com/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-class-hotfix@~0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/is-class-hotfix/download/is-class-hotfix-0.0.6.tgz#a527d31fb23279281dde5f385c77b5de70a72435"
  integrity sha1-pSfTH7IyeSgd3l84XHe13nCnJDU=

is-core-module@^2.13.0, is-core-module@^2.15.1, is-core-module@^2.5.0:
  version "2.15.1"
  resolved "http://registry.npm.qima-inc.com/is-core-module/download/is-core-module-2.15.1.tgz#a7363a25bee942fefab0de13bf6aa372c82dcc37"
  integrity sha1-pzY6Jb7pQv76sN4Tv2qjcsgtzDc=
  dependencies:
    hasown "^2.0.2"

is-data-descriptor@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-1.0.1.tgz#2109164426166d32ea38c405c1e0945d9e6a4eeb"
  integrity sha1-IQkWRCYWbTLqOMQFweCUXZ5qTus=
  dependencies:
    hasown "^2.0.0"

is-data-view@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-data-view/download/is-data-view-1.0.1.tgz#4b4d3a511b70f3dc26d42c03ca9ca515d847759f"
  integrity sha1-S006URtw89wm1CwDypylFdhHdZ8=
  dependencies:
    is-typed-array "^1.1.13"

is-date-object@^1.0.1, is-date-object@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-decimal/download/is-decimal-1.0.4.tgz#65a3a5958a1c5b63a706e1b333d7cd9f630d3fa5"
  integrity sha1-ZaOllYocW2OnBuGzM9fNn2MNP6U=

is-descriptor@^0.1.0:
  version "0.1.7"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-0.1.7.tgz#2727eb61fd789dcd5bdf0ed4569f551d2fe3be33"
  integrity sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-1.0.3.tgz#92d27cb3cd311c4977a4db47df457234a13cb306"
  integrity sha1-ktJ8s80xHEl3pNtH30VyNKE8swY=
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/is-docker/download/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-finalizationregistry/download/is-finalizationregistry-1.0.2.tgz#c8749b65f17c133313e661b1289b95ad3dbd62e6"
  integrity sha1-yHSbZfF8EzMT5mGxKJuVrT29YuY=
  dependencies:
    call-bind "^1.0.2"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.10, is-generator-function@^1.0.3, is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/is-generator-function/download/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-hexadecimal/download/is-hexadecimal-1.0.4.tgz#cc35c97588da4bd49a8eedd6bc4082d44dcb23a7"
  integrity sha1-zDXJdYjaS9Saju3WvECC1E3LI6c=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-map@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/is-map/download/is-map-2.0.2.tgz#00922db8c9bf73e81b7a335827bc2a43f2b91127"
  integrity sha1-AJItuMm/c+gbejNYJ7wqQ/K5ESc=

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/is-negative-zero/download/is-negative-zero-2.0.3.tgz#ced903a027aca6381b777a5743069d7376a49747"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.0, is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz#bfe2dca26c69f397265a4009963602935a053acb"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-path-inside/download/is-path-inside-2.1.0.tgz#7c9810587d659a40d27bcdb4d5616eab059494b2"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-plain-obj/download/is-plain-obj-2.1.0.tgz#45e42e37fccf1f40da8e5f76ee21515840c09287"
  integrity sha1-ReQuN/zPH0Dajl927iFRWEDAkoc=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz#af6f2ea14ac5a646183a5bbdb5baabbc156ad9d7"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-property@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"
  integrity sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-regexp@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-regexp/download/is-regexp-2.1.0.tgz#cd734a56864e23b956bf4e7c66c396a4c0b22c2d"
  integrity sha1-zXNKVoZOI7lWv058ZsOWpMCyLC0=

is-set@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/is-set/download/is-set-2.0.2.tgz#90755fa4c2562dc1c5d4024760d6119b94ca18ec"
  integrity sha1-kHVfpMJWLcHF1AJHYNYRm5TKGOw=

is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.3.tgz#1237f1cba059cdb62431d378dcc37d9680181688"
  integrity sha1-Ejfxy6BZzbYkMdN43MN9loAYFog=
  dependencies:
    call-bind "^1.0.7"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-text-path@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-text-path/download/is-text-path-1.0.1.tgz#4e1aa0fb51bfbcb3e92688001397202c1775b66e"
  integrity sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=
  dependencies:
    text-extensions "^1.0.0"

is-type-of@^1.2.1:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/is-type-of/download/is-type-of-1.4.0.tgz#3ed175a0eee888b1da4983332e7714feb8a8fb2b"
  integrity sha1-PtF1oO7oiLHaSYMzLncU/rio+ys=
  dependencies:
    core-util-is "^1.0.2"
    is-class-hotfix "~0.0.6"
    isstream "~0.1.2"

is-typed-array@^1.1.13, is-typed-array@^1.1.3:
  version "1.1.13"
  resolved "http://registry.npm.qima-inc.com/is-typed-array/download/is-typed-array-1.1.13.tgz#d6c5ca56df62334959322d7d7dd1cca50debe229"
  integrity sha1-1sXKVt9iM0lZMi19fdHMpQ3r4ik=
  dependencies:
    which-typed-array "^1.1.14"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-weakmap@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/is-weakmap/download/is-weakmap-2.0.1.tgz#5008b59bdc43b698201d18f62b37b2ca243e8cf2"
  integrity sha1-UAi1m9xDtpggHRj2KzeyyiQ+jPI=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

is-weakset@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/is-weakset/download/is-weakset-2.0.2.tgz#4569d67a747a1ce5a994dfd4ef6dcea76e7c0a1d"
  integrity sha1-RWnWenR6HOWplN/U723Op258Ch0=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-2.0.1.tgz#a37d94ed9cda2d59865c9f76fe596ee1f338741e"
  integrity sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4=

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

iterator.prototype@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/iterator.prototype/download/iterator.prototype-1.1.2.tgz#5e29c8924f01916cb9335f1ff80619dcff22b0c0"
  integrity sha1-XinIkk8BkWy5M18f+AYZ3P8isMA=
  dependencies:
    define-properties "^1.2.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    reflect.getprototypeof "^1.0.4"
    set-function-name "^2.0.1"

javascript-stringify@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/javascript-stringify/download/javascript-stringify-2.1.0.tgz#27c76539be14d8bd128219a2d731b09337904e79"
  integrity sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=

jmespath@^0.15.0:
  version "0.15.0"
  resolved "http://registry.npm.qima-inc.com/jmespath/download/jmespath-0.15.0.tgz#a3f222a9aae9f966f5d27c796510e28091764217"
  integrity sha1-o/Iiqarp+Wb10nx5ZRDigJF2Qhc=

joycon@^2.2.5:
  version "2.2.5"
  resolved "http://registry.npm.qima-inc.com/joycon/download/joycon-2.2.5.tgz#8d4cf4cbb2544d7b7583c216fcdfec19f6be1615"
  integrity sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU=

joycon@^3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/joycon/download/joycon-3.1.1.tgz#bce8596d6ae808f8b68168f5fc69280996894f03"
  integrity sha1-vOhZbWroCPi2gWj1/GkoCZaJTwM=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/jsesc/download/jsesc-3.0.2.tgz#bb8b09a6597ba426425f2e4a07245c3d00b9343e"
  integrity sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^1.0.1, json5@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

json5@^2.2.3:
  version "2.2.3"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/jsonfile/download/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonp-body@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/jsonp-body/download/jsonp-body-1.0.0.tgz#e610fb6fcea79cf0cc9f27baa7b56377d4b0bb36"
  integrity sha1-5hD7b86nnPDMnye6p7Vjd9SwuzY=

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/jsonparse/download/jsonparse-1.3.1.tgz#3f4dae4a91fac315f71062f8521cc239f1366280"
  integrity sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "http://registry.npm.qima-inc.com/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz#4766bd05a8e2a11af222becd19e15575e52a853a"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keygrip@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/keygrip/download/keygrip-1.0.3.tgz#399d709f0aed2bab0a059e0cdd3a5023a053e1dc"
  integrity sha1-OZ1wnwrtK6sKBZ4M3TpQI6BT4dw=

keygrip@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/keygrip/download/keygrip-1.1.0.tgz#871b1681d5e159c62a445b0c74b615e0917e7226"
  integrity sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=
  dependencies:
    tsscmp "1.0.6"

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://registry.npm.qima-inc.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

killable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/killable/download/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

known-css-properties@^0.21.0:
  version "0.21.0"
  resolved "http://registry.npm.qima-inc.com/known-css-properties/download/known-css-properties-0.21.0.tgz#15fbd0bbb83447f3ce09d8af247ed47c68ede80d"
  integrity sha1-FfvQu7g0R/POCdivJH7UfGjt6A0=

koa-body@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/koa-body/download/koa-body-2.5.0.tgz#84e8fcd8d5229a8cc1cb98a926e939069e716915"
  integrity sha1-hOj82NUimozBy5ipJuk5Bp5xaRU=
  dependencies:
    co-body "^5.1.1"
    formidable "^1.1.1"

koa-body@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/koa-body/download/koa-body-4.2.0.tgz#37229208b820761aca5822d14c5fc55cee31b26f"
  integrity sha1-NyKSCLggdhrKWCLRTF/FXO4xsm8=
  dependencies:
    "@types/formidable" "^1.0.31"
    co-body "^5.1.1"
    formidable "^1.1.1"

koa-bodyparser@4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-bodyparser/download/koa-bodyparser-4.2.1.tgz#4d7dacb5e6db1106649b595d9e5ccb158b6f3b29"
  integrity sha1-TX2stebbEQZkm1ldnlzLFYtvOyk=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"

koa-bodyparser@^4.3.0:
  version "4.4.1"
  resolved "http://registry.npm.qima-inc.com/koa-bodyparser/download/koa-bodyparser-4.4.1.tgz#a908d848e142cc57d9eece478e932bf00dce3029"
  integrity sha1-qQjYSOFCzFfZ7s5HjpMr8A3OMCk=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"
    type-is "^1.6.18"

koa-bunyan-logger@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/koa-bunyan-logger/download/koa-bunyan-logger-2.1.0.tgz#a3a32c0329ba52b10a33f01b3adcb5f01d70eac5"
  integrity sha1-o6MsAym6UrEKM/AbOty18B1w6sU=
  dependencies:
    bunyan "~1.8.12"
    on-finished "~2.3.0"
    uuid "^3.0.0"

koa-compose@4.1.0, koa-compose@^4.0.0, koa-compose@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-3.2.1.tgz#a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7"
  integrity sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=
  dependencies:
    any-promise "^1.1.0"

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/koa-convert/download/koa-convert-1.2.0.tgz#da40875df49de0539098d1700b50820cebcd21d0"
  integrity sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

koa-convert@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-convert/download/koa-convert-2.0.0.tgz#86a0c44d81d40551bae22fee6709904573eea4f5"
  integrity sha1-hqDETYHUBVG64i/uZwmQRXPupPU=
  dependencies:
    co "^4.6.0"
    koa-compose "^4.1.0"

koa-is-json@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-is-json/download/koa-is-json-1.0.0.tgz#273c07edcdcb8df6a2c1ab7d59ee76491451ec14"
  integrity sha1-JzwH7c3Ljfaiwat9We52SRRR7BQ=

koa-router@7.4.0:
  version "7.4.0"
  resolved "http://registry.npm.qima-inc.com/koa-router/download/koa-router-7.4.0.tgz#aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0"
  integrity sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=
  dependencies:
    debug "^3.1.0"
    http-errors "^1.3.1"
    koa-compose "^3.0.0"
    methods "^1.0.1"
    path-to-regexp "^1.1.1"
    urijs "^1.19.0"

koa-send@^4.1.0:
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/koa-send/download/koa-send-4.1.3.tgz#0822207bbf5253a414c8f1765ebc29fa41353cb6"
  integrity sha1-CCIge79SU6QUyPF2Xrwp+kE1PLY=
  dependencies:
    debug "^2.6.3"
    http-errors "^1.6.1"
    mz "^2.6.0"
    resolve-path "^1.4.0"

koa-static@4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/koa-static/download/koa-static-4.0.2.tgz#6cda92d88d771dcaad9f0d825cd94a631c861a1a"
  integrity sha1-bNqS2I13Hcqtnw2CXNlKYxyGGho=
  dependencies:
    debug "^2.6.8"
    koa-send "^4.1.0"

koa@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/koa/download/koa-2.5.0.tgz#b0fbe1e195e43b27588a04fd0be0ddaeca2c154c"
  integrity sha1-sPvh4ZXkOydYigT9C+DdrsosFUw=
  dependencies:
    accepts "^1.2.2"
    content-disposition "~0.5.0"
    content-type "^1.0.0"
    cookies "~0.7.0"
    debug "*"
    delegates "^1.0.0"
    depd "^1.1.0"
    destroy "^1.0.3"
    error-inject "~1.0.0"
    escape-html "~1.0.1"
    fresh "^0.5.2"
    http-assert "^1.1.0"
    http-errors "^1.2.8"
    is-generator-function "^1.0.3"
    koa-compose "^4.0.0"
    koa-convert "^1.2.0"
    koa-is-json "^1.0.0"
    mime-types "^2.0.7"
    on-finished "^2.1.0"
    only "0.0.2"
    parseurl "^1.3.0"
    statuses "^1.2.0"
    type-is "^1.5.5"
    vary "^1.0.0"

koa@^2.13.0, koa@^2.13.1:
  version "2.15.3"
  resolved "http://registry.npm.qima-inc.com/koa/download/koa-2.15.3.tgz#062809266ee75ce0c75f6510a005b0e38f8c519a"
  integrity sha1-BigJJm7nXODHX2UQoAWw44+MUZo=
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.9.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/lcid/download/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

leven@2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/leven/download/leven-2.1.0.tgz#c2e7a9f772094dee9d34202ae8acce4687875580"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lightning-request@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-1.0.0.tgz#b36d5a9714045781b04a7d4e8ef37017b95d0ff6"
  integrity sha1-s21alxQEV4GwSn1OjvNwF7ldD/Y=
  dependencies:
    sync-rpc "^1.3.6"

lightning-request@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-1.0.1.tgz#89b5d2dca88609e41d32d03d505d7a29c574739f"
  integrity sha1-ibXS3KiGCeQdMtA9UF16KcV0c58=
  dependencies:
    sync-rpc "^1.3.6"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

lint-staged@^10.1.3:
  version "10.5.4"
  resolved "http://registry.npm.qima-inc.com/lint-staged/download/lint-staged-10.5.4.tgz#cd153b5f0987d2371fc1d2847a409a2fe705b665"
  integrity sha1-zRU7XwmH0jcfwdKEekCaL+cFtmU=
  dependencies:
    chalk "^4.1.0"
    cli-truncate "^2.1.0"
    commander "^6.2.0"
    cosmiconfig "^7.0.0"
    debug "^4.2.0"
    dedent "^0.7.0"
    enquirer "^2.3.6"
    execa "^4.1.0"
    listr2 "^3.2.2"
    log-symbols "^4.0.0"
    micromatch "^4.0.2"
    normalize-path "^3.0.0"
    please-upgrade-node "^3.2.0"
    string-argv "0.3.1"
    stringify-object "^3.3.0"

listr2@^3.2.2:
  version "3.14.0"
  resolved "http://registry.npm.qima-inc.com/listr2/download/listr2-3.14.0.tgz#23101cc62e1375fd5836b248276d1d2b51fdbe9e"
  integrity sha1-IxAcxi4Tdf1YNrJIJ20dK1H9vp4=
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.16"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.5.1"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/load-json-file/download/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

loader-runner@^2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/loader-runner/download/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^1.2.3:
  version "1.4.2"
  resolved "http://registry.npm.qima-inc.com/loader-utils/download/loader-utils-1.4.2.tgz#29a957f3a63973883eb684f10ffd3d151fec01a3"
  integrity sha1-KalX86Y5c4g+toTxD/09FR/sAaM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locutus@2.0.10:
  version "2.0.10"
  resolved "http://registry.npm.qima-inc.com/locutus/download/locutus-2.0.10.tgz#f903619466a98a4ab76e8b87a5854b55a743b917"
  integrity sha1-+QNhlGapikq3bouHpYVLVadDuRc=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clone@^4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.qima-inc.com/lodash.clone/download/lodash.clone-4.5.0.tgz#195870450f5a13192478df4bc3d23d2dea1907b6"
  integrity sha1-GVhwRQ9aExkkeN9Lw9I9LeoZB7Y=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz#d09178716ffea4dde9e5fb7b37f6f0802274580c"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.qima-inc.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz#f31c22225a9632d2bbf8e4addbef240aa765a61f"
  integrity sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "http://registry.npm.qima-inc.com/lodash.get/download/lodash.get-4.4.2.tgz#2d177f652fa31e939b4438d5341499dfa3825e99"
  integrity sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://registry.npm.qima-inc.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://registry.npm.qima-inc.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash@4.17.10:
  version "4.17.10"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.10.tgz#1b7793cf7259ea38fb3661d4d38b3260af8ae4e7"
  integrity sha1-G3eTz3JZ6jj7NmHU04syYK+K5Oc=

lodash@4.17.11:
  version "4.17.11"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"
  integrity sha1-s56mIp72B+zYniyN8SU2iRysm40=

lodash@4.17.19:
  version "4.17.19"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.19.tgz#e48ddedbe30b3321783c5b4301fbd353bc1e4a4b"
  integrity sha1-5I3e2+MLMyF4PFtDAfvTU7weSks=

lodash@4.17.21, lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.21, lodash@^4.2.1:
  version "4.17.21"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

lodash@4.17.5:
  version "4.17.5"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.5.tgz#99a92d65c0272debe8c96b6057bc8fbfa3bed511"
  integrity sha1-maktZcAnLevoyWtgV7yPv6O+1RE=

log-symbols@^4.0.0, log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/log-update/download/log-update-4.0.0.tgz#589ecd352471f2a1c0c570287543a64dfd20e0a1"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

loglevel@^1.6.8:
  version "1.9.1"
  resolved "http://registry.npm.qima-inc.com/loglevel/download/loglevel-1.9.1.tgz#d63976ac9bcd03c7c873116d41c2a85bafff1be7"
  integrity sha1-1jl2rJvNA8fIcxFtQcKoW6//G+c=

long@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/long/download/long-4.0.0.tgz#9a7b71cfb7d361a194ea555241c92f7468d5bf28"
  integrity sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=

long@^5.0.0:
  version "5.2.3"
  resolved "http://registry.npm.qima-inc.com/long/download/long-5.2.3.tgz#a3ba97f3877cf1d778eccbcb048525ebb77499e1"
  integrity sha1-o7qX84d88dd47MvLBIUl67d0meE=

longest-streak@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/longest-streak/download/longest-streak-2.0.4.tgz#b8599957da5b5dab64dee3fe316fa774597d90e4"
  integrity sha1-uFmZV9pbXatk3uP+MW+ndFl9kOQ=

loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lru-cache@5.1.1, lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.14.1:
  version "7.18.3"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-7.18.3.tgz#f793896e0fd0e954a59dfdd82f0773808df6aa89"
  integrity sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=

make-dir@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/map-obj/download/map-obj-2.0.0.tgz#a65cd29087a92598b8791257a523e021222ac1f9"
  integrity sha1-plzSkIepJZi4eRJXpSPgISIqwfk=

map-obj@^4.0.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/map-obj/download/map-obj-4.3.0.tgz#9304f906e93faae70880da102a9f1df0ea8bb05a"
  integrity sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/mathml-tag-names/download/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/md5.js/download/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdast-util-from-markdown@^0.8.0:
  version "0.8.5"
  resolved "http://registry.npm.qima-inc.com/mdast-util-from-markdown/download/mdast-util-from-markdown-0.8.5.tgz#d1ef2ca42bc377ecb0463a987910dae89bd9a28c"
  integrity sha1-0e8spCvDd+ywRjqYeRDa6JvZoow=
  dependencies:
    "@types/mdast" "^3.0.0"
    mdast-util-to-string "^2.0.0"
    micromark "~2.11.0"
    parse-entities "^2.0.0"
    unist-util-stringify-position "^2.0.0"

mdast-util-to-markdown@^0.6.0:
  version "0.6.5"
  resolved "http://registry.npm.qima-inc.com/mdast-util-to-markdown/download/mdast-util-to-markdown-0.6.5.tgz#b33f67ca820d69e6cc527a93d4039249b504bebe"
  integrity sha1-sz9nyoINaebMUnqT1AOSSbUEvr4=
  dependencies:
    "@types/unist" "^2.0.0"
    longest-streak "^2.0.0"
    mdast-util-to-string "^2.0.0"
    parse-entities "^2.0.0"
    repeat-string "^1.0.0"
    zwitch "^1.0.0"

mdast-util-to-string@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/mdast-util-to-string/download/mdast-util-to-string-2.0.0.tgz#b8cfe6a713e1091cb5b728fc48885a4767f8b97b"
  integrity sha1-uM/mpxPhCRy1tyj8SIhaR2f4uXs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-fs@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/memory-fs/download/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/meow/download/meow-5.0.0.tgz#dfc73d63a9afc714a5e371760eb5c88b91078aa4"
  integrity sha1-38c9Y6mvxxSl43F2DrXIi5EHiqQ=
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"
    yargs-parser "^10.0.0"

meow@^8.0.0:
  version "8.1.2"
  resolved "http://registry.npm.qima-inc.com/meow/download/meow-8.1.2.tgz#bcbe45bda0ee1729d350c03cffc8395a36c4e897"
  integrity sha1-vL5FvaDuFynTUMA8/8g5WjbE6Jc=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

meow@^9.0.0:
  version "9.0.0"
  resolved "http://registry.npm.qima-inc.com/meow/download/meow-9.0.0.tgz#cd9510bc5cac9dee7d03c73ee1f9ad959f4ea364"
  integrity sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize "^1.2.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-descriptors@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/merge-descriptors/download/merge-descriptors-1.0.3.tgz#d80319a65f3c7935351e5cfdac8f9318504dbed5"
  integrity sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3, merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@^1.0.1, methods@^1.1.2, methods@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromark@~2.11.0:
  version "2.11.4"
  resolved "http://registry.npm.qima-inc.com/micromark/download/micromark-2.11.4.tgz#d13436138eea826383e822449c9a5c50ee44665a"
  integrity sha1-0TQ2E47qgmOD6CJEnJpcUO5EZlo=
  dependencies:
    debug "^4.0.0"
    parse-entities "^2.0.0"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.8"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

microtime@3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/microtime/download/microtime-3.0.0.tgz#d140914bde88aa89b4f9fd2a18620b435af0f39b"
  integrity sha1-0UCRS96Iqom0+f0qGGILQ1rw85s=
  dependencies:
    node-addon-api "^1.2.0"
    node-gyp-build "^3.8.0"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

"mime-db@>= 1.43.0 < 2":
  version "1.53.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.53.0.tgz#3cb63cd820fc29896d9d4e8c32ab4fcd74ccb447"
  integrity sha1-PLY82CD8KYltnU6MMqtPzXTMtEc=

mime-types@^2.0.7, mime-types@^2.1.12, mime-types@^2.1.18, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0, mime@^1.3.4:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.3.1.tgz#b1621c54d63b97c47d3cfe7f7215f7d64517c369"
  integrity sha1-sWIcVNY7l8R9PP5/chX31kUXw2k=

mime@^2.4.4, mime@^2.4.6, mime@^2.5.2:
  version "2.6.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.6.0.tgz#a2a682a95cd4d0cb1d6257e28f83da7e35800367"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/min-indent/download/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

"minimatch@2 || 3", minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/minimist-options/download/minimist-options-4.1.0.tgz#c0655713c53a8a2ebd77ffa247d342c40f010619"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist-options@^3.0.1:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/minimist-options/download/minimist-options-3.0.2.tgz#fba4c8191339e13ecf4d61beb03f070103f3d954"
  integrity sha1-+6TIGRM54T7PTWG+sD8HAQPz2VQ=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"

minimist@1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minimist@^1.1.0, minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

mississippi@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/mississippi/download/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp-classic@^0.5.2:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/mkdirp-classic/download/mkdirp-classic-0.5.3.tgz#fa10c9115cc6d8865be221ba47ee9bed78601113"
  integrity sha1-+hDJEVzG2IZb4iG6R+6b7XhgERM=

mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@^0.5.6, mkdirp@~0.5.1:
  version "0.5.6"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

moment@^2.19.3:
  version "2.30.1"
  resolved "http://registry.npm.qima-inc.com/moment/download/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha1-+MkcB7enhuMMWZJt9TC06slpdK4=

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

mri@1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/mri/download/mri-1.1.4.tgz#7cb1dd1b9b40905f1fac053abe25b6720f44744a"
  integrity sha1-fLHdG5tAkF8frAU6viW2cg9EdEo=

mrmime@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/mrmime/download/mrmime-2.0.0.tgz#151082a6e06e59a9a39b46b3e14d5cfe92b3abb4"
  integrity sha1-FRCCpuBuWamjm0az4U1c/pKzq7Q=

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3, ms@^2.0.0, ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "http://registry.npm.qima-inc.com/multicast-dns/download/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mv@~2:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/mv/download/mv-2.1.1.tgz#ae6ce0d6f6d5e0a4f7d893798d03c1ea9559b6a2"
  integrity sha1-rmzg1vbV4KT32JN5jQPB6pVZtqI=
  dependencies:
    mkdirp "~0.5.1"
    ncp "~2.0.0"
    rimraf "~2.4.0"

mysql2@^2.2.5:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/mysql2/download/mysql2-2.3.3.tgz#944f3deca4b16629052ff8614fbf89d5552545a0"
  integrity sha1-lE897KSxZikFL/hhT7+J1VUlRaA=
  dependencies:
    denque "^2.0.1"
    generate-function "^2.3.1"
    iconv-lite "^0.6.3"
    long "^4.0.0"
    lru-cache "^6.0.0"
    named-placeholders "^1.1.2"
    seq-queue "^0.0.5"
    sqlstring "^2.3.2"

mz@^2.6.0, mz@^2.7.0:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

named-placeholders@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/named-placeholders/download/named-placeholders-1.1.3.tgz#df595799a36654da55dda6152ba7a137ad1d9351"
  integrity sha1-31lXmaNmVNpV3aYVK6ehN60dk1E=
  dependencies:
    lru-cache "^7.14.1"

nan@^2.12.1, nan@^2.14.0:
  version "2.22.0"
  resolved "http://registry.npm.qima-inc.com/nan/download/nan-2.22.0.tgz#31bc433fc33213c97bad36404bb68063de604de3"
  integrity sha1-MbxDP8MyE8l7rTZAS7aAY95gTeM=

nanoid@^3.1.16:
  version "3.3.7"
  resolved "http://registry.npm.qima-inc.com/nanoid/download/nanoid-3.3.7.tgz#d0c301a691bc8d54efa0a2226ccf3fe2fd656bd8"
  integrity sha1-0MMBppG8jVTvoKIibM8/4v1la9g=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz#17b09581988979fddafe0201e931ba933c96cbb4"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

ncp@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ncp/download/ncp-2.0.0.tgz#195a21d6c46e361d2fb1281ba38b91e9df7bdbb3"
  integrity sha1-GVoh1sRuNh0vsSgbo4uR6d9727M=

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://registry.npm.qima-inc.com/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

negotiator@~0.6.4:
  version "0.6.4"
  resolved "http://registry.npm.qima-inc.com/negotiator/download/negotiator-0.6.4.tgz#777948e2452651c570b712dd01c23e262713fff7"
  integrity sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=

neo-async@^2.5.0, neo-async@^2.6.1:
  version "2.6.2"
  resolved "http://registry.npm.qima-inc.com/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

node-addon-api@^1.2.0:
  version "1.7.2"
  resolved "http://registry.npm.qima-inc.com/node-addon-api/download/node-addon-api-1.7.2.tgz#3df30b95720b53c24e59948b49532b662444f54d"
  integrity sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=

node-fetch@2.6.7:
  version "2.6.7"
  resolved "http://registry.npm.qima-inc.com/node-fetch/download/node-fetch-2.6.7.tgz#24de9fba827e3b4ae44dc8b20256a379160052ad"
  integrity sha1-JN6fuoJ+O0rkTciyAlajeRYAUq0=
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "http://registry.npm.qima-inc.com/node-forge/download/node-forge-0.10.0.tgz#32dea2afb3e9926f02ee5ce8794902691a676bf3"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-gyp-build@^3.8.0:
  version "3.9.0"
  resolved "http://registry.npm.qima-inc.com/node-gyp-build/download/node-gyp-build-3.9.0.tgz#53a350187dd4d5276750da21605d1cb681d09e25"
  integrity sha1-U6NQGH3U1SdnUNohYF0ctoHQniU=

node-hex@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/node-hex/download/node-hex-1.0.1.tgz#606208e91f9c02b9b81531b692b9f1da4860fb24"
  integrity sha1-YGII6R+cArm4FTG2krnx2khg+yQ=

node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^2.0.18:
  version "2.0.18"
  resolved "http://registry.npm.qima-inc.com/node-releases/download/node-releases-2.0.18.tgz#f010e8d35e2fe8d6b2944f03f70213ecedc4ca3f"
  integrity sha1-8BDo014v6NaylE8D9wIT7O3Eyj8=

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/normalize-package-data/download/normalize-package-data-3.0.3.tgz#dbcc3e2da59509a0983422884cd172eefdfa525e"
  integrity sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-selector@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/normalize-selector/download/normalize-selector-0.2.0.tgz#d0b145eb691189c63a78d201dc4fdb1293ef0c03"
  integrity sha1-0LFF62kRicY6eNIB3E/bEpPvDAM=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nunjucks@3.1.6:
  version "3.1.6"
  resolved "http://registry.npm.qima-inc.com/nunjucks/download/nunjucks-3.1.6.tgz#6e3a3420c77ceae937ae323e9e2995383d6410fb"
  integrity sha1-bjo0IMd86uk3rjI+nimVOD1kEPs=
  dependencies:
    a-sync-waterfall "^1.0.0"
    asap "^2.0.3"
    postinstall-build "^5.0.1"
    yargs "^3.32.0"
  optionalDependencies:
    chokidar "^2.0.0"

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.13.1:
  version "1.13.2"
  resolved "http://registry.npm.qima-inc.com/object-inspect/download/object-inspect-1.13.2.tgz#dea0088467fb991e67af4058147a24824a3043ff"
  integrity sha1-3qAIhGf7mR5nr0BYFHokgkowQ/8=

object-is@^1.1.5:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/object-is/download/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
  integrity sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.2, object.assign@^4.1.4, object.assign@^4.1.5:
  version "4.1.5"
  resolved "http://registry.npm.qima-inc.com/object.assign/download/object.assign-4.1.5.tgz#3a833f9ab7fdb80fc9e8d2300c803d216d8fdbb0"
  integrity sha1-OoM/mrf9uA/J6NIwDIA9IW2P27A=
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.2, object.entries@^1.1.8:
  version "1.1.8"
  resolved "http://registry.npm.qima-inc.com/object.entries/download/object.entries-1.1.8.tgz#bffe6f282e01f4d17807204a24f8edd823599c41"
  integrity sha1-v/5vKC4B9NF4ByBKJPjt2CNZnEE=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "http://registry.npm.qima-inc.com/object.fromentries/download/object.fromentries-2.0.8.tgz#f7195d8a9b97bd95cbc1999ea939ecd1a2b00c65"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/object.groupby/download/object.groupby-1.0.3.tgz#9b125c36238129f6f7b61954a1e7176148d5002e"
  integrity sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.6, object.values@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/object.values/download/object.values-1.2.0.tgz#65405a9d92cee68ac2d303002e0b8470a4d9ab1b"
  integrity sha1-ZUBanZLO5orC0wMALguEcKTZqxs=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-exit-leak-free@^2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/on-exit-leak-free/download/on-exit-leak-free-2.1.2.tgz#fed195c9ebddb7d9e4c3842f93f281ac8dadd3b8"
  integrity sha1-/tGVyevdt9nkw4Qvk/KBrI2t07g=

on-finished@2.4.1, on-finished@^2.1.0, on-finished@^2.3.0:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

only@0.0.2, only@~0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/only/download/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

open@7.1.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/open/download/open-7.1.0.tgz#68865f7d3cb238520fa1225a63cf28bcf8368a1c"
  integrity sha1-aIZffTyyOFIPoSJaY88ovPg2ihw=
  dependencies:
    is-docker "^2.0.0"
    is-wsl "^2.1.1"

opener@^1.5.2:
  version "1.5.2"
  resolved "http://registry.npm.qima-inc.com/opener/download/opener-1.5.2.tgz#5d37e1f35077b9dcac4301372271afdeb2a13598"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

opn@^5.5.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/opn/download/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.9.1:
  version "0.9.4"
  resolved "http://registry.npm.qima-inc.com/optionator/download/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.3.0, ora@^5.4.1:
  version "5.4.1"
  resolved "http://registry.npm.qima-inc.com/ora/download/ora-5.4.1.tgz#1b2678426af4ac4a509008e5e4ac9e9959db9e18"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/os-locale/download/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  integrity sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=
  dependencies:
    lcid "^1.0.0"

os-name@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/os-name/download/os-name-1.0.3.tgz#1b379f64835af7c5a7f498b357cb95215c159edf"
  integrity sha1-GzefZINa98Wn9JizV8uVIVwVnt8=
  dependencies:
    osx-release "^1.0.0"
    win-release "^1.0.0"

osx-release@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/osx-release/download/osx-release-1.1.0.tgz#f217911a28136949af1bf9308b241e2737d3cd6c"
  integrity sha1-8heRGigTaUmvG/kwiyQeJzfTzWw=
  dependencies:
    minimist "^1.1.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-map@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/p-retry/download/p-retry-3.0.1.tgz#316b4c8893e2c8dc1cfa891f406c4b422bebf328"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/pako/download/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/parallel-transform/download/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.6:
  version "5.1.6"
  resolved "http://registry.npm.qima-inc.com/parse-asn1/download/parse-asn1-5.1.6.tgz#385080a3ec13cb62a62d39409cb3e88844cdaed4"
  integrity sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-entities@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-entities/download/parse-entities-2.0.0.tgz#53c6eb5b9314a1f4ec99fa0fdf7ce01ecda0cbe8"
  integrity sha1-U8brW5MUofTsmfoP33zgHs2gy+g=
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseqs@0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/parseqs/download/parseqs-0.0.6.tgz#8e4bb5a19d1cdc844a08ac974d34e273afa670d5"
  integrity sha1-jku1oZ0c3IRKCKyXTTTic6+mcNU=

parseuri@0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/parseuri/download/parseuri-0.0.6.tgz#e1496e829e3ac2ff47f39a4dd044b32823c4a25a"
  integrity sha1-4Ulugp46wv9H85pN0ESzKCPEolo=

parseurl@^1.3.0, parseurl@^1.3.2, parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/path-browserify/download/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-matching@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/path-matching/download/path-matching-0.0.2.tgz#2d94c47b81b59e48cc41b3b0e3d588e4d342f3fc"
  integrity sha1-LZTEe4G1nkjMQbOw49WI5NNC8/w=
  dependencies:
    path-to-regexp "^2.2.0"

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.10:
  version "0.1.10"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-0.1.10.tgz#67e9108c5c0551b9e5326064387de4763c4d5f8b"
  integrity sha1-Z+kQjFwFUbnlMmBkOH3kdjxNX4s=

path-to-regexp@^1.1.1:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz#5dc0753acbf8521ca2e0f137b4578b917b10cf24"
  integrity sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ=
  dependencies:
    isarray "0.0.1"

path-to-regexp@^2.2.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-2.4.0.tgz#35ce7f333d5616f1c1e1bfe266c3aba2e5b2e704"
  integrity sha1-Nc5/Mz1WFvHB4b/iZsOrouWy5wQ=

path-to-regexp@^3.1.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-3.3.0.tgz#f7f31d32e8518c2660862b644414b6d5c63a611b"
  integrity sha1-9/MdMuhRjCZghitkRBS21cY6YRs=

path-to-regexp@^6.1.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-6.3.0.tgz#2b6a26a337737a8e1416f9272ed0766b1c0389f4"
  integrity sha1-K2omozdzeo4UFvknLtB2axwDifQ=

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause-stream@~0.0.11:
  version "0.0.11"
  resolved "http://registry.npm.qima-inc.com/pause-stream/download/pause-stream-0.0.11.tgz#fe5a34b0cbce12b5aa6a2b403ee2e73b602f1445"
  integrity sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=
  dependencies:
    through "~2.3"

pbkdf2@^3.0.3:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/pbkdf2/download/pbkdf2-3.1.2.tgz#dd822aa0887580e52f1a039dc3eda108efae3075"
  integrity sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pend@^1.2.0, pend@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/picocolors/download/picocolors-0.2.1.tgz#570670f793646851d1ba135996962abad587859f"
  integrity sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=

picocolors@^1.0.0, picocolors@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/picocolors/download/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pify@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pino-abstract-transport@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/pino-abstract-transport/download/pino-abstract-transport-1.2.0.tgz#97f9f2631931e242da531b5c66d3079c12c9d1b5"
  integrity sha1-l/nyYxkx4kLaUxtcZtMHnBLJ0bU=
  dependencies:
    readable-stream "^4.0.0"
    split2 "^4.0.0"

pino-http@5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/pino-http/download/pino-http-5.2.0.tgz#d51da7981ab6ea7ca9f17c4db24a427e4ba9c546"
  integrity sha1-1R2nmBq26nyp8XxNskpCfkupxUY=
  dependencies:
    fast-url-parser "^1.1.3"
    pino "^6.0.0"
    pino-std-serializers "^2.4.0"

pino-pretty@*:
  version "11.2.2"
  resolved "http://registry.npm.qima-inc.com/pino-pretty/download/pino-pretty-11.2.2.tgz#5e8ec69b31e90eb187715af07b1d29a544e60d39"
  integrity sha1-Xo7GmzHpDrGHcVrwex0ppUTmDTk=
  dependencies:
    colorette "^2.0.7"
    dateformat "^4.6.3"
    fast-copy "^3.0.2"
    fast-safe-stringify "^2.1.1"
    help-me "^5.0.0"
    joycon "^3.1.1"
    minimist "^1.2.6"
    on-exit-leak-free "^2.1.0"
    pino-abstract-transport "^1.0.0"
    pump "^3.0.0"
    readable-stream "^4.0.0"
    secure-json-parse "^2.4.0"
    sonic-boom "^4.0.1"
    strip-json-comments "^3.1.1"

pino-pretty@4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/pino-pretty/download/pino-pretty-4.0.0.tgz#afbff81f946342b9d6eabc434942fe490e02faa9"
  integrity sha1-r7/4H5RjQrnW6rxDSUL+SQ4C+qk=
  dependencies:
    "@hapi/bourne" "^2.0.0"
    args "^5.0.1"
    chalk "^3.0.0"
    dateformat "^3.0.3"
    fast-safe-stringify "^2.0.7"
    jmespath "^0.15.0"
    joycon "^2.2.5"
    pump "^3.0.0"
    readable-stream "^3.6.0"
    split2 "^3.1.1"
    strip-json-comments "^3.0.1"

pino-pretty@^4.2.1, pino-pretty@^4.7.1:
  version "4.8.0"
  resolved "http://registry.npm.qima-inc.com/pino-pretty/download/pino-pretty-4.8.0.tgz#f2f3055bf222456217b14ffb04d8be0a0cc17fce"
  integrity sha1-8vMFW/IiRWIXsU/7BNi+CgzBf84=
  dependencies:
    "@hapi/bourne" "^2.0.0"
    args "^5.0.1"
    chalk "^4.0.0"
    dateformat "^4.5.1"
    fast-safe-stringify "^2.0.7"
    jmespath "^0.15.0"
    joycon "^2.2.5"
    pump "^3.0.0"
    readable-stream "^3.6.0"
    rfdc "^1.3.0"
    split2 "^3.1.1"
    strip-json-comments "^3.1.1"

pino-std-serializers@*:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/pino-std-serializers/download/pino-std-serializers-7.0.0.tgz#7c625038b13718dbbd84ab446bd673dc52259e3b"
  integrity sha1-fGJQOLE3GNu9hKtEa9Zz3FIlnjs=

pino-std-serializers@^2.4.0, pino-std-serializers@^2.4.2:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/pino-std-serializers/download/pino-std-serializers-2.5.0.tgz#40ead781c65a0ce7ecd9c1c33f409d31fe712315"
  integrity sha1-QOrXgcZaDOfs2cHDP0CdMf5xIxU=

pino-std-serializers@^3.1.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/pino-std-serializers/download/pino-std-serializers-3.2.0.tgz#b56487c402d882eb96cd67c257868016b61ad671"
  integrity sha1-tWSHxALYguuWzWfCV4aAFrYa1nE=

pino@6.3.2:
  version "6.3.2"
  resolved "http://registry.npm.qima-inc.com/pino/download/pino-6.3.2.tgz#55f73aa61584774ca5984068ffb78e8d519ce19e"
  integrity sha1-Vfc6phWEd0ylmEBo/7eOjVGc4Z4=
  dependencies:
    fast-redact "^2.0.0"
    fast-safe-stringify "^2.0.7"
    flatstr "^1.0.12"
    pino-std-serializers "^2.4.2"
    quick-format-unescaped "^4.0.1"
    sonic-boom "^1.0.0"

pino@^6.0.0, pino@^6.11.0, pino@^6.11.2:
  version "6.14.0"
  resolved "http://registry.npm.qima-inc.com/pino/download/pino-6.14.0.tgz#b745ea87a99a6c4c9b374e4f29ca7910d4c69f78"
  integrity sha1-t0Xqh6mabEybN05PKcp5ENTGn3g=
  dependencies:
    fast-redact "^3.0.0"
    fast-safe-stringify "^2.0.8"
    flatstr "^1.0.12"
    pino-std-serializers "^3.1.0"
    process-warning "^1.0.0"
    quick-format-unescaped "^4.0.3"
    sonic-boom "^1.0.2"

pkg-dir@4.2.0, pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

please-upgrade-node@^3.1.1, please-upgrade-node@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

portfinder@^1.0.26:
  version "1.0.32"
  resolved "http://registry.npm.qima-inc.com/portfinder/download/portfinder-1.0.32.tgz#2fe1b9e58389712429dc2bea5beb2146146c7f81"
  integrity sha1-L+G55YOJcSQp3CvqW+shRhRsf4E=
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/possible-typed-array-names/download/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha1-ibtjxvraLD6QrcSmR77us5zHv48=

postcss-html@^0.36.0:
  version "0.36.0"
  resolved "http://registry.npm.qima-inc.com/postcss-html/download/postcss-html-0.36.0.tgz#b40913f94eaacc2453fd30a1327ad6ee1f88b204"
  integrity sha1-tAkT+U6qzCRT/TChMnrW7h+IsgQ=
  dependencies:
    htmlparser2 "^3.10.0"

postcss-less@^3.1.4:
  version "3.1.4"
  resolved "http://registry.npm.qima-inc.com/postcss-less/download/postcss-less-3.1.4.tgz#369f58642b5928ef898ffbc1a6e93c958304c5ad"
  integrity sha1-Np9YZCtZKO+Jj/vBpuk8lYMExa0=
  dependencies:
    postcss "^7.0.14"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.6.tgz#3d84dec809f34de020372c41b039956966896686"
  integrity sha1-PYTeyAnzTeAgNyxBsDmVaWaJZoY=

postcss-safe-parser@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-safe-parser/download/postcss-safe-parser-4.0.2.tgz#a6d4e48f0f37d9f7c11b2a581bf00f8ba4870b96"
  integrity sha1-ptTkjw832ffBGypYG/APi6SHC5Y=
  dependencies:
    postcss "^7.0.26"

postcss-sass@^0.4.4:
  version "0.4.4"
  resolved "http://registry.npm.qima-inc.com/postcss-sass/download/postcss-sass-0.4.4.tgz#91f0f3447b45ce373227a98b61f8d8f0785285a3"
  integrity sha1-kfDzRHtFzjcyJ6mLYfjY8HhShaM=
  dependencies:
    gonzales-pe "^4.3.0"
    postcss "^7.0.21"

postcss-scss@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/postcss-scss/download/postcss-scss-2.1.1.tgz#ec3a75fa29a55e016b90bf3269026c53c1d2b383"
  integrity sha1-7Dp1+imlXgFrkL8yaQJsU8HSs4M=
  dependencies:
    postcss "^7.0.6"

postcss-selector-parser@^6.0.5:
  version "6.1.2"
  resolved "http://registry.npm.qima-inc.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz#27ecb41fb0e3b6ba7a1ec84fff347f734c7929de"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-syntax@^0.36.2:
  version "0.36.2"
  resolved "http://registry.npm.qima-inc.com/postcss-syntax/download/postcss-syntax-0.36.2.tgz#f08578c7d95834574e5593a82dfbfa8afae3b51c"
  integrity sha1-8IV4x9lYNFdOVZOoLfv6ivrjtRw=

postcss-value-parser@^4.1.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^7.0.14, postcss@^7.0.2, postcss@^7.0.21, postcss@^7.0.26, postcss@^7.0.32, postcss@^7.0.35, postcss@^7.0.6:
  version "7.0.39"
  resolved "http://registry.npm.qima-inc.com/postcss/download/postcss-7.0.39.tgz#9624375d965630e2e1f2c02a935c82a59cb48309"
  integrity sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postinstall-build@^5.0.1:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/postinstall-build/download/postinstall-build-5.0.3.tgz#238692f712a481d8f5bc8960e94786036241efc7"
  integrity sha1-I4aS9xKkgdj1vIlg6UeGA2JB78c=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^1.19.1:
  version "1.19.1"
  resolved "http://registry.npm.qima-inc.com/prettier/download/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

pretty-time@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/pretty-time/download/pretty-time-1.1.0.tgz#ffb7429afabb8535c346a34e41873adf3d74dd0e"
  integrity sha1-/7dCmvq7hTXDRqNOQYc63z103Q4=

prettyjson@1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/prettyjson/download/prettyjson-1.2.1.tgz#fcffab41d19cab4dfae5e575e64246619b12d289"
  integrity sha1-/P+rQdGcq0365eV15kJGYZsS0ok=
  dependencies:
    colors "^1.1.2"
    minimist "^1.2.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process-warning@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/process-warning/download/process-warning-1.0.0.tgz#980a0b25dc38cd6034181be4b7726d89066b4616"
  integrity sha1-mAoLJdw4zWA0GBvkt3JtiQZrRhY=

process@^0.11.10:
  version "0.11.10"
  resolved "http://registry.npm.qima-inc.com/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@2.0.3, progress@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://registry.npm.qima-inc.com/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

properties-parser@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/properties-parser/download/properties-parser-0.3.1.tgz#1316e9539ffbfd93845e369b211022abd478771a"
  integrity sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=
  dependencies:
    string.prototype.codepointat "^0.2.0"

protobufjs@^6.8.6:
  version "6.11.4"
  resolved "http://registry.npm.qima-inc.com/protobufjs/download/protobufjs-6.11.4.tgz#29a412c38bf70d89e537b6d02d904a6f448173aa"
  integrity sha1-KaQSw4v3DYnlN7bQLZBKb0SBc6o=
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" ">=13.7.0"
    long "^4.0.0"

protobufjs@^7.2.5:
  version "7.4.0"
  resolved "http://registry.npm.qima-inc.com/protobufjs/download/protobufjs-7.4.0.tgz#7efe324ce9b3b61c82aae5de810d287bc08a248a"
  integrity sha1-fv4yTOmzthyCquXegQ0oe8CKJIo=
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "http://registry.npm.qima-inc.com/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@1.1.0, proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

prr@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/public-encrypt/download/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@^1.2.4, punycode@^1.3.2, punycode@^1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

puppeteer-core@13.7.0:
  version "13.7.0"
  resolved "http://registry.npm.qima-inc.com/puppeteer-core/download/puppeteer-core-13.7.0.tgz#3344bee3994163f49120a55ddcd144a40575ba5b"
  integrity sha1-M0S+45lBY/SRIKVd3NFEpAV1uls=
  dependencies:
    cross-fetch "3.1.5"
    debug "4.3.4"
    devtools-protocol "0.0.981744"
    extract-zip "2.0.1"
    https-proxy-agent "5.0.1"
    pkg-dir "4.2.0"
    progress "2.0.3"
    proxy-from-env "1.1.0"
    rimraf "3.0.2"
    tar-fs "2.1.1"
    unbzip2-stream "1.4.3"
    ws "8.5.0"

q@^1.5.1:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@6.13.0, qs@^6.12.3, qs@^6.4.0, qs@^6.5.2:
  version "6.13.0"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.13.0.tgz#6ca3bd58439f7e245655798997787b0d88a51906"
  integrity sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=
  dependencies:
    side-channel "^1.0.6"

query-string@5:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-5.1.1.tgz#a78c012b71c17e05f2e3fa2319dd330682efb3cb"
  integrity sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s=
  dependencies:
    decode-uri-component "^0.2.0"
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

query-string@^6.13.7:
  version "6.14.1"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-6.14.1.tgz#7ac2dca46da7f309449ba0f86b1fd28255b0c86a"
  integrity sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-format-unescaped@^4.0.1, quick-format-unescaped@^4.0.3:
  version "4.0.4"
  resolved "http://registry.npm.qima-inc.com/quick-format-unescaped/download/quick-format-unescaped-4.0.4.tgz#93ef6dd8d3453cbc7970dd614fad4c5954d6b5a7"
  integrity sha1-k+9t2NNFPLx5cN1hT61MWVTWtac=

quick-lru@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/quick-lru/download/quick-lru-1.1.0.tgz#4360b17c61136ad38078397ff11416e186dcfbb8"
  integrity sha1-Q2CxfGETatOAeDl/8RQW4Ybc+7g=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/quick-lru/download/quick-lru-4.0.1.tgz#5b8878f113a58217848c6482026c73e1ba57727f"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

raf@^3.4.1:
  version "3.4.1"
  resolved "http://registry.npm.qima-inc.com/raf/download/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.2, raw-body@^2.2.0, raw-body@^2.3.3:
  version "2.5.2"
  resolved "http://registry.npm.qima-inc.com/raw-body/download/raw-body-2.5.2.tgz#99febd83b90e08975087e8f1f9419a149366b68a"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^16.13.1:
  version "16.13.1"
  resolved "http://registry.npm.qima-inc.com/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

read-pkg-up@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg-up/download/read-pkg-up-3.0.0.tgz#3ed496685dba0f8fe118d0691dc51f4a1ff96f07"
  integrity sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^3.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

read-pkg@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-4.0.1.tgz#963625378f3e1c4d48c85872b5a6ec7d5d093237"
  integrity sha1-ljYlN48+HE1IyFhytabsfV0JMjc=
  dependencies:
    normalize-package-data "^2.3.2"
    parse-json "^4.0.0"
    pify "^3.0.0"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.0, readable-stream@^2.3.3, readable-stream@^2.3.5, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@3, readable-stream@^3.0.0, readable-stream@^3.0.6, readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0, readable-stream@^3.6.2:
  version "3.6.2"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^4.0.0:
  version "4.5.2"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-4.5.2.tgz#9e7fc4c45099baeed934bff6eb97ba6cf2729e09"
  integrity sha1-nn/ExFCZuu7ZNL/265e6bPJyngk=
  dependencies:
    abort-controller "^3.0.0"
    buffer "^6.0.3"
    events "^3.3.0"
    process "^0.11.10"
    string_decoder "^1.3.0"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.qima-inc.com/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redent@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/redent/download/redent-2.0.0.tgz#c1b2007b42d57eb1389079b3c8333639d5e1ccaa"
  integrity sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=
  dependencies:
    indent-string "^3.0.0"
    strip-indent "^2.0.0"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/redent/download/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

redis-commands@1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/redis-commands/download/redis-commands-1.4.0.tgz#52f9cf99153efcce56a8f86af986bd04e988602f"
  integrity sha1-UvnPmRU+/M5WqPhq+Ya9BOmIYC8=

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/redis-errors/download/redis-errors-1.2.0.tgz#eb62d2adb15e4eaf4610c04afe1529384250abad"
  integrity sha1-62LSrbFeTq9GEMBK/hUpOEJQq60=

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/redis-parser/download/redis-parser-3.0.0.tgz#b66d828cdcafe6b4b8a428a7def4c6bcac31c8b4"
  integrity sha1-tm2CjNyv5rS4pCin3vTGvKwxyLQ=
  dependencies:
    redis-errors "^1.0.0"

reflect-metadata@^0.1.12:
  version "0.1.14"
  resolved "http://registry.npm.qima-inc.com/reflect-metadata/download/reflect-metadata-0.1.14.tgz#24cf721fe60677146bb77eeb0e1f9dece3d65859"
  integrity sha1-JM9yH+YGdxRrt37rDh+d7OPWWFk=

reflect.getprototypeof@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.4.tgz#aaccbf41aca3821b87bb71d9dcbc7ad0ba50a3f3"
  integrity sha1-qsy/QayjghuHu3HZ3Lx60LpQo/M=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    globalthis "^1.0.3"
    which-builtin-type "^1.1.3"

regenerator-runtime@^0.10.5:
  version "0.10.5"
  resolved "http://registry.npm.qima-inc.com/regenerator-runtime/download/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"
  integrity sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.qima-inc.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.2:
  version "1.5.2"
  resolved "http://registry.npm.qima-inc.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.2.tgz#138f644a3350f981a858c44f6bb1a61ff59be334"
  integrity sha1-E49kSjNQ+YGoWMRPa7GmH/Wb4zQ=
  dependencies:
    call-bind "^1.0.6"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.1"

regexpp@^3.1.0, regexpp@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-3.2.0.tgz#0425a2768d8f23bad70ca4b90461fa2f1213e1b2"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

remark-parse@^9.0.0:
  version "9.0.0"
  resolved "http://registry.npm.qima-inc.com/remark-parse/download/remark-parse-9.0.0.tgz#4d20a299665880e4f4af5d90b7c7b8a935853640"
  integrity sha1-TSCimWZYgOT0r12Qt8e4qTWFNkA=
  dependencies:
    mdast-util-from-markdown "^0.8.0"

remark-stringify@^9.0.0:
  version "9.0.1"
  resolved "http://registry.npm.qima-inc.com/remark-stringify/download/remark-stringify-9.0.1.tgz#576d06e910548b0a7191a71f27b33f1218862894"
  integrity sha1-V20G6RBUiwpxkacfJ7M/EhiGKJQ=
  dependencies:
    mdast-util-to-markdown "^0.6.0"

remark@^13.0.0:
  version "13.0.0"
  resolved "http://registry.npm.qima-inc.com/remark/download/remark-13.0.0.tgz#d15d9bf71a402f40287ebe36067b66d54868e425"
  integrity sha1-0V2b9xpAL0Aofr42Bntm1Uho5CU=
  dependencies:
    remark-parse "^9.0.0"
    remark-stringify "^9.0.0"
    unified "^9.1.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/repeat-element/download/repeat-element-1.1.4.tgz#be681520847ab58c7568ac75fbfad28ed42d39e9"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.0.0, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@2.0.2, require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requireindex@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/requireindex/download/requireindex-1.1.0.tgz#e5404b81557ef75db6e49c5a72004893fe03e162"
  integrity sha1-5UBLgVV+91225JxacgBIk/4D4WI=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@5.0.0, resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-global@1.0.0, resolve-global@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-global/download/resolve-global-1.0.0.tgz#a2a79df4af2ca3f49bf77ef9ddacd322dad19255"
  integrity sha1-oqed9K8so/Sb93753azTItrRklU=
  dependencies:
    global-dirs "^0.1.1"

resolve-path@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/resolve-path/download/resolve-path-1.4.0.tgz#c4bda9f5efb2fce65247873ab36bb4d834fe16f7"
  integrity sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=
  dependencies:
    http-errors "~1.6.2"
    path-is-absolute "1.0.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.12.0, resolve@^1.22.4:
  version "1.22.8"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-2.0.0-next.5.tgz#6b0ec3107e671e52b68cd068ef327173b90dc03c"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://registry.npm.qima-inc.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.qima-inc.com/retry/download/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.2.0, rfdc@^1.3.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/rfdc/download/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

rimraf@3.0.2, rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rimraf@^2.5.2, rimraf@^2.5.4, rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@~2.4.0:
  version "2.4.5"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.4.5.tgz#ee710ce5d93a8fdb856fb5ea8ff0e2d75934b2da"
  integrity sha1-7nEM5dk6j9uFb7Xqj/Di11k0sto=
  dependencies:
    glob "^6.0.1"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-node@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/run-node/download/run-node-1.0.0.tgz#46b50b946a2aa2d4947ae1d886e9856fd9cabe5e"
  integrity sha1-RrULlGoqotSUeuHYhumFb9nKvl4=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^7.5.1:
  version "7.8.1"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/safe-array-concat/download/safe-array-concat-1.1.2.tgz#81d77ee0c4e8b863635227c721278dd524c20edb"
  integrity sha1-gdd+4MTouGNjUifHISeN1STCDts=
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-json-stringify@~1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/safe-json-stringify/download/safe-json-stringify-1.2.0.tgz#356e44bc98f1f93ce45df14bcd7c01cda86e0afd"
  integrity sha1-NW5EvJjx+TzkXfFLzXwBzahuCv0=

safe-regex-test@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/safe-regex-test/download/safe-regex-test-1.0.3.tgz#a5b4c0f06e0ab50ea2c395c14d8371232924c377"
  integrity sha1-pbTA8G4KtQ6iw5XBTYNxIykkw3c=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-regex "^1.1.4"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@>=0.6.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/sax/download/sax-1.4.1.tgz#44cc8988377f126304d3b3fc1010c733b929ef0f"
  integrity sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/schema-utils/download/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.0.0:
  version "2.7.1"
  resolved "http://registry.npm.qima-inc.com/schema-utils/download/schema-utils-2.7.1.tgz#1ca4f32d1b24c590c203b8e7a50bf0ea4cd394d7"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

secure-json-parse@^2.4.0:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/secure-json-parse/download/secure-json-parse-2.7.0.tgz#5a5f9cd6ae47df23dba3151edd06855d47e09862"
  integrity sha1-Wl+c1q5H3yPboxUe3QaFXUfgmGI=

select-hose@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.8:
  version "1.10.14"
  resolved "http://registry.npm.qima-inc.com/selfsigned/download/selfsigned-1.10.14.tgz#ee51d84d9dcecc61e07e4aba34f229ab525c1574"
  integrity sha1-7lHYTZ3OzGHgfkq6NPIpq1JcFXQ=
  dependencies:
    node-forge "^0.10.0"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

"semver@2 || 3 || 4 || 5", semver@^5.0.1, semver@^5.5.0, semver@^5.6.0:
  version "5.7.2"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^6.0.0, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.2.1, semver@^7.3.4, semver@^7.3.5, semver@^7.3.7:
  version "7.6.3"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-7.6.3.tgz#980f7b5550bc175fb4dc09403085627f9eb33143"
  integrity sha1-mA97VVC8F1+03AlAMIVif56zMUM=

send@0.19.0:
  version "0.19.0"
  resolved "http://registry.npm.qima-inc.com/send/download/send-0.19.0.tgz#bbc5a388c8ea6c048967049dbeac0e4a3f09d7f8"
  integrity sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

seq-queue@^0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.qima-inc.com/seq-queue/download/seq-queue-0.0.5.tgz#d56812e1c017a6e4e7c3e3a37a1da6d78dd3c93e"
  integrity sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4=

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "http://registry.npm.qima-inc.com/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.16.2:
  version "1.16.2"
  resolved "http://registry.npm.qima-inc.com/serve-static/download/serve-static-1.16.2.tgz#b6a5343da47f6bdd2673848bf45754941e803296"
  integrity sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/set-function-length/download/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.1, set-function-name@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/set-function-name/download/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "http://registry.npm.qima-inc.com/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/shallow-clone/download/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@^0.8.4:
  version "0.8.5"
  resolved "http://registry.npm.qima-inc.com/shelljs/download/shelljs-0.8.5.tgz#de055408d8361bed66c669d2f000538ced8ee20c"
  integrity sha1-3gVUCNg2G+1mxmnS8ABTjO2O4gw=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel@^1.0.4, side-channel@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/side-channel/download/side-channel-1.0.6.tgz#abd25fb7cd24baf45466406b1096b7831c9215f2"
  integrity sha1-q9Jft80kuvRUZkBrEJa3gxySFfI=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "http://registry.npm.qima-inc.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

sirv@^2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/sirv/download/sirv-2.0.4.tgz#5dd9a725c578e34e449f332703eb2a74e46a29b0"
  integrity sha1-XdmnJcV4405EnzMnA+sqdORqKbA=
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

slash@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socket.io-adapter@~1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/socket.io-adapter/download/socket.io-adapter-1.1.2.tgz#ab3f0d6f66b8fc7fca3959ab5991f82221789be9"
  integrity sha1-qz8Nb2a4/H/KOVmrWZH4IiF4m+k=

socket.io-client@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/socket.io-client/download/socket.io-client-2.5.0.tgz#34f486f3640dde9c2211fce885ac2746f9baf5cb"
  integrity sha1-NPSG82QN3pwiEfzohawnRvm69cs=
  dependencies:
    backo2 "1.0.2"
    component-bind "1.0.0"
    component-emitter "~1.3.0"
    debug "~3.1.0"
    engine.io-client "~3.5.0"
    has-binary2 "~1.0.2"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    socket.io-parser "~3.3.0"
    to-array "0.1.4"

socket.io-parser@~3.3.0:
  version "3.3.4"
  resolved "http://registry.npm.qima-inc.com/socket.io-parser/download/socket.io-parser-3.3.4.tgz#ab84236b6d06eaf1fb68b179b3a7501195886cc3"
  integrity sha1-q4Qja20G6vH7aLF5s6dQEZWIbMM=
  dependencies:
    component-emitter "~1.3.0"
    debug "~3.1.0"
    isarray "2.0.1"

socket.io-parser@~3.4.0:
  version "3.4.3"
  resolved "http://registry.npm.qima-inc.com/socket.io-parser/download/socket.io-parser-3.4.3.tgz#b19bdaad38ed39fd68fba3f9d86768f667df0c29"
  integrity sha1-sZvarTjtOf1o+6P52Gdo9mffDCk=
  dependencies:
    component-emitter "1.2.1"
    debug "~4.1.0"
    isarray "2.0.1"

socket.io@^2.3.0:
  version "2.5.1"
  resolved "http://registry.npm.qima-inc.com/socket.io/download/socket.io-2.5.1.tgz#224459ecf13ab66bfc7fb6dd0c6bf760a9895e7f"
  integrity sha1-IkRZ7PE6tmv8f7bdDGv3YKmJXn8=
  dependencies:
    debug "~4.1.0"
    engine.io "~3.6.0"
    has-binary2 "~1.0.2"
    socket.io-adapter "~1.1.0"
    socket.io-client "2.5.0"
    socket.io-parser "~3.4.0"

sockjs-client@^1.5.0:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/sockjs-client/download/sockjs-client-1.6.1.tgz#350b8eda42d6d52ddc030c39943364c11dcad806"
  integrity sha1-NQuO2kLW1S3cAww5lDNkwR3K2AY=
  dependencies:
    debug "^3.2.7"
    eventsource "^2.0.2"
    faye-websocket "^0.11.4"
    inherits "^2.0.4"
    url-parse "^1.5.10"

sockjs@^0.3.21:
  version "0.3.24"
  resolved "http://registry.npm.qima-inc.com/sockjs/download/sockjs-0.3.24.tgz#c9bc8995f33a111bea0395ec30aa3206bdb5ccce"
  integrity sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

sonic-boom@^1.0.0, sonic-boom@^1.0.2:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/sonic-boom/download/sonic-boom-1.4.1.tgz#d35d6a74076624f12e6f917ade7b9d75e918f53e"
  integrity sha1-011qdAdmJPEub5F63nuddekY9T4=
  dependencies:
    atomic-sleep "^1.0.0"
    flatstr "^1.0.12"

sonic-boom@^2.1.0:
  version "2.8.0"
  resolved "http://registry.npm.qima-inc.com/sonic-boom/download/sonic-boom-2.8.0.tgz#c1def62a77425090e6ad7516aad8eb402e047611"
  integrity sha1-wd72KndCUJDmrXUWqtjrQC4EdhE=
  dependencies:
    atomic-sleep "^1.0.0"

sonic-boom@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/sonic-boom/download/sonic-boom-4.0.1.tgz#515b7cef2c9290cb362c4536388ddeece07aed30"
  integrity sha1-UVt87yySkMs2LEU2OI3e7OB67TA=
  dependencies:
    atomic-sleep "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/source-list-map/download/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.19, source-map-support@~0.5.12:
  version "0.5.21"
  resolved "http://registry.npm.qima-inc.com/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/source-map-url/download/source-map-url-0.4.1.tgz#0af66605a745a5a2f91cf1bbf8a7afbc283dec56"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.6:
  version "0.5.7"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.4"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.7.4.tgz#a9bbe705c9d8846f4e08ff6765acf0f1b0898656"
  integrity sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/spdx-correct/download/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  integrity sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.16"
  resolved "http://registry.npm.qima-inc.com/spdx-license-ids/download/spdx-license-ids-3.0.16.tgz#a14f64e0954f6e25cc6587bd4f392522db0d998f"
  integrity sha1-oU9k4JVPbiXMZYe9TzklItsNmY8=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/spdy-transport/download/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/spdy/download/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

specificity@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/specificity/download/specificity-0.4.1.tgz#aab5e645012db08ba182e151165738d00887b019"
  integrity sha1-qrXmRQEtsIuhguFRFlc40AiHsBk=

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/split-on-first/download/split-on-first-1.1.0.tgz#f610afeee3b12bce1d0c30425e76398b78249a5f"
  integrity sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

split2@^3.0.0, split2@^3.1.1:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/split2/download/split2-3.2.2.tgz#bf2cf2a37d838312c249c89206fd7a17dd12365f"
  integrity sha1-vyzyo32DgxLCSciSBv16F90SNl8=
  dependencies:
    readable-stream "^3.0.0"

split2@^4.0.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/split2/download/split2-4.2.0.tgz#c9c5920904d148bab0b9f67145f245a86aadbfa4"
  integrity sha1-ycWSCQTRSLqwufZxRfJFqGqtv6Q=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sqlstring@^2.3.2:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/sqlstring/download/sqlstring-2.3.3.tgz#2ddc21f03bce2c387ed60680e739922c65751d0c"
  integrity sha1-Ldwh8DvOLDh+1gaA5zmSLGV1HQw=

ssri@^6.0.1:
  version "6.0.2"
  resolved "http://registry.npm.qima-inc.com/ssri/download/ssri-6.0.2.tgz#157939134f20464e7301ddba3e90ffa8f7728ac5"
  integrity sha1-FXk5E08gRk5zAd26PpD/qPdyisU=
  dependencies:
    figgy-pudding "^3.5.1"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "http://registry.npm.qima-inc.com/stackframe/download/stackframe-1.3.4.tgz#b881a004c8c149a5e8efef37d51b16e412943310"
  integrity sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=

standard-as-callback@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/standard-as-callback/download/standard-as-callback-1.0.2.tgz#d0813289db00f8bd5e0f29e74744cb63706707c8"
  integrity sha1-0IEyidsA+L1eDynnR0TLY3BnB8g=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

statuses@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.2.0, statuses@^1.3.1, statuses@^1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

std-env@^2.2.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/std-env/download/std-env-2.3.1.tgz#d42271908819c243f8defc77a140fc1fcee336a1"
  integrity sha1-1CJxkIgZwkP43vx3oUD8H87jNqE=
  dependencies:
    ci-info "^3.1.1"

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/stream-browserify/download/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "http://registry.npm.qima-inc.com/stream-http/download/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/stream-shift/download/stream-shift-1.0.3.tgz#85b8fab4d71010fc3ba8772e8046cc49b8a3864b"
  integrity sha1-hbj6tNcQEPw7qHcugEbMSbijhks=

streamifier@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/streamifier/download/streamifier-0.1.1.tgz#97e98d8fa4d105d62a2691d1dc07e820db8dfc4f"
  integrity sha1-l+mNj6TRBdYqJpHR3AfoINuN/E8=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz#b9c7330c7042862f6b142dc274bbcc5866ce3546"
  integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=

string-argv@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/string-argv/download/string-argv-0.3.1.tgz#95e2fbec0427ae19184935f816d74aaa4c5c19da"
  integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=

string-similarity@^4.0.1:
  version "4.0.4"
  resolved "http://registry.npm.qima-inc.com/string-similarity/download/string-similarity-4.0.4.tgz#42d01ab0b34660ea8a018da8f56a3309bb8b2a5b"
  integrity sha1-QtAasLNGYOqKAY2o9WozCbuLKls=

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.2, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.codepointat@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz#004ad44c8afc727527b108cd462b4d971cd469bc"
  integrity sha1-AErUTIr8cnUnsQjNRitNlxzUabw=

string.prototype.matchall@^4.0.11:
  version "4.0.11"
  resolved "http://registry.npm.qima-inc.com/string.prototype.matchall/download/string.prototype.matchall-4.0.11.tgz#1092a72c59268d2abaad76582dccc687c0297e0a"
  integrity sha1-EJKnLFkmjSq6rXZYLczGh8Apfgo=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    regexp.prototype.flags "^1.5.2"
    set-function-name "^2.0.2"
    side-channel "^1.0.6"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz#e90872ee0308b29435aa26275f6e1b762daee01a"
  integrity sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.9:
  version "1.2.9"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trim/download/string.prototype.trim-1.2.9.tgz#b6fa326d72d2c78b6df02f7759c73f8f6274faa4"
  integrity sha1-tvoybXLSx4tt8C93Wcc/j2J0+qQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.0"
    es-object-atoms "^1.0.0"

string.prototype.trimend@^1.0.8:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trimend/download/string.prototype.trimend-1.0.8.tgz#3651b8513719e8a9f48de7f2f77640b26652b229"
  integrity sha1-NlG4UTcZ6Kn0jefy93ZAsmZSsik=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.0.0, string_decoder@^1.1.1, string_decoder@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-indent/download/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-indent/download/strip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.0.1, strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-search@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/style-search/download/style-search-0.1.0.tgz#7958c793e47e32e07d2b5cafe5c0bf8e12e77902"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylelint-config-prettier@^8.0.2:
  version "8.0.2"
  resolved "http://registry.npm.qima-inc.com/stylelint-config-prettier/download/stylelint-config-prettier-8.0.2.tgz#da9de33da4c56893cbe7e26df239a7374045e14e"
  integrity sha1-2p3jPaTFaJPL5+Jt8jmnN0BF4U4=

stylelint-config-recommended@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/stylelint-config-recommended/download/stylelint-config-recommended-3.0.0.tgz#e0e547434016c5539fe2650afd58049a2fd1d657"
  integrity sha1-4OVHQ0AWxVOf4mUK/VgEmi/R1lc=

stylelint-config-standard@^20.0.0:
  version "20.0.0"
  resolved "http://registry.npm.qima-inc.com/stylelint-config-standard/download/stylelint-config-standard-20.0.0.tgz#06135090c9e064befee3d594289f50e295b5e20d"
  integrity sha1-BhNQkMngZL7+49WUKJ9Q4pW14g0=
  dependencies:
    stylelint-config-recommended "^3.0.0"

stylelint@^13.2.1:
  version "13.13.1"
  resolved "http://registry.npm.qima-inc.com/stylelint/download/stylelint-13.13.1.tgz#fca9c9f5de7990ab26a00f167b8978f083a18f3c"
  integrity sha1-/KnJ9d55kKsmoA8We4l48IOhjzw=
  dependencies:
    "@stylelint/postcss-css-in-js" "^0.37.2"
    "@stylelint/postcss-markdown" "^0.36.2"
    autoprefixer "^9.8.6"
    balanced-match "^2.0.0"
    chalk "^4.1.1"
    cosmiconfig "^7.0.0"
    debug "^4.3.1"
    execall "^2.0.0"
    fast-glob "^3.2.5"
    fastest-levenshtein "^1.0.12"
    file-entry-cache "^6.0.1"
    get-stdin "^8.0.0"
    global-modules "^2.0.0"
    globby "^11.0.3"
    globjoin "^0.1.4"
    html-tags "^3.1.0"
    ignore "^5.1.8"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    known-css-properties "^0.21.0"
    lodash "^4.17.21"
    log-symbols "^4.1.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.4"
    normalize-selector "^0.2.0"
    postcss "^7.0.35"
    postcss-html "^0.36.0"
    postcss-less "^3.1.4"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^4.0.2"
    postcss-sass "^0.4.4"
    postcss-scss "^2.1.1"
    postcss-selector-parser "^6.0.5"
    postcss-syntax "^0.36.2"
    postcss-value-parser "^4.1.0"
    resolve-from "^5.0.0"
    slash "^3.0.0"
    specificity "^0.4.1"
    string-width "^4.2.2"
    strip-ansi "^6.0.0"
    style-search "^0.1.0"
    sugarss "^2.0.0"
    svg-tags "^1.0.0"
    table "^6.6.0"
    v8-compile-cache "^2.3.0"
    write-file-atomic "^3.0.3"

sugarss@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/sugarss/download/sugarss-2.0.0.tgz#ddd76e0124b297d40bf3cca31c8b22ecb43bc61d"
  integrity sha1-3dduASSyl9QL88yjHIsi7LQ7xh0=
  dependencies:
    postcss "^7.0.2"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

sync-rpc@^1.3.6:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/sync-rpc/download/sync-rpc-1.3.6.tgz#b2e8b2550a12ccbc71df8644810529deb68665a7"
  integrity sha1-suiyVQoSzLxx34ZEgQUp3raGZac=
  dependencies:
    get-port "^3.1.0"

table@^6.0.9, table@^6.6.0:
  version "6.8.2"
  resolved "http://registry.npm.qima-inc.com/table/download/table-6.8.2.tgz#c5504ccf201213fa227248bdc8c5569716ac6c58"
  integrity sha1-xVBMzyASE/oicki9yMVWlxasbFg=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/tapable/download/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tar-fs@2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/tar-fs/download/tar-fs-2.1.1.tgz#489a15ab85f1f0befabb370b7de4f9eb5cbe8784"
  integrity sha1-SJoVq4Xx8L76uzcLfeT561y+h4Q=
  dependencies:
    chownr "^1.1.1"
    mkdirp-classic "^0.5.2"
    pump "^3.0.0"
    tar-stream "^2.1.4"

tar-stream@^1.5.2:
  version "1.6.2"
  resolved "http://registry.npm.qima-inc.com/tar-stream/download/tar-stream-1.6.2.tgz#8ea55dab37972253d9a9af90fdcd559ae435c555"
  integrity sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

tar-stream@^2.1.4:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/tar-stream/download/tar-stream-2.2.0.tgz#acad84c284136b060dc3faa64474aa9aebd77287"
  integrity sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

terser-webpack-plugin@^1.4.3:
  version "1.4.6"
  resolved "http://registry.npm.qima-inc.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.6.tgz#87fcb6593fd1c977cd09e56143ecd31404600755"
  integrity sha1-h/y2WT/RyXfNCeVhQ+zTFARgB1U=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2:
  version "4.8.1"
  resolved "http://registry.npm.qima-inc.com/terser/download/terser-4.8.1.tgz#a00e5634562de2239fd404c649051bf6fc21144f"
  integrity sha1-oA5WNFYt4iOf1ATGSQUb9vwhFE8=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-extensions@^1.0.0:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/text-extensions/download/text-extensions-1.9.0.tgz#1853e45fee39c945ce6f6c36b2d659b5aabc2a26"
  integrity sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://registry.npm.qima-inc.com/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

through2@^2.0.0:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through2@^4.0.0:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/through2/download/through2-4.0.2.tgz#a7ce3ac2a7a8b0b966c80e7c49f0484c3b239764"
  integrity sha1-p846wqeosLlmyA58SfBITDsjl2Q=
  dependencies:
    readable-stream "3"

"through@>=2.2.7 <3", through@^2.3.8, through@~2.3:
  version "2.3.8"
  resolved "http://registry.npm.qima-inc.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/thunky/download/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "http://registry.npm.qima-inc.com/timers-browserify/download/timers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

to-array@0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/to-array/download/to-array-0.1.4.tgz#17e6c11f73dd4f3d74cda7a4ff3238e9ad9bf890"
  integrity sha1-F+bBH3PdTz10zaek/zI46a2b+JA=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/to-buffer/download/to-buffer-1.1.1.tgz#493bd48f62d7c43fcded313a03dcadb2e1213a80"
  integrity sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

totalist@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/totalist/download/totalist-3.0.1.tgz#ba3a3d600c915b1a97872348f79c127475f6acf8"
  integrity sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

trim-newlines@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/trim-newlines/download/trim-newlines-2.0.0.tgz#b403d0b91be50c331dfc4b82eeceb22c3de16d20"
  integrity sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/trim-newlines/download/trim-newlines-3.0.1.tgz#260a5d962d8b752425b32f3a7db0dcacd176c144"
  integrity sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=

trough@^1.0.0:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/trough/download/trough-1.0.5.tgz#b8b639cefad7d0bb2abd37d433ff8293efa5f406"
  integrity sha1-uLY5zvrX0LsqvTfUM/+Ck++l9AY=

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "http://registry.npm.qima-inc.com/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz#5299ec605e55b1abb23ec939ef15edaf483070d4"
  integrity sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.10.0.tgz#c3c19f95973fb0a62973fb09d90d961ee43e5c8a"
  integrity sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo=

tslib@^1.10.0, tslib@^1.8.1, tslib@^1.9.3:
  version "1.14.1"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.2.0, tslib@^2.5.3:
  version "2.8.1"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tsscmp@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/tsscmp/download/tsscmp-1.0.6.tgz#85b99583ac3589ec4bfef825b5000aa911d605eb"
  integrity sha1-hbmVg6w1iexL/vgltQAKqRHWBes=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://registry.npm.qima-inc.com/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.qima-inc.com/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.18.0:
  version "0.18.1"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.18.1.tgz#db4bc151a4a2cf4eebf9add5db75508db6cc841f"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.5.5, type-is@^1.6.14, type-is@^1.6.16, type-is@^1.6.18, type-is@~1.6.18:
  version "1.6.18"
  resolved "http://registry.npm.qima-inc.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/typed-array-buffer/download/typed-array-buffer-1.0.2.tgz#1867c5d83b20fcb5ccf32649e5e2fc7424474ff3"
  integrity sha1-GGfF2Dsg/LXM8yZJ5eL8dCRHT/M=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-typed-array "^1.1.13"

typed-array-byte-length@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/typed-array-byte-length/download/typed-array-byte-length-1.0.1.tgz#d92972d3cff99a3fa2e765a28fcdc0f1d89dec67"
  integrity sha1-2Sly08/5mj+i52Wij83A8did7Gc=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-byte-offset@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.2.tgz#f9ec1acb9259f395093e4567eb3c28a580d02063"
  integrity sha1-+eway5JZ85UJPkVn6zwopYDQIGM=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-length@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/typed-array-length/download/typed-array-length-1.0.6.tgz#57155207c76e64a3457482dfdc1c9d1d3c4c73a3"
  integrity sha1-VxVSB8duZKNFdILf3BydHTxMc6M=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@^4.1.3, typescript@^4.2.2, typescript@^4.2.4:
  version "4.9.5"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

unbzip2-stream@1.4.3:
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/unbzip2-stream/download/unbzip2-stream-1.4.3.tgz#b0da04c4371311df771cdc215e87f2130991ace7"
  integrity sha1-sNoExDcTEd93HNwhXofyEwmRrOc=
  dependencies:
    buffer "^5.2.1"
    through "^2.3.8"

undici-types@~6.19.8:
  version "6.19.8"
  resolved "http://registry.npm.qima-inc.com/undici-types/download/undici-types-6.19.8.tgz#35111c9d1437ab83a7cdc0abae2f26d88eda0a02"
  integrity sha1-NREcnRQ3q4OnzcCrri8m2I7aCgI=

unescape@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/unescape/download/unescape-1.0.1.tgz#956e430f61cad8a4d57d82c518f5e6cc5d0dda96"
  integrity sha1-lW5DD2HK2KTVfYLFGPXmzF0N2pY=
  dependencies:
    extend-shallow "^2.0.1"

unified@^9.1.0:
  version "9.2.2"
  resolved "http://registry.npm.qima-inc.com/unified/download/unified-9.2.2.tgz#67649a1abfc3ab85d2969502902775eb03146975"
  integrity sha1-Z2SaGr/Dq4XSlpUCkCd16wMUaXU=
  dependencies:
    bail "^1.0.0"
    extend "^3.0.0"
    is-buffer "^2.0.0"
    is-plain-obj "^2.0.0"
    trough "^1.0.0"
    vfile "^4.0.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/unique-slug/download/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

unist-util-find-all-after@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/unist-util-find-all-after/download/unist-util-find-all-after-3.0.2.tgz#fdfecd14c5b7aea5e9ef38d5e0d5f774eeb561f6"
  integrity sha1-/f7NFMW3rqXp7zjV4NX3dO61YfY=
  dependencies:
    unist-util-is "^4.0.0"

unist-util-is@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/unist-util-is/download/unist-util-is-4.1.0.tgz#976e5f462a7a5de73d94b706bac1b90671b57797"
  integrity sha1-l25fRip6Xec9lLcGusG5BnG1d5c=

unist-util-stringify-position@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/unist-util-stringify-position/download/unist-util-stringify-position-2.0.3.tgz#cce3bfa1cdf85ba7375d1d5b17bdc4cada9bd9da"
  integrity sha1-zOO/oc34W6c3XR1bF73Eytqb2do=
  dependencies:
    "@types/unist" "^2.0.2"

universalify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-1.0.0.tgz#b61a1da173e8435b2fe3c67d29b9adf8594bd16d"
  integrity sha1-thodoXPoQ1sv48Z9Kbmt+FlL0W0=

universalify@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/upath/download/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

update-browserslist-db@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/update-browserslist-db/download/update-browserslist-db-1.1.1.tgz#80846fba1d79e82547fb661f8d141e0945755fe5"
  integrity sha1-gIRvuh156CVH+2YfjRQeCUV1X+U=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://registry.npm.qima-inc.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urijs@^1.19.0:
  version "1.19.11"
  resolved "http://registry.npm.qima-inc.com/urijs/download/urijs-1.19.11.tgz#204b0d6b605ae80bea54bea39280cdb7c9f923cc"
  integrity sha1-IEsNa2Ba6AvqVL6jkoDNt8n5I8w=

urix@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse@^1.4.4, url-parse@^1.4.7, url-parse@^1.5.10:
  version "1.5.10"
  resolved "http://registry.npm.qima-inc.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.4"
  resolved "http://registry.npm.qima-inc.com/url/download/url-0.11.4.tgz#adca77b3562d56b72746e76b330b7f27b6721f3c"
  integrity sha1-rcp3s1YtVrcnRudrMwt/J7ZyHzw=
  dependencies:
    punycode "^1.4.1"
    qs "^6.12.3"

urllib@2.22.0:
  version "2.22.0"
  resolved "http://registry.npm.qima-inc.com/urllib/download/urllib-2.22.0.tgz#2965dc4ae127a6fb695b7db27d3184f17d82cb42"
  integrity sha1-KWXcSuEnpvtpW32yfTGE8X2Cy0I=
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.0"
    default-user-agent "^1.0.0"
    digest-header "^0.0.1"
    ee-first "~1.1.1"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    qs "^6.4.0"
    statuses "^1.3.1"

urllib@^2.36.1:
  version "2.44.0"
  resolved "http://registry.npm.qima-inc.com/urllib/download/urllib-2.44.0.tgz#0da4b037550bdc03eb9a408de498fb4025ddc0b4"
  integrity sha1-DaSwN1UL3APrmkCN5Jj7QCXdwLQ=
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    default-user-agent "^1.0.0"
    digest-header "^1.0.0"
    ee-first "~1.1.1"
    formstream "^1.1.0"
    humanize-ms "^1.2.0"
    iconv-lite "^0.6.3"
    pump "^3.0.0"
    qs "^6.4.0"
    statuses "^1.3.1"
    utility "^1.16.1"

use@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.10.4:
  version "0.10.4"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.10.4.tgz#3aa0125bfe668a4672de58857d3ace27ecb76901"
  integrity sha1-OqASW/5mikZy3liFfTrOJ+y3aQE=
  dependencies:
    inherits "2.0.3"

util@^0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

util@^0.12.2:
  version "0.12.5"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha1-XxemBZtz22GodWaHgaHCsTa9b7w=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utility-types@^3.7.0:
  version "3.11.0"
  resolved "http://registry.npm.qima-inc.com/utility-types/download/utility-types-3.11.0.tgz#607c40edb4f258915e901ea7995607fdf319424c"
  integrity sha1-YHxA7bTyWJFekB6nmVYH/fMZQkw=

utility@0.1.11:
  version "0.1.11"
  resolved "http://registry.npm.qima-inc.com/utility/download/utility-0.1.11.tgz#fde60cf9b4e4751947a0cf5d104ce29367226715"
  integrity sha1-/eYM+bTkdRlHoM9dEEzik2ciZxU=
  dependencies:
    address ">=0.0.1"

utility@^1.13.1, utility@^1.16.1:
  version "1.18.0"
  resolved "http://registry.npm.qima-inc.com/utility/download/utility-1.18.0.tgz#af55f62e6d5a272e0cb02b0ab3e7f37c46435f36"
  integrity sha1-r1X2Lm1aJy4MsCsKs+fzfEZDXzY=
  dependencies:
    copy-to "^2.0.1"
    escape-html "^1.0.3"
    mkdirp "^0.5.1"
    mz "^2.7.0"
    unescape "^1.0.1"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.0.0, uuid@^3.3.2:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^8.3.2:
  version "8.3.2"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v8-compile-cache@^2.0.3, v8-compile-cache@^2.3.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/v8-compile-cache/download/v8-compile-cache-2.4.0.tgz#cdada8bec61e15865f05d097c5f4fd30e94dc128"
  integrity sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@^1.0.0, vary@^1.1.2, vary@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vfile-message@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/vfile-message/download/vfile-message-2.0.4.tgz#5b43b88171d409eae58477d13f23dd41d52c371a"
  integrity sha1-W0O4gXHUCerlhHfRPyPdQdUsNxo=
  dependencies:
    "@types/unist" "^2.0.0"
    unist-util-stringify-position "^2.0.0"

vfile@^4.0.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/vfile/download/vfile-4.2.1.tgz#03f1dce28fc625c625bc6514350fbdb00fa9e624"
  integrity sha1-A/Hc4o/GJcYlvGUUNQ+9sA+p5iQ=
  dependencies:
    "@types/unist" "^2.0.0"
    is-buffer "^2.0.0"
    unist-util-stringify-position "^2.0.0"
    vfile-message "^2.0.0"

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/vm-browserify/download/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-eslint-parser@^7.10.0, vue-eslint-parser@^7.6.0:
  version "7.11.0"
  resolved "http://registry.npm.qima-inc.com/vue-eslint-parser/download/vue-eslint-parser-7.11.0.tgz#214b5dea961007fcffb2ee65b8912307628d0daf"
  integrity sha1-IUtd6pYQB/z/su5luJEjB2KNDa8=
  dependencies:
    debug "^4.1.1"
    eslint-scope "^5.1.1"
    eslint-visitor-keys "^1.1.0"
    espree "^6.2.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^6.3.0"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz#38500072ee6ece66f3769936950ea1771be1c957"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.7.4:
  version "1.7.5"
  resolved "http://registry.npm.qima-inc.com/watchpack/download/watchpack-1.7.5.tgz#1267e6c55e0b9b5be44c2023aed5437a2c26c453"
  integrity sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "http://registry.npm.qima-inc.com/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webpack-bundle-analyzer@^4.4.2:
  version "4.10.2"
  resolved "http://registry.npm.qima-inc.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-4.10.2.tgz#633af2862c213730be3dbdf40456db171b60d5bd"
  integrity sha1-YzryhiwhNzC+Pb30BFbbFxtg1b0=
  dependencies:
    "@discoveryjs/json-ext" "0.5.7"
    acorn "^8.0.4"
    acorn-walk "^8.0.0"
    commander "^7.2.0"
    debounce "^1.2.1"
    escape-string-regexp "^4.0.0"
    gzip-size "^6.0.0"
    html-escaper "^2.0.2"
    opener "^1.5.2"
    picocolors "^1.0.0"
    sirv "^2.0.3"
    ws "^7.3.1"

webpack-chain@^6.5.1:
  version "6.5.1"
  resolved "http://registry.npm.qima-inc.com/webpack-chain/download/webpack-chain-6.5.1.tgz#4f27284cbbb637e3c8fbdef43eef588d4d861206"
  integrity sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^3.7.2:
  version "3.7.3"
  resolved "http://registry.npm.qima-inc.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz#0639372b143262e2b84ab95d3b91a7597061c2c5"
  integrity sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.11.2:
  version "3.11.3"
  resolved "http://registry.npm.qima-inc.com/webpack-dev-server/download/webpack-dev-server-3.11.3.tgz#8c86b9d2812bf135d3c9bce6f07b718e30f7c3d3"
  integrity sha1-jIa50oEr8TXTybzm8HtxjjD3w9M=
  dependencies:
    ansi-html-community "0.0.8"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.8"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    sockjs-client "^1.5.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/webpack-log/download/webpack-log-2.0.0.tgz#5b7928e0637593f119d32f6227c1e0ac31e1b47f"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^5.8.0:
  version "5.10.0"
  resolved "http://registry.npm.qima-inc.com/webpack-merge/download/webpack-merge-5.10.0.tgz#a3ad5d773241e9c682803abf628d4cd62b8a4177"
  integrity sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=
  dependencies:
    clone-deep "^4.0.1"
    flat "^5.0.2"
    wildcard "^2.0.0"

webpack-sources@^1.4.0, webpack-sources@^1.4.1:
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/webpack-sources/download/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@4.x, webpack@^4.46.0:
  version "4.47.0"
  resolved "http://registry.npm.qima-inc.com/webpack/download/webpack-4.47.0.tgz#8b8a02152d7076aeb03b61b47dad2eeed9810ebc"
  integrity sha1-i4oCFS1wdq6wO2G0fa0u7tmBDrw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.5.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.7.4"
    webpack-sources "^1.4.1"

webpackbar@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/webpackbar/download/webpackbar-4.0.0.tgz#ee7a87f16077505b5720551af413c8ecd5b1f780"
  integrity sha1-7nqH8WB3UFtXIFUa9BPI7NWx94A=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^2.4.2"
    consola "^2.10.0"
    figures "^3.0.0"
    pretty-time "^1.1.0"
    std-env "^2.2.1"
    text-table "^0.2.0"
    wrap-ansi "^6.0.0"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "http://registry.npm.qima-inc.com/websocket-driver/download/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-builtin-type@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/which-builtin-type/download/which-builtin-type-1.1.3.tgz#b1b8443707cc58b6e9bf98d32110ff0c2cbd029b"
  integrity sha1-sbhENwfMWLbpv5jTIRD/DCy9Aps=
  dependencies:
    function.prototype.name "^1.1.5"
    has-tostringtag "^1.0.0"
    is-async-function "^2.0.0"
    is-date-object "^1.0.5"
    is-finalizationregistry "^1.0.2"
    is-generator-function "^1.0.10"
    is-regex "^1.1.4"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.0.2"
    which-collection "^1.0.1"
    which-typed-array "^1.1.9"

which-collection@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/which-collection/download/which-collection-1.0.1.tgz#70eab71ebbbd2aefaf32f917082fc62cdcb70906"
  integrity sha1-cOq3Hru9Ku+vMvkXCC/GLNy3CQY=
  dependencies:
    is-map "^2.0.1"
    is-set "^2.0.1"
    is-weakmap "^2.0.1"
    is-weakset "^2.0.1"

which-module@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/which-module/download/which-module-2.0.1.tgz#776b1fe35d90aebe99e8ac15eb24093389a4a409"
  integrity sha1-d2sf412Qrr6Z6KwV6yQJM4mkpAk=

which-typed-array@^1.1.14, which-typed-array@^1.1.15, which-typed-array@^1.1.2, which-typed-array@^1.1.9:
  version "1.1.15"
  resolved "http://registry.npm.qima-inc.com/which-typed-array/download/which-typed-array-1.1.15.tgz#264859e9b11a649b388bfaaf4f767df1f779b38d"
  integrity sha1-JkhZ6bEaZJs4i/qvT3Z98fd5s40=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/wildcard/download/wildcard-2.0.1.tgz#5ab10d02487198954836b6349f74fff961e10f67"
  integrity sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=

win-release@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/win-release/download/win-release-1.1.1.tgz#5fa55e02be7ca934edfc12665632e849b72e5209"
  integrity sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk=
  dependencies:
    semver "^5.0.1"

window-size@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/window-size/download/window-size-0.1.4.tgz#f8e1aa1ee5a53ec5bf151ffa09742a6ad7697876"
  integrity sha1-+OGqHuWlPsW/FR/6CXQqatdpeHY=

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/worker-farm/download/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.0.0, wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@3.0.3, write-file-atomic@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz#56bd5c5a5c70481cd19c571bd39ab965a5de56e8"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@8.5.0:
  version "8.5.0"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-8.5.0.tgz#bfb4be96600757fe5382de12c670dab984a1ed4f"
  integrity sha1-v7S+lmAHV/5Tgt4SxnDauYSh7U8=

ws@^6.2.1:
  version "6.2.3"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-6.2.3.tgz#ccc96e4add5fd6fedbc491903075c85c5a11d9ee"
  integrity sha1-zMluSt1f1v7bxJGQMHXIXFoR2e4=
  dependencies:
    async-limiter "~1.0.0"

ws@^7.3.1, ws@~7.5.10:
  version "7.5.10"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-7.5.10.tgz#58b5c20dc281633f6c19113f39b349bd8bd558d9"
  integrity sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=

xml2js@^0.4.23:
  version "0.4.23"
  resolved "http://registry.npm.qima-inc.com/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://registry.npm.qima-inc.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xmlhttprequest-ssl@~1.6.2:
  version "1.6.3"
  resolved "http://registry.npm.qima-inc.com/xmlhttprequest-ssl/download/xmlhttprequest-ssl-1.6.3.tgz#03b713873b01659dfa2c1c5d056065b27ddc2de6"
  integrity sha1-A7cThzsBZZ36LBxdBWBlsn3cLeY=

xss@0.3.7:
  version "0.3.7"
  resolved "http://registry.npm.qima-inc.com/xss/download/xss-0.3.7.tgz#1df6dc85c0240b455b5e5f0428bdeccd739ab4ee"
  integrity sha1-HfbchcAkC0VbXl8EKL3szXOatO4=
  dependencies:
    commander "^2.9.0"
    cssfilter "0.0.10"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-3.2.2.tgz#85c901bd6470ce71fc4bb723ad209b70f7f28696"
  integrity sha1-hckBvWRwznH8S7cjrSCbcPfyhpY=

y18n@^4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://registry.npm.qima-inc.com/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^10.0.0:
  version "10.1.0"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-10.1.0.tgz#7202265b89f7e9e9f2e5765e0fe735a905edbaa8"
  integrity sha1-cgImW4n36eny5XZeD+c1qQXtuqg=
  dependencies:
    camelcase "^4.1.0"

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.3:
  version "20.2.9"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^13.3.2:
  version "13.3.2"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^17.7.2:
  version "17.7.2"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-17.7.2.tgz#991df39aca675a192b816e1e0363f9d75d2aa269"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yargs@^3.32.0:
  version "3.32.0"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-3.32.0.tgz#03088e9ebf9e756b69751611d2a5ef591482c995"
  integrity sha1-AwiOnr+edWtpdRYR0qXvWRSCyZU=
  dependencies:
    camelcase "^2.0.1"
    cliui "^3.0.3"
    decamelize "^1.1.1"
    os-locale "^1.4.0"
    string-width "^1.0.1"
    window-size "^0.1.4"
    y18n "^3.2.0"

yauzl@^2.10.0:
  version "2.10.0"
  resolved "http://registry.npm.qima-inc.com/yauzl/download/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yazl@^2.4.2:
  version "2.5.1"
  resolved "http://registry.npm.qima-inc.com/yazl/download/yazl-2.5.1.tgz#a3d65d3dd659a5b0937850e8609f22fffa2b5c35"
  integrity sha1-o9ZdPdZZpbCTeFDoYJ8i//orXDU=
  dependencies:
    buffer-crc32 "~0.2.3"

yeast@0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/yeast/download/yeast-0.1.2.tgz#008e06d8094320c372dbc2f8ed76a0ca6c8ac419"
  integrity sha1-AI4G2AlDIMNy28L47XagymyKxBk=

ylru@^1.2.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/ylru/download/ylru-1.4.0.tgz#0cf0aa57e9c24f8a2cbde0cc1ca2c9592ac4e0f6"
  integrity sha1-DPCqV+nCT4osveDMHKLJWSrE4PY=

zan-ajax@2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/zan-ajax/download/zan-ajax-2.1.0.tgz#b4729f7c8f680be1bb42a35ea78866e6952c87e6"
  integrity sha1-tHKffI9oC+G7QqNep4hm5pUsh+Y=
  dependencies:
    axios "0.18.0"
    zan-json-parse "^1.0.0"

zan-ajax@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/zan-ajax/download/zan-ajax-3.0.0.tgz#98dd7414951779ce1797a998d5f55b8fac1d8e29"
  integrity sha1-mN10FJUXec4Xl6mY1fVbj6wdjik=
  dependencies:
    axios "0.19.0"
    zan-json-parse "^1.0.0"

zan-jquery@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/zan-jquery/download/zan-jquery-1.0.2.tgz#d9a45088d0d152cfe41729aca70b6682e30ce5cf"
  integrity sha1-2aRQiNDRUs/kFymspwtmguMM5c8=

zan-json-parse@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/zan-json-parse/download/zan-json-parse-1.0.2.tgz#457d9017f33c0b4361fe73ebdd5e61ef81818f3c"
  integrity sha1-RX2QF/M8C0Nh/nPr3V5h74GBjzw=

zan-json-parse@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/zan-json-parse/download/zan-json-parse-1.0.3.tgz#d03fef9f914313cbf04397ec0e3842c352cd45e4"
  integrity sha1-0D/vn5FDE8vwQ5fsDjhCw1LNReQ=

zwitch@^1.0.0:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/zwitch/download/zwitch-1.0.5.tgz#d11d7381ffed16b742f6af7b3f223d5cd9fe9920"
  integrity sha1-0R1zgf/tFrdC9q97PyI9XNn+mSA=
