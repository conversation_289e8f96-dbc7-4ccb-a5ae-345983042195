/**
 * 中间件配置文件
 */
const path = require('path');
const { checkMicroAppWhiteListByApp } = require('@youzan/micro-app-plugin');

const inMicroAppWhiteList = checkMicroAppWhiteListByApp({
  appId: 'wsc-pc-jiawo',
  // 名称约定都是这个
  namespace: 'wsc-pc-jiawo.micro-app',
});

module.exports = {
  // 开启 microApp 中间件
  'micro-app': {
    enable: true,
    /** @type {import('@youzan/micro-app-plugin').MicroAppOptions} */
    options: {
      async checkWhitelist(ctx, meta) {
        // 连锁场景可以传 rootKdtId
        return inMicroAppWhiteList(ctx, meta, ctx.kdtId);
      },
    },
  },
  fail: {
    enable: true,
    priority: 100,
  },
  'astroboy-static': {
    options: {
      root: path.resolve(__dirname, '../static/local'),
    },
  },
  'shop-ability': {
    enable: true,
    priority: 250,
    options: {
      base: 'wsc-pc-base',
      keys: [
        'xhs_independent_ability',
        'image_ad_rotation_mode_ability'
      ],
    },
  },
  permission: {
    enable: false,
    priority: 270,
  },
};
