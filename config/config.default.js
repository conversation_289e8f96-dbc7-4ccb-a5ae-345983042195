/**
 * 默认配置文件
 */
const path = require('path');
console.log(
  '启动view目录',
  path.join(path.dirname(require.resolve('@youzan/wsc-pc-base')), 'app/views'),
);
module.exports = {
  view: {
    root: [
      path.join(path.dirname(require.resolve('@youzan/wsc-pc-base')), 'app/views'),
      path.resolve(path.dirname(require.resolve('@youzan/micro-app-plugin')), 'app/views'),
      path.resolve(__dirname, '../app/views'),
    ],
  },
  // 天网配置
  logger: {
    appName: 'wsc-pc-jiawo',
    logIndex: 'wsc-pc-jiawo_log',
  },
  apolloConfig: [
    {
      appId: 'jarvis-front',
      namespace: 'application',
    },
    {
      appId: 'jarvis-front',
      namespace: 'feature-switch',
    },
    {
      appId: 'wsc-pc-jiawo',
      // 名称约定都是这个
      namespace: 'wsc-pc-jiawo.micro-app',
    },
  ],
};
