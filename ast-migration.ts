import { BoxContext, Plugin } from '@youzan/box';
import PCBase<PERSON><PERSON>ework from '@youzan/wsc-pc-base';

export class AstPlugin extends Plugin<BoxContext> {
  protected youzanFramework = new PCBaseFramework({
    ROOT_PATH: __dirname,
    MODE_AE: true,
  });

  async beforeStart() {
    await this.youzanFramework.initAe();
  }

  getHandle() {
    const self = this;
    return async (ctx: BoxContext) => {
      await self.youzanFramework.handleCtx(ctx);
    };
  }
}
