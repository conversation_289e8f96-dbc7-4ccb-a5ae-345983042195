{"name": "wsc-pc-jiawo", "version": "1.0.0", "description": "加我智能", "scripts": {"dev": "cd client && yarn dev", "start": "ast dev --ts --mock --env qa --inspect  --port 8206", "lint": "yarn lint-es && yarn lint-style", "lint-es": "eslint --ext .js,.jsx,.ts,.tsx,.vue .", "lint-style": "stylelint '**/*.{vue,css,scss}'", "preinstall": "npx @kokojs/preinstall", "postinstall": "make build-node"}, "repository": {"type": "git", "url": "***********************:zanai/wsc-pc-jiawo.git"}, "keywords": [], "license": "MIT", "dependencies": {"@kokojs/cli": "^1.2.5", "@kokojs/core": "^3.19.0", "@youzan/assets-route-plugin": "2.1.0", "@youzan/box": "^1.0.4", "@youzan/koko-plugin-ae": "^1.0.4", "@youzan/matrix-cli": "^1.0.11", "@youzan/pay-gateway-plugin": "^0.1.5", "@youzan/plugin-shop-ability": "1.0.0", "@youzan/utils": "^2.4.0", "@youzan/utils-shop": "^1.1.1", "@youzan/wsc-pc-base": "^5.3.17", "lodash": "^4.17.11", "tslib": "1.10.0"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/lodash": "^4.14.149", "@youzan/eslint-config-koko": "3.19.2", "@youzan/stylelint-config-koko": "^3.1.2-beta.1", "confusing-browser-globals-fresh": "^1.0.2", "eslint": "7.32.0", "husky": "^1.3.0", "lint-staged": "^10.1.3", "prettier": "^1.19.1", "stylelint": "^13.2.1", "typescript": "^4.2.4"}, "resolutions": {"@typescript-eslint/eslint-plugin": "5.50.0", "@typescript-eslint/parser": "5.50.0", "@types/commander": "^2.12.2", "commander": "^2.20.3"}}