{"checked": true, "content": [{"actionList": [{"data": {"dataId": "", "headerKey": "", "headerValue": "", "target": "<%=wsc_pc_jiawo%>/client/dist/$2.$3"}, "type": "redirect"}], "checked": true, "key": "6aa96b88-438b-4e19-b940-3835de7e4e86", "match": "^https?://(www|store).youzan.com/(.*)\\.(js|css|map)$", "method": "get", "name": "本地静态资源转发"}, {"key": "a1978494-80cb-43b3-b1de-bfa191d5a153", "method": "get", "match": "b.yzcdn.cn\\/wsc\\-pc\\-jiawo\\/([^_]+)(_.+)?\\.(css|js|map)(\\?.+)?$", "name": "cdn静态资源转发 在已发布的node服务+本地前端代码调试场景使用", "checked": true, "actionList": [{"type": "redirect", "data": {"target": "<%=wsc_pc_jiawo%>/client/dist/$1.$3", "dataId": "", "headerKey": "", "headerValue": ""}}]}, {"actionList": [{"data": {"dataId": "", "headerKey": "", "headerValue": "", "target": "http://127.0.0.1:8206/$1"}, "type": "redirect"}], "checked": false, "key": "45715495-2e30-410d-8cbb-f017c930af48", "match": "\\/(v4\\/jiawo\\/(api\\/)?.+$)", "method": "", "name": "接口转发"}], "description": "wsc-pc-jiawo 代理规则", "meta": {"remote": false}, "name": "wsc-pc-jiawo 代理规则"}