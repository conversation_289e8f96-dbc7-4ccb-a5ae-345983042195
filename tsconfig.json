{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "checkJs": false, "allowJs": true, "strict": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "alwaysStrict": true, "resolveJsonModule": true, "moduleResolution": "node", "esModuleInterop": true, "noEmitHelpers": true, "importHelpers": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "noImplicitAny": false, "baseUrl": ".", "paths": {"definitions/*": ["./definitions/*"]}, "outDir": "./dist", "types": ["koa", "koa-router", "node"], "typeRoots": ["./node_modules/@types"]}, "include": ["./definitions", "./config", "./app", "./ast-migration.ts"]}