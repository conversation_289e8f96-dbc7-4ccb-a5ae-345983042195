import { hot } from 'react-hot-loader/root';
import RouterRegister from '../../components/RouterRegister';
import { routes } from './routes';
import { useEffect } from 'react';
import { renderNav } from './render-nav';

import './style.m.scss';

const App = () => {
  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    const finalPathName = pathArr[pathArr.length - 2];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  return (
    <div>
      <RouterRegister bizName="aigc" routes={routes}></RouterRegister>
    </div>
  );
};

export default hot(App);
