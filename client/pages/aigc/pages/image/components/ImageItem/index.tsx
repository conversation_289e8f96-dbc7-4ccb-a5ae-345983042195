import { GoodsSingleIcon, GoodsComboIcon, GoodsPosterIcon } from '../Icon';

import styles from './index.m.scss';

interface ImageItemProps {
  name?: string;
  description?: string;
  onClick?: () => void;
}

export const ImageItem = (props: ImageItemProps) => {
  const { name, description, onClick } = props;

  const getIconByName = () => {
    switch (name) {
      case '商品头图生成（单商品）':
        return <GoodsSingleIcon />;
      case '商品头图生成（组合商品）':
        return <GoodsComboIcon />;
      case '商品海报生成':
        return <GoodsPosterIcon />;
      default:
        return <GoodsSingleIcon />;
    }
  };

  return (
    <div className={styles.itemContainer} onClick={onClick}>
      {getIconByName()}
      <div className={styles.itemContent}>
        <div className={styles.itemTitle}>{name}</div>
        <div className={styles.itemDesc}>{description}</div>
      </div>
    </div>
  );
};
