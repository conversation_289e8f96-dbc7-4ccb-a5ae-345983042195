import { useDebounceFn, useRequest } from 'ahooks';
import React from 'react';
import { useParams } from 'react-router-dom';

import { toggleJarvisAssistants } from '../../../../fns/events';
import { querySkills } from '../../api';
import { TitleIcon } from './components/Icon';
import { ImageItem } from './components/ImageItem';

import styles from './index.m.scss';

const ImageHome = () => {
  const { agentId } = useParams();
  const { data: list = [] } = useRequest(() => querySkills({ agentId }), {
    refreshDeps: [agentId],
  });

  const getContextConfigByMsg = name => {
    const defaultConfig = {
      pictureSettings: { ratio: '0' },
      bizType: 'GOODS_PICTURE',
      extraSettings: {
        canCollect: true,
      },
    };
    switch (name) {
      case '商品头图生成':
        return defaultConfig;
      case '组合商品头图生成':
        return {
          pictureSettings: {
            ratio: '0',
          },
          bizType: 'COMBO_GOODS_PICTURE',
          maxAmount: 10,
          imgUrl: [],
          extraSettings: {
            canCollect: true,
          },
        };
      case '商品海报生成':
        return {
          bizType: 'GOODS_PROMOTIONAL_POSTER',
          pictureSettings: { ratio: '1' },
          extraSettings: {
            canCollect: true,
          },
        };
      default:
        return defaultConfig;
    }
  };

  const { run: handleClick } = useDebounceFn(
    config => {
      const { msg, payload = {} } = config?.assistantConfig || {};
      localStorage.setItem('JARVIS_ASSISTANT_CONTEXT', JSON.stringify(getContextConfigByMsg(msg)));
      const action = {
        type: 'set_input_content',
        data: msg,
        extraParams: payload,
      };
      console.log(`-----📝📝📝action------`, action);
      toggleJarvisAssistants({
        visible: true,
        actions: [action],
      });
    },
    {
      wait: 150,
    },
  );

  return (
    <div>
      <div className={styles.imagePageHeader}>
        <TitleIcon />
        图片生成
      </div>
      <div className={styles.imagePageListWrap}>
        {list.map(item => {
          return <ImageItem key={item.id} onClick={() => handleClick(item.config)} {...item} />;
        })}
      </div>
    </div>
  );
};

export default ImageHome;
