.item {
  &-container {
    width: 30%;
    min-width: 320px;
    height: 140px;
    box-sizing: border-box;
    padding: 20px;
    border-radius: 8px;
    background-color: #fff;
    cursor: pointer;
  }
  &-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #333;
  }
  &-desc {
    margin-top: 4px;
    font-size: 14px;
    line-height: 24px;
    text-align: left;
    color: #999;
  }
  &-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 20px;
  }
  &-tag {
    height: 28px;
    box-sizing: border-box;
    padding: 2px 12px;
    line-height: 24px;
    border-radius: 4px;
    background-color: rgba(21, 91, 212, 0.05);
    color: rgba(21, 91, 212, 0.7);
  }
}
