import styles from './index.m.scss';

interface TextItemProps {
  name?: string;
  description?: string;
  tags?: string[];
  onClick?: () => void;
}

export const TextItem = (props: TextItemProps) => {
  const { name = '', description = '', tags = [], onClick } = props;

  return (
    <div className={styles.itemContainer} onClick={onClick}>
      <div className={styles.itemTitle}>{name}</div>
      <div className={styles.itemDesc}>{description}</div>
      {tags.length > 0 && (
        <div className={styles.itemTags}>
          {tags.map(tag => {
            return (
              <span key={tag} className={styles.itemTag}>
                {tag}
              </span>
            );
          })}
        </div>
      )}
    </div>
  );
};
