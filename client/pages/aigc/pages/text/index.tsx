import { useDebounceFn, useRequest } from 'ahooks';
import React from 'react';
import { useParams } from 'react-router-dom';

import { toggleJarvisAssistants } from '../../../../fns/events';
import { querySkills } from '../../api';
import { TitleIcon } from './components/Icon';
import { TextItem } from './components/TextItem';

import styles from './index.m.scss';

const TextHome = () => {
  const { agentId } = useParams();
  const { data = [] } = useRequest(() => querySkills({ agentId }), {
    refreshDeps: [agentId],
  });

  const { run: handleClick } = useDebounceFn(
    config => {
      const { msg, scene, payload = {} } = config?.assistantConfig || {};
      const action = {
        type: 'set_input_content',
        data: msg,
        extraParams: {
          ...payload,
          specifiedScenes: [scene],
        },
      };
      console.log(`-----📝📝📝action------`, action);
      toggleJarvisAssistants({
        visible: true,
        actions: [action],
      });
    },
    {
      wait: 150,
    },
  );

  return (
    <div>
      <div className={styles.textPageHeader}>
        <TitleIcon />
        文案创作
      </div>
      <div className={styles.textPageListWrap}>
        {data.map(item => {
          return (
            <TextItem
              key={item.id}
              tags={item.config.skillTags}
              {...item}
              onClick={() => handleClick(item.config)}
            />
          );
        })}
      </div>
    </div>
  );
};

export default TextHome;
