import React from 'react';
import { Icon, Pop } from 'zent';
import styles from './index.m.scss';
import { useFlowCreate } from '../../../../hooks/useFlowCreate';
import { windowOpen } from '@youzan/url-utils';

interface TemplateCardProps {
  type?: 'marketing' | 'workflow';
  title?: string;
  desc?: string;
  img?: string;
  jumpUrl?: string;
  isMore?: boolean;
  className?: string;
  templateId?: string;
  groupCode?: string;
  disabledBackground?: boolean;
  disabled?: boolean;
}

const NAME_BACKGROUND_MAP = {
  // 积分提醒
  沉睡90天人群天天唤醒:
    'https://img01.yzcdn.cn/upload_files/2024/01/29/FkE7n449jz6193qUwXGLjelCGi3C.png',
  // 关怀
  退款自动化关怀: 'https://img01.yzcdn.cn/upload_files/2024/01/29/Fs9NcxXKV1NMfhGTlz0TXYc00S2d.png',
  // 促单转化
  未下单企微新好友促单:
    'https://img01.yzcdn.cn/upload_files/2024/01/29/FruvQcv0pRMJgUblx_4xW_02aYrW.png',
  '大促预热期，挖掘敏感人群促转化':
    'https://img01.yzcdn.cn/upload_files/2024/01/29/FruvQcv0pRMJgUblx_4xW_02aYrW.png',
  // 策略对比
  新客首购自动促复购:
    'https://img01.yzcdn.cn/upload_files/2024/01/29/FjwMS-nhg-pkLqffzZEvqzofiksH.png',
  // 商品
  商品: 'https://img01.yzcdn.cn/upload_files/2024/01/29/FsA3P2I2N_0w8T7-Z7p462920yj3.png',
  // 优惠券
  优惠: 'https://img01.yzcdn.cn/upload_files/2024/01/29/FkPVNpJP6h-8MHDnlul8PopPg1KZ.png',
  // 赠品
  赠品: 'https://img01.yzcdn.cn/upload_files/2024/01/29/Fvh3DAKkNNqZDMxfEoE268W0ZGs1.png',
  未发货自动同意退款:
    'https://img01.yzcdn.cn/upload_files/2024/03/26/FgXffmtsG0pv_M7V8xINFnmOoFkl.png',
  差评提醒: 'https://img01.yzcdn.cn/upload_files/2024/03/26/FgXffmtsG0pv_M7V8xINFnmOoFkl.png',
  物流异常提醒: 'https://img01.yzcdn.cn/upload_files/2024/03/26/FgXffmtsG0pv_M7V8xINFnmOoFkl.png',
  定时推送经营周报:
    'https://img01.yzcdn.cn/upload_files/2024/04/07/FpR6Oj-vUOUDlwPovx7lsyQfSett.jpg',
};

const GROUP_CODE_BACKGROUND_MAP = {
  goods: 'https://img01.yzcdn.cn/upload_files/2024/01/29/FsA3P2I2N_0w8T7-Z7p462920yj3.png',
  order: 'https://img01.yzcdn.cn/upload_files/2024/03/26/FgXffmtsG0pv_M7V8xINFnmOoFkl.png',
  ump: 'https://img01.yzcdn.cn/upload_files/2024/01/29/FkPVNpJP6h-8MHDnlul8PopPg1KZ.png',
  distributors: 'https://img01.yzcdn.cn/upload_files/2024/03/26/FheYuF3m2IUqCHKaKAKusXfyJVT_.png',
  cloud_distribution:
    'https://img01.yzcdn.cn/upload_files/2024/03/26/FqRVnEO2MYEsugckAesKClybsp-M.png',
  business_data: 'https://img01.yzcdn.cn/upload_files/2024/04/07/FpR6Oj-vUOUDlwPovx7lsyQfSett.jpg',
  supplyChainStockManager:
    'https://img01.yzcdn.cn/upload_files/2024/05/31/FvZQF-7xveeDpeGiBvMkjNbpm7nj.png',
  miniprogram: 'https://img01.yzcdn.cn/upload_files/2024/06/03/FrHJUx-HOVpSgjb3e5ZCABeDcMQa.jpeg',
};

const TemplateCard: React.FC<TemplateCardProps> = ({
  type = 'marketing',
  title = '',
  desc = '',
  img = '',
  jumpUrl = '',
  isMore = false,
  className = '',
  templateId = '',
  groupCode = '',
  disabledBackground = false,
  disabled = false,
}) => {
  const isMarketing = type === 'marketing';
  const { quickCreate } = useFlowCreate({ templateId, flowName: title });
  return (
    <div
      style={{
        backgroundImage: disabledBackground
          ? ''
          : `url(${
          // title中包NAME_BACKGROUND_MAP中的key，就用对应的value，否则用img  title是完整的名称需要用includes
          GROUP_CODE_BACKGROUND_MAP[groupCode] ||
          NAME_BACKGROUND_MAP[
          Object.keys(NAME_BACKGROUND_MAP).find(key => title.includes(key)) || ''
          ]
          })`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: '80px 80px',
        backgroundPosition: 'right bottom',
        border: `1px solid ${isMarketing ? 'rgba(217, 76, 76, 0.10)' : 'rgba(76, 111, 217, 0.10)'}`,
      }}
      className={`${styles.templateCardContainer} ${className} ${disabled ? styles.disabled : ''}`}
      onClick={() => {
        if (disabled) return;
        isMore ? windowOpen(jumpUrl) : !disabled && quickCreate(jumpUrl, !isMarketing);
      }}
    >
      {isMore ? (
        <div className={styles.more}>
          更多模板&nbsp;
          <Icon type="right-circle-o" style={{ fontSize: 16 }} />
        </div>
      ) : (
        <>
          <div className={styles.title}>{title}</div>
          <div className={styles.desc}>{desc}</div>
          <div className={styles.create}>
            {disabled ? (
              <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
                <a
                  style={{
                    color: '#ccc',
                    cursor: 'not-allowed',
                    padding: '4px 4px 8px 0px',
                  }}
                >
                  快速创建
                </a>
              </Pop>
            ) : (
              <a
                style={{
                  color: isMarketing ? '#D94C4C' : '#4C6FD9',
                  cursor: 'pointer',
                  padding: '4px 4px 8px 0px',
                }}
              >
                快速创建
              </a>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default TemplateCard;
