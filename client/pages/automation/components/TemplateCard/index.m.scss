.template-card-container {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: left;
  padding: 16px;
  gap: 8px;
  border-radius: 4px;

  .title {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }

  .desc {
    color: #999;
    font-size: 12px;
    line-height: 20px;
    height: 40px;
  }

  .create {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }

  .more {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height: 100%;
    width: 100%;
    color: #333;

    &:hover {
      color: black;
    }
  }
}

.disabled {
  // background-color: #f7f7f7;
  cursor: not-allowed;
  color: #ccc;
}
