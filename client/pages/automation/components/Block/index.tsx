import React from 'react';
import styles from './index.m.scss';
import usePermission from 'hooks/usePermission';
import jumpTo from 'fns/jumpTo';

const Block = ({
  title = '',
  desc = '',
  businessReloadUrl = '',
  businessReloadDesc = '',
  needPermissionCheck,
  ...rest
}) => {
  const { checkAutoMarketingPermission } = usePermission();

  return (
    <div className={styles.blockContainer}>
      {title && (
        <>
          <div className={styles.header}>
            <div>
              <div className={styles.title}>{title}</div>
              <div className={styles.desc}>{desc}</div>
            </div>
            <div className={styles.jumpTo}>
              <a
                onClick={async () => {
                  if (needPermissionCheck) {
                    await checkAutoMarketingPermission();
                    jumpTo({ url: businessReloadUrl, needNewWindow: true });
                  } else {
                    jumpTo({ url: businessReloadUrl, needNewWindow: true });
                  }
                }}
              >
                {businessReloadDesc}
              </a>
            </div>
          </div>
        </>
      )}
      <div className={styles.content}>{rest.children}</div>
    </div>
  );
};

export default Block;
