.automation-container {
  width: 100%;

  .intro-container {
    width: 100%;
    min-width: 100%;
    //max-width: 1488px;
    //height: 136px;
    //min-width: 1288px;
    //max-width: 1488px;
    //overflow: hidden;
    margin-bottom: 16px;

    .intro-card {
      width: 100%;
      height: 136px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: right;
      min-width: 1200px;

      .title {
        font-weight: 500;
        font-size: 24px;
        margin-bottom: 8px;
        padding-top: 37px;
        padding-left: 59px;
        line-height: 34px;
      }

      .desc {
        padding-left: 59px;
        font-family: PingFang SC;
        opacity: 0.7;
      }
    }
  }

  .block-area {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    .item {
      flex-shrink: 0;
      height: 136px;
      box-sizing: border-box;
    }
  }
}

:global {
  .app-inner {
    background-color: unset !important;
    padding: 0 !important;
  }
}
