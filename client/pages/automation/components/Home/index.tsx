import React, { useEffect, useMemo } from 'react';
import { Carousel } from 'zent';
import 'zent/css/index.css';
import { getTemplates } from '../../api';
import Block from '../Block';
import TemplateCard from '../TemplateCard';
import styles from './index.m.scss';
import withBusinessCheck from 'pages/work-flow/HOC/withBusinessCheck';
import { getPluginLifeCycle, isCustomerPro } from 'api/automation';
import { isValid } from 'hooks/usePermission';

const INTRO_BANNER_CONFIG = [];

const bannerByNameMap = {
  营销计划类: {
    title: '自动营销',
    desc:
      '基于人群，做灵活的营销计划编排和配置，自动执行营销任务，提升营销效果；同时采集和分析效果数据，实现精准、智能的数据化营销',
    img: 'https://img01.yzcdn.cn/upload_files/2024/03/08/FmPUQpRZrq6RDVfJyeUiXNfrgxP_.png',
    titleColor: '#A55C64',
    descColor: '#7A3840',
  },
  运营管理类: {
    title: '自动管理',
    desc:
      '基于日常运营管理，通过将功能流程串联搭建自定义任务流程，实现重复性工作流程的自动化，帮助提升运营管理效率',
    img: 'https://img01.yzcdn.cn/upload_files/2024/03/08/FlFi22RvgTfqvxhytB3TUSRBeK7r.png',
    titleColor: '#4E6B9F',
    descColor: '#3B598F',
  },
};

const AccessableTemplateCard = withBusinessCheck(TemplateCard);

const Automation = () => {
  const [allTemplateTypes, setAllTemplateTypes] = React.useState<any[]>([]);
  // 自动化页面没有二级菜单，导致pc-shared计算的宽度会变化，导致轮播组件无法正确的计算item宽度，所以在页面加载完成后再显示轮播组件
  const [showCarousel, setShowCarousel] = React.useState(false);
  useEffect(() => {
    isCustomerPro({}).then(async isPro => {
      console.log('is Pro is ', isPro)
      getTemplates().then(async res => {
        // 使用 Promise.all 确保所有异步操作完成
        res = await Promise.all(
          res?.map(async item => {
            const result = {
              ...item,
              templateList: getTemplateList(item)
            };

            // id 1 自动任务  id 2 ma

            function getTemplateList(item) {
              const moreTemplateUrls = {
                1: 'https://www.youzan.com/v4/jiawo/work-flow/templates',
                2: isPro ? 'https://www.youzan.com/crm/cdp/plan/list?from=PC-SHARED-NAV' : 'https://www.youzan.com/crm/cdp/plan/tpl/market'
              };

              const moreTemplateUrl = moreTemplateUrls[item.id];
              if (!moreTemplateUrl) return item.templateList || [];

              const moreTemplate = getMoreTemplate(moreTemplateUrl);

              return item.templateList
                ? [...item.templateList, moreTemplate]
                : [moreTemplate];
            }

            function getMoreTemplate(detailUrl) {
              return {
                detailUrl,
                isMore: true,
                canUse: true,
              };
            }
            if (item.id === 2) {
              // 营销计划类，需要根据客户pro等权限判断跳转链接
              /* const isPro = await isCustomerPro({});
            const permission = await getPluginLifeCycle();
            const { crmPluginLifecycle, customerProPluginLifecycle } = permission;
      
            // 别看这里写的傻逼，其实是客户pro和crm应该是两套逻辑，但是目前是跳到同一个地方。。。
            if (isPro) {
              result.businessReloadDesc = '前往营销计划列表';
              result.businessReloadUrl = 'https://www.youzan.com/crm/cdp/plan/tpl/market';
            } else if (
              isValid(crmPluginLifecycle?.endTime) &&
              isValid(customerProPluginLifecycle?.endTime)
            ) {
              result.businessReloadDesc = '前往营销计划列表';
              result.businessReloadUrl = 'https://www.youzan.com/crm/cdp/plan/tpl/market';
            } */
              result.needPermissionCheck = true;
              result.businessReloadDesc = '前往营销计划列表';
              result.businessReloadUrl = 'https://www.youzan.com/crm/cdp/plan/list?from=PC-SHARED-NAV';
            }
            return result;
          }),
        );

        console.log('res', res);
        setAllTemplateTypes(res);
      });

      setTimeout(() => {
        setShowCarousel(true);
      }, 500);
    });

  }, []);

  const filteredBannerList = useMemo(() => {
    const bannerNames = allTemplateTypes.map(item => item.name);
    bannerNames.forEach(name => {
      if (bannerByNameMap[name]) {
        // @ts-ignore
        INTRO_BANNER_CONFIG.push(bannerByNameMap[name]);
      }
    });
    return INTRO_BANNER_CONFIG;
  }, [allTemplateTypes]);

  return (
    <div className={styles.automationContainer}>
      {showCarousel && (
        <Carousel className={styles.introContainer} dots={true} autoplay autoplayInterval={10000}>
          {filteredBannerList.map(({ title, desc, img, titleColor, descColor }, index) => {
            return (
              <div>
                <div
                  style={{
                    backgroundImage: `url(${img})`,
                  }}
                  className={styles.introCard}
                >
                  <div className={styles.title} style={{ color: titleColor }}>
                    {title}
                  </div>
                  <div className={styles.desc} style={{ color: descColor }}>
                    {desc}
                  </div>
                </div>
              </div>
              // <div key={index} className={styles.item}>
              //  <img src={img}></img>
              // </div>
            );
          })}
        </Carousel>
      )}
      {allTemplateTypes.map(
        ({
          name = '',
          id,
          description,
          templateList = [],
          businessReloadUrl,
          businessReloadDesc,
          needPermissionCheck = false,
        }) => (
          <>
            {/* {!!templateList.length && ( */}
            <Block
              title={name}
              desc={description}
              needPermissionCheck={needPermissionCheck}
              businessReloadDesc={businessReloadDesc}
              businessReloadUrl={businessReloadUrl}
            >
              <div className={styles.blockArea}>
                {templateList.map(
                  ({
                    templateName,
                    detailUrl,
                    templateDescription,
                    templateId,
                    isMore,
                    canUse,
                    unusableReason,
                    obtainPermissionWay,
                    obtainPermissionNotice,
                  }) => (
                    <AccessableTemplateCard
                      isValid={canUse}
                      validationText={unusableReason}
                      title={templateName}
                      jumpUrl={detailUrl}
                      url={obtainPermissionWay}
                      detailText={obtainPermissionNotice}
                      desc={templateDescription}
                      className={styles.item}
                      type={id === 1 ? 'workflow' : 'marketing'}
                      templateId={templateId}
                      isMore={isMore}
                    />
                  ),
                )}
              </div>
            </Block>
            {/* )} */}
          </>
        ),
      )}
    </div>
  );
};

export default Automation;
