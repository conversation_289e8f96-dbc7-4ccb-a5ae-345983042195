import React, { useEffect } from 'react';
import Automation from './components/Home/index';
import { hot } from 'react-hot-loader/root';
import { checkShopFlowPermission } from 'pages/work-flow/api';

const App = () => {
  useEffect(() => {
    // checkPermission();
  }, []);

  /* const checkPermission = async () => {
    const { permission } = await checkShopFlowPermission();
    console.log('permission', permission);
    if (permission == 1) {
      window.location.href = 'https://www.youzan.com/v4/jiawo/work-flow/intro';
    }
  } */

  return (
    <div>
      <Automation />
    </div>
  );
};

export default hot(App);
