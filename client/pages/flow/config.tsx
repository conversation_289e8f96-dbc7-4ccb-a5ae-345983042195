import React from 'react';
import { ClampLines, IGridColumn, previewImage } from 'zent';
import styles from './index.m.scss';
import { dimension } from './app';

type dataBlock = {
  title: string;
  key: dimension;
  data?: number;
  unit?: string;
};

interface IFlowRenderConfig {
  [tab: string]: {
    data: dataBlock[];
    column: IGridColumn[];
  };
}

export const flowRenderConfig: IFlowRenderConfig = {
  taskFlow: {
    data: [
      {
        title: '自动执行操作',
        key: 'taskCount',
      },
      {
        title: '总技能',
        key: 'skillCount',
      },
      {
        title: '运行中的技能数',
        key: 'runningSkillCount',
      },
    ],
    column: [
      { title: '执行时间', name: 'executeTime' },
      { title: '执行的智能体', name: 'agentName' },
      { title: '执行内容', name: 'executeContent' },
      { title: '执行结果', name: 'executeResult' },
    ],
  },
  aigc: {
    data: [
      {
        title: '文案创作次数',
        key: 'textGenerationCount',
      },
      {
        title: '生成文案条数',
        key: 'textCount',
      },
      {
        title: '图片创作次数',
        key: 'imageGenerationCount',
      },
      {
        title: '生成图片张数',
        key: 'imageCount',
      },
    ],
    column: [
      { title: '生成时间', name: 'generateTime' },
      {
        title: '生成内容',
        name: 'generateContent',
        width: 530,
        bodyRender(record) {
          if (record.type === 'image') {
            const imgList = record.generateContent;
            const handlePreview = e => {
              previewImage({
                images: imgList,
                index: imgList.indexOf(e.target.src),
                scaleRatio: 3,
              });
            };
            return imgList.map(src => (
              <img className={styles.imagePreview} src={src} onClick={handlePreview} />
            ));
          }
          if (record.type === 'text') {
            return (
              <ClampLines
                lines={2}
                popWidth={400}
                text={record.generateContent.join('\n')}
                renderPop={() => (
                  <div>
                    {record.generateContent.map((txt, idx) => (
                      <div
                        style={
                          idx !== record.generateContent.length - 1 ? { marginBottom: 12 } : {}
                        }
                      >
                        {txt}
                      </div>
                    ))}
                  </div>
                )}
              />
            );
          }
          return <div>-</div>;
        },
      },
      { title: '数量', name: 'count' },
      {
        title: '类型',
        name: 'type',
        bodyRender(record) {
          if (record.type === 'image') {
            return '图片';
          }
          if (record.type === 'text') {
            return '文案';
          }
          return '-';
        },
      },
      { title: '操作人', name: 'operator' },
    ],
  },
};
