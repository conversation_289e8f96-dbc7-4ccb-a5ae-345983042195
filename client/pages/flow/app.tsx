import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { hot } from 'react-hot-loader/root';
import { BlockLoading, Grid, Tabs } from 'zent';
import styles from './index.m.scss';
import { flowRenderConfig } from './config';
import { getDashboardData, getExecResultList } from './api';
import { renderNav } from './render-nav';

const TabPanel = Tabs.TabPanel;

export type category = 'taskFlow' | 'aigc';
export type dimension =
  | 'profit'
  | 'workload'
  | 'textCount'
  | 'imageCount'
  | 'textGenerationCount'
  | 'imageGenerationCount'
  | 'skillCount'
  | 'runningSkillCount'
  | 'taskCount';

const unitMap: { [key in dimension]: string } = {
  profit: '元',
  workload: '人/日',
  textCount: '条',
  imageCount: '张',
  textGenerationCount: '次',
  imageGenerationCount: '次',
  skillCount: '个',
  runningSkillCount: '个',
  taskCount: '次',
};
interface IFlowContent {
  category: category;
  timeStamp: number;
  isActive: boolean;
}
const pageSize = 10;
const FlowContent = (props: IFlowContent) => {
  const { category, timeStamp, isActive } = props;
  const [config, setConfig] = useState(flowRenderConfig[category]);
  const [loading, setLoading] = useState(false);
  const [dataset, setData] = useState([]);

  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    const finalPathName = pathArr[pathArr.length - 2];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  const onChange = useCallback(
    ({ current }) => {
      setCurrent(current);
      getExecResultList({ category, current, pageSize }).then(res => {
        setData(res.data);
        setTotal(res.totalCount);
      });
    },
    [category],
  );

  useEffect(() => {
    if (!isActive) {
      return;
    }
    setLoading(true);
    Promise.all([
      getDashboardData(category).then(res => {
        const result = res.reduce((acc, current) => {
          return {
            ...acc,
            [current.dimension]: current.value,
          };
        }, {});
        config.data = config.data.map(cfg => ({
          ...cfg,
          data: result[cfg.key],
          unit: unitMap[cfg.key],
        }));
        setConfig({ ...config });
      }),

      getExecResultList({ category, current, pageSize }).then(res => {
        setData(res.data);
        setTotal(res.totalCount);
      }),
    ]).finally(() => setLoading(false));
  }, [category, timeStamp, isActive]);

  return (
    <BlockLoading loading={loading} icon="youzan">
      <div className={styles.flowContent}>
        <div className={styles.flowData}>
          <div className={styles.flowDataTitle}>数据</div>
          <div className={styles.flowDataDetail}>
            {config.data.map(cfg => (
              <div className={styles.flowDataItem}>
                <div className={styles.flowDataItemTitle}>{cfg.title}</div>
                <div className={styles.flowDataItemInfo}>
                  <span className={styles.flowDataItemNum}>{cfg.data || '-'}</span>
                  <span className={styles.flowDataItemUnit}>{cfg.unit}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className={styles.flowList}>
          <div className={styles.flowListTitle}>运行明细</div>
          <div className={styles.flowListDetail}>
            <Grid
              size="large"
              columns={config.column}
              datasets={dataset}
              pageInfo={{
                current: current,
                pageSize,
                total: total,
              }}
              onChange={onChange}
            />
          </div>
        </div>
      </div>
    </BlockLoading>
  );
};

const App = () => {
  const [activeId, setActiveId] = useState('1');
  const [timeStamp, setTimeStamp] = useState(0);

  const panels = [
    <TabPanel key="1" tab="经营托管" id="1">
      <FlowContent
        isActive={activeId === '1'}
        timeStamp={timeStamp}
        key="workflow"
        category="taskFlow"
      />
    </TabPanel>,
    // <TabPanel key="2" tab="决策建议" id="2" disabled></TabPanel>,
    <TabPanel key="3" tab="创意生成" id="3">
      <FlowContent isActive={activeId === '3'} timeStamp={timeStamp} key="aigc" category="aigc" />
    </TabPanel>,
  ];

  return (
    <div className={styles.flowContainer}>
      <Tabs
        activeId={activeId}
        onChange={id => {
          setActiveId(id);
          setTimeStamp(new Date().getTime());
        }}
        type="card"
      >
        {panels}
      </Tabs>
    </div>
  );
};

export default hot(App);
