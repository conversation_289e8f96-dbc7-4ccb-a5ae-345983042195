import React from 'react';
import ReactDOM from 'react-dom';
import { Breadcrumb } from 'zent';
// import args from '@youzan/utils/url/args';
import styles from './style.m.scss';
interface BaseBread {
  name: string;
  href?: string;
}

const baseBreads: BaseBread[] = [
  {
    name: '智能体市场',
    href: '/v4/jiawo/dashboard/index',
  },
];

let navMap: BaseBread[] = [];

export const renderNav = ({ path, search }): void => {
  if (path === 'flow') {
    navMap = [{ name: '工单' }];
  }

  // 不能直接修改 baseBreads, 因为 SPA 下, 这个变量会继续保持下去
  let breads = baseBreads.map(obj => ({ ...obj }));
  breads = breads.concat(navMap);

  const CustomNav = () => {
    return (
      <div>
        {breads.map(item => {
          return item.href ? (
            <a href={item.href} className={styles.navLink}>
              {item.name}
            </a>
          ) : (
            <span>{item.name}</span>
          );
        })}
      </div>
    );
  };

  ReactDOM.render(<CustomNav />, document.getElementById('thirdbar-nav'));
};
