import ajax from 'zan-pc-ajax';
import { category } from '../app';

const prefix = '/v4/jiawo/api/flow';

export const getDashboardData = async (category: category) => {
  return ajax(`${prefix}/getDashboardData`, {
    data: {
      category,
    },
    method: 'get',
  });
};

export const getExecResultList = async ({
  category,
  current,
  pageSize,
}: {
  category: category;
  current: number;
  pageSize: number;
}) => {
  return ajax(`${prefix}/getExecResultList`, {
    data: {
      category,
      current,
      pageSize,
    },
    method: 'get',
  });
};
