.flow-container {
  display: flex;
  flex-direction: column;

  .flow-content {
    padding-top: 16px;
    .flow-data {
      padding: 20px;
      background: #FFFFFF;
      display: flex;
      flex-direction: column;
      .flow-data-title {
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
      }
      .flow-data-detail {
        margin-top: 20px;
        display: flex;
        flex-direction: row;
        .flow-data-item {
          flex: 1;
          .flow-data-item-title {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }
          .flow-data-item-info {
            margin-top: 8px;
            .flow-data-item-num {
              font-family: Avenir;
              font-size: 20px;
              font-weight: 800;
              line-height: 26px;
            }
            .flow-data-item-unit {
              font-size: 12px;
              font-weight: 500;
              line-height: 20px;
              margin-left: 2px;
              vertical-align: text-bottom;
            }
          }
        }
      }
    }
    .flow-list {
      margin-top: 16px;
      padding: 20px;
      background: #FFFFFF;
      display: flex;
      flex-direction: column;

      .flow-list-title {
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
      }
      .flow-list-detail {
        margin-top: 20px;
      }
    }
  }
}

.image-preview {
  width: 28px;
  height: 28px;
  border-radius: 2px;
  &:not(:last-child) {
    margin-right: 4px;
  }
}

:global {
  .app-inner {
    background-color: transparent !important;
    padding: 0 !important;
  }
}
