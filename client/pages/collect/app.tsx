import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { hot } from 'react-hot-loader/root';
import styles from './index.m.scss';
import Text from './components/Text/Text';
import Report from './components/Report/Report';
import Picture from './components/Picture/Picture';
import { getCollectList, updateCollectState } from './api';
import { useInfinityList } from 'hooks/useInfinityList';
import { BlockLoading, InfiniteScroller, LayoutRow as Row, LayoutCol as Col } from 'zent';
import { renderNav } from './render-nav';

export interface ICollectItem {
  content: string;
  id: number;
  type: CollectType;
  userId: string;
  userName: string;
  updatedAt: number;
  skillName: string;
  itemId: number;
}

export enum CollectType {
  TEXT = 1,
  PICTURE = 2,
  REPORT = 3,
}

export function splitArrayIntoChunks(arr: any[], chunkSize: number) {
  const result: any[] = [];
  for (let i = 0; i < arr.length; i += chunkSize) {
    result.push(arr.slice(i, i + chunkSize));
  }
  return result;
}

const App = () => {
  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    // 这里改为 -2 了 因为最后是 index...
    const finalPathName = pathArr[pathArr.length - 2];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  const { loading, hasMore, list, loadMore, onItemUpdate: onUpdate } = useInfinityList({
    fetchList: async ({ page, pageSize }) => {
      const result = await getCollectList({ current: page, pageSize });
      return {
        items: result.data,
        paginator: { page: result.page, totalCount: result.totalCount },
      };
    },
    defaultPageSize: 3 * 8, // 一行3个 一次加载8行
  });

  const onItemUpdate = useCallback(() => {
    // 请求
    onUpdate();
  }, [onUpdate]);

  const renderList = useMemo(() => {
    const line = splitArrayIntoChunks(list, 3);
    return line.map(i => {
      return (
        <Row>
          {i.map((i, idx) => {
            if (i.type === CollectType.TEXT) {
              return (
                <Col
                  span={8}
                  style={{
                    marginBottom: 20,
                    marginLeft: !idx ? 0 : 20,
                    maxWidth: 'calc(33.3% - 13.3px)',
                  }}
                >
                  <Text data={i} onItemUpdate={onItemUpdate} />
                </Col>
              );
            }
            if (i.type === CollectType.PICTURE) {
              return (
                <Col
                  span={8}
                  style={{
                    marginBottom: 20,
                    marginLeft: !idx ? 0 : 20,
                    maxWidth: 'calc(33.3% - 13.3px)',
                  }}
                >
                  <Picture data={i} onItemUpdate={onItemUpdate} />
                </Col>
              );
            }
            if (i.type === CollectType.REPORT) {
              return (
                <Col
                  span={8}
                  style={{
                    marginBottom: 20,
                    marginLeft: !idx ? 0 : 20,
                    maxWidth: 'calc(33.3% - 13.3px)',
                  }}
                >
                  <Report data={i} onItemUpdate={onItemUpdate} />
                </Col>
              );
            }
          })}
        </Row>
      );
    });
  }, [list]);

  if (!loading && !list.length) {
    return (
      <div className={styles.collectContainer}>
        <div className={styles.empty}>暂无内容</div>
      </div>
    );
  }
  return (
    <BlockLoading loading={loading} icon="youzan">
      <div className={styles.collectContainer}>
        <InfiniteScroller
          hasMore={hasMore} // 当不在加载状态时，表示还有更多数据可加载
          loadMore={loadMore}
          skipLoadOnMount
          className={styles.scroll}
        >
          {renderList}
        </InfiniteScroller>
      </div>
    </BlockLoading>
  );
};

export default hot(App);
