import React, { useCallback, useMemo, useState } from 'react';
import styles from './index.m.scss';
import { Notify, Button, Popover, closeDialog, openDialog } from 'zent';
import { QRCodeCanvas } from 'qrcode.react';
import { CrossIcon } from 'svg';
import cn from 'classnames';
import EditIcon from 'svg/Edit';
import LikeIcon from 'svg/Like';
import QRIcon from 'svg/QR';
import CopyIcon from 'svg/Copy';
import { copyText, downloadFile } from 'fns/utils';
import UnLikeIcon from 'svg/UnLike';
import CollectDownloadIcon from 'svg/CollectDownload';
import { updateCollectState } from 'pages/collect/api';
import { ICollectItem } from 'pages/collect/app';
import moment from 'moment';

enum PromoteChannel {
  Random = 4,
  WX = 1,
  Weibo = 2,
  RedBook = 3,
  GroupShare = 0,
  SMS = 5,
  Live = 6,
  TaoBao = 7,
}

const contentInfoPromotionChannelMap = {
  [PromoteChannel.Random]: '用途不限',
  [PromoteChannel.WX]: '朋友圈',
  [PromoteChannel.Weibo]: '微博',
  [PromoteChannel.RedBook]: '小红书',
  [PromoteChannel.GroupShare]: '群分享',
  [PromoteChannel.SMS]: '短信',
  [PromoteChannel.Live]: '直播',
  [PromoteChannel.TaoBao]: '逛逛',
};

interface IDetailDialogProps {
  type?: 'text' | 'picture';
  data?: any;
  onItemUpdate: () => void;
  onClose?: (id) => void;
}

interface IDetailContentProps {
  contentType?: 'text' | 'picture';
  data: ICollectItem;
  onItemUpdate: () => void;
  close: () => void;
}

const DetailContent = ({ close, contentType, data, onItemUpdate }: IDetailContentProps) => {
  const { itemId, content, skillName, userName, updatedAt } = data;

  const handleContent = useMemo(() => {
    try {
      const parsedContent = JSON.parse(content);
      // TODO: @飞宇 imgList
      return parsedContent.content;
    } catch (error) {
      return content;
    }
  }, [content]);

  const [isLike, setIsLike] = useState(1);
  const [isEdit, setEditMode] = useState(false);
  const [textInput, setText] = useState(handleContent);

  const {
    userId,
    shopInfo: { kdtId, rootKdtId },
  } = window._global;
  const qrcodePage =
    'https://jarvis.youzan.com/h5/text-landing?' +
    `id=${itemId}
    &userId=${userId}
    &kdtId=${kdtId}
    &hqKdtId=${rootKdtId || kdtId}
    &promoteChannel=${PromoteChannel.Random}
    &promoteChannelName=${contentInfoPromotionChannelMap[PromoteChannel.Random]}`;

  const handleCopy = useCallback(async () => {
    await copyText(content);
    Notify.success('复制成功');
  }, [data]);

  const handleLikeChange = useCallback(async () => {
    try {
      setIsLike(Number(!isLike));

      // @ts-ignore
      await updateCollectState({
        ...data,
        collected: !isLike,
      });

      Notify.success(!isLike ? '收藏成功' : '取消收藏');
      await onItemUpdate();
    } catch (error) {
      setIsLike(isLike);
      Notify.success((error as any)?.message || '收藏失败');
    }
  }, [isLike, data]);

  const handleEditFinish = useCallback(async () => {
    // @ts-ignore
    await updateCollectState({
      ...data,
      collected: isLike,
      content: textInput,
    });

    Notify.success('编辑成功');
    setEditMode(false);
    await onItemUpdate();
  }, [textInput]);

  const renderContent = useMemo(() => {
    if (contentType === 'text') {
      if (isEdit) {
        return (
          <div className={cn(styles.dialogContent, styles[contentType!])}>
            <textarea
              className={styles.edit}
              onChange={e => {
                setText(e.target.value);
              }}
              value={textInput}
            />
          </div>
        );
      }
      return <div className={cn(styles.dialogContent, styles[contentType!])}>{textInput}</div>;
    }
    if (contentType === 'picture') {
      return (
        <div className={cn(styles.dialogContent, styles[contentType!])}>
          <img src={content} />
        </div>
      );
    }
  }, [contentType, isEdit, textInput]);

  const renderFooter = useMemo(() => {
    if (contentType === 'text') {
      if (isEdit) {
        return (
          <div className={styles.footer}>
            <Button type="primary" onClick={handleEditFinish}>
              确定
            </Button>
            <Button onClick={() => setEditMode(false)}>取消</Button>
          </div>
        );
      }
      return (
        <div className={styles.footer}>
          <span onClick={handleCopy}>
            <CopyIcon />
          </span>
          {itemId.toString().length !== 16 && (
            <Popover
              style={{ zIndex: 99999 }}
              className="zent-doc-popover"
              position={Popover.Position.TopCenter}
              cushion={5}
            >
              <Popover.Trigger.Hover>
                <span>
                  <QRIcon />
                </span>
              </Popover.Trigger.Hover>

              <Popover.Content>
                <div id="qrcode">
                  <QRCodeCanvas value={qrcodePage} />
                  <div className="qrcode-desc">扫码后在手机上预览</div>
                </div>
              </Popover.Content>
            </Popover>
          )}
          {isLike ? (
            <span onClick={() => handleLikeChange()}>
              <LikeIcon className={styles.like} />
            </span>
          ) : (
            <span onClick={() => handleLikeChange()}>
              <UnLikeIcon />
            </span>
          )}
          <span onClick={() => setEditMode(true)}>
            <EditIcon />
          </span>
        </div>
      );
    }
    if (contentType === 'picture') {
      return (
        <div className={styles.footer}>
          <span
            onClick={() => {
              downloadFile(content, `${skillName}.png`);
              Notify.success('开始下载');
            }}
          >
            <CollectDownloadIcon />
          </span>
          {isLike ? (
            <span onClick={() => handleLikeChange()}>
              <LikeIcon className={styles.like} />
            </span>
          ) : (
            <span onClick={() => handleLikeChange()}>
              <UnLikeIcon />
            </span>
          )}
        </div>
      );
    }
  }, [content, handleEditFinish, isLike, isEdit, data]);

  return (
    <div className={styles.dialogContentContainer}>
      <div className={styles.dialogHeader}>
        <div className={styles.dialogTitle}>
          <div className={styles.title}>{skillName}</div>
          <div className={styles.closeBtn} onClick={close}>
            <CrossIcon style={{ verticalAlign: 'middle' }} />
          </div>
        </div>
        <div className={styles.dialogSubTitle}>
          <div className={styles.creator}>{userName}</div>
          <div className={styles.time}>{moment(updatedAt).format('MM月DD日 HH:mm')}</div>
        </div>
      </div>
      {renderContent}
      {renderFooter}
    </div>
  );
};

const openDetailDialog = ({ type, data, onItemUpdate }: IDetailDialogProps) => {
  openDialog({
    className: styles.dialogContainer,
    dialogId: type,
    closeBtn: false,
    title: null,
    footer: null,
    children: (
      <DetailContent
        close={() => closeDialog(type!)}
        contentType={type}
        data={data}
        onItemUpdate={onItemUpdate}
      />
    ),
  });
};

export default openDetailDialog;
