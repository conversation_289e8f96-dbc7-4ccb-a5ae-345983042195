.dialog-container {
  border-radius: 16px!important;
  width: 680px;
  .dialog-content-container {
    padding: 8px 4px;
    .dialog-header {
      .dialog-title {
        display: flex;
        flex-direction: row;
        .title {
          flex: 1;
          font-size: 20px;
          font-weight: 500;
          line-height: 28px;
        }
        .close-btn {
          display: inline-block;
          cursor: pointer;
          path {
            fill: #999;
          }
          &:hover {
            path {
              fill: #4a4a4a;
            }
          }
        }
      }
      .dialog-sub-title {
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        margin-top: 4px;
        color: #999999;
        display: flex;
        flex-direction: row;
        .time {
          margin-left: 12px;
        }
      }
    }

    .dialog-content {
      &.text {
        margin-top: 20px;
        background: #F8F8F8;
        padding: 16px;
        min-height: 240px;
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
        display: flex;
        flex-direction: column;
        .edit {
          resize: none;
          outline: none;
          padding: 0;
          display: flex;
          border: none;
          background: transparent;
          flex: 1;
        }
      }
      &.picture {
        margin-top: 20px;
        width: 100%;
        height: 370px;
        text-align: center;
        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
    }

    .footer {
      margin-top: 20px;
      height: 32px;
      display: flex;
      flex-direction: row-reverse;
      gap: 16px;
      svg {
        cursor: pointer;
        padding: 3px;
        &:not(.like) {
          path {
            fill: #999;
          }
          &:hover {
            path {
              fill: #4a4a4a;
            }
          }
        }
        // &:hover {
        //   background-color: #f0f0f0;
        //   border-radius: 4px;
        // }
      }
    }
  }
}

:global {
  #qrcode {
    position: relative;
    z-index: 9999;
    padding: 16px 16px 12px 16px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.1);
    .qrcode-desc {
      color: #999;
      margin-top: 12px;
      text-align: center;
      font-feature-settings: 'clig' off, 'liga' off;
      font-family: PingFang SC;
      font-size: 12px;
    }
  }
}