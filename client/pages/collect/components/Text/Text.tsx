import React, { useMemo } from 'react';
import styles from './index.m.scss';
import { ClampLines } from 'zent';
import openDetailDialog from '../DetailDialog/DetailDialog';
import { ICollectItem } from 'pages/collect/app';
import moment from 'moment';

interface ITextProps {
  data: ICollectItem;
  onItemUpdate: () => void;
}

const Text = (props: ITextProps) => {
  const { data, onItemUpdate } = props;
  const { id, content, skillName, type, updatedAt, userName } = data;

  const handleContent = useMemo(() => {
    try {
      const parsedContent = JSON.parse(content);
      // TODO: @飞宇 imgList
      return parsedContent.content;
    } catch (error) {
      return content;
    }
  }, [content]);
  return (
    <div
      className={styles.container}
      onClick={() => openDetailDialog({ type: 'text', data, onItemUpdate })}
    >
      <div className={styles.content}>
        <div className={styles.left}>
          <ClampLines lines={3} popWidth={400} showPop={false} text={handleContent} />
        </div>
        {/* {img ? (
          <div className={styles.right}>
            <img src={img} />
          </div>
        ) : null} */}
      </div>
      <div className={styles.footer}>
        <div className={styles.info}>
          <span className={styles.type}>{skillName}</span>
          <span className={styles.user}>{userName}</span>
        </div>
        <div className={styles.create}>{moment(updatedAt).format('MM月DD日 HH:mm')}</div>
      </div>
    </div>
  );
};

export default Text;
