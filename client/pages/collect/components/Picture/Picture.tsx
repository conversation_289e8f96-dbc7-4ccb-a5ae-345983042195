import React, { useCallback } from 'react';
import styles from './index.m.scss';
import { previewImage } from 'zent';
import openDetailDialog from '../DetailDialog/DetailDialog';
import { ICollectItem } from 'pages/collect/app';
import moment from 'moment';

interface IPicturerProps {
  data: ICollectItem;
  onItemUpdate: () => void;
}

const Picture = (props: IPicturerProps) => {
  const { data, onItemUpdate } = props;
  const { id, content, skillName, type, updatedAt, userName } = data;
  const imgList = [content];
  const handlePreview = useCallback(
    e => {
      previewImage({
        images: imgList,
        index: imgList.indexOf(e.target.src),
        scaleRatio: 3,
      });
    },
    [imgList],
  );
  return (
    <div
      className={styles.container}
      onClick={() => openDetailDialog({ type: 'picture', data, onItemUpdate })}
    >
      <div className={styles.content}>
        {imgList.map(img => (
          <img
            className={styles.imgItem}
            onClick={e => {
              e.stopPropagation();
              handlePreview(e);
            }}
            src={img}
          />
        ))}
      </div>
      <div className={styles.footer}>
        <div className={styles.info}>
          <span className={styles.type}>{skillName}</span>
          <span className={styles.user}>{userName}</span>
        </div>
        <div className={styles.create}>{moment(updatedAt).format('MM月DD日 HH:mm')}</div>
      </div>
    </div>
  );
};

export default Picture;
