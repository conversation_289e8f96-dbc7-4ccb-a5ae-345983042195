.container {
  height: 152px;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  cursor: pointer;
  flex: 0.33;

  .content {
    display: flex;
    flex-direction: row;
    flex: 1;
    .left {
      flex: 1;
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
    }
    .right {
      margin-left: 20px;
      width: 80px;
      height: 80px;
      line-height: 80px;
      border-radius: 4px;
      text-align: center;
      svg {
        vertical-align: middle;
      }
    }
  }

  .footer {
    margin-top: 12px;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    color: #999999;
    display: flex;
    flex-direction: row;
    .info {
      flex: 1;
      .user {
        margin-left: 12px;
      }
    }
  }
}