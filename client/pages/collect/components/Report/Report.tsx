import React from 'react';
import styles from './index.m.scss';
import ReportIcon from 'svg/Report';
import moment from 'moment';
import { useReportDialog } from 'pages/task-flow/hooks/useReportDialog';
import { ICollectItem } from 'pages/collect/app';

const getJSON = data => {
  try {
    return JSON.parse(data);
  } catch (error) {
    return {};
  }
};

interface IReportProps {
  data: ICollectItem;
  onItemUpdate: () => void;
}
const Report = (props: IReportProps) => {
  const { data, onItemUpdate } = props;
  const { content, skillName, updatedAt, userName } = data;
  const { openReportDialog } = useReportDialog(true, skillName);
  const reportData = getJSON(content);
  return (
    <div
      className={styles.container}
      onClick={() => openReportDialog(reportData, data, onItemUpdate)}
    >
      <div className={styles.content}>
        <div className={styles.left}>{reportData.reportName}</div>
        <div className={styles.right}>
          <ReportIcon />
        </div>
      </div>
      <div className={styles.footer}>
        <div className={styles.info}>
          <span className={styles.type}>{skillName}</span>
          <span className={styles.user}>{userName}</span>
        </div>
        <div className={styles.create}>{moment(updatedAt).format('MM月DD日 HH:mm')}</div>
      </div>
    </div>
  );
};

export default Report;
