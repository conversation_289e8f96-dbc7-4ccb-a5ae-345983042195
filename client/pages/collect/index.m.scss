.collect-container {
  overflow: hidden;
  min-width: 1288px;
  height: calc(100vh - 150px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .empty {
    font-size: 12px;
    text-align: center;
    color: #999;
  }

  .scroll {
    height: 100%;
    .collect-row {
      height: 152px;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-start;
      margin-bottom: 20px;
      gap: 20px;
    }
  }
}

:global {
  .app-inner {
    background-color: transparent !important;
    padding: 0 !important;
  }
}
