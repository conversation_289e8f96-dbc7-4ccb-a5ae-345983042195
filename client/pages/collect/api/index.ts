import ajax from 'zan-pc-ajax';

const prefix = '/v4/jiawo/api/collect';

export const getCollectList = async ({ current, pageSize }) => {
  return ajax(`${prefix}/getCollectList`, {
    data: {
      current,
      pageSize,
    },
    method: 'get',
  });
};

export const updateCollectState = async ({ collected, content, id = '', type, skillName, itemId }) => {
  return ajax(`${prefix}/updateCollectState`, {
    data: {
      collected,
      content,
      id,
      skillName,
      itemId,
      type, // 1: 图文 2：图片 3：报告
    },
    method: 'post',
  });
};
