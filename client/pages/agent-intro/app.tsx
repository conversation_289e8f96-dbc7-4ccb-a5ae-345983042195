import React, { useEffect } from 'react';
import Index from './index';
import { hot } from 'react-hot-loader/root';
import { renderNav } from './render-nav';

const App = () => {

  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    // 这里改为 -2 了 因为最后是 index...
    const finalPathName = pathArr[pathArr.length - 2];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  return (
    <div>
      <Index />
    </div>
  );
};

export default hot(App);
