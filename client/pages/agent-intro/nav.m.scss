:global {
    .app-inner {
        background-color: unset !important;
        padding: 0 !important;
    }

    #thirdbar-nav {
        padding: 14px 16px;
        line-height: 20px;
    }

}

.beta {
    width: 28px;
    height: 16px;
    background: url(https://img01.yzcdn.cn/upload_files/2024/12/21/FrN8m0Zinqt3UFROR4APHTtX0gPI.png) no-repeat;
    background-size: 100% 100%;
    color: #fff !important;
    font-family: Inter;
    font-size: 8px !important;
    font-weight: 400;
    line-height: 9.68px;
    position: absolute;
    left: 57px;
    top: 3px;

    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

}

.betaText {
    font-weight: bold !important;
    color: #fff !important;
    font-family: Inter;
    font-size: 8px !important;
}



.nav {
    a::after {
        content: '' !important;
    }

    span::after {
        content: '' !important;
    }
}