import React from 'react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './style.m.scss';

export default function AgentIntro() {

  return (
    <div className={styles.scrollView}>
      <div className={styles.agentIntro}>
        <picture>
          <source
            media="(max-width: 768px)"
            srcSet="https://img01.yzcdn.cn/upload_files/2024/12/21/FqC5_NW0rG_HQWGp_cPqHE8_9Oda.png"
          />
          <img
            className={styles.agentIntroHeaderImg}
            src="https://img01.yzcdn.cn/upload_files/2024/12/21/FmQ5mG1YYrA-3FiyeQ4Mn8R-fY-e.png"
          />
        </picture>
        <picture>
          <source
            media="(max-width: 768px)"
            srcSet="https://img01.yzcdn.cn/upload_files/2024/12/21/Fkck9X8ASa71CH8NB0zLKCZEI2C_.png"
          />
          <img
            className={styles.agentIntroContentImg}
            src="https://img01.yzcdn.cn/upload_files/2024/12/21/Fp2N3D3ADDNtCbpEhbFt5_EJs-ob.png"
          />
        </picture>

        <div className={styles.agentIntroFooter}>
          <img style={{cursor: 'pointer'}} onClick={() => {
            window.location.href = 'https://www.youzan.com/v4/jiawo/dashboard/index'
          }} src="https://img01.yzcdn.cn/upload_files/2024/12/23/Fs6XZF33lKpOCMgySFJKR0-QT5FH.png" />
        </div>
      </div>
    </div>
  );
}
