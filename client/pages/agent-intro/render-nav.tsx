import React from 'react';
import ReactDOM from 'react-dom';
import { Breadcrumb } from 'zent';
// import args from '@youzan/utils/url/args';
import styles from './nav.m.scss';
interface BaseBread {
  name: string;
  href?: string;
}

const baseBreads: BaseBread[] = [
  {
    name: '智能体市场',
  },
];

let navMap: BaseBread[] = [];

export const renderNav = ({ path, search }): void => {
  // 不能直接修改 baseBreads, 因为 SPA 下, 这个变量会继续保持下去
  let breads = baseBreads.map(obj => ({ ...obj }));
  breads = breads.concat(navMap);

  const CustomNav = () => {
    return (
      <div className={styles.nav}>
        {breads.map(item => {
          return item.href ? (
            <a key={item.href} href={item.href} className={styles.navLink}>
              {item.name}
            </a>
          ) : (
            <span className={styles.navLink}>{item.name}</span>
          );
        })}
      </div>
    );
  };

  ReactDOM.render(<CustomNav />, document.getElementById('thirdbar-nav'));
};
