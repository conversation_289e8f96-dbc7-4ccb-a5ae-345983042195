.scroll-view {
  overflow-y: auto;
  min-height: calc(100vh - 150px);
  width: 100%;

  .agent-intro {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 150px);
    padding: 20px;
    box-sizing: border-box;
    background: linear-gradient(0deg, #d6e0ec 0%, #f2f7fc 100%);
    width: 100%;

    .agent-intro-header-img {
      width: 100%;
      max-width: 476px;
      height: auto;
      aspect-ratio: 476/168;
      margin-top: 40px;
    }

    .agent-intro-content-img {
      margin-top: clamp(40px, 8vh, 80px);
      width: 100%;
      max-width: 872px;
      height: auto;
      aspect-ratio: 872/220;
    }

    .agent-intro-footer {
      margin-top: clamp(40px, 8vh, 80px);
      width: 258px;
      height: auto;
      margin-bottom: 40px;

      img {
        width: 100%;
        height: auto;
        // cursor: pointer;
      }
    }
  }

  // 移动端适配
  @media screen and (max-width: 768px) {
    .agent-intro {
      padding: 16px;
      background: #d6e0ec;

      padding-bottom: 140px;

    justify-content: flex-start;

      .agent-intro-header-img {
        max-width: 154px;
        margin-top: 20px;
        aspect-ratio: 154/150;
      }

      .agent-intro-content-img {
        margin-top: 40px;
        max-width: 354px;
        aspect-ratio: 304/744;
      }

      .agent-intro-footer {
        width: 354px;
        position: fixed;
        bottom: 28px;
        left: 50%;
        transform: translateX(-50%);

        margin-bottom: 0;
      }
    }
  }
}
