import React, { useCallback, useEffect, useMemo, useState } from 'react';
import cn from 'classnames';
import { hot } from 'react-hot-loader/root';
import styles from './index.m.scss';
import Introduce from './components/Introduce/Introduce';
import Form from './components/Form/Form';
import Process from './components/Process/Process';
import Confirm from './components/Confirm/Confirm';
import { renderNav } from './render-nav';

const App = () => {
  const [step, setStep] = useState(0);
  const [objective, setObjective] = useState('');
  const next = useCallback(
    (step: number) => {
      setStep(step);
    },
    [step],
  );
  const renderContent = useMemo(() => {
    switch (step) {
      case 0:
        return <Introduce next={() => next(1)} />;
      case 1:
        return <Form prevValue={objective} setObjective={setObjective} next={() => next(2)} />;
      case 2:
        return <Process next={() => next(3)} />;
      case 3:
        return <Confirm back={() => next(1)} />;
    }
  }, [step, next]);



  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    const finalPathName = pathArr[pathArr.length - 1];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  return (
    <div className={cn(styles.guideContainer, { [styles.center]: step === 0 })}>
      {renderContent}
    </div>
  );
};

export default hot(App);
