import React from 'react';
import { hot } from 'react-hot-loader/root';

import MessageBoard from './MessageBoard';
import HandleBoard from './HandleBoard';

import useCustomReducer from './store';

const App = () => {
  const { getData } = useCustomReducer();
  const { showHandler } = getData();

  return (
    <div className="contianer">
      <div className="message-board-wrap">
        <MessageBoard />
      </div>
      {showHandler && (
        <div className="handle-board-wrap">
          <HandleBoard />
        </div>
      )}
    </div>
  );
};

export default hot(App);
