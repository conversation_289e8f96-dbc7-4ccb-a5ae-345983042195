import React, { useEffect } from 'react';

import MessageBlock from './components/MessageBlock';
import useCustomReducer from './store';

const MessageBoard = () => {
  const { setShowHandler } = useCustomReducer();

  const thinkingBlock = {
    id: 434,
    type: 3, // 思维链
    isStreamTyping: true,
    thoughtChain: {
      isAutoCollapse: false,
      title: '分析中...',
      currentStep: 0,
      steps: [
        {
          topic: '正在分析你的店铺数据',
          content:
            '我是一名智能数据助手，致力于为老板提供专业、精准的数据报表和深度分析。为了更好地完成这一任务，我深入学习了店铺的行业动态、品类特点、经营状况以及客户群体等知识。这些知识为我呈现数据报表提供了坚实基础。同时，我重点关注老板最为关心的业绩完成情况，通过数据分析帮助老板洞察店铺的运营状况，为决策提供有力支持。',
        },
      ],
    },
  };

  const messageList = [thinkingBlock];

  useEffect(() => {
    setTimeout(() => {
      setShowHandler(true);
    }, 2000);
  }, []);

  return (
    <div>
      {messageList.map(block => (
        <MessageBlock key={block.id} block={block} messageId={block.type.toString()} />
      ))}
    </div>
  );
};

export default MessageBoard;
