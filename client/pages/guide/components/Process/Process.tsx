import React, { useEffect, useState } from 'react';
import styles from './index.m.scss';
import Success from 'svg/Success';
import { getAgentList } from 'pages/guide/api';
import { Notify } from 'zent';

interface IProcessProps {
  next: () => void;
}

const Process = (props: IProcessProps) => {
  const { next } = props;
  const [flag, setFlag] = useState(false);
  useEffect(() => {
    setTimeout(() => {
      setFlag(true);
    }, 1800);

    const loop = 1000;
    const timer = setInterval(() => {
      if (!flag) {
        return;
      }
      getAgentList()
        .then(res => {
          if (res.status === 'finished') {
            next();
          }
        })
        .catch(() => {
          Notify.error('构建异常。请刷新页面重新提交表单。');
        });
    }, loop);
    return () => {
      clearInterval(timer);
    };
  }, [flag]);
  return (
    <div className={styles.processContainer}>
      <div className={styles.processHeader}>
        <p>
          正在构建适合你的团队的智能体，请稍等
          <span className={styles.dot} />
        </p>
      </div>
      <div className={styles.processContent}>
        <div className={styles.processRow}>
          <Success style={{ verticalAlign: 'middle', marginRight: 8 }} />
          已明确你的团队组成
        </div>
        <div className={styles.processRow}>
          <Success style={{ verticalAlign: 'middle', marginRight: 8 }} />
          已了解你想要我帮你的团队做什么
        </div>
        <div className={styles.processRow}>
          <Success style={{ verticalAlign: 'middle', marginRight: 8 }} />
          已为你匹配到合适的技能
        </div>
      </div>
      <div className={styles.processBtn}></div>
    </div>
  );
};

export default Process;
