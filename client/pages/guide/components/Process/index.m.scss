.process-container {
  display: flex;
  flex-direction: column;
  padding: 0 84px;
  width: 100%;

  .process-header {
    color: #333333;
    font-size: 24px;
    font-weight: 500;
    line-height: 40px;

  .dot {
    display: inline-block;
    position: relative;
    width: 4px;
    height: 4px;
    border-radius: 2px;
    background-color: #333333;
    color: #333333;
    animation: dotFlashing .6s infinite linear alternate;
    animation-delay: .3s;
    left: 8px;
    bottom: -2px;
  }

  .dot::before, .dot::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 0;
  }

  .dot::before {
    left: -6px;
    width: 4px;
    height: 4px;
    border-radius: 2px;
    background-color: #333333;
    color: #333333;
    animation: dotFlashing .6s infinite alternate;
    animation-delay: 0s;
  }

  .dot::after {
    left: 6px;
    width: 4px;
    height: 4px;
    border-radius: 2px;
    background-color: #333333;
    color: #333333;
    animation: dotFlashing .6s infinite alternate;
    animation-delay: .6s;
  }

  @keyframes dotFlashing {
    0% {
      background-color: #333333;
    }
    50%,
    100% {
      background-color: #f7f8fa;
    }
  }
  }

  .process-content {
    margin-top: 40px;
    border-radius: 4px;
    background-color: #FFFFFF;
    padding: 20px;
    width: 400px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    .process-row {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      opacity: 0;
      &:nth-child(1) {
        animation: load 1s ease-in-out 0s 1 forwards;
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation: load 1s ease-in-out 0s 1 forwards;
        animation-delay: 0.6s;
      }
      &:nth-child(3) {
        animation: load 1s ease-in-out 0s 1 forwards;
        animation-delay: 1.2s;
      }
    }
  }

  .process-btn {}
}


@keyframes load {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
