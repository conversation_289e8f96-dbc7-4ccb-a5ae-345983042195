import React from 'react';
import { AgentImageMessageBlock } from '@youzan-cloud/cloud-biz-types';

type ImageMessageBlockProps = Omit<AgentImageMessageBlock, 'type'>;

const ImageMessageBlock = ({ image }: ImageMessageBlockProps) => {
  const { imageUrl, width, height } = image;
  return (
    <div className="image-message-block">
      <img src={imageUrl} alt="消息图片" width={width} height={height} />
    </div>
  );
};

export default ImageMessageBlock;
