import React from 'react';
import styles from './style.m.scss';
import classNames from 'classnames';
import MarkdownText from '../MarkdownText';

enum AgentTaskExecutionStatusEnum {
  /** 等待执行 */
  PENDING = 0,
  /** 正在执行 */
  RUNNING = 1,
  /** 执行完成 */
  COMPLETED = 2,
  /** 执行失败 */
  FAILED = 3,
}

const TaskExecutionMessageBlock = ({ taskExecution }: any) => {
  const { steps, currentStep } = taskExecution;

  const renderStepByStatus = step => {
    switch (step.status) {
      case AgentTaskExecutionStatusEnum.COMPLETED:
        return <MarkdownText>{step.completedContent}</MarkdownText>;
      case AgentTaskExecutionStatusEnum.FAILED:
        return <MarkdownText>{step.failedContent}</MarkdownText>;
      default:
        return <MarkdownText>{step.desc}</MarkdownText>;
    }
  };

  // 渲染步骤数字或完成图标
  const renderStepNumber = (status: number, index: number) => {
    if (status === AgentTaskExecutionStatusEnum.COMPLETED) {
      return (
        <div className={styles['step-number']}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM12 1.5C6.20101 1.5 1.5 6.20101 1.5 12C1.5 17.799 6.20101 22.5 12 22.5C17.799 22.5 22.5 17.799 22.5 12C22.5 6.20101 17.799 1.5 12 1.5ZM18.5172 8.7931L17.4828 7.7069L10.1235 14.7135L6.51724 11.2783L5.48276 12.3645L10.125 16.7857L18.5172 8.7931Z"
              fill="#45A110"
            />
          </svg>
        </div>
      );
    }
    if (status === AgentTaskExecutionStatusEnum.FAILED) {
      return (
        <img
          className={styles['step-number']}
          src={'https://img01.yzcdn.cn/upload_files/2025/03/31/FiltWIYGiuVM7rziIiw-Dtfxi3JG.png'}
          alt="failed"
        />
      );
    }
    return <div className={styles['step-number']}>{index + 1}</div>;
  };

  return (
    <div className={styles['task-execution-message-block']}>
      <div className={styles['steps-container']}>
        {steps.map((step, index) => (
          <div
            key={index}
            className={classNames(styles['step-item-container'], {
              [styles['completed-step']]: step.status === AgentTaskExecutionStatusEnum.COMPLETED,
            })}
          >
            {index === currentStep && (
              <div className={styles['step-execution-content']}>
                <div className={styles['step-execution-content-item']}>
                  <MarkdownText>{step.executionContent}</MarkdownText>
                </div>
              </div>
            )}
            <div
              className={classNames(styles['step-item'], {
                [styles['step-active']]: index === currentStep,
                [styles['step-completed']]: step.status === AgentTaskExecutionStatusEnum.COMPLETED,
                [styles['step-error']]: step.status === AgentTaskExecutionStatusEnum.FAILED,
              })}
            >
              {renderStepNumber(step.status, index)}
              <div className={styles['step-content']}>
                {/* <div className={styles['step-title']}>{step.title}</div> */}

                <div className={styles['step-description']}>{renderStepByStatus(step)}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TaskExecutionMessageBlock;
