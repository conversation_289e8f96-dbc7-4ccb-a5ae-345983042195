.task-execution-message-block {
  // padding: 16px;
  // background-color: #f8f8f8;
  border-radius: 8px;
  font-family: PingFang SC, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      display: flex;
      align-items: center;
      gap: 8px;

      .loading-text {
        font-size: 14px;
        color: #999;
        font-weight: normal;
      }
    }

    .complete-badge {
      background-color: #07c160;
      color: white;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 10px;
    }
  }

  .progress-bar-container {
    height: 4px;
    background-color: #e8e8e8;
    border-radius: 2px;
    margin-bottom: 20px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background-color: #07c160;
      border-radius: 2px;
      transition: width 0.3s ease-in-out;
    }
  }

  .steps-container {
    .step-item-container {
      position: relative;
      // overflow: hidden;
      border-radius: 4px;

      &:not(:last-child) {
        margin-bottom: 24px;
        &::before {
          content: '';
          position: absolute;
          left: 25px;
          bottom: -20px;
          width: 2px;
          background-color: #ddd;
          z-index: 1;
          height: 16px;
          opacity: 0.6;
          border-radius: 1px;
        }
      }

      // 添加边框光效容器

      @keyframes border-rotate {
        100% {
          transform: translate(-50%, -50%) rotate(1turn);
        }
      }

      .step-execution-content {
        filter: blur(0) brightness(1);
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: transparent;
        z-index: 3;
        padding: 2px;
        overflow: hidden;

        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0px;
        border-radius: 4px;

        color: #155bd4;
        display: flex;
        align-items: center;
        justify-content: center;

        .step-execution-content-item {
          width: 100%;
          height: 100%;
          background-color: #fff;
          position: relative;
          z-index: 4;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform-origin: center;
          transform: translate(-50%, -50%);
          border-radius: 8px;
          z-index: 0;
          width: 200%;
          height: 1600px;
          pointer-events: none;
          background-repeat: no-repeat;
          background-size: 50% 50%, 50% 50%;
          background-position: 0 0, 100% 0, 100% 100%, 0 100%;
          background-image: linear-gradient(#155bd4, #155bd439);
          //   background-image: linear-gradient(#399953, #399953), linear-gradient(#fbb300, #fbb300),
          // linear-gradient(#d53e33, #d53e33), linear-gradient(#377af5, #377af5);
          animation: border-rotate 4s linear infinite;
        }
      }

      .step-item {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        padding: 16px 12px;
        position: relative;
        background-color: #f8f8f8;
        border-radius: 6px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        z-index: 2;

        &:last-child {
          margin-bottom: 0;
        }

        .step-number {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background-color: #c8c8c8;
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          margin-right: 12px;
          flex-shrink: 0;
          position: relative;
          z-index: 2;
          transition: background-color 0.3s ease;

          font-family: PingFang SC;
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0px;
          text-align: center;

          .status-pending {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: white;
          }

          .status-processing {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: white;
            animation: pulse 1.5s infinite;
          }

          .status-success,
          .status-error {
            font-size: 16px;
            font-weight: bold;
          }
        }

        .step-content {
          flex: 1;

          .step-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
            transition: color 0.3s ease;
          }

          .step-description {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 100%;
            letter-spacing: 0px;
          }

          .step-output {
            font-size: 12px;
            color: #666;
            background-color: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 8px;
            max-height: 0;
            opacity: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, opacity 0.3s ease, margin-top 0.3s ease;

            &.step-output-show {
              max-height: 500px;
              opacity: 1;
              margin-top: 8px;
            }
          }
        }

        &.step-active {
          // background-color: #f0f8ff;
          // border: 1px solid #4096ff;
          // box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.15);
          position: relative;
          overflow: hidden;
          filter: blur(3px);
        }

        &.step-completed {
          .step-number {
            background-color: #07c160;
            background-color: transparent;
            // transition: background-color 0.3s ease;
          }

          .step-title {
            // color: #07c160;
          }

          &:not(:last-child):before {
          }
        }

        &.step-error {
          .step-number {
            background-color: transparent;
          }

          .step-title {
            color: #ee0a24;
          }
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.2);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes blurPulse {
  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 0.3;
  }
}
