.typewriterEffect {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
  /* color: #999;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0.4px; */
}

.markdownContainer {
  display: flex;
  align-items: flex-start; 
  width: 100%;
}

.markdownContent {
  display: inline-block;
  flex: 1;

  /* 确保Markdown渲染的段落是内联的 */
  :global(p) {
    display: inline;
    margin: 0;
  }

  /* 保证光标紧跟最后一个字符 */
  :global(p:last-child) {
    margin-right: 0;
  }
}

.typewriter-effect :global(_) {
  display: inline-block;
  width: 0.5em;
  animation: blink 0.7s infinite;
}

.typewriterText {
  font-family: PingFang SC;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  // color: #999;
  margin: 0;
  padding: 0;
  background: none;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
  border: none;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #999;
  margin-left: 1px;
  vertical-align: middle;
  animation: blink 0.7s infinite;
}

.markdownWrapper {
  display: inline-block;
  flex: 0 1 auto;
  /* 确保Markdown不会使光标换行 */
  :global(p) {
    display: inline;
    margin: 0;
  }
}

/* 在Markdown中渲染的特殊光标字符 */
:global(█) {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #999;
  margin-left: 1px;
  vertical-align: middle;
  animation: blink 0.7s infinite;
}

.typingContainer {
  display: inline;
  white-space: pre-wrap;
  word-break: break-word;
}

.inlineCursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #999;
  vertical-align: text-bottom;
  margin: 0 1px;
  animation: blink 0.7s infinite;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
} 