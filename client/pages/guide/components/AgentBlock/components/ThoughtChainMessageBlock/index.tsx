import React, { useState, useEffect } from 'react';
import { AgentThoughtChainMessageBlock } from '@youzan-cloud/cloud-biz-types';
import styles from './style.m.scss';
import classNames from 'classnames';
import MarkdownText from '../MarkdownText';
import TypewriterEffect from '../TypewriterEffect';

type ThoughtChainMessageBlockProps = Omit<AgentThoughtChainMessageBlock, 'type'> & {
  messageId?: string; // 添加 messageId 参数
};

const ThoughtChainMessageBlock = ({ thoughtChain, messageId }: ThoughtChainMessageBlockProps) => {
  const { steps, currentStep, isAutoCollapse, title } = thoughtChain;

  // 为每个步骤创建折叠状态数组
  const [collapsedSteps, setCollapsedSteps] = useState<boolean[]>(() => {
    // 初始化折叠状态：如果isAutoCollapse为true，只有当前步骤不折叠
    if (isAutoCollapse) {
      return steps.map((_, index) => index !== currentStep);
    }
    // 否则根据全局设置
    return steps.map(() => isAutoCollapse ?? true);
  });

  // 当步骤数量变化或当前步骤变化时，更新折叠状态数组
  useEffect(() => {
    if (isAutoCollapse) {
      // 如果是自动折叠模式，当前步骤展开，其他步骤折叠
      setCollapsedSteps(steps.map((_, index) => index !== currentStep));
    } else {
      // 否则保持原有设置
      setCollapsedSteps(prev => {
        if (prev.length !== steps.length) {
          return steps.map(() => isAutoCollapse ?? true);
        }
        return prev;
      });
    }
  }, [steps.length, currentStep, isAutoCollapse]);

  const onStepCollapse = (index: number) => {
    return (e: React.MouseEvent) => {
      e.stopPropagation(); // 防止事件冒泡
      setCollapsedSteps(prev => {
        const newState = [...prev];
        newState[index] = !newState[index];
        return newState;
      });
    };
  };

  const handleComplete = () => {
  };

  return (
    <div className={classNames(styles.thoughtChainMessageBlock)}>
      <div className={styles.titleContainer}>
        <span className={styles.title}>
          <img
            className={styles.titleIcon}
            src={
              (currentStep ?? 0) > steps.length - 1
                ? 'https://img01.yzcdn.cn/upload_files/2025/03/31/FnvlB3dWJaTH_A1D30Y-4dsQdPFX.png'
                : 'https://img01.yzcdn.cn/upload_files/2025/03/25/Fkggwmb65HQ5aXeqLmXVHvVBS4J_.gif'
            }
          />
          {title}
        </span>
      </div>
      {steps.map((step, index) => (
        <div key={index} className={styles.thoughtStepContainer}>
          <img
            className={styles.thoughtStepIcon}
            src={
              index === currentStep
                ? 'https://img01.yzcdn.cn/upload_files/2025/03/08/FkKzTBJF8gnx3CQTdXZKg955T158.png'
                : 'https://img01.yzcdn.cn/upload_files/2025/03/08/FvWPm_1-9S6rS__ZZ0NOGEKRwCdu.png'
            }
          />
          <div
            className={classNames(styles.thoughtStep, {
              [styles.current]: index === currentStep,
            })}
          >
            <div className={styles.thoughtTopic}>
              <MarkdownText>{step.topic}</MarkdownText>
            </div>
            <div
              className={classNames(styles.thoughtContentWrapper, {
                [styles.collapsed]: collapsedSteps[index],
              })}
            >
              <div className={styles.thoughtContent}>
                {index === currentStep ? (
                  <TypewriterEffect
                    text={step.content}
                    isComplete={currentStep !== steps.length - 1}
                    messageId={`${messageId}_step_${index}`}
                    onComplete={handleComplete}
                  />
                ) : (
                  <MarkdownText>{step.content}</MarkdownText>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ThoughtChainMessageBlock;
