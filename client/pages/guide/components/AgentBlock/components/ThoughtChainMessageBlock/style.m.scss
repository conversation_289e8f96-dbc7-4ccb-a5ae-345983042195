.thought-chain-message-block {
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f8f8;

  .title {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .title-icon {
      width: 16px;
      height: 16px;
    }

    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.4px;
  }

  .thought-step-container {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-left: 2px;
    position: relative;

    .thought-step-icon {
      width: 12px;
      height: 12px;
    }

    &::before {
      content: '';
      position: absolute;
      left: 5px;
      top: 18px;
      width: 1px;
      background-color: #ddd;
      z-index: 1;
      height: calc(100% - 12px - 8px);
      opacity: 0.6;
      border-radius: 1px;
    }

    &:last-child::before {
      content: '';
      position: absolute;
      left: 5px;
      top: 18px;
      width: 1px;
      background: linear-gradient(to bottom, #ddd, transparent);
      z-index: 1;
      height: calc(100% - 12px - 8px);
      opacity: 0.6;
      border-radius: 1px;
    }
  }

  .thought-step {
    margin-bottom: 12px;
    flex: 1;

    .thought-topic {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0.4px;
      display: flex;
      align-items: center;

      color: #666;

      .thought-topic-collapse-icon {
        width: 16px;
        height: 16px;
        margin-left: 4px;
        cursor: pointer;
        transition: transform 0.3s ease;
      }

      .thought-topic-collapse-icon-rotate {
        transform: rotate(180deg);
      }
    }

    .thought-content-wrapper {
      overflow: hidden;
      transition: max-height 0.3s ease, opacity 0.3s ease, margin-top 0.3s ease;
      max-height: 1000px; // 设置一个足够大的高度
      opacity: 1;
      margin-top: 8px;

      &.collapsed {
        max-height: 0;
        opacity: 0;
        margin-top: 0;
      }
    }

    .thought-content {
      color: #999;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0.4px;
    }
  }

  &.collapsed {
    .thought-step:not(.current) {
      display: none;
    }
  }
}
