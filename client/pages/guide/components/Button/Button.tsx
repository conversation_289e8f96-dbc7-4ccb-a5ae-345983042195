import React, { CSSProperties, ReactNode } from 'react';
import styles from './index.m.scss';
import cn from 'classnames';

interface IButtonProps {
  style?: CSSProperties;
  type: 'primary' | 'default' | 'blank';
  onClick: () => void;
  children: ReactNode;
}

const Button = (props: IButtonProps) => {
  const { onClick, type, style } = props;
  const className = cn(styles.guideButton, {
    [styles.primary]: type === 'primary',
    [styles.default]: type === 'default',
    [styles.blank]: type === 'blank',
  });
  return (
    <button style={style} className={className} onClick={onClick}>
      {props.children}
    </button>
  );
};

export default Button;
