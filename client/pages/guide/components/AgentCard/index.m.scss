.agent-container {
  padding         : 20px;
  display         : flex;
  flex            : 0 0 300px;
  flex-direction  : column;
  border-radius   : 4px;
  background-color: #FFFFFF;
  box-sizing      : border-box;

  .agent-icon {
    height: 32px;
    img {
      height: 32px;
    }
  }

  .agent-content {
    margin-top: 20px;

    .agent-title {
      .main {
        color      : #333333;
        font-size  : 16px;
        font-weight: 500;
        line-height: 24px;
      }

      .sub {
        margin-top : 4px;
        font-size  : 14px;
        font-weight: 400;
        line-height: 24px;
        min-height: 20px;
        opacity    : .3;
      }
    }

    .agent-detail {
      margin-top : 20px;
      font-size  : 14px;
      font-weight: 400;
      line-height: 24px;
      opacity    : .8;
    }
  }

  .agent-tag {
    margin-top: 20px;

    .agent-tag-good {
      font-size  : 14px;
      font-weight: 400;
      line-height: 24px;
      opacity    : .3;
    }

    .agent-good-tag {
      margin-top    : 8px;
      display       : flex;
      flex-direction: row;
      flex-wrap     : wrap;
      gap           : 8px;

      .tag {
        display      : inline-flex;
        padding      : 2px 8px;
        background   : #155BD40D;
        border-radius: 4px;
        font-size    : 14px;
        font-weight  : 400;
        line-height  : 24px;
        color        : #155BD4B2;
      }
    }
  }
}