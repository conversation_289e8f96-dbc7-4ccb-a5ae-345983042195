import jumpTo from 'fns/jumpTo';
import styles from './index.m.scss';

interface IAgentCardProps {
  icon: string;
  id: number;
  type: string;
  mainTitle: string;
  subTitle?: string;
  description: string;
  tags: string[];
  inUse?: boolean;
  url?: string;
}

const goTaskFlow = id => {
  jumpTo({ url: `/v4/jiawo/task-flow/${id}` });
};
const goText = id => {
  jumpTo({ url: `/v4/jiawo/aigc/text/${id}` });
};
const goPicture = id => {
  jumpTo({ url: `/v4/jiawo/aigc/image/${id}` });
};
const goOutLink = url => {
  jumpTo({ url });
};

export function goAgentDetail(type, id, url) {
  if (type === 'aigc_text') {
    goText(id);
    return;
  }
  if (type === 'aigc_image') {
    goPicture(id);
    return;
  }
  if (type === 'taskFlow' || type === 'business_report') {
    goTaskFlow(id);
    return;
  }
  if (type === 'out_link') {
    goOutLink(url);
    return;
  }
}

export const AgentCard = (props: IAgentCardProps) => {
  const { id, type, icon, mainTitle, subTitle, description, tags = [], inUse = true, url } = props;
  return (
    <div
      className={styles.agentContainer}
      style={inUse ? { cursor: 'pointer' } : {}}
      onClick={() => {
        if (!inUse) {
          return;
        }
        goAgentDetail(type, id, url);
      }}
    >
      <div className={styles.agentIcon}>
        <img src={icon} />
      </div>
      <div className={styles.agentContent}>
        <div className={styles.agentTitle}>
          <div className={styles.main}>{mainTitle}</div>
          <div className={styles.sub}>{subTitle}</div>
        </div>
        <div className={styles.agentDetail}>{description}</div>
      </div>
      <div className={styles.agentTag}>
        <div className={styles.agentGood}>擅长：</div>
        <div className={styles.agentGoodTag}>
          {tags.map(tag => (
            <div className={styles.tag}>{tag}</div>
          ))}
        </div>
      </div>
    </div>
  );
};
