import React, { useCallback, useEffect, useState } from 'react';
import styles from './index.m.scss';
import Button from '../Button/Button';
import Left from 'svg/Left';
import Right from 'svg/Right';
import { getAgentList } from 'pages/guide/api';
import jumpTo from 'fns/jumpTo';
import { BlockLoading } from 'zent';
import { AgentCard } from '../AgentCard/AgentCard';
import { navigateTo } from 'fns/link';

interface IConfirmProps {
  back: () => void;
}

const Confirm = (props: IConfirmProps) => {
  const { back } = props;
  const [loading, setLoading] = useState(false);
  const [offset, setOffset] = useState(0);
  const [agentList, setAgentList] = useState<any[]>([]);
  const calcLeft = (offset: number) => {
    const idx = Math.abs(offset);
    const pos = idx * 300 + idx * 20;
    if (offset < 0) {
      return -pos; // 宽度 + padding
    }
    return pos;
  };
  const goDashboard = useCallback(() => {
    navigateTo(`/v4/jiawo/dashboard/index`);
  }, []);
  const changeOffset = useCallback(
    (type: 'increase' | 'decrease') => {
      // 保留3个
      if (offset <= -(agentList.length - 3) && type === 'decrease') {
        return;
      }
      if (offset >= 0 && type === 'increase') {
        return;
      }
      if (type === 'increase') {
        setOffset(offset + 1);
      } else {
        setOffset(offset - 1);
      }
    },
    [offset, agentList],
  );
  const submit = useCallback(() => {
    goDashboard();
  }, []);
  useEffect(() => {
    setLoading(true);
    getAgentList()
      .then(res => {
        setAgentList(res.agents || []);
      })
      .finally(() => setLoading(false));
  }, []);
  return (
    <BlockLoading loading={loading} icon="youzan">
      <div className={styles.confirmContainer}>
        <div className={styles.confirmHeader}>
          <div className={styles.main}>以下是为你的团队构建的智能体</div>
          <div className={styles.sub}>
            可以根据你的业务模式来修改它们的具体工作方式，以达到更好的效果
          </div>
        </div>
        <div style={{ overflow: 'hidden' }}>
          <div className={styles.confirmContent} style={{ left: calcLeft(offset) }}>
            {agentList.map(config => {
              const props = {
                id: config.id,
                type: config.type,
                url: config.url,
                icon: config.icon,
                mainTitle: config.name,
                // subTitle: config.objective,
                description: config.description,
                tags: config.competenceFields,
              };
              return <AgentCard {...props} />;
            })}
          </div>
        </div>

        {agentList.length ? (
          <div style={{ marginTop: 12 }}>
            <span className={styles.arrow} onClick={() => changeOffset('increase')}>
              <Left />
            </span>
            <span
              style={{ marginLeft: 12 }}
              className={styles.arrow}
              onClick={() => changeOffset('decrease')}
            >
              <Right />
            </span>
          </div>
        ) : null}

        {agentList.length ? (
          <div className={styles.confirmBtn}>
            <Button style={{ marginRight: 12 }} type="primary" onClick={submit}>
              启动我的智能体
            </Button>
            <Button type="default" onClick={back}>
              修改团队信息
            </Button>
          </div>
        ) : null}
      </div>
    </BlockLoading>
  );
};

export default Confirm;
