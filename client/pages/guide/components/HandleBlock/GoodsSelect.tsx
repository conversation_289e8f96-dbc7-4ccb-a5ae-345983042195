import React from 'react';
import { Radio } from 'zent';

const GoodsSelect = ({ goods }) => {
  return (
    <div>
      <div className="goods-select-field">
        <div className="title">选择商品</div>
        <div className="count"> {goods.length}个</div>
      </div>
      <div className="goods-select-container">
        <div className="goods-select-list">
          <Radio.Group>
            {goods.map(good => (
              <Radio value={good.id}>
                <div className="goods-select-item">
                  <img src={good.picture} width={48} height={48} />
                  <div className="goods-select-item-info">
                    <div className="goods-select-item-name">{good.name}</div>
                    <div className="goods-select-item-price">￥{good.price}</div>
                  </div>
                </div>
              </Radio>
            ))}
          </Radio.Group>
        </div>
      </div>
    </div>
  );
};

export default GoodsSelect;
