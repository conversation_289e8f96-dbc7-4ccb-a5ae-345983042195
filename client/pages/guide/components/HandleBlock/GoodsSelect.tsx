import React, { useState } from 'react';
import { Radio, Button } from 'zent';
import './GoodsSelect.scss';
import useCustomReducer from '../../store';

const GoodsSelect = ({ goods }) => {
  const { setShowHandler } = useCustomReducer();
  const [selectedGoodsId, setSelectedGoodsId] = useState<any>(null);

  const handleConfirm = () => {
    setShowHandler(false);
  };

  return (
    <div className="goods-select-wrapper">
      <div className="goods-select-field">
        <div className="title">请选择商品</div>
      </div>
      <div className="goods-select-container">
        <div className="goods-select-list">
          <Radio.Group
            className="goods-radio-group"
            value={selectedGoodsId}
            onChange={e => setSelectedGoodsId(e.target.value)}
          >
            {goods.map(good => (
              <Radio key={good.id} value={good.id} className="goods-radio-item">
                <div className="goods-select-item">
                  <img
                    src={good.picture}
                    width={48}
                    height={48}
                    alt={good.name}
                    className="goods-image"
                  />
                  <div className="goods-select-item-info">
                    <div className="goods-select-item-name" title={good.name}>
                      {good.name}
                    </div>
                    <div className="goods-select-item-price">￥{good.price}</div>
                  </div>
                </div>
              </Radio>
            ))}
          </Radio.Group>
        </div>
      </div>
      <div>
        <Button outline type="primary" className="confirm-btn" onClick={handleConfirm}>
          确定
        </Button>
      </div>
    </div>
  );
};

export default GoodsSelect;
