

.goods-select-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .count {
    font-size: 14px;
    color: #666;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.goods-select-container {
  width: 100%;
}

.goods-select-list {
  width: 100%;
}

.goods-radio-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;

  // 重置 zent Radio 的默认样式
  :global(.zent-radio-wrap) {
    width: 100%;
    margin: 0;
  }

  :global(.zent-radio) {
    width: 100%;
    margin: 0;
  }
}

.goods-radio-item {
  width: 100%;
  margin-bottom: 12px !important;
  display: flex !important;
  align-items: center !important;; 
  
  // 重置 Radio 的默认样式
  :global(.zent-radio-inner) {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 2;
  }

  :global(.zent-radio-label) {
    width: 100%;
    padding: 0;
    margin: 0;
  }

  // 选中状态的样式
  &:global(.zent-radio-checked) {
    .goods-select-item {
      border-color: #1890ff;
      background-color: #f6ffed;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
  }

  // 悬停状态
  &:hover {
    .goods-select-item {
      border-color: #d9d9d9;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.goods-select-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px 16px 16px 48px; // 左侧留出空间给 radio 按钮
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.goods-image {
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
  margin-right: 12px;
}

.goods-select-item-info {
  flex: 1;
  min-width: 0; // 防止文本溢出
}

.goods-select-item-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.goods-select-item-price {
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
  line-height: 1.4;
}

// 响应式设计
@media (max-width: 768px) {
  .goods-select-wrapper {
    padding: 12px;
  }

  .goods-select-item {
    padding: 12px 12px 12px 40px;
  }

  .goods-image {
    width: 40px !important;
    height: 40px !important;
  }

  .goods-select-item-name {
    font-size: 13px;
  }

  .goods-select-item-price {
    font-size: 14px;
  }
}

// 空状态样式
.goods-select-empty {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;

  .empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
}

// 加载状态样式
.goods-select-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
}


.confirm-btn {
  margin-left: 24px;
}