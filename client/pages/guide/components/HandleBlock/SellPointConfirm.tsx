import React, { useState } from 'react';
import { Checkbox } from 'zent';

const SellPointConfirm = () => {
  const [checkedList, setCheckedList] = useState<string[]>([]);

  const list = ['Apple', 'Pear'];

  const handleChange = (checkedList: string[]) => {
    setCheckedList(checkedList);
  };

  return (
    <div>
      <h1>商品名称</h1>

      <p>卖点信息</p>

      <p>
        <Checkbox.Group value={checkedList} onChange={handleChange}>
          {list.map(item => () => (
            <Checkbox key={item} value={item}>
              {item}
            </Checkbox>
          ))}
        </Checkbox.Group>
      </p>
    </div>
  );
};

export default SellPointConfirm;
