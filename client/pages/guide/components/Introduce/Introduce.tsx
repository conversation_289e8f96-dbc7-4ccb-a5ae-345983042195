import React from 'react';
import styles from './index.m.scss';
import BotIcon from 'svg/Bot';
import Button from '../Button/Button';
import { isRetailHqStore } from '@youzan/utils-shop';

const introConfig = [
  [
    {
      icon: 'https://img01.yzcdn.cn/upload_files/2024/12/22/Fs_0Y68G_NPDS91Emb95016s3qsr.png',
      title: '微信小店托管',
      desc: '自动化运营，提升小店运营效率',
    },
    {
      icon: '🎳',
      title: '多渠道营销',
      desc: '多渠道店铺营销管理',
    },
    {
      icon: '⏳',
      title: '智能任务',
      desc: '自动化执行任务',
    },
  ],
  [
    {
      icon: '📝',
      title: '营销策划',
      desc: '策划店铺营销方案和执行策略',
    },
    {
      icon: '🎯',
      title: '商机推荐',
      desc: '发现商机、推荐转化策略',
    },
    {
      icon: '🏞',
      title: '创意生成',
      desc: '文案、图片创意生成',
    },
  ],
];

interface ICardProps {
  icon: string;
  title: string;
  desc: string;
}

interface IIntroduceProps {
  next: () => void;
}

const Card = (props: ICardProps) => {
  const { icon, title, desc } = props;
  return (
    <div className={styles.card}>
      <div className={styles.cardIcon}>
        {icon.startsWith('http') ? <img src={icon} alt="" /> : icon}
      </div>
      <div className={styles.cardTitle}>{title}</div>
      <div className={styles.cardDesc}>{desc}</div>
    </div>
  );
};

const Introduce = (props: IIntroduceProps) => {
  const { next } = props;
  const renderRow = (config: ICardProps[][]) => {
    return config.map(group => {
      return (
        <div className={styles.row}>
          {group.map(cfg => (
            <Card icon={cfg.icon} title={cfg.title} desc={cfg.desc} />
          ))}
        </div>
      );
    });
  };
  return (
    <div className={styles.introduceContainer}>
      <div className={styles.introduceHeader}>
        <div>
          <BotIcon />
        </div>
        <p className={styles.introduceHeaderDesc}>负责店铺运营的智能中心</p>
      </div>
      <div className={styles.introduceContent}>{renderRow(introConfig)}</div>
      <div className={styles.introduceBtn}>
        <Button type="primary" onClick={next}>
          启动我的智能体 →
        </Button>
      </div>
    </div>
  );
};

export default Introduce;
