.introduce-container {
  display: flex;
  flex-direction: column;
  width: 872px;
  margin-top: 6.6vh;

  .introduce-header {
    display: flex;
    flex-direction: column;
    .introduce-header-desc {
      height: 40px;
      line-height: 40px;
      margin-top: 20px;
      font-size: 28px;
      font-weight: 500;
      color: #333333;
    }
  }
  .introduce-content {
    margin-top: 60px;
    text-align: center;
    .row {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 12px;
      &:not(:first-child) {
        margin-top: 12px;
      }
      .card {
        width: 280px;
        padding: 12px;
        border-radius: 4px;
        box-sizing: border-box;
        background-color: #FFFFFF;
        .card-icon {
          font-size: 20px;
          font-weight: 500;
          line-height: 20px;
          width: 22px;
          height: 20px;
          margin: 0 auto;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .card-title {
          margin-top: 12px;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          color: #333333;
        }
        .card-desc {
          margin-top: 4px;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          color: #999999;
        }
      }
    }
  }
  .introduce-btn {
    margin-top: 60px;
  }
}
