.form-container {
  display: flex;
  flex-direction: column;
  padding: 0 84px;
  width: 100%;

  .form-header {
    color: #333333;
    font-size: 24px;
    font-weight: 500;
    line-height: 40px;
  }
  .form-content {
    margin-top: 60px;

    .form-question {
      color: #0D0C21;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }

    .form-input {
      margin-top: 20px;
      display: flex;
      flex-direction: row;
      height: auto;

      .form-input-text-area {
        flex: 1;
        div {
          height: 100%!important;
          border: none;
          border-radius: 4px;
        }
        textarea {
          padding: 12px;
          font-size: 14px;
          font-weight: 400;
          height:  100%;
          resize: none;
        }
      }
      .form-input-tag {
        margin-left: 8px;
        .row {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          gap: 8px;
          &:not(:first-child) {
            margin-top: 12px;
          }
        }
      }
    }
  }
  .form-btn {
    margin-top: 60px;
  }
}
