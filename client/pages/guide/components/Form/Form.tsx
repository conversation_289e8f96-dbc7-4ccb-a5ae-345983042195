import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Input, Notify } from 'zent';
import styles from './index.m.scss';
import Button from '../Button/Button';
import Tick from 'svg/Tick';
import { createAgent } from 'pages/guide/api';
import { isWscShop } from '@youzan/utils-shop';

const Tips = [
  ['门店履约管理', '商品管理与盘点'],
  ['线上多渠道经营', '营销活动策划'],
  ['内容创意推广', '客户运营'],
  ['客服服务'],
];

const WscTips = [
  ['商品管理与盘点', '线上多渠道经营'],
  ['营销活动策划', '内容创意推广'],
  ['客户运营', '客服服务'],
];

interface IFormProps {
  next: () => void;
  prevValue: string;
  setObjective: (objective: string) => void;
}

const Form = (props: IFormProps) => {
  const { next, prevValue, setObjective } = props;
  const [desc, setDesc] = useState(prevValue);
  const ref = useRef<any>(null);

  const insertAtCursor = useCallback(
    text => {
      if (!ref.current) {
        return;
      }
      if (!text) {
        return;
      }

      const compRef = ref.current!;
      const elementRef = compRef.elementRef.current;

      const startPos = elementRef.selectionStart;
      const endPos = elementRef.selectionEnd;
      const scrollTop = elementRef.scrollTop;

      if (!desc) {
        setDesc(`${text}`);
      } else {
        const value = `${desc.substring(0, startPos)}、${text}${desc.substring(
          endPos,
          desc.length,
        )}`;
        setDesc(value);
      }

      elementRef.focus();
      elementRef.selectionStart = startPos + text.length;
      elementRef.selectionEnd = startPos + text.length;
      elementRef.scrollTop = scrollTop;
    },
    [ref, desc],
  );

  const renderTags = useMemo(() => {
    return (isWscShop ? WscTips : Tips).map(group => {
      return (
        <div className={styles.row}>
          {group.map(tag => {
            const value = tag === '更多..' ? '' : tag;
            return (
              <Button style={{ width: 146 }} type="blank" onClick={() => insertAtCursor(value)}>
                {tag}
              </Button>
            );
          })}
        </div>
      );
    });
  }, [desc]);
  const submit = useCallback(() => {
    if (!desc) {
      Notify.info('请告诉我您需要什么帮助');
      return;
    }
    setObjective(desc);
    createAgent(desc)
      .then(() => {
        next();
      })
      .catch(err => {
        Notify.error(err);
      });
  }, [desc]);
  return (
    <div className={styles.formContainer}>
      <div className={styles.formHeader}>
        <div>开始之前，</div>
        <div>先让我了解一下关于你的必要信息</div>
      </div>
      <div className={styles.formContent}>
        <div className={styles.formQuestion}>你希望我能帮你的团队做什么？</div>
        <div className={styles.formInput}>
          <div className={styles.formInputTextArea}>
            <Input
              ref={ref}
              value={desc}
              type="textarea"
              placeholder="简单描述想得到的帮助，或者直接选择右侧的关键词"
              onChange={e => setDesc(e.target.value)}
            />
          </div>
          <div className={styles.formInputTag}>{renderTags}</div>
        </div>
      </div>
      <div className={styles.formBtn}>
        <Button type="primary" onClick={submit}>
          <span>
            开始
            <Tick style={{ position: 'relative', top: 2, left: 10 }} />
          </span>
        </Button>
      </div>
    </div>
  );
};

export default Form;
