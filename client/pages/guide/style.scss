#app-header, #shared-sidebar, #app-footer {
  display: none !important;
}
body.theme.theme-new-ui div#app-container.container {
  margin: 0 !important;
  padding: 0;
}


.contianer {
  display: flex;
  height: calc(100vh - 76px);
  overflow: hidden;

  .message-board-wrap {
    flex: 1;
    padding: 12px;
    box-shadow: 2px 0 2px 0 rgba(0, 0, 0, 0.04);
  }
  @keyframes widthGrow {
    from { width: 0; }
    to { width: 50%; }
  }

  .handle-board-wrap {
    padding: 12px;
    // display: none;
    animation: widthGrow 0.5s ease-in-out forwards;

  }
}