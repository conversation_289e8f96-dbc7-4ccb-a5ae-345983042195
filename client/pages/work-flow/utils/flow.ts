import { ITask, TemplateStyle } from '../constant';
import { IExpressions } from '../constant/flow';

const baseFormatter = (
  chainRules: ITask[],
  calcOpenField: (openField: IExpressions['openField']) => number,
) =>
  chainRules?.map(rule => ({
    ...rule,
    conditions:
      rule.conditions?.map(condition => {
        return {
          ...condition,
          expressions: condition.expressions.map(expression => {
            const formatType = JSON.parse(expression.variable.extension || '{}')?.variablePattern
              ?.styleTemplate?.template;
            const isYuanUnit = formatType === TemplateStyle.ChinaYuanStyle;
            const result = {
              ...expression,
              openField: isYuanUnit
                ? calcOpenField(expression.openField).toString()
                : expression.openField,
            };

            /**
             * ? https://jira.qima-inc.com/browse/CSWT-164662
             */
            // const { variable } = expression;
            // // @ts-ignore
            // if (variable.code === 'payTime') {
            //   try {
            //     // @ts-ignore
            //     result.timeValue = JSON.parse(expression.openField);
            //   } catch (error) {}
            // }

            return result;
          }),
        };
      }) || [],
    delayComponent: (delay => {
      if (!delay) return;

      return {
        delayTime: delay.delayTime,
        timeUnit: delay.timeUnit,
        variableId: delay.variableId,
        beforeOrAfter: delay.beforeOrAfter,
      };
    })(rule.delayComponent),
  }));

// 传给后端 * 100
export const formatCondition = (chainRules: ITask[]) =>
  baseFormatter(chainRules, v => Number(v) * 100);

// 展示时 / 100
export const formatConditionWhenGet = (chainRules: ITask[]) =>
  baseFormatter(chainRules, v => Number(v) / 100);

/**
 * 尝试将给定的字符串解析为 JSON 对象
 * 如果字符串为空或解析失败，则返回一个空对象
 * @param data - 要解析的字符串
 * @returns 解析后的 JSON 对象或空对象
 */
export function parseJSON(data) {
  if (!data) {
    return {};
  }
  try {
    return JSON.parse(data);
  } catch (error) {
    return {};
  }
}
