import ajax from 'zan-pc-ajax';

export const defaultGetToken = () =>
  ajax('/v4/goods/materials/getFileUploadToken', { method: 'get' });

export function upload(token: string, file: Blob) {
  const formData = new FormData();
  formData.append('token', token);
  formData.append('file', file);

  return ajax('https://up.qbox.me', {
    method: 'post',
    contentType: 'application/json',
    data: formData,
  });
}
