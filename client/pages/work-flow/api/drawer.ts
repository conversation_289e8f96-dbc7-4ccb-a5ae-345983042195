import ajax from 'zan-pc-ajax';

const prefix = '/v4/jiawo/api/work-flow';

// 查询triggers
export const getListTriggers = async (params: any) => {
  return ajax(`${prefix}/listTriggers`, { data: params, method: 'get' });
};

// 查询变量列表
export const getListVariablesByTrigger = async (params: any) => {
  return ajax(`${prefix}/listVariablesByTrigger`, { data: params, method: 'get' });
};

// 查询操作符列表
export const getListOperatorsByVariable = async (params: any) => {
  return ajax(`${prefix}/listOperatorsByVariable`, { data: params, method: 'get' });
};

// 查询变量可用值列表
export const getListFieldsByTriggerAndVariable = async (params: any) => {
  return ajax(`${prefix}/listFieldsByTriggerAndVariable`, { data: params, method: 'get' });
};

// 查询变量可用值列表
export const checkSendMsgActionChannelPurview = async () => {
  return ajax(`${prefix}/checkSendMsgActionChannelPurview`, { method: 'get' });
};

// 查询执行器列表
export const getListActionsByTrigger = async (params: any) => {
  return ajax(`${prefix}/listActions`, { data: params, method: 'get' });
};
// 查询action串联的action列表
export const getListCanBeConcatenateActions = async (params: any) => {
  return ajax(`${prefix}/listCanBeConcatenateActions`, { data: params, method: 'get' });
};

// 校验文本安全
export const checkMsgContent = async (params: any) => {
  return ajax(`${prefix}/checkMsgContent`, { data: params, method: 'get' });
};

export const checkVariableFileUpload = async (params: any) => {
  return ajax(`${prefix}/checkVariableFile`, {
    data: params,
    method: 'post',
  });
};

export const checkVariablePurview = async (params: any) => {
  return ajax(`${prefix}/checkVariablePurview`, {
    data: params,
    method: 'get',
  });
};
