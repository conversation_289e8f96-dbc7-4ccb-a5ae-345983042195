// 用于承载画布相关函数

import { register } from '@antv/x6-react-shape';
import { ECustomNodeName, ENodeType, nodeTypeMap } from '../constant';
import TriggerNode from '../components/WorkFlowGraph/components/TriggerNode';
import ConditionNode from '../components/WorkFlowGraph/components/ConditionNode';
import { Graph } from '@antv/x6';
import { getAddNodeEdgeConfig } from '../components/WorkFlowGraph/config/graph-config';
import { ICreateNodeParams } from '../constant/graph';
import ActionNode from '../components/WorkFlowGraph/components/ActionNode';
import DelayNode from '../components/WorkFlowGraph/components/DelayNode';

// 注册trigger节点
export const registerTriggerNode = () => {
  register({
    shape: ECustomNodeName.trigger,
    width: 100,
    height: 100,
    component: TriggerNode,
  });
};

// 注册condition节点
export const registerConditionNode = () => {
  register({
    shape: ECustomNodeName.condition,
    width: 100,
    height: 100,
    component: ConditionNode,
  });
};

// 注册action节点
export const registerActionNode = () => {
  register({
    shape: ECustomNodeName.action,
    width: 100,
    height: 100,
    component: ActionNode,
  });
};

// 注册delay节点
export const registerDelayNode = () => {
  register({
    shape: ECustomNodeName.delay,
    width: 100,
    height: 100,
    component: DelayNode,
  });
};

// 创建节点
export const createNode = ({ data, x, y, graph }: ICreateNodeParams) => {
  const { type } = data;
  return {
    shape: nodeTypeMap[type],
    x,
    y,
    width: 276,
    height: 92,
    // 传输数据
    data,
    label: {
      autoFit: true,
    },
  };
};

// 添加子节点
export const addChildNode = (parentNode, graph: Graph, data) => {
  console.log('parentNode is ', parentNode, graph, data);
  if (graph) {
    const position = parentNode.position();
    const size = parentNode.size();

    const siblings = graph.getNeighbors(parentNode, { outgoing: true });
    //   const lastSibling = siblings.sort((a, b) => a.position().y - b.position().y)[siblings.length - 1];
    const lastSibling = siblings[siblings.length - 1];
    console.log('lastSibling is ', siblings, lastSibling);
    const lastSiblingBBox = lastSibling?.getBBox();

    const childNodeX = lastSiblingBBox ? lastSiblingBBox.x : position.x + size.width + 100;
    const childNodeY = lastSiblingBBox
      ? lastSiblingBBox.y + lastSiblingBBox.height + 100
      : position.y;

    // 这里是为了对 seriesActions 进行改造，需要在创建时给 action 节点添加一个针对 action 的 index
    const parentNodeType = parentNode.getData().type;
    if (data.type === ENodeType.Action) {
      if (parentNodeType === ENodeType.Action) {
        data.actionIndex = parentNode.getData().actionIndex + 1;
      } else {
        // 为什么是 -1  因为在结构中 action 和 seriesActions 被分开了，为了方便 seriesActions 中 action 的 index 从 0 开始，第一个 action 不能为 0
        data.actionIndex = -1;
      }
    }

    const childNode = graph.addNode(
      createNode({
        data,
        x: childNodeX,
        y: childNodeY,
        graph,
      }),
    );

    graph.addEdge(getAddNodeEdgeConfig({ parentNode, childNode })); // 连线
  }
};

export const getGraphData = (graph: Graph) => {
  return graph.toJSON();
};
