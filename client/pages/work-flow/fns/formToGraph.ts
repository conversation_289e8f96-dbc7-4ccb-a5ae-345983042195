// @ts-nocheck
import { v4 as uuidv4 } from 'uuid';
import { ENodeType, IFlow } from '../constant';

// 定义节点接口
interface Node {
  id: string;
  position: { x: number; y: number };
  view: string;
  shape: string;
  size: { width: number; height: number };
  zIndex: number;
  data: any; // 这里需要根据实际数据结构定义更具体的类型
  label?: { autoFit?: boolean };
}

// 定义边接口
interface Edge {
  shape: string;
  attrs: {
    line: {
      stroke: string;
      targetMarker: { name: string; size: number };
    };
  };
  id: string;
  source: { cell: string };
  target: { cell: string };
  anchor: { source: string; target: string; name: string; args?: { dx?: number } };
  connectionPoint: string;
}

// 定义转换后的图数据接口
interface GraphData {
  cells: Array<Node | Edge>;
}

// 生成唯一的节点 ID
function generateNodeId(): string {
  return `${uuidv4()}`;
}

// 计算节点位置
function calculateNodePosition(xIndex: number, yIndex: number): { x: number; y: number } {
  return { x: 100 + xIndex * (276 + 100), y: 100 + yIndex * (92 + 100) };
}

// 创建初始节点
function createInitialNode(flowData): Node {
  return {
    id: generateNodeId(),
    position: calculateNodePosition(0, 0),
    view: 'react-shape-view',
    shape: 'trigger-node',
    size: { width: 276, height: 92 },
    zIndex: 1,
    data: {
      type: 'trigger',
      data: flowData.triggerDefine,
    },
    label: { autoFit: true },
  };
}

// 创建条件或动作节点
function createConditionNode(
  data: any,
  xIndex: number,
  index: number,
  positionIndex: number,
): Node {
  console.log('conditionNode data is ', data);
  return {
    id: generateNodeId(),
    position: calculateNodePosition(xIndex, positionIndex),
    view: 'react-shape-view',
    shape: 'condition-node',
    size: { width: 276, height: 92 },
    zIndex: index + 2,
    data: { type: 'condition', data, index },
    label: { autoFit: true },
  };
}

function createActionNode(
  data: any,
  xIndex: number,
  index: number,
  positionIndex: number,
  parentNode: Node,
): Node {
  console.log('createAction node parentNode is ', parentNode);
  const actionIndex =
    parentNode.data.type === ENodeType.Action ? parentNode.data.actionIndex + 1 : -1;
  return {
    id: generateNodeId(),
    position: calculateNodePosition(xIndex, positionIndex),
    view: 'react-shape-view',
    shape: 'action-node',
    size: { width: 276, height: 92 },
    zIndex: index + 2,
    data: {
      type: 'action',
      data,
      index,
      parentNode,
      actionIndex,
    },
    label: { autoFit: true },
  };
}

function createDelayNode(
  data: any,
  xIndex: number,
  index: number,
  positionIndex: number,
  parentNode: Node,
): Node {
  return {
    id: generateNodeId(),
    position: calculateNodePosition(xIndex, positionIndex),
    view: 'react-shape-view',
    shape: 'delay-node',
    size: { width: 276, height: 92 },
    zIndex: index + 2,
    data: { type: 'delay', data, index, parentNode },
    label: { autoFit: true },
  };
}
// 创建边
function createEdges(previousNodeId: string, nextNodeId: string): Edge {
  return {
    shape: 'edge',
    attrs: {
      line: {
        stroke: '#155BD4',
        targetMarker: { name: 'classic', size: 7 },
      },
    },
    id: generateNodeId(),
    source: { cell: previousNodeId, anchor: { name: 'right' }, connectionPoint: 'anchor' },
    target: { cell: nextNodeId, anchor: { name: 'left' }, connectionPoint: 'anchor' },
    anchor: { source: 'bottomRight', target: 'left', name: 'midSide', args: { dx: 10 } },
    connectionPoint: 'anchor',
  };
}

// 主转换函数
export function convertFormDataToGraphData(formData: IFlow): GraphData {
  const graphData: GraphData = {
    cells: [],
  };

  // 创建初始节点
  graphData.cells.push(createInitialNode(formData));

  // 遍历 chainRules 生成条件和动作节点
  let positionIndex = -1; // 这个positionIndex是为了计算实际被渲染的节点位置，因为有可能有被删除的节点，所以和index不一定一致
  if (formData.chainRules) {
    formData.chainRules.forEach((rule, index) => {
      let prevNode = graphData.cells[0];

      // 拦截被删除的不被渲染的flow分支，为啥不用filter呢？会导致index错乱
      const isDelete = rule.conditions?.length === 0 && !rule.action.id && !rule.delayComponent;
      if (isDelete) return;
      positionIndex += 1;

      let xIndex = 0;

      // ! 优先处理延迟节点 因为它总是在 trigger 后面
      if (rule.delayComponent) {
        xIndex += 1;
        const delayCell = createDelayNode(
          rule.delayComponent,
          xIndex,
          index,
          positionIndex,
          prevNode,
        );

        const previousNodeId = graphData.cells[0].id;
        const nextNodeId = delayCell.id;

        graphData.cells.push(delayCell);
        graphData.cells.push(createEdges(previousNodeId, nextNodeId));

        prevNode = delayCell;
      }

      let conditionCell = null;
      // 创建条件节点
      if (
        rule.conditions &&
        rule.conditions.length &&
        rule.conditions.find(condition => condition.expressions?.length)
      ) {
        xIndex += 1;

        conditionCell = createConditionNode(rule.conditions, xIndex, index, positionIndex);
        graphData.cells.push(conditionCell);

        //  // 如果有condition节点，则它的父节点一定是初始节点，不需要对xIndex做判断，直接链接初始节点
        // 还能是延迟节点
        const previousNodeId = prevNode.id;
        const nextNodeId = conditionCell.id;
        graphData.cells.push(createEdges(previousNodeId, nextNodeId));

        prevNode = conditionCell;
      }

      // 创建动作节点
      if (rule.action && rule.action.id) {
        xIndex += 1;

        const currentCell = createActionNode(rule.action, xIndex, index, positionIndex, prevNode);
        graphData.cells.push(currentCell);
        // action节点的父节点可能是condition也可能是初始节点，需要对xIndex做判断
        if (xIndex === 1) {
          // 说明父节点是初始节点
          const previousNodeId = graphData.cells[0].id;
          const nextNodeId = currentCell.id;
          graphData.cells.push(createEdges(previousNodeId, nextNodeId));
        } else {
          // ? 父节点是前一个条件节点 这里逻辑有点奇怪，为啥不都用 prevNode
          const previousNodeId = (conditionCell || prevNode).id;
          const nextNodeId = currentCell.id;
          graphData.cells.push(createEdges(previousNodeId, nextNodeId));
        }

        prevNode = currentCell;

        // 之所以放在创建动作节点的时候判断，是因为只有动作节点存在的情况下才会有 seriesActions
        // 新增：如果存在 seriesActions，则创建额外的动作节点
        if (rule.seriesActions && Array.isArray(rule.seriesActions) && rule.seriesActions.length > 0) {
          // 遍历 seriesActions 数组，为每个动作创建节点
          rule.seriesActions.forEach(seriesActions => {
            if (seriesActions.id) {
              xIndex += 1; // 为每个 seriesActions 增加一个 xIndex 以保持顺序

              const seriesActionsCell = createActionNode(
                seriesActions,
                xIndex,
                index,
                positionIndex,
                prevNode, // 使用 prevNode 作为父节点，这样每个 seriesActions 节点都会连接到前一个节点
              );
              graphData.cells.push(seriesActionsCell);

              // 创建边连接前一个节点和当前 seriesActions 节点
              const previousNodeId = prevNode.id;
              const nextNodeId = seriesActionsCell.id;
              graphData.cells.push(createEdges(previousNodeId, nextNodeId));

              prevNode = seriesActionsCell; // 更新 prevNode 为当前节点，以便下一个节点连接
            }
          });
        }
      }
    });
  }

  // 创建边连接节点
  /* graphData.cells.forEach((cell, index) => {
    if (index === 0) return; // 跳过初始节点
    const previousNodeId = graphData.cells[index - 1].id;
    const nextNodeId = cell.id;
    graphData.cells.push(createEdges(previousNodeId, nextNodeId));
  }); */

  return graphData;
}

// 示例表单数据
const formData: IFlow = {
  // ...你的表单数据结构

  name: 'test123',
  scenes: '商品库存变更',
  triggerId: 6,
  triggerDefine: {
    attributes: 'goodsId',
    code: 'goodsStockUpdate',
    corpus: '',
    description: '商品库存变更',
    id: 6,
    name: '商品库存变更',
  },
  status: 0,
  chainRules: [
    {
      conditions: [
        {
          expressions: [
            {
              field: '',
              operator: {
                compareType: 1,
                description: '小于',
                id: 1,
                name: '小于',
                symbol: '<',
                text: '小于',
                key: 1,
              },
              variable: {
                code: 'stockNum',
                description: '商品库存数量',
                dynamicEndpointConfig: '',
                id: 2,
                name: '商品库存数',
                type: 1,
                validateRule: '^[0-9]+[0-9]*$',
                valueSource: 1,
                text: '商品库存数',
                key: 2,
              },
              value: '',
              openField: '1',
            },
          ],
        },
      ],
      action: {
        code: 'SendShopMsg',
        description: '发送店铺消息提醒',
        id: 8,
        name: '发送店铺消息提醒',
        type: 'sendShopMsg',
        variables: [
          {
            code: 'receiver',
            description: '请选择消息接收人',
            dynamicEndpointConfig: '{"apiId":"storeStaff","responseCovert":"id"}',
            id: 25,
            name: '消息接收人',
            type: 1,
            validateRule: '',
            valueSource: 3,
          },
        ],
        actionFieldValueMaps4Execute: '{"receiver":[8228736986]}',
        actionFieldValueMaps4Display:
          '{"receiver":[{"id":8228736986,"linkPhone":"15626148136","name":"吴炜亮","roles":["高级管理员","普通管理员"],"key":8228736986,"text":"吴炜亮"}]}',
      },
      seriesActions: {
        code: 'SendShopMsg',
        description: '发送店铺消息提醒',
        id: 8,
        name: '发送店铺消息提醒',
        type: 'sendShopMsg',
        variables: [
          {
            code: 'receiver',
            description: '请选择消息接收人',
            dynamicEndpointConfig: '{"apiId":"storeStaff","responseCovert":"id"}',
            id: 25,
            name: '消息接收人',
            type: 1,
            validateRule: '',
            valueSource: 3,
          },
        ],
        actionFieldValueMaps4Execute: '{"receiver":[8228736986]}',
        actionFieldValueMaps4Display:
          '{"receiver":[{"id":8228736986,"linkPhone":"15626148136","name":"吴炜亮","roles":["高级管理员","普通管理员"],"key":8228736986,"text":"吴炜亮"}]}',
      },
    },
    {
      conditions: [],
      action: {
        code: 'SendShopMsg',
        description: '发送店铺消息提醒',
        id: 8,
        name: '发送店铺消息提醒',
        type: 'sendShopMsg',
        variables: [
          {
            // @ts-ignore
            code: 'receiver',
            description: '请选择消息接收人',
            dynamicEndpointConfig: '{"apiId":"storeStaff","responseCovert":"id"}',
            id: 25,
            name: '消息接收人',
            type: 1,
            // @ts-ignore
            validateRule: '',
            valueSource: 3,
          },
        ],
        actionFieldValueMaps4Execute: '{"receiver":[8345336773]}',
        actionFieldValueMaps4Display:
          '{"receiver":[{"id":8345336773,"linkPhone":"15533332130","name":"15533332130","roles":["导购员"],"key":8345336773,"text":"15533332130"}]}',
      },
    },
  ],
};

// 转换并打印结果
const graphData = convertFormDataToGraphData(formData);
console.log(graphData);
