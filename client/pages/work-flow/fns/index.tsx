import { EGroupCode, ENodeType, EValueSource, IVariable } from '../constant';
import { parseJSON } from '../utils/flow';

const colorByType = type => {
  const colorMapByType = {
    [ENodeType.Trigger]: '#A552E0',
    [ENodeType.Condition]: '#2DB26F',
    [ENodeType.Action]: '#E57A2E',
  };
  return colorMapByType[type];
};

const IconByGroup = ({ groupCode, type }) => {
  const iconMapByGroup = {
    [EGroupCode.GOODS]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.1"
          d="M0 2.85732C0 1.27927 1.27927 0 2.85732 0H17.1434C18.7215 0 20.0007 1.27927 20.0007 2.85732V17.1434C20.0007 18.7215 18.7215 20.0007 17.1434 20.0007H2.85732C1.27927 20.0007 0 18.7215 0 17.1434V2.85732Z"
          fill={colorByType(type)}
        />
        <path
          d="M5.2809 5.3383C5.3162 4.57577 5.94469 3.97571 6.70803 3.97571H13.2925C14.0558 3.97571 14.6843 4.57578 14.7196 5.3383L15.1694 15.0529C15.207 15.867 14.5572 16.5476 13.7422 16.5476H6.25828C5.44332 16.5476 4.79346 15.867 4.83115 15.0529L5.2809 5.3383Z"
          fill={colorByType(type)}
        />
        <path
          d="M7.3823 5.99988C7.38167 6.02353 7.38135 6.04725 7.38135 6.07106C7.38135 7.51757 8.55398 8.69021 10.0005 8.69021C11.447 8.69021 12.6197 7.51757 12.6197 6.07106C12.6197 6.04725 12.6193 6.02353 12.6187 5.99988H11.5704C11.5714 6.02356 11.572 6.04738 11.572 6.07133C11.572 6.93924 10.8684 7.64282 10.0005 7.64282C9.13258 7.64282 8.429 6.93924 8.429 6.07133C8.429 6.04738 8.42954 6.02356 8.4306 5.99988H7.3823Z"
          fill="#F7F7F7"
        />
      </svg>
    ),
    [EGroupCode.ORDER]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="20" height="20" rx="4" opacity="0.1" fill={colorByType(type)} />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.45898 4.68848C5.97007 4.68848 5.57373 5.08482 5.57373 5.57373V14.4263C5.57373 14.9152 5.97007 15.3115 6.45898 15.3115H13.541C14.0299 15.3115 14.4263 14.9152 14.4263 14.4263V5.57373C14.4263 5.08482 14.0299 4.68848 13.541 4.68848H6.45898ZM12.6558 6.39863H7.34424V7.28388H12.6558V6.39863ZM7.34424 12.0522H10V12.9374H7.34424V12.0522ZM12.6558 9.2355H7.34424V10.1208H12.6558V9.2355Z"
          fill={colorByType(type)}
        />
      </svg>
    ),
    [EGroupCode.SYSTEM_COMMON]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.1"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z"
          fill={colorByType(type)}
        />
        <path
          d="M4.48887 4.07458C3.93545 4.07458 3.48682 4.52322 3.48682 5.07663V13.093C3.48682 13.6464 3.93545 14.0951 4.48887 14.0951H5.99194V15.7247C5.99194 15.8848 6.17033 15.9802 6.30352 15.8915L8.99809 14.0951H15.5114C16.0648 14.0951 16.5135 13.6464 16.5135 13.093V5.07663C16.5135 4.52322 16.0648 4.07458 15.5114 4.07458H4.48887Z"
          fill={colorByType(type)}
        />
        <path d="M7.99609 8.08276H8.99814V9.58584H7.99609V8.08276Z" fill="#F7F7F7" />
        <path d="M11.0022 8.08276H12.0043V9.58584H11.0022V8.08276Z" fill="#F7F7F7" />
      </svg>
    ),
    [EGroupCode.UMP]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.1"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z"
          fill={colorByType(type)}
        />
        <path
          d="M12.1464 3.58566C12.3417 3.39039 12.6583 3.39039 12.8536 3.58566C13.0488 3.78092 13.0488 4.0975 12.8536 4.29276L12.2733 4.87305C11.8573 5.28903 12.1519 6.00029 12.7402 6.00029H14.5C15.6046 6.00029 16.5 6.89572 16.5 8.00029V13.5003C16.5 14.6049 15.6046 15.5003 14.5 15.5003H5.5C4.39543 15.5003 3.5 14.6049 3.5 13.5003V8.00029C3.5 6.89572 4.39543 6.00029 5.5 6.00029H7.46691C8.05518 6.00029 8.34979 5.28904 7.93382 4.87307L7.35355 4.29281C7.15829 4.09755 7.15829 3.78096 7.35355 3.5857C7.54882 3.39044 7.8654 3.39044 8.06066 3.5857L9.39642 4.92147C9.78695 5.31199 10.4201 5.31199 10.8106 4.92147L12.1464 3.58566Z"
          fill={colorByType(type)}
        />
        <rect x="9.5" y="6.00012" width="1" height="4" fill="#F7F7F7" />
      </svg>
    ),
    [EGroupCode.WECOM_HELPER]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.1"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z"
          fill={colorByType(type)}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.1133 9.66882C13.9638 9.70883 13.8269 9.78885 13.7194 9.8963C13.3741 10.2374 13.3699 10.7935 13.7111 11.1389L13.7216 11.1494C13.8669 11.2927 14.0564 11.3811 14.2586 11.4042C14.8484 11.5117 15.3876 11.8065 15.794 12.2447C15.8782 12.3289 16.013 12.3289 16.0973 12.2467C16.1816 12.1667 16.1837 12.034 16.1037 11.9498C16.0931 11.9393 16.0805 11.9287 16.0679 11.9203C15.6024 11.4906 15.3012 10.9135 15.217 10.2859C15.0884 9.81414 14.5935 9.5382 14.1133 9.66882ZM13.2798 11.2819L13.2651 11.2967C12.8248 11.7748 12.2289 12.0823 11.5822 12.1644C11.1063 12.2887 10.8197 12.7689 10.9461 13.2407C10.9861 13.3945 11.0705 13.5355 11.1842 13.6471C11.5359 13.9926 12.1045 13.9926 12.4562 13.6451C12.5995 13.5018 12.6901 13.3144 12.7132 13.1143C12.8248 12.5288 13.126 11.9959 13.5704 11.5978C13.6568 11.52 13.6652 11.3872 13.5894 11.3009L13.5873 11.2988C13.503 11.2103 13.3682 11.2041 13.2798 11.2819ZM16.6568 12.1667C16.5051 12.2067 16.3662 12.2867 16.2524 12.3984H16.2503C16.105 12.5416 16.0144 12.7291 15.9934 12.9312C15.8838 13.5168 15.5826 14.0496 15.1403 14.4477C15.0561 14.5277 15.054 14.6604 15.134 14.7446L15.1361 14.7468C15.2204 14.831 15.3573 14.8331 15.4437 14.7488C15.4542 14.7383 15.4626 14.7299 15.4689 14.7172C15.9091 14.256 16.4946 13.959 17.1265 13.879C17.6067 13.7506 17.8889 13.264 17.7605 12.7923C17.6299 12.3184 17.137 12.0383 16.6568 12.1667ZM7.84925 3.62834C6.24012 3.80314 4.77845 4.48344 3.72958 5.54499C3.32097 5.95355 2.98182 6.42529 2.72278 6.94134C1.92873 8.51891 2.0615 10.4039 3.07038 11.8529C3.35462 12.2783 3.82427 12.8091 4.25191 13.1861L4.05814 14.6879L4.03705 14.751C4.03069 14.77 4.03069 14.791 4.02863 14.81L4.02441 14.8584L4.02863 14.9068C4.05394 15.1743 4.2919 15.3702 4.55939 15.3471C4.63313 15.3407 4.70265 15.3176 4.7658 15.2818H4.77422L4.80376 15.2607L5.26702 15.0332L6.64664 14.3487C7.30163 14.534 7.98192 14.6268 8.66427 14.6226C9.50672 14.6247 10.345 14.4814 11.139 14.1971C10.7431 14.0707 10.4883 13.6853 10.5282 13.2703C9.70686 13.5315 8.83909 13.6158 7.98395 13.5211L7.84708 13.502C7.53753 13.462 7.23211 13.3989 6.93303 13.3146C6.7687 13.264 6.59399 13.283 6.44231 13.3652L6.40433 13.3841L5.26919 14.0434L5.22076 14.0728C5.1933 14.0876 5.18075 14.0939 5.16594 14.0939C5.1239 14.0918 5.09222 14.056 5.09439 14.0139L5.13643 13.8412L5.18703 13.6537L5.26919 13.3441L5.36391 12.9987C5.42715 12.8048 5.35765 12.5943 5.19123 12.4764C4.74686 12.1499 4.35927 11.7518 4.0455 11.2969C3.24723 10.1554 3.13978 8.66842 3.76537 7.4257C3.97381 7.01506 4.24767 6.63804 4.57411 6.31165C5.4356 5.43544 6.6445 4.87732 7.98395 4.73406C8.44738 4.68357 8.91498 4.68357 9.37827 4.73406C10.7094 4.88576 11.9141 5.45014 12.7713 6.32212C13.0957 6.65277 13.3653 7.02976 13.5696 7.44259C13.8328 7.96492 13.9698 8.54411 13.9718 9.12968C13.9718 9.19076 13.9656 9.25183 13.9634 9.31079C14.3173 9.09593 14.7721 9.1486 15.0671 9.43921L15.1071 9.48764C15.1766 8.61997 15.006 7.75006 14.6163 6.97085C14.3615 6.4548 14.0245 5.98512 13.6201 5.57448C12.5144 4.47921 11.0632 3.79894 9.51516 3.64304C8.95919 3.57146 8.40098 3.56941 7.84925 3.62834ZM12.6026 13.8012C12.5184 13.8834 12.5184 14.016 12.5984 14.0982C12.6068 14.1088 12.6173 14.1171 12.6299 14.1235C13.0954 14.5532 13.3966 15.1324 13.4808 15.76C13.6114 16.2317 14.1064 16.5119 14.5845 16.3813C15.0584 16.257 15.3406 15.7727 15.2163 15.3008C15.2163 15.2988 15.2142 15.2966 15.2142 15.2924C15.1153 14.9386 14.8078 14.6796 14.4412 14.6437C13.8516 14.5363 13.3144 14.2393 12.908 13.7991C12.8216 13.7191 12.6868 13.7191 12.6026 13.8012Z"
          fill={colorByType(type)}
        />
      </svg>
    ),
    [EGroupCode.SYSTEM]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="20" height="20" rx="4" opacity="0.1" fill={colorByType(type)} />
        <path
          d="M14.2546 6.74805C14.3879 6.82503 14.3879 7.01748 14.2546 7.09446L10.5002 9.26202C10.1907 9.44064 9.80933 9.44064 9.50024 9.26201L5.74585 7.09435C5.61206 7.01737 5.61206 6.82492 5.74585 6.74794L9.50024 4.58027C9.80933 4.40163 10.1907 4.40163 10.5002 4.58027L14.2546 6.74805Z"
          fill={colorByType(type)}
        />
        <path
          d="M14.7009 7.86966C14.8342 7.79268 15.0012 7.88891 15.0012 8.04287V12.3766C15.0012 12.7339 14.8103 13.064 14.5012 13.2426L10.9498 15.2932C10.7498 15.4086 10.4998 15.2643 10.4998 15.0333V10.8726C10.4998 10.5153 10.6904 10.1852 10.9998 10.0065L14.7009 7.86966Z"
          fill={colorByType(type)}
        />
        <path
          d="M9.49976 10.8721C9.49976 10.5148 9.30914 10.1846 8.99971 10.006L5.29858 7.86941C5.16528 7.79243 4.99878 7.88866 4.99878 8.04261V12.3766C4.99878 12.7338 5.18921 13.064 5.49878 13.2426L9.04976 15.2927C9.24976 15.4081 9.49976 15.2638 9.49976 15.0328V10.8721Z"
          fill={colorByType(type)}
        />
      </svg>
    ),
    [EGroupCode.SCHEDULED]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="20" height="20" rx="4" opacity="0.1" fill={colorByType(type)} />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.99993 15.5648C13.0735 15.5648 15.565 13.0732 15.565 9.99969C15.565 6.92616 13.0735 4.43457 9.99993 4.43457C6.9264 4.43457 4.43481 6.92616 4.43481 9.99969C4.43481 13.0732 6.9264 15.5648 9.99993 15.5648ZM10.4998 7.2168H9.49976V10.5372L11.5923 12.6297L12.2994 11.9226L10.4998 10.123V7.2168Z"
          fill={colorByType(type)}
        />
      </svg>
    ),
    [EGroupCode.SCHEDULED_TASK]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="20" height="20" rx="4" opacity="0.1" fill={colorByType(type)} />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.99993 15.5648C13.0735 15.5648 15.565 13.0732 15.565 9.99969C15.565 6.92616 13.0735 4.43457 9.99993 4.43457C6.9264 4.43457 4.43481 6.92616 4.43481 9.99969C4.43481 13.0732 6.9264 15.5648 9.99993 15.5648ZM10.4998 7.2168H9.49976V10.5372L11.5923 12.6297L12.2994 11.9226L10.4998 10.123V7.2168Z"
          fill={colorByType(type)}
        />
      </svg>
    ),
    [EGroupCode.SUPPLY_CHAIN_STOCK_MANAGER]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.1"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z"
          fill={colorByType(type)}
        />
        <path
          d="M3.58936 7.09956C3.58936 6.72601 3.80041 6.38452 4.13452 6.21746L9.0656 3.75193C9.34325 3.6131 9.67005 3.6131 9.9477 3.75193L14.8788 6.21746C15.2129 6.38452 15.4239 6.72601 15.4239 7.09956V11.221C14.9858 10.9774 14.4814 10.8386 13.9445 10.8386C12.2611 10.8386 10.8964 12.2032 10.8964 13.8867C10.8964 14.4235 11.0352 14.9279 11.2788 15.366H4.57557C4.0309 15.366 3.58936 14.9244 3.58936 14.3798V7.09956Z"
          fill={colorByType(type)}
        />
        <path
          d="M13.9446 16.3522C15.3063 16.3522 16.4102 15.2483 16.4102 13.8867C16.4102 12.525 15.3063 11.4211 13.9446 11.4211C12.5829 11.4211 11.4791 12.525 11.4791 13.8867C11.4791 15.2483 12.5829 16.3522 13.9446 16.3522Z"
          fill={colorByType(type)}
        />
        <path d="M7.04111 9.44869H5.56179V10.928H7.04111V9.44869Z" fill="white" />
        <path d="M7.04111 11.9142H5.56179V13.3935H7.04111V11.9142Z" fill="white" />
        <path d="M8.02733 11.9142H9.50665V13.3935H8.02733V11.9142Z" fill="white" />
        <path
          d="M13.8952 14.7595L13.5008 15.0553L12.6538 13.9261L13.4961 12.7244L13.8999 13.0075L13.4323 13.6746H15.4239V14.1677H13.4515L13.8952 14.7595Z"
          fill="white"
        />
        <path
          d="M3.58936 7.09956C3.58936 6.72601 3.80041 6.38452 4.13452 6.21746L9.0656 3.75193C9.34325 3.6131 9.67005 3.6131 9.9477 3.75193L14.8788 6.21746C15.2129 6.38452 15.4239 6.72601 15.4239 7.09956V11.221C14.9858 10.9774 14.4814 10.8386 13.9445 10.8386C12.2611 10.8386 10.8964 12.2032 10.8964 13.8867C10.8964 14.4235 11.0352 14.9279 11.2788 15.366H4.57557C4.0309 15.366 3.58936 14.9244 3.58936 14.3798V7.09956Z"
          fill={colorByType(type)}
        />
        <path
          d="M13.9446 16.3522C15.3063 16.3522 16.4102 15.2483 16.4102 13.8867C16.4102 12.525 15.3063 11.4211 13.9446 11.4211C12.5829 11.4211 11.4791 12.525 11.4791 13.8867C11.4791 15.2483 12.5829 16.3522 13.9446 16.3522Z"
          fill={colorByType(type)}
        />
        <path d="M7.04111 9.44868H5.56179V10.928H7.04111V9.44868Z" fill="white" />
        <path d="M7.04111 11.9142H5.56179V13.3935H7.04111V11.9142Z" fill="white" />
        <path d="M8.02733 11.9142H9.50665V13.3935H8.02733V11.9142Z" fill="white" />
        <path
          d="M13.8952 14.7595L13.5008 15.0553L12.6538 13.9261L13.4961 12.7244L13.8999 13.0075L13.4323 13.6746H15.4239V14.1677H13.4515L13.8952 14.7595Z"
          fill="white"
        />
      </svg>
    ),
    [EGroupCode.DING_TALK]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 200 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_302_605)">
          <rect x="-12" y="-8" width="217" height="217" fill="#F5EAE3" />
          <path
            d="M114.515 177C114.261 177 113.754 177 113.5 176.746C112.485 176.239 111.724 174.97 112.231 173.955L119.59 144.269H107.918C107.157 144.269 106.396 144.015 105.888 143.254C105.381 142.493 105.127 141.731 105.381 140.97L109.694 123.463C106.396 124.224 102.843 125.493 99.0373 126.507C98.2761 126.761 96.7537 127.522 94.2164 127.522C89.903 127.522 83.0522 125.746 73.6642 117.119C71.888 115.597 66.0522 110.015 66.8134 105.448C67.0671 104.433 67.5746 102.657 70.1119 101.642C70.6194 101.388 71.3806 101.134 72.3955 100.881C70.3657 100.881 68.8433 100.627 67.8283 100.373C58.694 98.8507 49.0522 85.1493 47.0224 74.2388C46.5149 73.2239 46.0074 71.194 47.0224 69.1642C47.5298 68.1493 49.0522 66.8806 51.8433 66.8806C53.1119 66.8806 54.888 67.1343 56.9179 67.6418C57.9328 67.8955 59.4552 68.403 61.2313 68.6567C54.6343 66.1194 49.5597 63.8358 48.5448 62.5672C44.485 58 37.6343 39.7313 38.3955 28.3134C38.6492 26.0299 40.4253 24 43.2164 24C43.7239 24 44.2313 24 44.7388 24.2537C44.9925 24.2537 44.9925 24.2537 45.2462 24.5075C45.7537 24.7612 84.3209 42.2687 110.455 51.9104C115.276 53.6866 119.843 55.209 124.157 56.7313C145.216 63.8358 161.709 69.6716 158.664 82.1045C158.41 83.3731 157.903 84.8955 156.888 86.9254C156.888 87.1791 156.888 87.1791 156.634 87.4328C151.56 98.3433 138.366 119.403 137.858 120.418C137.604 120.672 137.604 120.925 137.351 120.925L136.082 123.209H150.545C151.56 123.209 152.321 123.716 152.828 124.731C153.336 125.746 153.082 126.507 152.575 127.269L116.545 175.478C116.037 176.746 115.276 177 114.515 177ZM110.963 139.194H122.634C123.396 139.194 124.157 139.448 124.664 140.209C125.172 140.97 125.425 141.731 125.172 142.493L120.097 163.045L145.724 129.045H132.022C131.007 129.045 130.246 128.537 129.739 127.776C129.231 127.015 129.231 126 129.739 125.239L133.545 118.388C133.798 118.134 133.798 117.881 134.052 117.627C136.336 113.821 147.5 95.5522 152.067 85.9104C153.082 83.6269 153.59 82.3582 153.59 81.8507C155.619 73.7313 140.903 68.6567 122.381 62.3134C118.067 60.791 113.246 59.2687 108.679 57.4925C83.8134 48.3582 47.7836 31.8657 43.2164 29.8358C42.4552 39.9851 49.3059 56.7313 52.097 60.0299C54.6343 62.3134 82.0373 71.7015 107.157 79.3134C108.425 79.8209 109.187 81.0896 108.933 82.3582C108.679 83.6269 107.157 84.3881 105.888 84.1343C104.112 83.8806 65.291 75.7612 55.3955 72.9701C53.3656 72.4627 52.097 72.209 51.5895 72.209V72.4627C51.5895 72.7164 51.8433 72.9701 51.8433 73.2239C53.8731 84.1343 62.7537 94.791 68.5895 95.5522C71.6343 96.0597 80.5149 96.3134 93.2015 96.3134H106.903C108.172 96.3134 109.44 97.3284 109.44 98.597C109.44 99.8657 108.425 101.134 107.157 101.388C107.157 101.388 100.813 102.149 90.9179 103.418L88.1268 103.672C82.0373 104.433 73.9179 105.701 71.888 106.209C71.6343 106.209 71.6343 106.463 71.6343 106.463C71.6343 107.224 74.1716 110.776 76.9627 113.06C85.5895 120.925 91.4254 122.194 94.2164 122.194C95.9925 122.194 96.7537 121.687 96.7537 121.687C96.7537 121.687 97.0074 121.433 97.2612 121.433C102.843 119.657 107.918 118.134 112.485 117.119C113.246 116.866 114.261 117.119 114.769 117.881C115.276 118.642 115.53 119.403 115.53 120.164L110.963 139.194Z"
            fill={colorByType(type)}
          />
        </g>
        <defs>
          <clipPath id="clip0_302_605">
            <rect width="200" height="200" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    [EGroupCode.FEISHU]: (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="20" height="20" fill="#ECECEC" />
        <path
          d="M5.81726 3.312C7.86714 4.99021 9.60536 7.00654 10.9543 9.271L12.2772 7.985C12.9053 7.37883 13.6516 6.90429 14.4711 6.59C14.1007 5.35318 13.4886 4.19956 12.669 3.194C12.6199 3.13332 12.5575 3.08435 12.4865 3.05075C12.4155 3.01716 12.3377 2.99981 12.2589 3H5.92995C5.8931 2.99994 5.85713 3.01111 5.82697 3.03197C5.79681 3.05283 5.77394 3.08237 5.76149 3.11654C5.74903 3.1507 5.74761 3.18783 5.75742 3.22282C5.76722 3.25781 5.78777 3.28896 5.81624 3.312H5.81726ZM9.48731 11.552C10.4477 11.956 11.4477 12.296 12.468 12.567C14.2812 13.05 16.003 12.329 16.8518 10.731L17.8802 8.712C18.1096 8.219 18.402 7.762 18.7513 7.347C18.1311 7.12163 17.4752 7.00684 16.8142 7.008C15.3435 7.00462 13.9308 7.57314 12.8832 8.59L11.3117 10.119C10.756 10.6584 10.1441 11.1388 9.48629 11.552H9.48731ZM2.30254 7.867C2.27733 7.84312 2.24557 7.82706 2.21118 7.82079C2.1768 7.81452 2.1413 7.81833 2.10909 7.83175C2.07688 7.84516 2.04938 7.86759 2.02999 7.89625C2.0106 7.92492 2.00017 7.95856 2 7.993L2.00406 14.09C2.00406 14.266 2.09137 14.427 2.23553 14.52C3.89266 15.6057 5.83924 16.1833 7.82944 16.18C10.4563 16.1838 12.9811 15.1782 14.867 13.377C14.0487 13.613 13.1513 13.627 12.2467 13.387C8.42437 12.372 5.08528 10.517 2.30254 7.867Z"
          fill={colorByType(type)}
        />
      </svg>
    ),
  };

  return iconMapByGroup[groupCode] || null;
};

export const renderIconByType = ({ type, groupCode }) => {
  // 有6种不同的icon类型，分别是 商品、优惠券、赠品、短信、消息、库存
  // 有三种不同的icon颜色，分别是trigger：ENodeColor.trigger、condition：ENodeColor.condition、action：ENodeColor.action
  return IconByGroup({ type, groupCode });
};

// 根据变量的 valueSource 字段过滤出隐藏的变量
export const filteredVariableList = variables => {
  if (!variables) return;
  const hiddenVariables = variables
    ?.filter(variable => variable.valueSource === EValueSource.RADIO) // 过滤出 valueSource 为 5 的变量
    .map(variable => {
      return JSON.parse(variable.dynamicEndpointConfig || '{}');
    }) // 解析 dynamicEndpointConfig
    .flatMap(config => config.group); // 取出 group 字段，并扁平化数组
  return variables.filter(item => {
    return !hiddenVariables.includes(item.id);
  });
};

// 根据变量联动关系，返回需要隐藏的 codes
export function getHiddenCodes({ fields = {}, variables }) {
  if (!variables || variables.length === 0) {
    return [];
  }

  const variablesByCode: Map<string, IVariable> = new Map(
    variables.map(variable => [variable.code, variable]),
  );

  const hiddenCodes: string[] = [];
  variables.forEach(variable => {
    const { code, dynamicEndpointConfig } = variable;
    const { showAbleDependenciesVariables = [] } = parseJSON(dynamicEndpointConfig);

    const needHide =
      showAbleDependenciesVariables.length > 0 &&
      showAbleDependenciesVariables.some(relatedCode => {
        const relatedVariable = variablesByCode.get(relatedCode);
        if (!relatedVariable?.id) return false;

        const groupVariable = variables.find(v => {
          const { group = [] } = parseJSON(v.dynamicEndpointConfig);
          return group.includes(relatedVariable.id);
        });

        return groupVariable?.code && fields[groupVariable.code] === relatedVariable.id;
      });

    if (needHide) {
      hiddenCodes.push(code);
    }
  });

  return hiddenCodes;
}
