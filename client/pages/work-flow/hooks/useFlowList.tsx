import { useEffect, useState } from 'react';
import { getFlowList } from '../../../api/workflow';
import { Notify } from 'zent';
import formatDate from '@youzan/utils/date/formatDate';

interface IUseFlowList {
  defaultPageSize?: number;
  defaultFilterParams?: any;
}

const useFlowList = ({ defaultPageSize = 10, defaultFilterParams = {} }: IUseFlowList = {}) => {
  const [filterParams, setFilterParams] = useState(defaultFilterParams);
  const [list, setList] = useState<any>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);

  const fetchList = async page => {
    try {
      setLoading(true);
      const res = await getFlowList({
        page,
        pageSize,
        startTime:
          (filterParams?.createTime?.[0] &&
            formatDate(filterParams?.createTime?.[0], 'YYYY-MM-DD HH:mm:ss')) ||
          undefined,
        endTime:
          (filterParams?.createTime?.[1] &&
            formatDate(filterParams?.createTime?.[1], 'YYYY-MM-DD HH:mm:ss')) ||
          undefined,
        name: filterParams?.name,
        status: filterParams?.status,
      });
      setList(res.items);
      setTotal(res.paginator.totalCount);
      setLoading(false);
    } catch (error) {
      error instanceof Error && Notify.error(error.message || '获取列表失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('filterParams changed ', filterParams);
  }, [filterParams]);

  const handleSetFilterParams = params => {
    console.log('handleSetFilterParams', params);
    setFilterParams(params);
    setPage(1);
  };

  const refreshList = () => {
    console.log('filterParams is ', filterParams);
    // setFilterParams({ ...filterParams });
    fetchList(1);
    setPage(1);
  };

  useEffect(() => {
    fetchList(page);
  }, [filterParams, page, pageSize]);

  return {
    total,
    list,
    loading,
    page,
    pageSize,
    refreshList,
    setFilterParams: handleSetFilterParams,
    setPage,
    setPageSize,
  };
};

export default useFlowList;
