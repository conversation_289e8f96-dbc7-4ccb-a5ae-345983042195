import { ReactNode } from 'react';
import { ActionIcon, AddBtnIcon, ConditionIcon, TriggerIcon } from 'svg';

export type NodeType = 'trigger' | 'condition' | 'action' | 'delay';
export enum ENodeType {
  Trigger = 'trigger',
  Condition = 'condition',
  Action = 'action',
  Delay = 'delay',
}
export type TypeNameMap = {
  [K in NodeType]: string;
};

export interface INode {
  id: string;
  source?: string;
  targets?: string[];
  type: NodeType;
  children: ReactNode;
}

export const typeNameMap: TypeNameMap = {
  trigger: '业务场景',
  condition: '执行条件',
  action: '执行操作',
  delay: '延迟',
};

// 创建一个映射对象，将 type 映射到对应的组件
export const leftIconMap: Record<NodeType, React.FC> = {
  trigger: TriggerIcon,
  condition: ConditionIcon,
  action: ActionIcon,
  delay: ConditionIcon,
};

export const rightIconMap: Record<NodeType, React.FC | null> = {
  trigger: AddBtnIcon,
  condition: AddBtnIcon,
  action: AddBtnIcon,
  delay: AddBtnIcon,
};

export const addNodeListMap: Record<
  ENodeType,
  Array<{
    label: string;
    type: ENodeType;
    disabled?: boolean;
  }>
> = {
  trigger: [
    { label: '执行条件', type: ENodeType.Condition },
    { label: '执行操作', type: ENodeType.Action },
    { label: '延迟', type: ENodeType.Delay },
  ],
  condition: [{ label: '执行操作', type: ENodeType.Action }],
  action: [{ label: '执行操作', type: ENodeType.Action }],
  delay: [
    { label: '执行条件', type: ENodeType.Condition },
    { label: '执行操作', type: ENodeType.Action },
  ],
};

export type NodeColorType = '#A552E0' | '#2DB26F' | '#E57A2E';
export enum ENodeColor {
  trigger = '#A552E0',
  condition = '#2DB26F',
  action = '#E57A2E',
}

// 三种节点主题色
export const nodeThemeColorMap: Record<NodeType, NodeColorType> = {
  trigger: ENodeColor.trigger,
  condition: ENodeColor.condition,
  action: ENodeColor.action,
  delay: ENodeColor.condition,
};

// 自定义节点名称
export type CustomNodeNameType = 'trigger-node' | 'condition-node' | 'action-node' | 'delay-node';
export enum ECustomNodeName {
  trigger = 'trigger-node',
  condition = 'condition-node',
  action = 'action-node',
  delay = 'delay-node',
}

// 三种节点map
export const nodeTypeMap: Record<NodeType, CustomNodeNameType> = {
  trigger: ECustomNodeName.trigger,
  condition: ECustomNodeName.condition,
  action: ECustomNodeName.action,
  delay: ECustomNodeName.delay,
};

const VALID_TRIGGER =
  'https://img01.yzcdn.cn/upload_files/2024/03/12/FpXeB2uw1f2fVxKua8OYhu-dZGOR.png';
const INVALID_TRIGGER =
  'https://img01.yzcdn.cn/upload_files/2024/03/14/Fu4RRLZbwX6OSQOooIaRCUmkGV1-.png';
const VALID_ACTION =
  'https://img01.yzcdn.cn/upload_files/2024/03/12/Fml3zPnGbtSCIP_SwmU8IeHpdY3n.png';
const INVALID_ACTION =
  'https://img01.yzcdn.cn/upload_files/2024/03/14/FptQ6icA-AcW3odDwbELyLA52nj2.png';

export const bgMap = {
  [ENodeType.Trigger]: {
    valid: VALID_TRIGGER,
    invalid: INVALID_TRIGGER,
  },
  [ENodeType.Condition]: {
    valid: VALID_TRIGGER,
    invalid: INVALID_TRIGGER,
  },
  [ENodeType.Action]: {
    valid: VALID_ACTION,
    invalid: INVALID_ACTION,
  },
  [ENodeType.Delay]: {
    valid: VALID_ACTION,
    invalid: INVALID_ACTION,
  },
};
