import {
  ESelectorType,
  IAction,
  ICondition,
  IExpressions,
  ITrigger,
  Paginator,
  TriggerType,
} from '.';
import { ENodeType } from './node';
import { Node } from '@antv/x6';

export const widthByType: Record<ENodeType, number> = {
  [ENodeType.Trigger]: 464,
  [ENodeType.Action]: 464,
  [ENodeType.Delay]: 464,
  [ENodeType.Condition]: 682,
};

export interface ITriggers {
  items: ITrigger[];
  paginator: Paginator;
}

export interface IConditionContent {
  data?: ICondition[];
}

// 这个是从接口获取到的action格式
export interface IActionItem {
  code: string;
  description: string;
  id: number;
  name: string;
  type: string;
  variables: IVariableItem[];
  groupCode: EGroupCode;
  canUse: boolean;
  unusableReason?: string;
  obtainPermissionWay?: string;
  obtainPermissionNotice?: string;
  supportFlowConcatenation: boolean;
}

export enum EGroupCode {
  GOODS = 'goods',
  ORDER = 'order',
  UMP = 'ump',
  SYSTEM_COMMON = 'system_common',
  WECOM_HELPER = 'wecom_helper',
  SYSTEM = 'system',
  SUPPLY_CHAIN_STOCK_MANAGER = 'supplyChainStockManager',
  SCHEDULED = 'scheduled',
  SCHEDULED_TASK = 'scheduled_task',
  DING_TALK = 'ding_talk',
  FEISHU = 'feishu',
}

export interface IVariableItem {
  code?: ESelectorType;
  description: string;
  dynamicEndpointConfig: string;
  id: number;
  name: string;
  type: number;
  validateRule?: RegExp;
  valueSource: number;
  isRequired: boolean;
}

export interface ISubDrawer {
  data: IActionItem;
  selectedData: IAction | null;
  onClose: () => void;
  onConfirm: (action?: IAction) => void;
  visible: boolean;
}

export const DEFAULT_EXPRESSION: IExpressions = {
  openField: '',
  operator: {
    compareType: 1,
    description: '',
    id: 0,
    name: '',
    symbol: '',
  },
  variable: {
    description: '',
    id: 0,
    valueSource: 1,
    name: '',
    dynamicEndpointConfig: '',
    // type: TriggerType.EVENT,
    type: 1,
    isRequired: true,
  },
};

// 新增、编辑节点时记录当前节点所在位置信息
export interface IDrawerData {
  type: ENodeType;
  conditionIdx: number | null;
  actionIdx: number | null;
  parentNode: Node | null;
  currentNode: Node | null;
  isEdit: boolean;
}

export enum TemplateStyle {
  ChinaYuanStyle = 'chinaYuanStyle',
  DateTimePicker = 'dateTimePicker',
  TextArea = 'textArea',
}
