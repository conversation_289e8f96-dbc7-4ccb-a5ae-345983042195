export interface IExpressions {
  openField?: string | number; // 开放变量的值
  operator: IOperator;
  variable: IVariable;
  restrictedFields?: IRestrictedFields[];
  fieldValueMaps4Display?: string;
  fieldValueMaps4Execute?: string;
  timeValue?: Record<'time' | 'timeUnit', number | string>;
}

export interface IRestrictedFields {
  description: string;
  id: number;
  name: string;
  value: string;
}

export interface ICondition {
  rule?: 1 | 2;
  expressions: IExpressions[];
}

export interface IDelayComponent {
  delayTime?: number;
  timeUnit?: number;
  variableId?: number;
  beforeOrAfter?: number;
}

export interface ITask {
  // name: string;
  // id: string;
  conditions: ICondition[];
  action: IAction;
  seriesActions?: IAction[];
  delayComponent?: IDelayComponent;
  actionFieldValueMaps?: Record<string, any>;
  // type: string;
  // options: Array<any>;
}

export interface IFlow {
  id?: string;
  name?: string;
  scenes?: string;
  triggerId?: number;
  triggerDefine?: ITrigger;
  status?: FlowStatusEnum;
  chainRules?: ITask[];
}

export enum Subject {
  Scheduled = 'scheduled',
  Empty = '',
}

export interface ITrigger {
  code: string;
  description: string;
  id: number;
  name: string;
  // 获取权限方式
  obtainPermissionWay: string;
  obtainPermissionNotice: string;
  unusableReason: string;
  canUse: boolean;
  // 目前只有定时任务
  subject: Subject;
  subjectDetail: string;
  type: TriggerType;
}

export interface ITriggerWithGroupCode extends ITrigger {
  groupCode: string;
}

export interface ScheduledTaskField {
  repeatRule: {
    startTime: string;
    endTime: string;
    cycleRule: string;
    weekDays?: number[];
    endType: 0 | 1;
  };
}

export enum TriggerType {
  // 事件
  EVENT = 1,
  // 定时任务
  SCHEDULED = 2,
}

export const TRIGGER_TYPE_TO_SUBJECT_MAP = {
  [TriggerType.EVENT]: Subject.Empty,
  [TriggerType.SCHEDULED]: Subject.Scheduled,
};

export interface ITriggerGroups {
  groupName: string;
  groupCode: string;
  triggerDefineList: ITrigger[];
}

export interface IVariable {
  description: string;
  id: number;
  name: string;
  code?: ESelectorType;
  dynamicEndpointConfig: any;
  valueSource: EValueSource;
  validateRule?: RegExp;
  extension?: string;
  type: TriggerType;
  subject?: string;
  subjectDetail?: string;
  isRequired: boolean;
  isMultiSelect?: boolean;
  needPermissionVerification?: boolean;
}

export interface IOperator {
  description: string;
  id: number;
  name: string;
  symbol: string;
  compareType: 1 | 2;
}

export enum EValueSource {
  // 开放变量 1
  OPEN = 1,
  // 系统设定 2
  FIELDS = 2,
  // 动态 3
  DYNAMIC = 3,
  // 固定值 4
  FIXED = 4,
  // 新增5 为单选组
  RADIO = 5,
  // 新增6 仅文本渲染没有交互
  TEXT = 6,
  // 超过下单时间
  TIME = 7,
  // 批量导入
  BATCHIMPORT = 8,
  // 业务特殊新增 9 发单信息
  SEND_ORDER_INFO = 9,
}

// 这是最后提交给后端的action格式
export interface IAction {
  actionFields?: any[];
  code?: string;
  description?: string;
  isEnableNeedSecondaryConfirm?: boolean;
  id?: number;
  name: string;
  type?: string;
  actionFieldValueMaps4Display?: string;
  actionFieldValueMaps4Execute?: string;

  variables: IVariable[];

  // 新增的可插入变量的文本域用的
  content?: string;
  contentVariables?: string[];
}

export enum ESelectorType {
  goodsId = 'goodsId',
  goods = 'goods',
  storeStaff = 'storeStaff',
  workFlowTemplate = 'workFlowTemplate',
  coupon = 'coupon',
  couponCode = 'couponCode',
  present = 'present',
  retailGoodsCategory = 'retailGoodsCategory',
}

export enum FlowStatusEnum {
  // 未启用
  DISABLE = 0,
  // 启用
  ENABLE = 1,
}
export enum FlowStatus {
  // 停用
  Stoped = 0,
  // 启用
  Enabled = 1,
  // 未执行
  UnExecuted = 2,
  // 全部
  All = '',
}

export const FlowStatusList = [
  { key: FlowStatus.All, text: '全部' },
  { key: FlowStatus.Stoped, text: '已停用' },
  { key: FlowStatus.Enabled, text: '启用中' },
  { key: FlowStatus.UnExecuted, text: '草稿' },
];

export const FlowStatusCNMap = {
  [FlowStatus.Stoped]: '已停用',
  [FlowStatus.Enabled]: '启用中',
  [FlowStatus.UnExecuted]: '草稿',
};

// 商品：goods，订单：order，营销：ump，分销员：distributors，云分销：cloud_distribution

export const TRIGGER_GROUPS_ICON_MAP = {
  // 定时任务没有groupCode临时作为兜底
  system: 'https://img01.yzcdn.cn/upload_files/2024/06/21/Fqjh8s06cghHgKH7ay3NLdUtE2KK.png',
  goods: 'https://img01.yzcdn.cn/upload_files/2024/03/25/FubYbHah7HbPdrIM_NDFDIe25XPh.png',
  order: 'https://img01.yzcdn.cn/upload_files/2024/03/25/Fmis-7e6E9yX7Pi0UvzULdS5IZo4.png',
  ump: 'https://img01.yzcdn.cn/upload_files/2024/03/25/FhZXMFo2LuMb9ZW48FCkcAj-y7HU.png',
  distributors: 'https://img01.yzcdn.cn/upload_files/2024/03/25/FpAuE6lL1gGtU2efk5WJEO1HAi9b.png',
  cloud_distribution:
    'https://img01.yzcdn.cn/upload_files/2024/03/25/FtvDYbR3UYGKcF4INdF4mu6zB-Bn.png',
  miniprogram: 'https://img01.yzcdn.cn/upload_files/2024/05/30/Fk7jKKzSeX0S5PxIhbkOuBoGa_my.png',
  supplyChainStockManager:
    'https://img01.yzcdn.cn/upload_files/2024/05/30/Frd63DDloO_d_KjtlQqgBqAXR6vu.png',
  scheduled_task: 'https://img01.yzcdn.cn/upload_files/2024/04/03/FqLrHOOC-6FdYyvV-p6bD8sHqLyW.png',
};
