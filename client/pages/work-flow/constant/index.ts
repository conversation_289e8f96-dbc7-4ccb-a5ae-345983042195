export * from './graph';
export * from './node';
export * from './drawer';
export * from './flow';

export interface Paginator {
  page: number;
  pageSize: number;
  totalCount: number;
}

export enum ItemSelectType {
  goods = 'goods',
  coupon = 'coupon',
  couponCode = 'couponCode',
  present = 'present',
  storeStaff = 'storeStaff',
  goodsGroup = 'goodsGroup',
  chainStoreSubShop = 'chainStoreSubShop',
  warehouse = 'warehouse',
  chainStoreGoods = 'chainStoreGoods',
  chainStoreSellGoods = 'chainStoreSellGoods',
  yzCloudClient = 'yzCloudClient',
  retailPurchaseGoods = 'retailPurchaseGoods',
  wxxdShopSelector = 'wxxdShopSelector',
  retailShopSaleChannel = 'retailShopSaleChannel',
}

export const TYPE_DESC_MAP = {
  [ItemSelectType.goods]: '商品',
  [ItemSelectType.coupon]: '优惠券',
  [ItemSelectType.couponCode]: '优惠码',
  [ItemSelectType.present]: '赠品',
  [ItemSelectType.storeStaff]: '员工',
  [ItemSelectType.goodsGroup]: '商品分组',
  [ItemSelectType.chainStoreSubShop]: '店铺',
  [ItemSelectType.warehouse]: '仓库/门店',
  [ItemSelectType.chainStoreSellGoods]: '商品',
  [ItemSelectType.chainStoreGoods]: '商品',
  [ItemSelectType.yzCloudClient]: '应用',
  [ItemSelectType.retailPurchaseGoods]: '商品',
  [ItemSelectType.wxxdShopSelector]: '微信小店',
  [ItemSelectType.retailShopSaleChannel]: '门店销售渠道',
};

const options2Map = options =>
  options.reduce((pre, cur) => {
    pre[cur.key] = cur.text;
    return pre;
  }, {});

export const TimeMax = {
  1: 10080,
  2: 720,
  3: 30,
};

export const TimeUnit = [
  { text: '分钟', key: 1 },
  { text: '小时', key: 2 },
  { text: '天', key: 3 },
];

export const BeforeOrAfter = [
  { text: '前', key: 1 },
  { text: '后', key: 2 },
];

export const defaultUnitTime = {
  time: '1',
  opt: TimeUnit[0],
};

export const TimeUnitMap = options2Map(TimeUnit);

export const BeforeOrAfterMap = options2Map(BeforeOrAfter);
