import React, { FC, useEffect, useState } from 'react';
import { Node } from '@antv/x6';
import { useAtomValue } from 'jotai';
import CommonNode from '../components/WorkFlowGraph/components/CommonNode';

// 假设 CommonNodeProps 是 CommonNode 组件的 props 类型
interface CommonNodeProps {
  node: Node;
  graph: any; // 替换为具体的 Graph 类型
  onAddNode?: (type: any) => void; // 替换为具体的类型
  customNode?: (setInvalidTipsArr: (tips: string[]) => void) => JSX.Element;
}

// 创建 withCommonNode 高阶组件
const withCommonNode = <P extends CommonNodeProps>(WrappedComponent: FC<P>) => {
  const WithCommonNode: FC<P> = (props: P) => {
    const [invalidTipsArr, setInvalidTipsArr] = useState<string[]>([]);
    const { node, graph } = props; // 从 WrappedComponent 的 props 中提取 node 和 graph

    // ...其他需要从 props 中提取的值

    // 将 setInvalidTipsArr 传递给 WrappedComponent
    const wrappedProps = { ...props, invalidTipsArr, setInvalidTipsArr, node };

    // @ts-ignore
    return (
      <CommonNode {...wrappedProps}>
        <WrappedComponent {...wrappedProps} />
      </CommonNode>
    );
    // return
  };

  return WithCommonNode;
};

export default withCommonNode;
