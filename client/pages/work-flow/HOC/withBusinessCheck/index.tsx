import React, { useEffect, useState } from 'react';
import styles from './index.m.scss';
import { Pop } from 'zent';

interface IWithBusinessCheckProps {
  validationText?: string;
  url?: string;
  isValid?: boolean;
  detailText?: string;
}

const withBusinessCheck = <T extends {}>(Component: React.ComponentType<T>) => (
  props: T & IWithBusinessCheckProps,
) => {
  const { validationText = '', url = '', isValid, detailText, ...rest } = props;

  return isValid ? (
    <Component {...(rest as T)} />
  ) : validationText ? (
    <div className={styles.mask}>
      <Pop
        trigger="hover"
        position="top-center"
        content={
          <>
            {validationText}
            &nbsp;
            {url && (
              <a href={url} target="_blank">
                {detailText}
              </a>
            )}
          </>
        }
      >
        {childProps => (
          <div {...childProps}>
            <Component {...(rest as T)} disabled />
          </div>
        )}
      </Pop>
    </div>
  ) : (
    <div className={styles.mask}>
      <Component {...(rest as T)} disabled />
    </div>
  );
};

export default withBusinessCheck;
