import React from 'react';
import WorkFlowList from '../pages/list';
import WorkFlowGraph from '../pages/graph';
import WorkFlowTemplates from '../pages/templates';

// export const routes = [
//  {
//    path: '/list',
//    component: React.lazy(() => import(/* webpackChunkName: 'work-flow-list' */ '../pages/list')),
//  },
//  {
//    path: '/graph',
//    component: React.lazy(() => import(/* webpackChunkName: 'work-flow-graph' */ '../pages/graph')),
//  },
// ];

// 换成非懒加载的方式 懒加载单独分出chunks 导致node render模版找不到css，属于框架设计问题
// 默认模板中包含了必然加载vendor.css 当前并没有打出vendor.css，故删掉了模版中的loadCSS('vendor.css')逻辑
export const routes = [
  {
    path: '/list',
    component: WorkFlowList,
  },
  {
    path: '/graph',
    component: WorkFlowGraph,
  },
  {
    path: '/templates',
    component: WorkFlowTemplates,
  },
];
