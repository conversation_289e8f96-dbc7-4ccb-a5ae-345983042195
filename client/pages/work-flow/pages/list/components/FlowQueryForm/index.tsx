import React, { useEffect } from 'react';
import {
  Button,
  Form,
  FormContext,
  FormDateRangePickerField,
  FormInputField,
  FormSelectField,
  FormStrategy,
} from 'zent';
import styles from './index.m.scss';
import { FlowStatus, FlowStatusList } from '../../../../constant';

const FilterForm = ({ onQuery, loading }) => {
  const form = Form.useForm(FormStrategy.View);

  const onSubmit = form => {
    const formData = form.getValue();
    onQuery({
      ...formData,
      status: formData?.status?.key,
    });
  };

  return (
    <div className={styles.formContainer}>
      <Form
        layout="horizontal"
        direction="row"
        form={form}
        onSubmit={onSubmit}
        disableEnterSubmit={false}
      >
        <FormContext.Provider value={{ controlStyle: { width: 350 } }}>
          <FormInputField
            name="name"
            label="任务名称："
            props={{ placeholder: '输入任务名称' }}
          ></FormInputField>
          <FormDateRangePickerField
            name="createTime"
            label="创建时间："
            style={{ width: 600 }}
            props={{
              // showTime: true,
              placeholder: ['请选择开始时间', '请选择结束时间'],
              defaultDate: [null, null],
              valueType: 'number',
            }}
            defaultValue={[null, null]}
          ></FormDateRangePickerField>
          <FormSelectField
            name="status"
            initialValue={FlowStatusList.find(item => item.key === FlowStatus.All)}
            label="任务状态："
            props={{ options: FlowStatusList }}
          ></FormSelectField>
        </FormContext.Provider>
      </Form>
      <Button
        type="primary"
        className={styles.action}
        loading={loading}
        onClick={() => form.submit()}
      >
        筛选
      </Button>
    </div>
  );
};

export default FilterForm;
