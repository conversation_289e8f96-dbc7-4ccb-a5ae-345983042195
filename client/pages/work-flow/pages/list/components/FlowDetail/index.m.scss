.flow-detail-container {
  width: 832px;
  height: 400px;
  color: #333;
  font-weight: 500;
  position: relative;
  overflow: auto;

  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding-bottom: 32px;
    margin-top: 8px;
    .run-time {
      flex: 1;

      .detail {
        font-size: 12px;
        margin-top: 8px;
      }
    }

    .run-count {
      flex: 1;
      .detail {
        font-size: 12px;
        margin-top: 8px;
      }
    }

    .value {
      font-family: Avenir;
      font-size: 32px;
      font-weight: 800;
    }
  }

  .task-record {
    max-height: 500px;
    .task-record-title {
      margin-bottom: 16px;
    }
  }
}

:global {
  .zent-grid-empty {
    font-weight: normal;
    color: #ccc;
  }
}

.status-cell {
  display: flex;
  align-items: center;

  .success-icon,
  .error-icon {
    width: 5px;
    height: 5px;

    border-radius: 50%;
    margin-right: 8px;

    background-color: #45a110;
  }

  .error-icon {
    background-color: #d41f15;
  }
}
