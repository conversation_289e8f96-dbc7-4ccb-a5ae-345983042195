import React, { useRef } from 'react';
import { Grid } from 'zent';
import styles from './index.m.scss';
import { useRequest } from 'ahooks';
import { getFlowRuntimeData, getFlowRuntimeRecord } from 'api/workflow';
import { useInfinityList } from '../../../../../../hooks/useInfinityList';
import useListenScrollToBottom from '../../../../../../hooks/useListenScrollToBottom';
import { formatDateToStandard } from '../../../../../../fns/formatDate';

interface IFlowDetailProps {
  flowId?: string;
}

const FlowDetail: React.FC<IFlowDetailProps> = ({ flowId }) => {
  const { data = {} } = useRequest(getFlowRuntimeData, {
    defaultParams: [{ flowId }],
  });

  const { loadMore, list, hasMore, loading } = useInfinityList({
    fetchList: getFlowRuntimeRecord,
    defaultParams: {
      flowId,
    },
  });

  const ref = useRef<any>();

  const { executeCount, flowRuntimeDuration: { day = 0, hour = 0, minute = 0 } = {} } = data;

  const columns = [
    {
      title: '执行时间',
      name: 'executeTime',
      width: '30%',
      bodyRender: data => {
        return formatDateToStandard(data.executeTime || 0);
      },
    },
    {
      title: '执行结果',
      name: 'status',
      bodyRender: data => {
        return data.status ? (
          <div className={styles.statusCell}>
            <span className={styles.successIcon}></span>成功
          </div>
        ) : (
          <div className={styles.statusCell}>
            <span className={styles.errorIcon}></span>失败
          </div>
        );
      },
    },
    {
      title: '描述',
      name: '',
      bodyRender: data => {
        if (data?.taskExecutionLink) {
          return (
            <>
              <p dangerouslySetInnerHTML={{ __html: data?.result || '' }} />
              <a href={data?.taskExecutionLink} target="_blank">执行结果</a>
            </>
          );
        }
        return <p dangerouslySetInnerHTML={{ __html: data?.result || '' }} />;
      },
    },
  ];

  useListenScrollToBottom(ref, () => {
    !loading && hasMore && loadMore();
  });

  return (
    <div className={styles.flowDetailContainer} ref={ref}>
      <div className={styles.header}>
        <div className={styles.runTime}>
          <div>运行时长</div>
          <div className={styles.detail}>
            <span className={styles.value}>{day}</span> 天&nbsp;
            <span className={styles.value}>{hour}</span>
            &nbsp;小时&nbsp;
            <span className={styles.value}>{minute}</span>
            &nbsp;分钟&nbsp;
          </div>
        </div>
        <div className={styles.runCount}>
          <div>运行次数</div>
          <div className={styles.detail}>
            <span className={styles.value}>{executeCount || 0}</span>&nbsp;次
          </div>
        </div>
      </div>
      <div className={styles.taskRecord}>
        <div className={styles.taskRecordTitle}>任务记录</div>
        <Grid columns={columns} datasets={list} />
      </div>
    </div>
  );
};

export default FlowDetail;
