import React, { useEffect, useState } from 'react';
import FlowQueryForm from './components/FlowQueryForm';
import { Button, Grid, MenuCascader, Notify, Sweetalert, openDialog, Tooltip, Pop } from 'zent';
import { formatDateToStandard } from '../../../../fns/formatDate';
import { FlowStatus, FlowStatusCNMap } from '../../constant/flow';
import QuickCreateCard, { QuickCreateCardType } from '../../components/QuickCreateCard';
import styles from './index.m.scss';
import {
  copyFlow,
  deleteFlow,
  disableFlow,
  enableFlow,
  queryFlowTemps,
} from '../../../../api/workflow';
import jumpTo from '../../../../fns/jumpTo';
import FlowDetail from './components/FlowDetail';
import useFlowList from '../../hooks/useFlowList';
import { useRequest } from 'ahooks';
import { getUrlParams } from '../../../../fns/getUrlParams';
import { cloneDeep } from 'lodash';
import withBusinessCheck from 'pages/work-flow/HOC/withBusinessCheck';
import { checkShopFlowPermission } from 'pages/work-flow/api';

const DEFAULT_PAGE_SIZE = 10;

const WorkFlowList = () => {
  const {
    list,
    page,
    pageSize,
    total,
    loading,
    setFilterParams,
    setPage,
    refreshList,
  } = useFlowList({
    defaultPageSize: DEFAULT_PAGE_SIZE,
  });
  // FIXME: 临时解决权限线上题
  const [businessPermission, setBusinessPermission] = useState<number>(1);

  /* useEffect(() => {
    checkShopFlowPermission().then(res => {
      setBusinessPermission(res.permission);
    });
  }, []); */

  const onStop = async (flowId: string) => {
    Sweetalert.confirm({
      title: '提示',
      content: '确定停用该任务？',
      maskClosable: true,
      onConfirm: async () => {
        try {
          await disableFlow({ flowId });
          Notify.success('停用成功');
          refreshList();
        } catch (error) {
          Notify.error(error || '停用失败');
        }
      },
      confirmText: '停用',
    });
  };

  const onCopy = async (flowId: string) => {
    const newFlowId = await copyFlow({ flowId });
    refreshList();
    jumpTo({ url: `/v4/jiawo/work-flow/graph?flowId=${newFlowId}`, isAsync: true });
  };

  const onDelete = async (flowId: string) => {
    Sweetalert.confirm({
      title: '提示',
      content: '确定删除该任务？',
      maskClosable: true,
      onConfirm: async () => {
        try {
          await deleteFlow({ flowId });
          Notify.success('删除成功');
          refreshList();
        } catch (error) {
          Notify.error(error || '删除失败');
        }
      },
      confirmText: '删除',
    });
  };

  const openStatistic = (flowId: string, flowName) => {
    // 弹窗打开统计 使用FlowDetail组件作为内容
    openDialog({
      title: flowName,
      children: <FlowDetail flowId={flowId} />,
      footer: null,
    });
  };

  const onEnable = async (flowId: string) => {
    try {
      await enableFlow({ flowId }, { rawResponse: true });
      Notify.success('启用成功');
      refreshList();
    } catch (error) {
      // @ts-ignore
      // 流程配置不完整
      if (error.code === 121000106) {
        Sweetalert.confirm({
          title: '提示',
          content: '流程配置不完整，请检查',
          maskClosable: true,
          confirmText: '编辑',
          onConfirm: () => {
            jumpTo({ url: `/v4/jiawo/work-flow/graph?flowId=${flowId}` });
          },
        });
        return;
      }
      Notify.error(error || '启用失败');
    }
  };

  const [columns, setColumns] = useState([
    {
      title: '任务名称',
      key: 'name',
      bodyRender: data => {
        const { name } = data;
        // name中间可能出现多个空格，需要保留原有的空格数
        return <div style={{ whiteSpace: 'pre-wrap' }}>{name}</div>;
      },
    },

    {
      title: '任务状态',
      key: 'status',
      bodyRender: data => {
        return FlowStatusCNMap[data.status];
      },
    },
    {
      title: '创建人',
      key: 'creator',
      name: 'creator',
    },
    {
      title: '创建时间',
      key: 'createTime',
      bodyRender: data => {
        return <div>{formatDateToStandard(data.createTime)}</div>;
      },
    },

    {
      title: '操作',
      key: 'operation',
      width: 300,
      bodyRender: _ => {
        const { status, id, name, permission } = _;
        const { editable, deletable, copyAble, turnOnOrOffAble } = permission || {
          editable: true,
          deletable: true,
        };
        return (
          <div>
            {status !== FlowStatus.UnExecuted && (
              <Button type="text" onClick={() => openStatistic(id, name)}>
                数据
              </Button>
            )}
            {status === FlowStatus.UnExecuted && (
              businessPermission === 0 ? (
                <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
                  <Button
                    className={styles.disabledButton}
                    type="text"
                  >
                    启用
                  </Button>
                </Pop>
              ) : (
                <Button
                  disabled={!turnOnOrOffAble}
                  type="text"
                  onClick={() => onEnable(id)}
                >
                  启用
                </Button>
              )
            )}
            {status !== FlowStatus.Stoped && (
              businessPermission === 0 ? (
                <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
                  <Button
                    type="text"
                    className={styles.disabledButton}
                  >
                    编辑
                  </Button>
                </Pop>
              ) : (
                <Button
                  type="text"
                  onClick={() => jumpTo({ url: `/v4/jiawo/work-flow/graph?flowId=${id}` })}
                  disabled={!editable}
                >
                  编辑
                </Button>
              )
            )}
            {status === FlowStatus.Enabled && (
              businessPermission === 0 ? (
                <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
                  <Button
                    className={styles.disabledButton}
                    type="text"
                  >
                    停用
                  </Button>
                </Pop>
              ) : (
                <Button
                  disabled={!turnOnOrOffAble}
                  type="text"
                  onClick={() => onStop(id)}
                >
                  停用
                </Button>
              )
            )}
            {businessPermission === 0 ? (
              <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
                <Button
                  className={styles.disabledButton}
                  type="text"
                >
                  复制
                </Button>
              </Pop>
            ) : (
              <Button
                disabled={!copyAble}
                type="text"
                onClick={() => onCopy(id)}
              >
                复制
              </Button>
            )}
            {status !== FlowStatus.Enabled && (
              businessPermission === 0 ? (
                <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
                  <Button
                    type="text"
                    className={styles.disabledButton}
                  >
                    删除
                  </Button>
                </Pop>
              ) : (
                <Button
                  type="text"
                  onClick={() => onDelete(id)}
                  disabled={!deletable}
                >
                  删除
                </Button>
              )
            )}
          </div>
        );
      },
    },
  ]);

  const { data: templates = [] } = useRequest(queryFlowTemps, {
    defaultParams: [
      {
        pageSize: 30,
        page: 1,
        isRecommend: true,
      },
    ],
  });

  const workFlowTemplates = templates.items || [];

  useEffect(() => {
    if (
      list.some(item => item.createShopName) &&
      !columns.find(item => item.name === 'createShopName')
    ) {
      const newColumns = cloneDeep(columns);
      newColumns.splice(1, 0, {
        title: '创建店铺',
        key: 'createShopName',
        name: 'createShopName',
      });
      setColumns(newColumns);
    }
  }, [list]);

  useEffect(() => {
    const flowId = getUrlParams('flowId');
    const scene = getUrlParams('scene');
    if (flowId && scene === 'data') {
      openStatistic(flowId, '任务详情');
    }
  }, []);

  const AccessableTemplateCard = withBusinessCheck(QuickCreateCard);

  return (
    <div className={styles.listContainer}>
      <div className={styles.templateList}>
        <QuickCreateCard type={QuickCreateCardType.New} disabled={businessPermission === 0}></QuickCreateCard>
        {workFlowTemplates?.map(
          ({
            name,
            description,
            id,
            canUse,
            unusableReason,
            obtainPermissionWay,
            obtainPermissionNotice,
          }) => (
            <AccessableTemplateCard
              isValid={canUse}
              validationText={unusableReason}
              title={name}
              desc={description}
              url={obtainPermissionWay}
              detailText={obtainPermissionNotice}
              templateId={id}
              disabled={businessPermission === 0}
            />
          ),
        )}
        <QuickCreateCard type={QuickCreateCardType.More} disabled={businessPermission === 0}></QuickCreateCard>
      </div>
      <FlowQueryForm onQuery={params => setFilterParams(params)} loading={loading} />
      {/* <ItemSelect type={ItemSelectType.goodsGroup}></ItemSelect> */}
      <Grid
        columns={columns}
        datasets={list}
        pageInfo={{
          current: page,
          pageSize,
          total,
        }}
        loading={loading}
        onChange={({ current = 1 }) => {
          setPage(current);
        }}
      ></Grid>
    </div>
  );
};

export default WorkFlowList;
