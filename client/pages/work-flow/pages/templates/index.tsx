import { useRequest } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { BlockLoading, Notify, Tabs } from 'zent';
import { queryAllFlowTempGroup, queryFlowTemps } from '../../../../api/workflow';
import TemplateCard from '../../../automation/components/TemplateCard';
import styles from './index.m.scss';
import withBusinessCheck from '../../HOC/withBusinessCheck';
import { checkShopFlowPermission } from 'pages/work-flow/api';

const AccessableTemplateCard = withBusinessCheck(TemplateCard);

const WorkFlowTemplates = () => {
  const [groupCode, setGroupCode] = useState('');
  const { data: allCategories = [] } = useRequest(queryAllFlowTempGroup, {});
  const [templates, setTemplates] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  // FIXME: 临时解决权限线上题
  const [businessPermission, setBusinessPermission] = useState<number>(1);

  useEffect(() => {
    /* checkShopFlowPermission().then(res => {
      setBusinessPermission(res.permission);
    }); */
  }, []);

  const fetchList = async () => {
    setLoading(true);
    try {
      const reuslt = await queryFlowTemps({
        page: 1,
        pageSize: 100,
        groupCode,
      });
      setTemplates(reuslt.items);
    } catch (error) {
      Notify.error(error || '获取模板列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchList();
  }, [groupCode]);
  return (
    <div className={styles.templatesContainer}>
      <Tabs
        type="card"
        tabs={[
          { title: '全部', key: '' },
          ...allCategories.map(item => ({ key: item.code, title: item.name })),
        ]}
        onChange={code => setGroupCode(code)}
        activeId={groupCode}
      ></Tabs>
      <BlockLoading loading={loading}>
        <div className={styles.listContainer}>
          {templates.map(
            ({
              id,
              description,
              name,
              groupCode,
              canUse,
              unusableReason,
              obtainPermissionWay,
              obtainPermissionNotice,
            }) => (
              <AccessableTemplateCard
                key={id}
                templateId={id}
                desc={description}
                type="workflow"
                title={name}
                isValid={canUse && businessPermission !== 0}
                groupCode={groupCode}
                validationText={businessPermission === 0 ? '升级更高版本后可用' : unusableReason}
                url={businessPermission === 0 ? 'https://store.youzan.com/v4/subscribe/pc-order/software/choose#/' : obtainPermissionWay}
                detailText={businessPermission === 0 ? '去升级' : obtainPermissionNotice}

              ></AccessableTemplateCard>
            ),
          )}
        </div>
      </BlockLoading>
    </div>
  );
};

export default WorkFlowTemplates;
