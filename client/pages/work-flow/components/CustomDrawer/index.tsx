// react函数组件模板
import { FC, useRef } from 'react';
// import './style.scss';
import { <PERSON><PERSON>, Drawer } from 'zent';
import { useCustomDrawer } from './useCustomDrawer';
import { ENodeType, typeNameMap } from 'pages/work-flow/constant';
import { IActionItem, widthByType } from 'pages/work-flow/constant/drawer';
import TriggerContent from './components/TriggerContent';
import ConditionContent from './components/ConditionContent';
import ActionContent from './components/ActionContent';
import DelayContent from './components/DelayContent';
import SubDrawer from './components/SubDrawer';
import useGraphOperation from './useGraphOperation';

const CustomDrawer: FC = () => {
  const childRef = useRef<{ onConfirm: () => void }>(null);
  const { getDrawerData, hideDrawer, getSubDrawerData, hideSubDrawer } = useCustomDrawer();
  const { type, visible } = getDrawerData();
  const { subDrawerData, subDrawerSelected, subDrawerVisible } = getSubDrawerData();
  const { handleFlowData, handleGraphData } = useGraphOperation();

  const renderDrawerContent = () => {
    // 根据type 渲染不同的内容
    switch (type) {
      case ENodeType.Trigger:
        return <TriggerContent ref={childRef} />;
      case ENodeType.Condition:
        // @ts-ignore
        return <ConditionContent ref={childRef} />;
      case ENodeType.Action:
        return <ActionContent ref={childRef} />;
      case ENodeType.Delay:
        return <DelayContent ref={childRef} />;
      default:
        return null;
    }
  };

  const onConfirmEdit = () => {
    // 打散到各编辑页自己实现了，因为action的编辑页是多层的，后续也会有改动，各自实现
    childRef.current?.onConfirm();
  };

  return (
    <div>
      <Drawer
        title={typeNameMap[type] || ''}
        placement="right"
        width={widthByType[type] || 520}
        footer={
          <div style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              onClick={() => {
                hideDrawer();
                onConfirmEdit();
              }}
            >
              确定
            </Button>
            <Button onClick={() => hideDrawer()}>取消</Button>
          </div>
        }
        visible={visible}
        onClose={() => hideDrawer()}
      >
        {renderDrawerContent()}
      </Drawer>
      <SubDrawer
        data={subDrawerData as IActionItem}
        selectedData={subDrawerSelected}
        visible={subDrawerVisible}
        onClose={hideSubDrawer}
        onConfirm={action => {
          handleFlowData(action);
          handleGraphData(action);
          hideSubDrawer();
          hideDrawer();
        }}
      />
    </div>
  );
};

export default CustomDrawer;
