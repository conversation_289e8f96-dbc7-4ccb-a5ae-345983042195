import { useAtom, useAtomValue } from 'jotai';
import React, { useState } from 'react';
import {
  editingActionAtom,
  editingNodeAtom,
  flowAtom,
  graphAtom,
  parentNodeAtom,
} from '../../atoms';
import { cloneDeep } from 'lodash';
import { Notify } from 'zent';
import { ENodeType } from '../../constant';
import { addChildNode } from '../../fns/graph';
import { useCustomDrawer } from './useCustomDrawer';

const useGraphOperation = () => {
  const [flowData, setFlowData] = useAtom(flowAtom);
  const editingAction = useAtomValue(editingActionAtom);
  const parentNode = useAtomValue(parentNodeAtom);
  const graph = useAtomValue(graphAtom);
  const editingNode = useAtomValue(editingNodeAtom);

  const { getDrawerData } = useCustomDrawer();
  const { isEditing } = getDrawerData();

  const handleFlowData = (action?: any) => {
    const newAction = action || cloneDeep(editingAction);
    const newFlowData = cloneDeep(flowData);

    // 编辑情况
    if (isEditing) {
      const parentNode = editingNode?.getData()?.parentNode;
      const parentNodeType = parentNode?.data ? parentNode?.data.type : parentNode.getData()?.type;
      const flowIndex = editingNode?.getData()?.index;

      if (parentNodeType === ENodeType.Action) {
        // newFlowData.chainRules![flowIndex].seriesActions = newAction;
        const parentActionIndex = parentNode.data?.actionIndex;
        const thisActionIndex = parentActionIndex + 1;
        // 确保seriesActions数组存在
        if (!newFlowData.chainRules![flowIndex].seriesActions) {
          newFlowData.chainRules![flowIndex].seriesActions = [];
        }
        newFlowData.chainRules![flowIndex].seriesActions![thisActionIndex] = newAction;
      } else {
        // 其余情况分两种 1、编辑链路 2、从条件新增子节点  都只需要对当前链路的action进行编辑
        newFlowData.chainRules![flowIndex].action = newAction!;
      }

      setFlowData(newFlowData);
      return;
    }
    // 其余情况
    // 如果父节点是trigger，说明action是新增链路，所以index是链路的长度
    // 如果父节点是condition，说明action是当前链路内的操作，所以index沿用父节点链路的索引
    if (!parentNode) return Notify.error('未找到父节点');
    if (!graph) return Notify.error('未找到画布');
    const { type: parentNodeType, index: parentIndex } = parentNode.getData();
    const flowIndex =
      parentNodeType === ENodeType.Trigger ? newFlowData.chainRules?.length || 0 : parentIndex;

    // 1、处理数据到flowData，把newAction的数据给flowData
    if (parentNodeType === ENodeType.Trigger) {
      // 只有从trigger进来是新增链路
      if (newFlowData.chainRules) {
        newFlowData.chainRules?.push({
          conditions: [],
          action: newAction!,
        });
      } else {
        newFlowData.chainRules = [{ conditions: [], action: newAction! }];
      }
    } else if (parentNodeType === ENodeType.Action) {
      const parentActionIndex = parentNode.data.actionIndex;
      const thisActionIndex = parentActionIndex + 1;
      // 确保seriesActions数组存在
      if (!newFlowData.chainRules![flowIndex].seriesActions) {
        newFlowData.chainRules![flowIndex].seriesActions = [];
      }
      newFlowData.chainRules![flowIndex].seriesActions![thisActionIndex] = newAction;
    } else {
      // 其余情况分两种 1、编辑链路 2、从条件新增子节点  都只需要对当前链路的action进行编辑
      newFlowData.chainRules![flowIndex].action = newAction!;
    }
    setFlowData(newFlowData);
  };

  const handleGraphData = (action?: any) => {
    const newAction = action || cloneDeep(editingAction);
    // 2、处理画布，编辑或新增节点信息
    if (isEditing) {
      editingNode?.setData(
        {
          ...editingNode.getData(),
          data: newAction,
        },
        { overwrite: true },
      );
    } else {
      if (!parentNode) return Notify.error('未找到父节点');
      if (!graph) return Notify.error('未找到画布');
      const { type: parentNodeType, index: parentIndex } = parentNode.getData();
      const flowIndex =
        parentNodeType === ENodeType.Trigger ? flowData.chainRules?.length || 0 : parentIndex;
      // 添加子节点
      addChildNode(parentNode, graph, {
        data: newAction,
        type: ENodeType.Action,

        index: flowIndex,
        parentNode,
      });
    }
  };

  return {
    handleFlowData,
    handleGraphData,
  };
};

export default useGraphOperation;
