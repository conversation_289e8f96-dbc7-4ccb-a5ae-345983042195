import { useAtom } from 'jotai';
import {
  drawerVisibleAtom,
  drawerType<PERSON>tom,
  drawerConditionIdxAtom,
  parentNodeAtom,
  isEditingAtom,
  editingNodeAtom,
  subDrawerVisibleAtom,
  subDrawerDataAtom,
  subDrawerSelectedAtom,
  editingActionAtom,
} from '../../atoms'; // 假设这是你的原子状态管理器
import { ENodeType } from 'pages/work-flow/constant/node'; // 确保正确导入 ENodeType 类型
import { Node } from '@antv/x6';
import { IAction, IActionItem, IDrawerData } from 'pages/work-flow/constant';

interface CustomDrawerState {
  showDrawer: (params: IDrawerData) => void;
  showSubDrawer: (
    params: { data: IActionItem; selected: IAction } & Omit<IDrawerData, 'type'>,
  ) => void;
  hideDrawer: () => void;
  getDrawerData: () => {
    visible: boolean;
    type: ENodeType;
    conditionIdx: number | null;
    parentNode: Node | null;
    isEditing: boolean;
  };
  getSubDrawerData: () => {
    subDrawerVisible: boolean;
    subDrawerData: IActionItem | null;
    subDrawerSelected: IAction | null;
  };
  hideSubDrawer: () => void;
}

export function useCustomDrawer(): CustomDrawerState {
  const [visible, setVisible] = useAtom(drawerVisibleAtom);
  const [drawerType, setDrawerType] = useAtom(drawerTypeAtom);
  const [drawerConditionIdx, setDrawerConditionIdx] = useAtom(drawerConditionIdxAtom);

  const [isEditing, setIsEditing] = useAtom(isEditingAtom);
  const [parentNode, setParentNode] = useAtom(parentNodeAtom);
  const [editingNode, setEditingNode] = useAtom(editingNodeAtom);
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [subDrawerVisible, setSubDrawerVisible] = useAtom(subDrawerVisibleAtom);
  const [subDrawerData, setSubDrawerData] = useAtom(subDrawerDataAtom);
  const [subDrawerSelected, setSubDrawerSelected] = useAtom(subDrawerSelectedAtom);

  const showDrawer = ({ type, isEdit, conditionIdx, parentNode, currentNode }: IDrawerData) => {
    setDrawerType(type);
    setDrawerConditionIdx(conditionIdx);
    setParentNode(parentNode);
    setIsEditing(isEdit);
    setEditingNode(currentNode);
    setVisible(true);
  };

  const showSubDrawer = ({ isEdit, conditionIdx, parentNode, currentNode, data, selected }) => {
    setSubDrawerData(data);
    setSubDrawerSelected(selected);
    setDrawerConditionIdx(conditionIdx);
    setParentNode(parentNode);
    setIsEditing(isEdit);
    setEditingNode(currentNode);
    setSubDrawerVisible(true);
    // 从模板一进来，action只有一半，二级action为空，直接设置的话需要先给editingAction赋值
    setEditingAction(data);
  };

  const getSubDrawerData = () => {
    return {
      subDrawerVisible,
      subDrawerData,
      subDrawerSelected,
    };
  };

  const hideSubDrawer = () => {
    setSubDrawerVisible(false);
  };

  const getDrawerData = () => {
    return {
      visible,
      type: drawerType,
      conditionIdx: drawerConditionIdx,
      parentNode,
      isEditing,
    };
  };

  const hideDrawer = () => setVisible(false);

  return {
    showDrawer,
    showSubDrawer,
    hideDrawer,
    getDrawerData,
    getSubDrawerData,
    hideSubDrawer,
  };
}
