import { useMemo } from 'react';
import { Select, NumberInput } from 'zent';
import { useAtom } from 'jotai';
import { editingConditionAtom } from 'pages/work-flow/atoms';
import { TimeUnit, TimeMax, defaultUnitTime } from 'pages/work-flow/constant';

type SyncParams = {
  time?: string;
  opt?: typeof TimeUnit[number];
};

export default function UnitTime({ index, expression, expressionIndex }) {
  const [editingCondition, setEditingCondition] = useAtom(editingConditionAtom);

  const sync = ({ time = defaultUnitTime.time, opt = defaultUnitTime.opt }: SyncParams = {}) => {
    setEditingCondition(prev => {
      const copies = [...prev!];
      const expression = copies[index].expressions[expressionIndex];
      if (!expression) return prev;
      const newValue = { time, timeUnit: opt?.key };
      /**
       * ? https://jira.qima-inc.com/browse/CSWT-164666
       * ! 使用这个组件的地方有的是 openField 有的是 timeValue 所以两个都存上
       */
      expression.openField = JSON.stringify(newValue);
      expression.timeValue = newValue;

      return copies;
    });
  };

  const [input, select] = useMemo(() => {
    let timeValue = {
      time: defaultUnitTime.time,
      timeUnit: defaultUnitTime.opt.key,
    };

    try {
      /**
       * ? https://jira.qima-inc.com/browse/CSWT-164661
       * ! 使用这个组件的地方有的是 openField 有的是 timeValue 所以两个都存上
       */
      timeValue = expression.timeValue || JSON.parse(expression.openField);
    } catch (error) {
      // 没有值的时候 使用默认值
      sync();
    }

    const { time, timeUnit } = timeValue;
    const timeOpt = TimeUnit.find(item => item.key === timeUnit);

    return [time, timeOpt];
  }, [editingCondition]);

  const max = TimeMax[select?.key!] || 0;

  const handleInputChange = event => {
    // let value: string | number = +event.target.value;
    let value: string | number = +event;
    if (value < 0) {
      value = 0;
    }

    if (value > max) {
      value = max;
    }

    sync({ time: `${value}`, opt: select });
  };

  const handleSelectChange = opt => {
    sync({ opt, time: input });
  };

  if (+input > max) {
    sync({ time: `${max}`, opt: select });
  }

  return (
    <div className="delay-field">
      <NumberInput
        size="large"
        className="delay-field__item"
        type="number"
        placeholder="输入时间数值"
        value={input}
        min={0}
        max={max}
        decimal={0}
        onChange={handleInputChange}
      />
      <Select
        size="l"
        className="delay-field__item"
        value={select}
        options={TimeUnit}
        onChange={handleSelectChange}
      />
    </div>
  );
}
