@use 'sass/vars/index.scss';

.condition-content {
  padding: 16px 24px 0;
  .label {
    color: #333;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }
  .condition-container {
    .and {
      margin: 16px 0;
      color: #333;
      font-feature-settings: 'clig' off, 'liga' off;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px; /* 157.143% */
    }
    .condition-group {
      padding: 12px 8px 8px 12px;
      background-color: #f7f7f7;
      border-radius: 4px;
      .name-wrapper {
        display: flex;
        align-items: center;
        // padding-bottom: 4px;
        padding: 0 0 4px 4px;
        .name {
          color: #333;
          font-feature-settings: 'clig' off, 'liga' off;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px; /* 157.143% */
          flex-grow: 1;
        }
        .reduce-fill-icon {
          width: 28px;
          height: 28px;
          cursor: pointer;

          &:hover {
            background-color: #f0f0f0;
          }
        }
      }

      .condition-wrapper {
        margin-top: 8px;
        display: flex;
        .expressions-box {
          flex-grow: 1;
        }
        .expression-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;

          .date-picker {
            //flex:1;
            //width: 185px;
            height: 38px;
            flex: 1;
            .zent-datepicker-trigger {
              width: unset !important;
              height: 37px;
            }
          }

          .delay-field {
            width: 225px;
            display: flex;
            flex-grow: 1;
            gap: 5px;
            margin-right: 4px;

            &__item {
              flex: 110px;
              display: flex;
              justify-content: flex-start;
              align-items: center;

              & .zent-select-v2-search {
                position: static;
              }
            }
          }

          .custom-select {
            width: 185px;
            height: 38px;
            margin-right: 4px;

            &:last-child {
              margin-right: 0;
            }
            .zent-select-v2-text {
              line-height: 26px;
            }
            .zent-select-v2-placeholder {
              line-height: 26px;
            }
            .zenticon-down {
              top: 50%;
              transform: translateY(-50%);
            }
            .option-scroll-wrapper {
              height: 100px;
            }
            .zent-select-v2-search {
              line-height: 26px;
            }
            .zenticon-close-circle {
              top: 8px;
            }

            .custom-selected-item {
              display: flex;
              align-items: center;
              .selected-item-name {
                flex-shrink: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .selected-item-length {
                flex-shrink: 0;
              }
            }
          }

          .variable-select {
            width: 180px;
          }

          .operator-select {
            width: 120px;
          }

          .value-select {
            flex-grow: 1;
          }
        }
        .expression-item:first-child {
          margin-top: 0;
        }

        .reduce-icon {
          width: 28px;
          height: 28px;
          margin-left: 8px;
          cursor: pointer;

          &:hover {
            background-color: #f0f0f0;
          }
        }
      }

      .add-condition {
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 16px 8px 12px;
        margin-top: 8px;
        &:hover {
          background-color: #f0f0f0;
        }
        .add-icon {
          width: 20px;
          height: 20px;
          margin-right: 4px;
        }
      }
    }
    .field-value-input {
      height: 38px;
    }

    .add-condition-group {
      cursor: pointer;
      display: inline-flex;
      // width: 122px;
      // height: 36px;
      box-sizing: border-box;
      border-radius: 4px;
      margin-top: 16px;
      padding: 8px 16px 8px 12px;
      background-color: #f7f7f7;
      align-items: center;

      &:hover {
        background-color: #f0f0f0;
      }

      .add-icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
      span {
        color: #333;
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 142.857% */
      }
    }
  }
}

.zent-popover-v2 {
  // padding: 8px!important;
}
