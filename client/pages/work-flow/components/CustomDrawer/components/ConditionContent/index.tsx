import {
  DEFAULT_EXPRESSION,
  ENodeType,
  ICondition,
  IConditionContent,
} from 'pages/work-flow/constant';
import { forwardRef, useEffect, useImperativeHandle } from 'react';
import './style.scss';
import { AddFillIcon, AddIcon, ReduceFillIcon } from 'svg';
import { Notify } from 'zent';
import {
  drawerConditionIdxAtom,
  editingConditionAtom,
  editingNodeAtom,
  flowAtom,
} from 'pages/work-flow/atoms';
import { useAtom, useAtomValue } from 'jotai';
import SelectItem from './SelectItem';
import { useCustomDrawer } from '../../useCustomDrawer';
import { addChildNode } from 'pages/work-flow/fns/graph';
import { graphAtom } from 'pages/work-flow/atoms/graph';
import { cloneDeep } from 'lodash';
import ResponsiveCurlyBrace from 'pages/work-flow/components/ResponsiveCurlyBrace';

const ConditionContent = forwardRef<any, IConditionContent>((_, ref) => {
  const { getDrawerData } = useCustomDrawer();
  const { parentNode, isEditing } = getDrawerData();

  const editingNode = useAtomValue(editingNodeAtom);
  const [flowData, setFlowData] = useAtom(flowAtom);
  const [editingCondition, setEditingCondition] = useAtom(editingConditionAtom);
  const [drawerConditionIdx, setDrawerConditionIdx] = useAtom(drawerConditionIdxAtom);
  const graph = useAtomValue(graphAtom);
  const handleFlowData = filteredCondition => {
    const newFlow = cloneDeep(flowData);
    // 1、更新flowData数据
    if (isEditing) {
      const flowIndex = editingNode?.getData().index;
      newFlow.chainRules![flowIndex].conditions = filteredCondition!;

      setFlowData(newFlow);
      return;
    }

    if (filteredCondition) {
      if (drawerConditionIdx && newFlow.chainRules?.[drawerConditionIdx]) {
        newFlow.chainRules[drawerConditionIdx].conditions = filteredCondition;
        return;
      }
      if (newFlow.chainRules) {
        // 前面不是 trigger 就不要新增 rule 了
        const { index } = parentNode?.getData() || {};
        if (newFlow.chainRules[index]) {
          newFlow.chainRules[index].conditions = filteredCondition;
        } else {
          newFlow.chainRules.push({
            conditions: filteredCondition,
            action: {
              name: '',
              variables: [],
            },
          });
        }
      } else {
        newFlow.chainRules = [
          {
            conditions: filteredCondition,
            action: {
              name: '',
              variables: [],
            },
          },
        ];
      }
    }
    // 设置完了，把DrawerConditionIdx设置为null
    setDrawerConditionIdx(null);
    setFlowData(newFlow);
  };

  const handleGraphData = filteredCondition => {
    // 2、更新graph
    // addChildNode
    if (isEditing) {
      const newData = {
        ...editingNode?.getData(),
        data: cloneDeep(filteredCondition),
      };
      editingNode?.setData(newData, { overwrite: true });
      const allConditionNodes = graph
        ?.getNodes()
        .filter(node => node.getData().type === ENodeType.Condition && node !== editingNode);

      let currentNode = editingNode;
      allConditionNodes?.forEach(node => {
        // 根据x,y,size计算节点是否重叠，重叠的话默认往下移动 间距150
        // TODO: 临时针对condition写一下，后续监听node:changed 做autoLayout就好了
        const { y } = currentNode?.getPosition() || {
          y: 0,
        };
        const { height } = currentNode?.getSize() || {
          height: 0,
        };
        const index = currentNode?.getData().index;
        const editingNodeBottomY = y + height;
        if (node) {
          const { x, y } = node.getPosition();
          const compareIndex = node.getData().index;
          if (editingNodeBottomY > y - 100 && index <= compareIndex) {
            node.setPosition(
              {
                x,
                y: editingNodeBottomY + 150,
              },
              { overwrite: true },
            );
            currentNode = node;
          }
        }
      });
    } else {
      // 添加新条件节点
      if (!graph) return Notify.error('未找到graph');
      const flowIndex = editingNode?.getData().index;
      const parentData = parentNode?.getData();
      /**
       * 1. 父节点是 trigger 需要使用 chainRules 的 length 为 index 这样后面的 action 可以直接插进去
       * 2. 父节点是其他 Node 则需要使用父节点的 index 去插入
       */
      let index = flowIndex;

      if (parentData.type === ENodeType.Trigger) {
        index = flowData.chainRules?.length;
      }

      // 如果是 delay 节点，那么 index 就等于 parentNode 的 index
      if (parentData.type === ENodeType.Delay) {
        index = parentData.index;
      }

      addChildNode(parentNode, graph, {
        type: ENodeType.Condition,
        data: cloneDeep(filteredCondition),
        index: index || 0,
        parentNode,
      });
    }
  };

  useImperativeHandle(ref, () => ({
    onConfirm: () => {
      const clonedEditingCondition = cloneDeep(editingCondition);
      let filteredCondition = clonedEditingCondition?.map(item => {
        return {
          ...item,
          expressions: item.expressions.filter(expression => {
            return expression.variable.name;
          }),
        };
      });
      filteredCondition = filteredCondition?.filter(
        item => item.expressions.filter(item => item.variable.name).length,
      );
      setEditingCondition(filteredCondition as ICondition[]);

      handleFlowData(filteredCondition);
      handleGraphData(filteredCondition);
    },
  }));

  useEffect(() => {
    // 初次渲染时，将传入的数据设置到编辑数据中
    if (isEditing) {
      const { data } = editingNode?.getData();
      setEditingCondition(cloneDeep(data));
    } else {
      // 如果是新增节点，设置默认空值
      setEditingCondition([
        {
          expressions: [
            {
              field: '',
              // @ts-ignore
              operator: null,
              // @ts-ignore
              variable: null,
              value: '',
            },
          ],
        },
      ]);
    }
  }, []);

  const addCondition = index => {
    // 限制最大10条
    if (editingCondition && editingCondition?.[index]?.expressions?.length >= 10)
      return Notify.error('一个条件组最多添加10个条件');

    const defaultExpression = cloneDeep(DEFAULT_EXPRESSION);
    setEditingCondition(prev => {
      const newCondition = [...prev!];
      newCondition[index].expressions.push(defaultExpression);
      return newCondition;
    });
  };

  const addConditionGroup = () => {
    if (editingCondition && editingCondition.length >= 10)
      return Notify.error('最多添加10个条件组');

    const defaultExpression = cloneDeep(DEFAULT_EXPRESSION);
    if (!editingCondition) {
      setEditingCondition([
        {
          expressions: [defaultExpression],
        },
      ]);
      return;
    }
    setEditingCondition(prev => {
      return [
        ...prev!,
        {
          expressions: [defaultExpression],
        },
      ];
    });
  };

  const reduceCondition = index => {
    setEditingCondition(prev => {
      const newCondition = [...prev!];
      newCondition.splice(index, 1);
      return newCondition;
    });
  };

  const deleteSelectItem = (index, expressionIndex) => {
    setEditingCondition(prev => {
      const newCondition = [...prev!];
      newCondition[index].expressions.splice(expressionIndex, 1);
      return newCondition;
    });
  };

  return (
    <div className="condition-content">
      <div className="condition-container">
        {editingCondition?.map((item, index) => {
          return (
            <div key={index + '' + new Date().toDateString}>
              {index !== 0 && <div className="and">或</div>}
              <div className="condition-group">
                <div className="name-wrapper">
                  <p className="name">条件组{index + 1}</p>
                  {editingCondition.length > 1 && (
                    <div className="reduce-fill-icon" onClick={() => reduceCondition(index)}>
                      <ReduceFillIcon width={28} height={28} mainColor="#4a4a4a" />
                    </div>
                  )}
                </div>

                <div className="condition-wrapper">
                  {/* 左侧大括号图案 */}
                  {item.expressions.length > 1 && <ResponsiveCurlyBrace />}

                  <div className="expressions-box">
                    {item.expressions?.map((expression, expressionIndex) => {
                      return (
                        <SelectItem
                          key={index + '' + expressionIndex}
                          expression={expression}
                          expressionIndex={expressionIndex}
                          showDelBtn={item.expressions.length > 1}
                          index={index}
                          setEditingCondition={setEditingCondition}
                          deleteSelectItem={deleteSelectItem}
                        />
                      );
                    })}
                  </div>
                </div>

                <div className="add-condition" onClick={() => addCondition(index)}>
                  <div className="add-icon">
                    <AddIcon width={20} height={20} mainColor="#4a4a4a" />
                  </div>
                  <p>添加条件</p>
                </div>
              </div>
            </div>
          );
        })}
        <div className="add-condition-group" onClick={addConditionGroup}>
          <div className="add-icon">
            <AddFillIcon />
          </div>
          <span>添加条件组</span>
        </div>
      </div>
    </div>
  );
});

export default ConditionContent;
