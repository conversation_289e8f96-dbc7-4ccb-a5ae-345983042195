import { useAtom } from 'jotai';
import {
  getListFieldsByTriggerAndVariable,
  getListOperatorsByVariable,
  getListVariablesByTrigger,
} from 'pages/work-flow/api';
import { flowAtom } from 'pages/work-flow/atoms';
import React, { useMemo, useState } from 'react';
import {
  InfiniteScroller,
  InlineLoading,
  Input,
  Notify,
  Select,
  NumberInput,
  DatePicker,
} from 'zent';
import './style.scss';
import ItemSelect, { ItemSelectField } from '../../../ItemSelect';
import UnitTime from './UnitTime';
import { ReduceIcon } from 'svg';
import { DEFAULT_EXPRESSION, TemplateStyle } from 'pages/work-flow/constant';
import { cloneDeep } from 'lodash';

const SelectItem = ({
  index,
  expression,
  expressionIndex,
  setEditingCondition,
  deleteSelectItem,
  showDelBtn,
  key,
}) => {
  const [variableOptions, setVariableOptions] = useState<any[]>([]);
  const [operatorOptions, setOperatorOptions] = useState<any[]>([]);
  const [fieldOptions, setFieldOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const [variableOpen, setVariableOpen] = useState(false);
  const [operatorOpen, setOperatorOpen] = useState(false);
  const [fieldOpen, setFieldOpen] = useState(false);

  const [flowData, setFlowData] = useAtom(flowAtom);

  // 特殊选择
  const isItemSelect = expression.variable?.valueSource === 3;
  const itemSelectConfig = useMemo(
    () =>
      expression.variable?.dynamicEndpointConfig &&
      JSON.parse(expression.variable?.dynamicEndpointConfig),
    [expression],
  );

  // 获取变量列表
  const fetchVariableOptions = async (page: number) => {
    setLoading(true);
    try {
      const res = await getListVariablesByTrigger({
        page,
        pageSize: 99, // 假设每次加载99条数据
        triggerId: flowData.triggerId,
      }).catch(err => {
        Notify.error(err?.msg || err || '查询变量列表失败');
      });
      const { items } = res;
      setLoading(false);
      // 更新选项
      if (page === 1) {
        setVariableOptions(
          items.map(item => ({
            data: item,
            key: item.id,
            text: item.name,
          })),
        );
        return;
      }
      setVariableOptions(prevOptions => {
        const newVariable = [
          ...prevOptions,
          ...items.map(item => ({
            data: item,
            key: item.id,
            text: item.name,
          })),
        ];
        return newVariable;
      });
    } catch (error) {
      console.error('Error fetching options:', error);
      setLoading(false);
    }
  };
  // 获取操作符列表
  const fetchOperatorOptions = async page => {
    setLoading(true);
    try {
      const res = await getListOperatorsByVariable({
        variableId: expression.variable?.id,
        page,
        pageSize: 99,
      }).catch(err => {
        Notify.error(err?.msg || err || '查询操作符列表失败');
      });
      setLoading(false);
      // 更新选项
      if (page === 1) {
        setOperatorOptions(
          res.map(item => ({
            data: item,
            key: item.id,
            text: item.name,
          })),
        );
        return;
      }
      setOperatorOptions(prevOptions => [
        ...prevOptions,
        ...res.map(item => ({
          data: item,
          key: item.id,
          text: item.name,
        })),
      ]);
    } catch (error) {
      console.error('Error fetching options:', error);
      setLoading(false);
    }
  };
  // 获取字段列表
  const fetchFieldOptions = async page => {
    setLoading(true);
    try {
      const res = await getListFieldsByTriggerAndVariable({
        triggerId: flowData.triggerId,
        variableId: expression.variable?.id,
        page,
        pageSize: 99,
      });
      const { items } = res;
      setLoading(false);
      // 更新选项
      if (page === 1) {
        setFieldOptions(
          items.map(item => ({
            ...item,
            key: item.id,
            text: item.name,
          })),
        );
        return;
      }
      setFieldOptions(prevOptions => [
        ...prevOptions,
        ...items.map(item => ({
          ...item,
          key: item.id,
          text: item.name,
        })),
      ]);
    } catch (error) {
      console.error('Error fetching options:', error);
      setLoading(false);
    }
  };

  // 处理浮层打开
  const handleOpenChange = (isOpen: boolean, type) => {
    if (type === 'variable' && isOpen) {
      fetchVariableOptions(1); // 首次打开时加载第一页数据
    } else if (type === 'operator' && isOpen) {
      if (!expression.variable.id) return Notify.error('请先选择变量');
      fetchOperatorOptions(1); // 首次打开时加载第一页数据
    } else if (type === 'field' && isOpen) {
      if (!expression.variable.id) return Notify.error('请先选择变量');
      fetchFieldOptions(1); // 首次打开时加载第一页数据
    }
    if (type === 'variable') setVariableOpen(isOpen);
    if (type === 'operator') setOperatorOpen(isOpen);
    if (type === 'field') setFieldOpen(isOpen);
  };

  // 滚动加载更多
  const loadMore = (closeLoading: () => void) => {
    setLoading(true);
    return fetchVariableOptions(variableOptions.length + 1) // 加载下一页数据
      .then(() => closeLoading())
      .catch(() => setLoading(false));
  };

  const renderOptionList = (
    optionList: any[],
    renderOption: (option: any, index: number) => JSX.Element,
    type: 'variable' | 'operator' | 'field',
  ): React.JSX.Element => {
    return (
      <InfiniteScroller
        hasMore={type === 'variable'}
        skipLoadOnMount
        loadMore={() => {
          if (type !== 'variable') return;
          loadMore(() => setLoading(false));
        }}
        className="option-scroll-wrapper"
        loader={
          <div className="loading-text">
            <InlineLoading
              iconSize={18}
              loading
              icon="circle"
              iconText="加载中…"
              textPosition="right"
              colorPreset="grey"
            />
          </div>
        }
      >
        {optionList.map((item, index) => renderOption(item, index))}
      </InfiniteScroller>
    );
  };

  const onVariableChange = value => {
    const defaultExpression = cloneDeep(DEFAULT_EXPRESSION);
    if (!value) {
      setEditingCondition(prev => {
        const newCondition = [...prev!];
        // newCondition[index].expressions[expressionIndex].variable = null;
        newCondition[index].expressions[expressionIndex] = defaultExpression;
        return newCondition;
      });
      return;
    }
    setEditingCondition(prev => {
      const newCondition = [...prev!];
      // 清空当前expression的operator和value
      newCondition[index].expressions[expressionIndex] = defaultExpression;
      newCondition[index].expressions[expressionIndex].variable = {
        ...value.data,
        text: value.data.name,
        key: value.data.id,
      };
      return newCondition;
    });
    // 切换变量时 清空当前expression的operator和value
    onFieldValueChange([]);
  };

  const onOperatorChange = (value: any) => {
    const defaultExpression = cloneDeep(DEFAULT_EXPRESSION);
    if (!value) {
      setEditingCondition(prev => {
        const newCondition = [...prev!];
        newCondition[index].expressions[expressionIndex] = {
          ...defaultExpression,
          variable: newCondition[index].expressions[expressionIndex].variable,
        };
        return newCondition;
      });
      return;
    }
    setEditingCondition(prev => {
      const newCondition = [...prev!];
      newCondition[index].expressions[expressionIndex] = {
        ...defaultExpression,
        variable: newCondition[index].expressions[expressionIndex].variable,
        operator: {
          ...value.data,
          text: value.data.name,
          key: value.data.id,
        },
      };
      return newCondition;
    });
  };

  const formatSelectValue = (value: any[]) => {
    return (
      value?.map(item => {
        // 如果没有text和key，那么就用name和id作为text和key
        if (!item.text) {
          item.text = item.name;
          item.key = item.id;
        }
        return item;
      }) || []
      /**
       * ? https://jira.qima-inc.com/browse/CSWT-164542
       * ! 看着像是组件的 bug，如果这个函数返回 undefined
       * ! 组件 renderTagList 就会渲染上次的值
       * todo: 时间紧迫 以后再定位具体原因
       */
    );
  };

  const onFieldValueChange = (value, isMultiSelect = true) => {
    console.log('onFieldValueChange value is ', value);
    console.log('valueSource is ', expression.variable?.valueSource);
    setEditingCondition(prev => {
      const newCondition = [...prev!];
      const valueSource = expression.variable?.valueSource;
      if (valueSource === 1) {
        newCondition[index].expressions[expressionIndex].openField = value;
      } else if (valueSource === 2) {
        if (!isMultiSelect) {
          const oldValue = formatSelectValue(expression.restrictedFields);
          if (oldValue && oldValue.length) {
            // 单选 选择value比oldValue多的值
            const newValue = value.find(item => !oldValue.find(oldItem => oldItem.id === item.id));
            value = [newValue];
          }
        }
        if (value.length || value.length !== 0) {
          newCondition[index].expressions[expressionIndex].restrictedFields = value.map(item => {
            return { ...item, text: item.name, key: item.id };
          });
        } else {
          newCondition[index].expressions[expressionIndex].restrictedFields = [];
        }
      } else if (valueSource === 3) {
        const { selected, ...rest } = value;
        newCondition[index].expressions[expressionIndex].fieldValueMaps4Display = JSON.stringify({
          [expression.variable?.code]: selected,
        });
        newCondition[index].expressions[expressionIndex].fieldValueMaps4Execute = JSON.stringify(
          rest,
        );
      } else if (valueSource === 4) {
        newCondition[index].expressions[expressionIndex].openField = String(value);
      }
      return newCondition;
    });
  };

  const renderValueCmp = expression => {
    const pattern = expression.variable?.validateRule;
    const valueSource = expression.variable?.valueSource || 1;
    const dynamicEndpointConfig = expression.variable?.dynamicEndpointConfig || '{}';
    const parsedDynamicEndpointConfig = JSON.parse(dynamicEndpointConfig || '{}');
    const { isMultiSelect } = parsedDynamicEndpointConfig;
    const extension = JSON.parse(expression.variable?.extension || '{}');
    const styleTemplate = extension?.variablePattern?.styleTemplate?.template;
    const isUnitYuan = styleTemplate === TemplateStyle.ChinaYuanStyle;
    const validateRules = extension?.variablePattern?.parameterRules || {};
    const inputValue = expression.openField || '';
    const customSelectSubValue = JSON.parse(expression?.fieldValueMaps4Execute || '{}')?.[
      expression?.variable?.code
    ];
    const customSelectMainValue =
      JSON.parse(expression?.fieldValueMaps4Display || '{}')?.[expression?.variable?.code]?.map(
        item => item.id,
      ) || [];

    const onInputChange = e => {
      const value = typeof e === 'object' ? e.target.value : e;
      if (pattern && value) {
        const regex = new RegExp(pattern);
        if (regex.test(value)) {
          onFieldValueChange(value);
        }
      } else {
        onFieldValueChange(value);
      }
    };
    switch (valueSource) {
      case 1:
        return isUnitYuan ? (
          <NumberInput
            size="large"
            className={'field-value-input value-select'}
            value={inputValue}
            onChange={onInputChange}
            decimal={2}
            {...validateRules}
          />
        ) : (
          <Input
            size="large"
            className={'field-value-input value-select'}
            value={inputValue}
            onChange={onInputChange}
            {...validateRules}
          />
        );
      case 2:
        return (
          <Select
            key={expressionIndex + 'field'}
            className="custom-select value-select"
            // clearable 开启后默认可输入搜索
            // clearable
            options={fieldOptions}
            placeholder="请选择"
            multiple
            loading={loading}
            open={fieldOpen}
            onOpenChange={isOpen => handleOpenChange(isOpen, 'field')}
            containerSelector={'.expression-item'}
            // @ts-ignore
            renderOptionList={(
              optionList: any[],
              renderOption: (option: any, index: number) => JSX.Element,
            ) => renderOptionList(optionList, renderOption, 'field')}
            value={formatSelectValue(expression.restrictedFields)}
            onChange={value => {
              onFieldValueChange(value, isMultiSelect);
            }}
            renderTagList={items => {
              return (
                <p className="custom-selected-item">
                  <span className="selected-item-name">{items.list[0].name}</span>
                  <span className="selected-item-length">
                    {items.list.length > 1 ? `等${items.list.length}个` : ''}
                  </span>
                </p>
              );
            }}
          />
        );
      case 3:
        // 公用选择器
        return (
          <ItemSelectField
            type={itemSelectConfig.apiId}
            outputKey={expression.variable?.code}
            subType={itemSelectConfig.responseCovert}
            extParam={itemSelectConfig.extParam}
            value={{ selectedMainIds: customSelectMainValue, selectedSubIds: customSelectSubValue }}
            onChange={onFieldValueChange}
            disabled={!expression.operator?.id}
            onError={() => {
              Notify.error('请先选择操作符');
            }}
          />
        );
      case 4:
        if (styleTemplate === TemplateStyle.DateTimePicker) {
          // 时间选择器
          return (
            <DatePicker
              className="date-picker"
              value={Number(expression.openField)}
              onChange={value => {
                if (value) {
                  const formatedValue = new Date(value);
                  formatedValue.setSeconds(0, 0);
                  formatedValue.setMilliseconds(0);
                  onFieldValueChange(formatedValue.getTime());
                }
              }}
              showTime={{
                format: 'HH:mm',
              }}
              format="YYYY-MM-DD HH:mm"
              valueType="number"
            ></DatePicker>
          );
        }
        break;
      case 7:
        return <UnitTime index={index} expression={expression} expressionIndex={expressionIndex} />;
      default:
        return null;
    }
  };
  return (
    <div className="expression-item" key={key}>
      <>
        <Select
          key={expressionIndex + 'variable'}
          className="custom-select variable-select"
          clearable
          options={variableOptions}
          placeholder="请选择"
          loading={loading}
          value={
            expression.variable?.name
              ? {
                  ...expression.variable,
                  text: expression.variable?.name,
                  key: expression.variable?.id,
                }
              : null
          }
          onChange={onVariableChange}
          open={variableOpen}
          onOpenChange={isOpen => handleOpenChange(isOpen, 'variable')}
          // @ts-ignore
          renderOptionList={renderOptionList}
        />

        <Select
          key={expressionIndex + 'operator'}
          className="custom-select operator-select"
          clearable
          options={operatorOptions}
          placeholder="请选择"
          loading={loading}
          open={operatorOpen}
          value={
            expression.operator?.name
              ? {
                  ...expression.operator,
                  text: expression.operator?.name,
                  key: expression.operator?.id,
                }
              : null
          }
          onOpenChange={isOpen => {
            if (!expression.variable) return Notify.error('请先选择变量');
            handleOpenChange(isOpen, 'operator');
          }}
          // @ts-ignore
          renderOptionList={renderOptionList}
          onChange={onOperatorChange}
        />
        {renderValueCmp(expression)}
      </>
      {showDelBtn && (
        <div className="reduce-icon" onClick={() => deleteSelectItem(index, expressionIndex)}>
          <ReduceIcon width={28} height={28} mainColor="#4a4a4a" />
        </div>
      )}
    </div>
  );
};

export default SelectItem;
