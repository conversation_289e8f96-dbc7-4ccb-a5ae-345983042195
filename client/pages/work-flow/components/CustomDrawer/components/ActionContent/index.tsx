import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { getListActionsByTrigger, getListCanBeConcatenateActions } from 'pages/work-flow/api';
import {
  editingActionAtom,
  editingNodeAtom,
  flowAtom,
  parentNodeAtom,
} from 'pages/work-flow/atoms';
import { useAtom } from 'jotai';
import './style.scss';
import { Icon, Notify, Pop } from 'zent';
import { EGroupCode, ENodeType, IActionItem } from 'pages/work-flow/constant';
import SubDrawer from '../SubDrawer';
import { useCustomDrawer } from '../../useCustomDrawer';
import { cloneDeep } from 'lodash';
import classNames from 'classnames';
import { SelectedSubScriptIcon } from '../../../../../../svg';
import useGraphOperation from '../../useGraphOperation';
import withBusinessCheck from 'pages/work-flow/HOC/withBusinessCheck';
import { renderIconByType } from 'pages/work-flow/fns';

const ActionContent = forwardRef<any, any>((props, ref) => {
  // drawer显示隐藏
  const [visible, setVisible] = useState(false);
  const [subDrawerData, setSubDrawerData] = useState<IActionItem | null>(null);

  const [flowData, setFlowData] = useAtom(flowAtom);
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [actions, setActions] = useState<IActionItem[]>([]);
  const [editingNode, setEditingNode] = useAtom(editingNodeAtom);
  const [parentNode, setParentNode] = useAtom(parentNodeAtom);

  const { hideDrawer, getDrawerData } = useCustomDrawer();
  const { isEditing } = getDrawerData();

  const { handleFlowData, handleGraphData } = useGraphOperation();

  useImperativeHandle(ref, () => ({
    onConfirm: () => {},
  }));

  useEffect(() => {
    if (visible) {
      const DrawerMasks = document.querySelectorAll('.zent-drawer-backdrop');
      DrawerMasks?.forEach(item => {
        // @ts-ignore
        item.style.zIndex = -1;
      });
      // @ts-ignore
      DrawerMasks[0].style.zIndex = 1000;
    }
  }, [visible]);

  useEffect(() => {
    if (isEditing) {
      const { data } = editingNode?.getData();
      setEditingAction(cloneDeep(data));
    } else {
      setEditingAction({
        name: '',
        variables: [],
      });
    }

    let parentType = '';
    let parentData: any = {};
    if (parentNode) {
      const { type, data = {} } = parentNode?.getData() || {};
      parentType = type;
      parentData = data;
    } else {
      const parentNode = editingNode?.getData().parentNode;
      if (parentNode) {
        const { type, data } = parentNode.data ? parentNode.data : parentNode.getData();
        parentType = type;
        parentData = data;
      }
    }
    const { id: actionId } = parentData;
    if (parentType === ENodeType.Action) {
      getListCanBeConcatenateActions({
        actionId,
      })
        .then(res => {
          const { items } = res;
          setActions(res as IActionItem[]);
        })
        .catch(err => {
          Notify.error(err?.msg || err || '查询操作列表失败');
        });
    } else {
      getListActionsByTrigger({
        page: 1,
        pageSize: 99,
        triggerId: flowData.triggerId,
      })
        .then(res => {
          const { items } = res;
          setActions(items as IActionItem[]);
        })
        .catch(err => {
          Notify.error(err?.msg || err || '查询操作列表失败');
        });
    }
  }, []);

  const hideSubDrawer = () => {
    setVisible(false);
    setSubDrawerData(null);
  };

  const onSelectAction = (action: IActionItem) => {
    // 如果action的variables没有，则说明不需要打开子页面
    if (!action.variables || !action.variables.length) {
      // 给editingAction赋值
      const newAction = {
        ...action,
      };
      setEditingAction(newAction);

      handleFlowData(newAction);
      handleGraphData(newAction);

      hideDrawer();
      return;
    }

    // 给editingAction赋值
    // setEditingAction(action);
    // 判断是不是点的同一个二级，二级页如果有数据需要带过去
    if (editingAction?.id !== action.id) {
      setEditingAction(prev => {
        return {
          ...action,
        };
      });
    }

    setSubDrawerData(action);
    // hideDrawer();
    setVisible(true);
  };

  const ActionItem = withBusinessCheck(
    ({ action, disabled }: { action: IActionItem; disabled?: boolean }) => {
      return (
        <div
          // className={'trigger-item' + (selectedTrigger === trigger.id ? ' selected' : '')}
          className={classNames('action-item-container', {
            selected: editingAction?.id === action.id,
          })}
          key={action.id}
          onClick={() => {
            if (disabled) {
              return;
            }
            onSelectAction(action);
          }}
        >
          <div className="action-item">
            {renderIconByType({ type: ENodeType.Action, groupCode: action?.groupCode })}
            {!disabled ? (
              <Pop
                trigger="hover"
                position={'auto-top-center'}
                content={<div style={{ whiteSpace: 'pre-wrap' }}>{action.name}</div>}
                style={{ maxWidth: 300 }}
              >
                <span className="action-name">{action.name}&nbsp;</span>
              </Pop>
            ) : (
              <span className="action-name">{action.name}&nbsp;</span>
            )}
            {action.description && (
              <>
                {!disabled ? (
                  <Pop
                    trigger="hover"
                    content={
                      <div
                        style={{ whiteSpace: 'pre-wrap' }}
                        dangerouslySetInnerHTML={{ __html: action.description }}
                      ></div>
                    }
                    style={{ maxWidth: 300 }}
                    position={'auto-top-center'}
                  >
                    <Icon type="info-circle-o" />
                  </Pop>
                ) : (
                  <Icon type="info-circle-o" />
                )}
              </>
            )}
          </div>
          <div className="sub-script-icon">
            <SelectedSubScriptIcon />
          </div>
        </div>
      );
    },
  );

  return (
    <div className="action-content">
      <div className="label">选择操作类型</div>
      <div className="action-wrapper">
        {actions.map((action: IActionItem) => {
          return (
            <ActionItem
              action={action}
              isValid={action.canUse}
              validationText={action.unusableReason || ''}
              url={action.obtainPermissionWay}
              detailText={action.obtainPermissionNotice}
            />
          );
        })}
      </div>

      {/* action页面的子抽屉 */}
      <SubDrawer
        data={subDrawerData as IActionItem}
        visible={visible}
        onClose={hideSubDrawer}
        onConfirm={action => {
          handleFlowData(action);
          handleGraphData(action);
          hideSubDrawer();
          hideDrawer();
        }}
        selectedData={editingAction}
      />
    </div>
  );
});

export default ActionContent;
