@use 'sass/vars/index.scss' as *;

.action-content {
  padding: 16px 24px 0;
  .label {
    color: #333;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }

  .action-wrapper {
    // 这是一个容器，需要实现里面的元素两列布局 元素之间间距是16
    display: grid; /* 使用 CSS Grid 布局 */
    grid-template-columns: 1fr 1fr; /* 创建两列，每列等宽 */
    gap: 16px; /* 设置列与列之间的间隔 */
    margin-top: 16px;

    .action-item-container {
      // flex: 0 0 calc(50% - 16px);
      padding: 12px 16px;
      background-color: #f7f7f7;
      border-radius: 4px;
      position: relative;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        background-color: #f7f7f7;
      }

      .action-item {
        display: flex;
        align-items: center;
        .action-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .name {
          color: #333;
          font-feature-settings: 'clig' off, 'liga' off;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px; /* 157.143% */
          flex: 1;
        }
        .icon {
          flex-shrink: 0;
          width: 28px;
          height: 28px;
        }
      }
    }

    .sub-script-icon {
      width: 26px;
      height: 26px;
      visibility: hidden;
      position: absolute;
      right: -2px;
      bottom: -2px;
    }
    .selected {
      border: 2px solid $selected-color;

      .sub-script-icon {
        visibility: visible;
      }
    }
  }
}

.zent-drawer-backdrop {
  z-index: -1;
}
.zent-drawer-backdrop:first-of-type {
  z-index: 1000;
}
