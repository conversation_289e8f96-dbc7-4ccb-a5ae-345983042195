import { getListTriggers } from 'pages/work-flow/api/drawer';
import {
  ENodeType,
  IFlow,
  ITrigger,
  ITriggerGroups,
  ITriggerWithGroupCode,
  ITriggers,
  ScheduledTaskField,
  TRIGGER_GROUPS_ICON_MAP,
  TRIGGER_TYPE_TO_SUBJECT_MAP,
  TriggerType,
  typeNameMap,
  widthByType,
} from 'pages/work-flow/constant';
import React, { useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import './style.scss';
import { SelectedSubScriptIcon } from 'svg';
import classnames from 'classnames';
import {
  editingNode<PERSON>tom,
  editingTriggerAtom,
  flowAtom,
  graphAtom,
  isFlowEditing<PERSON>tom,
  triggerSubDrawerVisible,
} from 'pages/work-flow/atoms';
import { useAtom, useAtomValue } from 'jotai';
import { useCustomDrawer } from '../../useCustomDrawer';
import { convertFormDataToGraphData } from 'pages/work-flow/fns/formToGraph';
import { BlockLoading, Button, Drawer, Icon, Input, Notify, Pop } from 'zent';
import withBusinessCheck from '../../../../HOC/withBusinessCheck';
import { queryAllTriggerWithGroup } from '../../../../../../api/workflow';

const TriggerContent = (props, ref) => {
  const [triggerGroups, setTriggerGroups] = useState<ITriggerGroups[]>([]);
  const [selectedTrigger, setSelectedTrigger] = useAtom(editingTriggerAtom);
  const [keywords, setKeywords] = useState<string>('');

  const [flowData, setFlowData] = useAtom(flowAtom);
  const editingTrigger = useAtomValue(editingTriggerAtom);
  const isFlowEditing = useAtomValue(isFlowEditingAtom);
  const graph = useAtomValue(graphAtom);
  const editingNode = useAtomValue(editingNodeAtom);
  const [loading, setLoading] = useState<boolean>(false);

  const { getDrawerData, hideDrawer } = useCustomDrawer();
  const [showSubDrawer, setShowSubDrawer] = useAtom(triggerSubDrawerVisible);

  const { isEditing } = getDrawerData();

  const onConfirm = (trigger?) => {
    const editingTrigger = trigger || selectedTrigger;
    if (isFlowEditing && editingTrigger?.type !== TriggerType.SCHEDULED)
      return Notify.error('已启用的工作流不能修改业务场景');

    console.log('🔥 editingTrigger ===>', editingTrigger, '<=== 🔥');

    // 将 editingTriggerAtom 给 flowAtom
    const newFlow: IFlow = {
      ...flowData,
      triggerId: editingTrigger?.id,
      scenes: editingTrigger?.name,
      triggerDefine: editingTrigger || undefined,
      // 触发器也有二级选择，判断上次的跟这次选的是不是同一个触发器，不是的话清空，反之保留
      chainRules: flowData.triggerId !== editingTrigger?.id ? [] : flowData.chainRules,
    };
    setFlowData(newFlow);
    editingNode?.setData({ data: { triggerDefine: editingTrigger } }, { overwrite: false });
    const graphData = convertFormDataToGraphData({ ...newFlow });
    graph?.fromJSON(graphData);
  };

  useImperativeHandle(ref, () => ({
    onConfirm,
  }));

  useEffect(() => {
    setLoading(true);
    queryAllTriggerWithGroup({})
      .then((res: ITriggerGroups[]) => {
        setTriggerGroups(res);
      })
      .finally(() => {
        setLoading(false);
      });

    if (flowData.triggerDefine) {
      setSelectedTrigger(flowData.triggerDefine);
    }
  }, []);

  const onSelectTrigger = (trigger: ITriggerWithGroupCode) => {
    console.log('trigger is ', trigger);
    setSelectedTrigger(trigger);
    if (trigger.type === TriggerType.SCHEDULED) {
      setShowSubDrawer(true);
    }
  };

  const TriggerItem = withBusinessCheck(
    ({
      trigger,
      isSelected,
      disabled,
      type,
    }: {
      trigger: ITriggerWithGroupCode;
      isSelected: boolean;
      disabled?: boolean;
      type: string;
    }) => {
      return (
        <div
          key={trigger.id}
          className={classnames('trigger-item', { 'selected-trigger-item': isSelected })}
          onClick={() => !disabled && onSelectTrigger(trigger)}
        >
          <img src={TRIGGER_GROUPS_ICON_MAP[type]} className={'trigger-icon'} />
          &nbsp;
          <div className="trigger-name-container">
            <Pop trigger="hover" content={trigger.name} position={'auto-top-center'}>
              <span className="trigger-name">{trigger.name}</span>
            </Pop>
            {trigger.description && (
              <Pop
                trigger="hover"
                content={<div dangerouslySetInnerHTML={{ __html: trigger.description }}></div>}
                position={'auto-top-center'}
              >
                <Icon type="info-circle-o" style={{ fontSize: 15, marginLeft: 4 }} />
              </Pop>
            )}
          </div>
          <div className="sub-script-icon">
            <SelectedSubScriptIcon />
          </div>
        </div>
      );
    },
  );

  const onKeywordsChange = e => {
    setKeywords(e.target.value);
  };

  const onSearch = () => {
    setLoading(true);
    queryAllTriggerWithGroup({
      name: keywords,
    })
      .then(
        (res: ITriggerGroups[]) => {
          setTriggerGroups(res);
        },
        () => {
          Notify.error('查询失败');
        },
      )
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="trigger-content">
      {/* <div className="label">选择{typeNameMap.trigger}</div> */}
      <Input
        onIconClick={onSearch}
        onPressEnter={onSearch}
        onChange={onKeywordsChange}
        icon="search"
        placeholder="输入业务场景名称"
        className={'trigger-search-input'}
      />
      <BlockLoading loading={loading} className={'trigger-list'}>
        {triggerGroups.map(group => {
          return (
            <div key={group.groupCode} className="trigger-group">
              <div className="group-title">{group.groupName}</div>
              <div className="trigger-list-content">
                {group.triggerDefineList.map(trigger => {
                  const isSelected = trigger.id === editingTrigger?.id;
                  return (
                    <TriggerItem
                      key={trigger.id}
                      trigger={{ ...trigger, groupCode: group.groupCode }}
                      isSelected={isSelected}
                      isValid={trigger.canUse}
                      type={group.groupCode}
                      validationText={trigger.unusableReason}
                      url={trigger.obtainPermissionWay}
                      detailText={trigger.obtainPermissionNotice}
                    />
                  );
                })}
              </div>
            </div>
          );
        })}
      </BlockLoading>
    </div>
  );
};

export default React.forwardRef(TriggerContent);
