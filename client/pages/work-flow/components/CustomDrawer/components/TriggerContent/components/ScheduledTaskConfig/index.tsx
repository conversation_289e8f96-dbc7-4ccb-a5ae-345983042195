import React, { useEffect, useMemo } from 'react';
import styles from './index.m.scss';
import {
  Form,
  FormDatePickerField,
  FormRadioGroupField,
  FormSelectField,
  FormStrategy,
  Notify,
  Radio,
  FormCheckboxGroupField,
  Checkbox,
  Validators,
} from 'zent';

const REPEAT_RULE_OPTIONS = [
  { text: '每小时', key: 'hour' },
  { text: '每天', key: 'day' },
  { text: '每周', key: 'week' },
  { text: '每月', key: 'month' },
  { text: '每年', key: 'year' },
  // { text: '自定义', key: 'custom' },
];

// 周几选项
const WEEK_DAYS_OPTIONS = [
  { text: '周一', key: 2 },
  { text: '周二', key: 3 },
  { text: '周三', key: 4 },
  { text: '周四', key: 5 },
  { text: '周五', key: 6 },
  { text: '周六', key: 7 },
  { text: '周日', key: 1 },
];

export const defaultWeekValue = WEEK_DAYS_OPTIONS.map(w => w.key);

interface ScheduleTaskConfigProps {
  value?: any;
  onChange?: (value: any) => void;
}

const ScheduleTaskConfig = ({ value = {}, onChange }: ScheduleTaskConfigProps) => {
  const form = Form.useForm(FormStrategy.View);

  const { cycleRule = '', startTime = '', endType = 0, endTime = '', weekDays = [] } =
    value?.repeatRule || {};
  // const currentTime =

  useEffect(() => {
    if (value.repeatRule) {
      const formValue = form.getValue();
      onChange &&
        onChange({
          repeatRule: {
            cycleRule: formValue?.cycleRule?.key,
            startTime: formValue?.startTime,
            endType: formValue?.endType,
            endTime: formValue?.endTime || value.repeatRule.endTime,
            weekDays: formValue?.weekDays || value.repeatRule.weekDays,
          },
        });
    }
  }, []);

  const onFormChange = () => {
    const value = form.getValue();
    let weekDays = value?.weekDays || [];
    if (value?.cycleRule?.key === 'week' && !weekDays.length) {
      weekDays = defaultWeekValue;
    }
    onChange &&
      onChange({
        repeatRule: {
          cycleRule: value?.cycleRule?.key,
          startTime: value?.startTime,
          endTime: value?.endType === 0 ? '' : value?.endTime,
          endType: value?.endType,
          weekDays,
        },
      });
  };

  const endTypeValue = form.getValue()?.endType;

  return (
    <div className={styles.scheduleContainer}>
      <div className={styles.title}>定时任务</div>
      <div className={styles.content}>
        <Form form={form} layout="horizontal" direction="row">
          <FormDatePickerField
            label="开始时间："
            name="startTime"
            initialValue={startTime}
            props={{
              placeholder: '选择时间',
              showTime: {
                format: 'HH:mm',
              },
              format: 'YYYY-MM-DD HH:mm',
              // 禁止选择过去的时间
              disabledDate: date => {
                // 判断日期是否小于今天 不包括小时级别
                return date < new Date(new Date().setHours(0, 0, 0, 0));
              },
            }}
            onChange={onFormChange}
          ></FormDatePickerField>
          <span className={styles.datepickerTips}>此时间的次日为当前任务的开始执行时间</span>
          {/* ：每小时、每天、每周、每月、每年 */}
          {/* // hours/day/week/month/year/custom/none */}
          <FormSelectField
            label="循环规律："
            name="cycleRule"
            // defaultValue={{ text: '每周', key: 'week' }}
            onChange={onFormChange}
            initialValue={REPEAT_RULE_OPTIONS.find(item => item.key === cycleRule)}
            props={{
              options: REPEAT_RULE_OPTIONS,
            }}
          ></FormSelectField>
          <div>
            {form.getValue()?.cycleRule?.key === 'week' && (
              <FormCheckboxGroupField
                label=" "
                name="weekDays"
                onChange={onFormChange}
                defaultValue={weekDays}
                validators={[Validators.required('请选择星期几')]}
                required
              >
                {WEEK_DAYS_OPTIONS.map(item => {
                  return (
                    <Checkbox key={item.key} value={item.key}>
                      {item.text}
                    </Checkbox>
                  );
                })}
              </FormCheckboxGroupField>
            )}
          </div>
          <div>
            <FormRadioGroupField
              label="结束时间："
              name="endType"
              initialValue={endType}
              onChange={onFormChange}
            >
              <Radio value={1}>自定义</Radio>
              <Radio value={0}>永不结束</Radio>
            </FormRadioGroupField>
          </div>
          {endTypeValue === 1 && (
            <FormDatePickerField
              label="结束时间："
              name="endTime"
              initialValue={endTime}
              onChange={onFormChange}
              props={{
                placeholder: '选择时间',
                showTime: {
                  format: 'HH:mm',
                },
                format: 'YYYY-MM-DD HH:mm',
                disabledDate: date => {
                  return date < new Date(new Date().setHours(0, 0, 0, 0));
                },
              }}
            ></FormDatePickerField>
          )}
        </Form>
      </div>
    </div>
  );
};

export default ScheduleTaskConfig;
