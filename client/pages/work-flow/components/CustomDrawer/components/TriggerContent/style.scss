@use 'sass/vars/index.scss' as *;

.trigger-content {
  padding: 16px 24px;
  .label {
    color: #333;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }

  .trigger-list {
    min-height: 600px;
    .trigger-search-input {
      margin-bottom: 12px;
    }

    .trigger-group {
      margin-top: 12px;

      .group-title {
        margin-bottom: 16px;
      }

      .trigger-list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        .trigger-item {
          flex-shrink: 0;
          display: flex;
          width: 163px;
          padding: 14px 16px;
          align-items: center;
          cursor: pointer;
          border: 2px solid transparent;
          background-color: $gray-color;
          position: relative;
          border-radius: 4px;
          overflow: hidden;

          .trigger-name-container {
            display: flex;
            align-items: center;

            .trigger-name {
              flex: 1;
              -webkit-line-clamp: 1;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .trigger-icon {
            flex-shrink: 0;
            width: 20px;
            height: 20px;
            margin-right: 8px;
          }

          .sub-script-icon {
            width: 26px;
            height: 26px;
            visibility: hidden;
            position: absolute;
            right: -2px;
            bottom: -2px;
          }

          &:hover {
            border: 2px solid $selected-color;
          }
        }
        .selected-trigger-item {
          border: 2px solid $selected-color;

          .sub-script-icon {
            visibility: visible;
          }
        }
      }
    }
  }
}
