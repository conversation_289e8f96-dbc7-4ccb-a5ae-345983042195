import { cloneDeep } from 'lodash';
import { ENodeType, IVariable } from 'pages/work-flow/constant';
import { FC, useEffect, useMemo, useRef } from 'react';
import { getHiddenCodes, renderIconByType } from 'pages/work-flow/fns';
import { parseJSON } from 'pages/work-flow/utils/flow';
import { renderValueInput } from './fns';

interface DrawerContentProps {
  data: any;
  filteredVariableList?: IVariable[];
  selectedData: any;
  editingAction: any;
  forceInit?: boolean;
  setEditingAction: (action: any) => void;
}

const DrawerContent: FC<DrawerContentProps> = ({
  data,
  filteredVariableList,
  selectedData,
  editingAction,
  forceInit,
  setEditingAction,
}) => {
  const processedFieldsRef = useRef<string>('');

  const actionFieldValueMaps4Display = parseJSON(
    editingAction?.actionFieldValueMaps4Display,
  ) as any;
  const actionFieldValueMaps4Execute = parseJSON(
    editingAction?.actionFieldValueMaps4Execute,
  ) as any;

  /**
   * 变量展示联动：选择变量 A 关联的变量时，则隐藏变量 A
   * 示例：采购订单，采购数量关联补货建议，则选择补货建议时隐藏采购数量
   */
  const { hiddenVariables, clearFields } = useMemo(() => {
    const result: Record<string, boolean> = {};
    const fieldsToClear: string[] = getHiddenCodes({
      fields: actionFieldValueMaps4Execute,
      variables: data?.variables || [],
    });

    if (fieldsToClear.length > 0) {
      filteredVariableList?.forEach(variable => {
        if (fieldsToClear.includes(variable.code as string)) {
          result[variable.id] = true;
        }
      });
    }

    return {
      hiddenVariables: result,
      clearFields: fieldsToClear,
    };
  }, [filteredVariableList, actionFieldValueMaps4Execute]);

  useEffect(() => {
    if (clearFields.length > 0) {
      const currentFieldsKey = clearFields.sort().join(',');

      if (processedFieldsRef.current === currentFieldsKey) {
        return;
      }

      const newActionFieldValueMaps4Display = cloneDeep(actionFieldValueMaps4Display);
      const newActionFieldValueMaps4Execute = cloneDeep(actionFieldValueMaps4Execute);
      clearFields.forEach(code => {
        delete newActionFieldValueMaps4Display[code];
        delete newActionFieldValueMaps4Execute[code];
      });

      setEditingAction(prev => {
        processedFieldsRef.current = currentFieldsKey;
        return {
          ...prev,
          actionFieldValueMaps4Execute: JSON.stringify(newActionFieldValueMaps4Execute),
          actionFieldValueMaps4Display: JSON.stringify(newActionFieldValueMaps4Display),
        };
      });
    } else {
      processedFieldsRef.current = '';
    }
  }, [clearFields]);

  const renderIcon = () => {
    const icon = renderIconByType({ type: ENodeType.Action, groupCode: data?.groupCode });
    if (!icon) return null;
    return <div className="icon">{icon}</div>;
  };
  return (
    <div className="sub-drawer">
      <div className="name">
        {renderIcon()}
        {data?.name}
      </div>
      {filteredVariableList?.map(variable => {
        if (hiddenVariables[variable.id]) {
          return null;
        }
        return (
          <div key={variable.id} className="variable-form-item">
            <div className="label matchMaxWidthDiv">{variable.name}：</div>
            <div className="variable-value-wrapper">
              {renderValueInput(
                variable,
                selectedData,
                data,
                false,
                editingAction,
                setEditingAction,
                forceInit,
              )}
              {!!variable.description && <div style={{ marginBottom: 16 }}></div>}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DrawerContent;
