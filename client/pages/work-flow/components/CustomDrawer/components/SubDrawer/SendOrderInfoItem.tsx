import { useAtom } from 'jotai';
import { editingActionAtom } from 'pages/task-flow/atoms';
import { IVariable } from 'pages/work-flow/constant';
import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer } from 'zent';
import './SendOrderInfoItem.scss';
interface SendOrderInfoItemProps {
    variable: IVariable;
    iframe: string;
    selectedData: any;
    editingAction: any;
    setEditingAction: (action: any) => void;
}

interface IResponseIframeData {
    configId: number;
    expressAddress: string;
    expressName: string;
    expressWayBillType: string;
    templateName: string;
    printerName: string;
}

const SendOrderInfoItem = ({ variable, iframe, selectedData, editingAction, setEditingAction }: SendOrderInfoItemProps) => {
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [iframeUrl, setIframeUrl] = useState(iframe);
    const [orderInfo, setOrderInfo] = useState<IResponseIframeData | null>(null);


    const iframeRef = useRef<HTMLIFrameElement>(null);

    useEffect(() => {
        const { actionFieldValueMaps4Display, actionFieldValueMaps4Execute } = selectedData;
        const { code } = variable;

        if (!code) return;
        const parsedActionFieldValueMaps4Display = JSON.parse(actionFieldValueMaps4Display || '{}');
        const parsedActionFieldValueMaps4Execute = JSON.parse(actionFieldValueMaps4Execute || '{}');

        const value = parsedActionFieldValueMaps4Display[code];
        setOrderInfo(value);

        const { configId } = parsedActionFieldValueMaps4Execute[code] || {};
        if (configId) {
            setIframeUrl(`${iframe}?id=${configId}`);
        }
    }, []);

    useEffect(() => {

        // if (iframeRef.current) {
        window.addEventListener('message', (event) => {
            if (event.data.type === 'responseIframeData') {
                const data: IResponseIframeData = event.data.payload;
                const { configId, ...rest } = data;

                const { code } = variable;

                const actionFieldValueMaps4Execute = JSON.parse(
                    editingAction?.actionFieldValueMaps4Execute || '{}',
                ) as any;

                const actionFieldValueMaps4Display = JSON.parse(
                    editingAction?.actionFieldValueMaps4Display || '{}',
                ) as any;

                if (!code) return;
                actionFieldValueMaps4Execute[code] = configId;
                actionFieldValueMaps4Display[code] = rest;

                const newAction = {
                    ...editingAction,
                    actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
                    actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
                };

                // @ts-ignore
                setEditingAction(newAction);

                setOrderInfo(data);
                setIframeUrl(`${iframe}?id=${configId}`);

                setVisible(false);
                setLoading(false);
            }
            if (event.data.type === 'responseIframeError') {
                setLoading(false);
            }
        });
        // }
    }, []);

    return (
        <>
            <a onClick={
                // drawer 打开
                () => {
                    setVisible(true);
                }
            }>完善信息</a>

            <div className="send-order-info">
                <div className="info-item">
                    <span className="label">快递公司：</span>
                    <span className="value">{orderInfo?.expressName || '-'}</span>
                </div>
                <div className="info-item">
                    <span className="label">发货类型：</span>
                    <span className="value">{orderInfo?.expressWayBillType || '-'}</span>
                </div>
                <div className="info-item">
                    <span className="label">发货地址：</span>
                    <span className="value">{orderInfo?.expressAddress || '-'}</span>
                </div>
                <div className="info-item">
                    <span className="label">面单模板：</span>
                    <span className="value">{orderInfo?.templateName || '-'}</span>
                </div>
                <div className="info-item">
                    <span className="label">打印机：</span>
                    <span className="value">{orderInfo?.printerName || '-'}</span>
                </div>
            </div>

            <Drawer
                title="发单信息"
                visible={visible}
                width={464}
                onClose={() => setVisible(false)}
                placement="right"

                footer={
                    <div style={{ textAlign: 'center' }}>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={() => {
                                iframeRef.current?.contentWindow?.postMessage({
                                    type: 'requestIframeData'
                                }, '*');
                                setLoading(true);
                            }}
                        >
                            确定
                        </Button>
                        <Button onClick={() => setVisible(false)}>取消</Button>
                    </div>
                }
            >
                <iframe ref={iframeRef} style={{ width: '100%', height: '100%' }} src={iframeUrl} />
            </Drawer>
        </>
    );
};

export default SendOrderInfoItem;