import { searchData } from 'api/workflow';
import { IAction, IActionItem, IVariable } from 'pages/work-flow/constant';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { InfiniteScroller, InlineLoading, Notify, Select } from 'zent';
import { isNil } from 'lodash';
import './style.scss';
import { editingActionAtom, flowAtom } from 'pages/work-flow/atoms';
import { useAtom } from 'jotai';
import { checkVariablePurview, getListFieldsByTriggerAndVariable } from 'pages/work-flow/api';
import { FormChangeEvent } from 'pages/work-flow/utils/event';
import { parseJSON } from 'pages/work-flow/utils/flow';

// 修改为false，确保每次组件重新渲染时都重置该值
let firstOpen = false;

const SelectItem = ({
  variable,
  subDrawerData,
  selectedData,
  selectedDataValue,
  extParam,
  disabled,
}: {
  variable: IVariable;
  subDrawerData: IActionItem;
  selectedData?: string;
  selectedDataValue?: string;
  extParam?: any;
  disabled?: boolean;
}) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [keyword, onKeywordChange] = useState('');
  const [canSelect, setCanSelect] = useState(true);

  // 添加isLoadingMore状态，避免重复加载
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // 这个 code 的接口不支持关键字搜索
  const isRetailGoodsCategory = variable?.code === 'retailGoodsCategory';

  const hasMoreRef = useRef(false);

  // const optionsRef = useRef([]);

  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [flowData, setFlowData] = useAtom(flowAtom);

  // 配置对象
  const dynamicEndpointConfigObj = useMemo(() => {
    const { dynamicEndpointConfig } = variable;
    return parseJSON(dynamicEndpointConfig || '{"isMultiSelect": true}');
  }, [variable]);

  // 字段的值，单选返回第1个内容
  const fieldValue = dynamicEndpointConfigObj.isMultiSelect ? value : value?.[0];

  useEffect(() => {
    if (!variable?.code) {
      return;
    }
    // 当前select框变更值事件注册，为了解决select框改变，需要清空其他select框值的交互。
    // 目前只有select组件有此场景，暂时只注册select组件。
    const offEvent = FormChangeEvent.listen(variable?.code, value => {
      setValue(value || []);
    });
    return () => {
      offEvent();
    };
  }, [variable]);

  useEffect(() => {
    const { code } = variable;
    const { isMultiSelect } = dynamicEndpointConfigObj;
    if (variable && selectedData && code) {
      const defaultValue = JSON.parse(selectedData || '{}')[code];
      if (defaultValue) {
        const values = defaultValue.map(item => ({
          ...item,
          text: item.name,
          key: item.id || item.staffId,
        }));

        setValue(isMultiSelect ? values : values[0]);
      }
    }
  }, []);

  /**
   * 变量权限校验
   * 示例：采购订单，选择仓库后，校验补货建议是否可用
   */
  useEffect(() => {
    const params = parseJSON(extParam);
    const needCheck =
      variable.needPermissionVerification && Object.values(params).every(v => !isNil(v));
    if (needCheck) {
      checkVariablePurview({
        relatedDataParam: extParam,
        variableId: variable.id,
      })
        .then((res = {}) => {
          const { havePurview } = res;
          setCanSelect(havePurview);
        })
        .catch(error => {
          Notify.error(error?.msg || error || '查询失败');
          setCanSelect(false);
        });
    }
  }, [extParam]);

  const fetchFieldOptions = async ({ page, variableId }) => {
    try {
      const res = await getListFieldsByTriggerAndVariable({
        triggerId: flowData.triggerId,
        variableId,
        page,
        pageSize: 99,
      });
      const { items } = res;

      // 更新选项
      if (page === 1) {
        setOptions(
          items.map(item => ({
            ...item,
            disabled: item.isOptional === false,
            key: item.id,
            text: item.name,
          })),
        );
        // 判断是否还有更多数据
        hasMoreRef.current = items.length === 99;
        return;
      }

      setOptions(prevOptions => {
        const newOptions = [
          ...prevOptions,
          ...items.map(item => ({
            ...item,
            disabled: item.isOptional === false,
            key: item.id,
            text: item.name,
          })),
        ];
        // 判断是否还有更多数据
        hasMoreRef.current = items.length === 99;
        return newOptions;
      });
    } catch (error) {
      console.error('Error fetching options:', error);
      hasMoreRef.current = false;
    }
  };

  const fetchData = (pageNum, isSearch = false) => {
    // 如果正在加载或者没有更多数据，直接返回
    if ((pageNum > 1 && !hasMoreRef.current) || (pageNum > 1 && isLoadingMore)) {
      return Promise.resolve();
    }

    const { dynamicEndpointConfig, valueSource } = variable;

    // 是第一页或者加载更多时设置对应的loading状态
    if (pageNum === 1) {
      setLoading(true);
    } else {
      setIsLoadingMore(true);
    }

    if (valueSource === 2) {
      return fetchFieldOptions({ page: pageNum, variableId: variable.id }).finally(() => {
        if (pageNum === 1) {
          setLoading(false);
        } else {
          setIsLoadingMore(false);
        }
      });
    }

    const { apiId, responseCovert } = JSON.parse(dynamicEndpointConfig);

    return searchData({
      apiId,
      page: pageNum,
      pageSize: 10,
      keyWord: keyword,
      extParam,
    })
      .then(res => {
        const { paginator, items } = res;
        const parsedOptions = items.map(item => {
          return {
            ...item,
            disabled: item.isOptional === false,
            key: item.id || item.staffId,
            text: item.name,
          };
        });

        const { totalCount } = paginator;

        if (pageNum === 1) {
          setOptions(parsedOptions);
          // 只有当总数大于当前加载的数量时，才设置hasMore为true
          hasMoreRef.current = totalCount > parsedOptions.length;
        } else {
          setOptions(prev => {
            const result = prev.concat(parsedOptions);
            // 判断是否已经加载了所有数据
            hasMoreRef.current = totalCount > result.length;
            return result;
          });
        }
      })
      .catch(() => {
        // 出错时设置hasMore为false，避免继续请求
        hasMoreRef.current = false;
      })
      .finally(() => {
        // 完成请求后清除对应的加载状态
        if (pageNum === 1) {
          setLoading(false);
        } else {
          setIsLoadingMore(false);
        }
      });
  };

  useEffect(() => {
    if (isRetailGoodsCategory) return;

    // 清除之前的定时器，防止多次请求
    const timer = setTimeout(() => {
      // 搜索时先重置页码和hasMore状态
      setPage(1);
      hasMoreRef.current = true;

      // 执行搜索
      fetchData(1, true);
    }, 200); // ! 不清楚为啥要延迟 2s 但太慢了先改成 .2s

    return () => clearTimeout(timer);
  }, [keyword]);

  // 处理浮层打开
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen) {
      // 打开时重置页码和hasMore状态
      setPage(1);

      // 重置firstOpen，确保每次打开都加载新数据
      firstOpen = true;

      if (firstOpen) {
        firstOpen = false;
        hasMoreRef.current = true;
        fetchData(1);
      }
    }
    if (!isOpen) {
      setOptions([]);
      setPage(1);
      hasMoreRef.current = false;
    }
  };

  // 滚动加载更多
  const loadMore = closeLoading => {
    // 如果没有更多数据或者正在加载中，不执行加载
    if (!hasMoreRef.current || loading || isLoadingMore) {
      closeLoading();
      return Promise.resolve();
    }

    // 加载下一页数据
    return fetchData(page + 1)
      .then(() => {
        setPage(page + 1);
        closeLoading();
      })
      .catch(() => {
        closeLoading();
        // 出错时设置hasMore为false
        hasMoreRef.current = false;
      });
  };

  const renderOptionList = (
    optionList: any[],
    renderOption: (option: any, index: number) => JSX.Element,
  ): React.JSX.Element => {
    return (
      <InfiniteScroller
        hasMore={hasMoreRef.current && !isLoadingMore}
        skipLoadOnMount
        loadMore={loadMore}
        className="option-scroll-wrapper"
        loader={
          <div className="loading-text">
            <InlineLoading
              iconSize={18}
              loading
              icon="circle"
              iconText="加载中…"
              textPosition="right"
              colorPreset="grey"
            />
          </div>
        }
      >
        {optionList.map((item, index) => renderOption(item, index))}
      </InfiniteScroller>
    );
  };

  const onMultiValueChange = (values: any[]) => {
    const { isMultiSelect } = dynamicEndpointConfigObj;

    // 都用多选的逻辑
    if (!Array.isArray(values)) {
      values = [values];
    }

    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    const { dynamicEndpointConfig, code, valueSource } = variable;
    if (valueSource === 2) {
      actionFieldValueMaps4Execute[code!] = values.map(item => item.id);
      actionFieldValueMaps4Display[code!] = values;
    } else {
      const { responseCovert, effectVarialeCodeList = [] } = JSON.parse(dynamicEndpointConfig);

      actionFieldValueMaps4Execute[code!] = values.map(item => item[responseCovert]);
      actionFieldValueMaps4Display[code!] = values;

      // 清空effectVarialeCodeList的value
      // 为了解决当前select框带来的其他select框值的影响
      effectVarialeCodeList.forEach(item => {
        if ([2, 3].includes(item.valueSource)) {
          actionFieldValueMaps4Execute[item.code] = [];
          actionFieldValueMaps4Display[item.code] = [];
        } else {
          actionFieldValueMaps4Execute[item.code] = '';
          actionFieldValueMaps4Display[item.code] = '';
        }
        // 由于上述修改state并不会更新select框的value，因此需要主动触发对应select的value改变事件清主动清空
        FormChangeEvent.trigger(item.code, []);
      });
    }

    const isArray = Array.isArray(values);

    if (isMultiSelect && !isArray) {
      values = [values];
    }

    if (!isMultiSelect && isArray) {
      values = values[0];
    }

    // 给Select组件value赋值
    setValue(values);

    // 选中之后清空搜索关键字
    onKeywordChange('');

    // @ts-ignore
    setEditingAction(prev => {
      return {
        ...subDrawerData,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  };

  const onValueChange = (value: any) => {
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    const { dynamicEndpointConfig, code, valueSource } = variable;
    if (valueSource === 2) {
      actionFieldValueMaps4Execute[code!] = value.id;
      actionFieldValueMaps4Display[code!] = value;
    } else {
      const { responseCovert } = JSON.parse(dynamicEndpointConfig);

      actionFieldValueMaps4Execute[code!] = value[responseCovert];
      actionFieldValueMaps4Display[code!] = value;
    }

    // 给Select组件value赋值
    setValue([value]);

    // 选中之后清空搜索关键字
    onKeywordChange('');

    // @ts-ignore
    setEditingAction(prev => {
      return {
        ...subDrawerData,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  };

  const renderOptionContent = item => {
    const { name, linkPhone } = item || {};
    return (
      <div className="option-item">
        <span className="name">{name}</span>
        {linkPhone && <span className="phone">{linkPhone}</span>}
      </div>
    );
  };

  return (
    <div>
      <Select
        className="custom-select"
        clearable={!!dynamicEndpointConfigObj.isMultiSelect}
        disabled={disabled || !canSelect}
        options={options}
        placeholder="请选择"
        multiple={!!dynamicEndpointConfigObj.isMultiSelect}
        filter={false}
        loading={loading}
        // 接口不支持关键字搜索 这里强制写死空字符串
        keyword={isRetailGoodsCategory ? '' : keyword}
        onKeywordChange={onKeywordChange}
        value={value}
        onChange={onMultiValueChange}
        open={open}
        onOpenChange={isOpen => handleOpenChange(isOpen)}
        // @ts-ignore
        renderOptionList={renderOptionList}
        renderOptionContent={renderOptionContent}
      />
      {variable.description ? (
        <div className="variable-description">{variable.description}</div>
      ) : null}
    </div>
  );
};

export default SelectItem;
