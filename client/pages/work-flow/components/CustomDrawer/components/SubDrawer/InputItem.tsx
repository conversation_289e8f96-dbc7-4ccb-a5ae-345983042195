import { useAtom } from 'jotai';
import { editingActionAtom } from 'pages/work-flow/atoms';
import { parseJSON } from 'pages/work-flow/utils/flow';
import React, { useEffect, useState } from 'react';
import { Input } from 'zent';

const InputItem = ({
  variable,
  subDrawerData,
  selectedData,
  disabled,
  addonBefore = '',
  addonAfter = '',
}) => {
  const [inputValue, setInputValue] = useState('');
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const { showAbleDependenciesVariables = [] } = parseJSON(variable.dynamicEndpointConfig);

  useEffect(() => {
    if (variable && selectedData) {
      const { code } = variable;
      const defaultValue =
        showAbleDependenciesVariables.length > 0
          ? parseJSON(editingAction?.actionFieldValueMaps4Display)[code]?.name
          : JSON.parse(selectedData || '{}')[code]?.name;
      setInputValue(defaultValue);
    }
  }, []);

  const onInputChange = e => {
    const pattern = variable?.validateRule;
    const { value } = e.target;
    const trimmedValue = value.trim(); // 去除前导和尾随空格
    if (pattern && (trimmedValue || trimmedValue === '0')) {
      const regex = new RegExp(pattern);
      if (regex.test(trimmedValue)) {
        setInputValue(trimmedValue);
      } else {
        // 这一步看起来很傻逼  但是是为了限制输入框直接被输入，和inputValue对不上的问题
        setInputValue(inputValue || '');
      }
    } else {
      setInputValue(trimmedValue);
    }
  };

  const onInputBlur = e => {
    const { value } = e.target;
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const { dynamicEndpointConfig, code } = variable;

    actionFieldValueMaps4Execute[code!] = value;
    actionFieldValueMaps4Display[code!] = { name: value };

    // @ts-ignore
    setEditingAction(prev => {
      return {
        ...subDrawerData,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  };

  return (
    <>
      <Input
        value={inputValue}
        onChange={onInputChange}
        onBlur={onInputBlur}
        disabled={disabled}
        addonBefore={addonBefore}
        addonAfter={addonAfter}
        min={2}
        max={10}
        width={'100%'}
        type={'text'}
      />
      {variable.description ? (
        <div className="variable-description">{variable.description}</div>
      ) : null}
    </>
  );
};

export default InputItem;
