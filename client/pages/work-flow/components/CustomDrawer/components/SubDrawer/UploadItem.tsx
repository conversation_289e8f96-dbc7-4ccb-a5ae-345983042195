import { useEffect, useState } from 'react';
import { Upload, IUploadFileItemInner, IUploadFileItem, Notify } from 'zent';
import { useAtom } from 'jotai';
import { isEmpty } from 'lodash';
import { editingActionAtom, isSubDrawerLoading } from 'pages/work-flow/atoms';
import { checkVariableFileUpload, defaultGetToken, upload } from 'pages/work-flow/api';
import { parseJSON } from 'pages/work-flow/utils/flow';

type IFileItem = IUploadFileItemInner<IUploadFileItem>;

const UploadItem = props => {
  const {
    accept = '.xlsx',
    tips,
    hrefTarget = '_self',
    maxSize = 1,
    maxAmount = 1,
    variable,
  } = props;

  let timeIndex = 1;

  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [loading, setLoading] = useAtom(isSubDrawerLoading);
  const [files, setFiles] = useState<IFileItem[]>([]);

  const dynamicConfig = parseJSON(variable?.dynamicEndpointConfig);

  useEffect(() => {
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    const { code } = variable;
    const { fileName, fileUrl } = actionFieldValueMaps4Display[code!] || {};
    if (fileName && fileUrl) {
      setFiles([
        {
          name: fileName,
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      ] as IFileItem[]);
    }
  }, []);

  function handleFail(err) {
    Notify.error(err?.msg || err || '导入失败');
    setLoading(false);
  }

  function updateField(file: { fileUrl?: string; fileName?: string } = {}) {
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const { code } = variable;

    if (!isEmpty(file)) {
      const { fileUrl, fileName } = file || {};
      actionFieldValueMaps4Execute[code!] = fileUrl;
      actionFieldValueMaps4Display[code!] = { fileUrl, fileName };
    } else {
      delete actionFieldValueMaps4Display[code!];
      delete actionFieldValueMaps4Execute[code!];
    }

    // @ts-ignore
    setEditingAction((prev = {}) => {
      return {
        ...prev,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  }

  const handleUploadSuccess = (res, rawFiles) => {
    const { attachment_url: fileUrl, attachment_title: fileName } = res || {};
    checkVariableFileUpload({
      variableId: variable.id,
      fileName,
      fileUrl,
    })
      .then(() => {
        updateField({ fileUrl, fileName });
        setFiles(rawFiles);
        setLoading(false);
      })
      .catch(err => handleFail(err));
  };

  // 获取 token
  function onUpload(files, rawFiles) {
    defaultGetToken()
      .then(({ token }) => {
        files?.forEach((file: any) => doUpload(token, new File([file.raw], file.name), rawFiles));
      })
      .catch(err => handleFail(err));
  }

  // 上传到 CDN
  function doUpload(token: string, file: Blob, rawFiles: IFileItem[]) {
    upload(token, file)
      .then(res => handleUploadSuccess(res, rawFiles))
      .catch(err => handleFail(err));
  }

  const handleFileChange = files => {
    const rawFiles = files;

    if (!rawFiles) return;

    const currentFiles = Array.prototype.slice
      .call(rawFiles)
      .map(rawFile => ({
        uid: rawFile.uid || Date.now() + timeIndex++,
        name: rawFile.name,
        size: rawFile._file.size,
        type: rawFile.type,
        raw: rawFile._file,
      }))
      .slice(0, 1);

    if (currentFiles.length) {
      setLoading(true);
      onUpload(currentFiles, rawFiles);
    } else {
      updateField();
      setFiles([]);
    }
  };

  return (
    <div className="upload-field">
      <Upload
        manualUpload
        fileList={files}
        accept={accept}
        maxSize={maxSize * 1024 * 1024}
        maxAmount={maxAmount}
        tips={tips || `仅支持 xlsx 格式文件，最大不超过 ${maxSize}M`}
        onChange={handleFileChange}
      />
      <a
        href={dynamicConfig.templateFileUrl}
        target={hrefTarget}
        className="upload-field__template"
      >
        下载模板
      </a>
    </div>
  );
};

export default UploadItem;
