import {
  ENodeType,
  EValueSource,
  ISubDrawer,
  IVariable,
  TemplateStyle,
  widthByType,
} from 'pages/work-flow/constant';
import { FC, useEffect, useMemo, useState } from 'react';
import { BlockLoading, Button, Drawer, Icon, Notify } from 'zent';
import matchMaxWidth from 'fns/matchMaxWidth';
import './style.scss';
import { renderIconByType } from 'pages/work-flow/fns';
import { useAtom } from 'jotai';
import {
  editingActionAtom,
  isSubDrawerLoading,
  textAreaInvalidTextAtom,
} from 'pages/work-flow/atoms';
import { clone, cloneDeep, pick } from 'lodash';
import { checkMsgContent } from 'pages/work-flow/api';
import DrawerContent from './DrawerContent';
import { parseJSON } from 'pages/work-flow/utils/flow';

const SubDrawer: FC<ISubDrawer> = ({ onConfirm, onClose, visible, data, selectedData }) => {
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [loading, setLoading] = useAtom(isSubDrawerLoading);

  const [invalidText, setInvalidText] = useAtom(textAreaInvalidTextAtom);
  const [forceInit, setForceInit] = useState(false);

  // 根据变量的dependenciesVariales前置依赖的字段，推断出哪些字段改变后需要清空当前变量，关联关系维护到effectVarialeCodeList中去。
  useEffect(() => {
    if (!data) {
      return;
    }
    const variables = data?.variables || [];
    variables.forEach(item => {
      const dynamicEndpointConfigObj = parseJSON(item.dynamicEndpointConfig);
      const { dependenciesVariales = [] } = dynamicEndpointConfigObj;
      dependenciesVariales.forEach(verCode => {
        const findVarVo = data.variables.find(item => item.code === verCode);
        if (findVarVo) {
          const findDynamicEndpointConfigObj = parseJSON(findVarVo.dynamicEndpointConfig);
          findDynamicEndpointConfigObj.effectVarialeCodeList = [
            ...(findDynamicEndpointConfigObj.effectVarialeCodeList || []),
            pick(item, ['code', 'valueSource']),
          ];
          findVarVo.dynamicEndpointConfig = JSON.stringify(findDynamicEndpointConfigObj);
        }
      });
    });
  }, [data]);

  const filteredVariableList = useMemo(() => {
    if (!data) return;
    const hiddenVariables = data.variables
      ?.filter(variable => variable.valueSource === EValueSource.RADIO) // 过滤出 valueSource 为 5 的变量
      .map(variable => {
        return JSON.parse(variable.dynamicEndpointConfig || '{}');
      }) // 解析 dynamicEndpointConfig
      .flatMap(config => config.group); // 取出 group 字段，并扁平化数组
    const result = data.variables.filter(item => {
      return !hiddenVariables.includes(item.id);
    });
    return result;
  }, [data]);

  useEffect(() => {
    // 使用 Array.from() 将 NodeListOf<Element> 转换为 HTMLElement[]
    const divsArray: HTMLElement[] = Array.from(document.querySelectorAll('.matchMaxWidthDiv'));
    matchMaxWidth(divsArray, 120);

    // 找到valueSource为5的多选组，给默认值
    const radioVariableList = filteredVariableList?.filter(
      item => item.valueSource === EValueSource.RADIO,
    );
    if (radioVariableList && radioVariableList.length) {
      const actionFieldValueMaps4Execute = JSON.parse(
        editingAction?.actionFieldValueMaps4Execute || '{}',
      ) as any;
      const actionFieldValueMaps4Display = JSON.parse(
        editingAction?.actionFieldValueMaps4Display || '{}',
      ) as any;

      radioVariableList.forEach(item => {
        const { group, isMultiSelect } = JSON.parse(item.dynamicEndpointConfig || '{}');
        const radioGroupVariables = data.variables.filter(item => group.includes(item.id));
        const selectedValue = JSON.parse(selectedData?.actionFieldValueMaps4Display || '{}')?.[
          item.code!
        ]?.name;
        const defaultValue = isMultiSelect
          ? selectedValue || []
          : +selectedValue || radioGroupVariables[0].id;
        const { code } = item;

        actionFieldValueMaps4Execute[code!] = defaultValue;
        actionFieldValueMaps4Display[code!] = { name: defaultValue };
      });

      // @ts-ignore
      setEditingAction(prev => {
        return {
          ...prev,
          actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
          actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
        };
      });

      setForceInit(true);
    }
  }, [filteredVariableList]);

  return (
    <div className="action-drawer">
      <Drawer
        title={
          <div>
            <Icon
              style={{ width: '20px', height: '20px', cursor: 'pointer' }}
              type="left"
              onClick={onClose}
            />
            执行操作
          </div>
        }
        placement="right"
        width={widthByType[ENodeType.Action]}
        mask={true}
        footer={
          <div style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              loading={loading}
              onClick={() => {
                // 预先调用接口检查文本是否合法
                const textArea = data?.variables.find((item: IVariable) => {
                  const extension = JSON.parse(item?.extension || '{}');
                  const styleTemplate = extension?.variablePattern?.styleTemplate?.template || '';
                  return item.valueSource === 1 && styleTemplate === TemplateStyle.TextArea;
                });
                const action = cloneDeep(editingAction);

                // 0702新增逻辑：radioItem支持valueSource为6的 纯展示组件，需要在确认时添加对应的defaultValue
                const { variables = [], actionFieldValueMaps4Execute } = action || {};
                const execute = JSON.parse(actionFieldValueMaps4Execute || '{}');

                const radioVariables = variables.filter(
                  item => item.valueSource === EValueSource.RADIO,
                );
                const codes = radioVariables.filter(item => 'code' in item).map(item => item.code);

                codes.forEach(code => {
                  if (!code) return;
                  const selectGroup = execute[code];
                  if (selectGroup) {
                    const selectGroups = Array.isArray(selectGroup) ? selectGroup : [selectGroup];
                    selectGroups.forEach(group => {
                      const selectedVariable = variables.find(item => item.id === group);
                      if (selectedVariable?.valueSource === EValueSource.TEXT) {
                        const { dynamicEndpointConfig, code } = selectedVariable;
                        const config = JSON.parse(dynamicEndpointConfig || '{}');
                        const { defaultValue } = config;
                        if (code && action) {
                          execute[code] = defaultValue;
                          action.actionFieldValueMaps4Execute = JSON.stringify(execute);
                        }
                      }
                    });
                  }
                });

                setEditingAction(action);

                if (action?.content) {
                  checkMsgContent({ content: action?.content }).then(res => {
                    const { pass, errorNotice = '' } = res;

                    if (pass) {
                      onConfirm(action);
                    } else {
                      setInvalidText(errorNotice);
                    }
                  }).catch(err => {
                    Notify.error(err.message);
                  });
                  return;
                }
                if (action) {
                  onConfirm(action);
                } else {
                  onConfirm();
                }
              }}
            >
              确定
            </Button>
            <Button onClick={onClose}>取消</Button>
          </div>
        }
        visible={visible}
        onClose={onClose}
        maskClosable
      >
        <BlockLoading loading={loading}>
          <DrawerContent
            data={data}
            filteredVariableList={filteredVariableList}
            selectedData={selectedData}
            editingAction={editingAction}
            setEditingAction={setEditingAction}
            forceInit={forceInit}
          />
        </BlockLoading>
      </Drawer>
    </div>
  );
};

export default SubDrawer;
