import { EValueSource, IAction, IVariable, TemplateStyle } from 'pages/work-flow/constant';
import TextAreaItem from '../TextAreaItem';
import InputItem from '../InputItem';
import SelectItem from '../SelectItem';
import RadioItem from '../RadioItem';
import UnitTime from '../UnitTime';
import CheckboxItem from '../CheckboxItem';
import { ItemSelectField } from 'pages/work-flow/components/ItemSelect';
import { Notify } from 'zent';
import { parseJSON } from 'pages/work-flow/utils/flow';
import UploadItem from '../UploadItem';
import SendOrderInfoItem from '../SendOrderInfoItem';

// 根据不同的valueSource渲染不同的输入框
export const renderValueInput = (
  variable: IVariable,
  selectedData,
  data,
  disabled = false,
  editingAction,
  setEditingAction,
  forceInit = false,
) => {
  const extension = JSON.parse(variable?.extension || '{}');
  const styleTemplate = extension?.variablePattern?.styleTemplate?.template || '';
  const { prefixDesc, suffixDesc } = extension?.variablePattern?.styleTemplate || {};
  const { actionFieldValueMaps4Display, actionFieldValueMaps4Execute } = selectedData || {};

  const { code = '' } = variable;

  const onFieldValueChange = (value: any) => {
    const { skuMap, selected, selectedMainIds, selectedSubIds } = value;

    // 更新 actionFieldValueMaps4Execute
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;
    actionFieldValueMaps4Execute[code] = skuMap;

    // 更新 actionFieldValueMaps4Display
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    actionFieldValueMaps4Display[code] = selected;

    // 更新 editingAction
    setEditingAction?.(
      prev =>
        ({
          ...(prev as IAction), // 假设 prev 不为 null
          actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute || '{}'),
          actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display || '{}'),
        } as IAction),
    );

    // 更新自定义的状态（如果需要）
    // setCustomSelectMainValue(selectedMainIds);
    // setCustomSelectSubValue(selectedSubIds);
  };

  // return <div className="value">{renderIconByType(data)}</div>;
  if (variable.valueSource === 1) {
    // 开放变量，input输入 action中找不到使用场景
    return styleTemplate === TemplateStyle.TextArea ? (
      <TextAreaItem
        variable={variable}
        subDrawerData={data}
        maxWordsLength={extension?.variablePattern?.styleTemplate?.maxWordsLength || 70}
        selectedData={{
          actionFieldValueMaps4Display,
          content: selectedData?.content,
          contentVariables: selectedData?.contentVariables,
        }}
      />
    ) : (
      <InputItem
        addonBefore={prefixDesc}
        addonAfter={suffixDesc}
        variable={variable}
        subDrawerData={data}
        disabled={disabled}
        selectedData={actionFieldValueMaps4Display}
      />
    );
  }
  if (variable.valueSource === 2) {
    // 固定选项 也用选择器，但是
    return (
      <SelectItem
        variable={variable}
        subDrawerData={data}
        selectedData={actionFieldValueMaps4Display}
      />
    );
  }
  if (variable.valueSource === 3) {
    // 有改动，不使用公用选择器，用Select
    // 20240830改动，valueSource为3 根据dynamicEndpointConfig中的componentStyle字段 'popUpSelection' 'dropDownSelection'决定使用哪种选择器
    const dynamicEndpointConfig = JSON.parse(variable.dynamicEndpointConfig || '{}');
    const { componentStyle, extParam = {}, dependenciesVariales = [] } = dynamicEndpointConfig;
    // 字段联动关系时存在禁用状态
    let disabled = false;
    // 根据后端返回dependenciesVariales来获取对应的前置依赖字段值，追加到extParam中
    if (dependenciesVariales.length) {
      const actionFieldValueMaps4ExecuteObj = parseJSON(
        editingAction?.actionFieldValueMaps4Execute,
      );
      dependenciesVariales.forEach(code => {
        const val = actionFieldValueMaps4ExecuteObj[code] || null;
        extParam[code] = val;
        // 依赖字段为空时禁用当前选择器
        if (!val || (Array.isArray(val) && !val.length)) {
          disabled = true;
        }
      });
    }

    if (componentStyle === 'popUpSelection') {
      const { apiId, responseCovert, maxSelectedCount = Infinity } = dynamicEndpointConfig;

      const parsedData = JSON.parse(editingAction?.actionFieldValueMaps4Execute || '{}');

      const customSelectMainValue = Array.isArray(parsedData[code])
        ? parsedData[code]
        : Object.keys(parsedData[code] || {}).map(Number);

      const customSelectSubValue = Array.isArray(parsedData[code])
        ? []
        : Object.values(parsedData[code] || {})
            .flat()
            .filter((value, index, self) => self.indexOf(value) === index);
      return (
        <ItemSelectField
          type={apiId}
          outputKey={'skuMap'}
          isRetailGoods={responseCovert === 'skuIds'}
          subType={responseCovert}
          extParam={extParam}
          value={{ selectedMainIds: customSelectMainValue, selectedSubIds: customSelectSubValue }}
          onChange={onFieldValueChange}
          disabled={disabled}
          maxSelectedCount={maxSelectedCount}
          onError={() => {
            // 如果是采购任务的指定商品，不显示错误提示
            if (apiId === 'retailPurchaseGoods') {
              return;
            }
            Notify.error('请先选择操作符');
          }}
        />
      );
    }
    return (
      <SelectItem
        extParam={JSON.stringify(extParam || {})}
        variable={variable}
        subDrawerData={data}
        selectedData={actionFieldValueMaps4Display}
        selectedDataValue={actionFieldValueMaps4Execute}
        disabled={disabled}
      />
    );
  }
  if (variable.valueSource === EValueSource.RADIO) {
    // 新增的valueSource 单选组
    // 需要根据group中的id数组，判断这个单选组有哪些id的选项
    const { group, isMultiSelect } = JSON.parse(variable.dynamicEndpointConfig || '{}');
    const radioGroupVariables = data.variables.filter(item => group.includes(item.id));

    const SelectionComponent = isMultiSelect ? CheckboxItem : RadioItem;

    return (
      <SelectionComponent
        variable={variable}
        subDrawerData={data}
        selectedData={selectedData}
        radioGroupVariables={radioGroupVariables}
        forceInit={forceInit}
      />
    );
  }

  if (variable.valueSource === EValueSource.TIME) {
    return <UnitTime variable={variable} />;
  }

  if (variable.valueSource === EValueSource.BATCHIMPORT) {
    return <UploadItem variable={variable} />;
  }

  if (variable.valueSource === EValueSource.SEND_ORDER_INFO) {
    const { iframe } = JSON.parse(variable.dynamicEndpointConfig || '{}');
    return (
      <>
        <SendOrderInfoItem
          variable={variable}
          iframe={iframe}
          selectedData={selectedData}
          editingAction={editingAction}
          setEditingAction={setEditingAction}
        />
      </>
    );
  }
};
