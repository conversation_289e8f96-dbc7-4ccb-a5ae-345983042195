import React, { useEffect, useState } from 'react';
import { Checkbox } from 'zent';
import { useAtom } from 'jotai';
import { editingActionAtom } from 'pages/work-flow/atoms';
import matchMaxWidth from 'fns/matchMaxWidth';
import { parseJSON } from 'pages/work-flow/utils/flow';
import './style.scss';
import { IAction, IActionItem, IVariable, IVariableItem } from 'pages/work-flow/constant';

interface ICheckboxItemProps {
  radioGroupVariables: IVariableItem[];
  subDrawerData: IActionItem;
  variable: IVariable;
  selectedData: IAction;
  forceInit: boolean;
}

const CheckboxItem = ({ radioGroupVariables, subDrawerData, variable }: ICheckboxItemProps) => {
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);

  const [checkedList, setCheckedList] = useState<number[]>([]);

  const setActionData = async value => {
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    const { code } = variable;

    actionFieldValueMaps4Execute[code!] = value;
    actionFieldValueMaps4Display[code!] = { name: value };

    // @ts-ignore
    setEditingAction(prev => {
      return {
        ...subDrawerData,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  };

  useEffect(() => {
    const defaultValue =
      parseJSON(editingAction?.actionFieldValueMaps4Display)?.[variable.code!]?.name || [];
    setCheckedList(defaultValue);

    const divsArray: HTMLElement[] = Array.from(document.querySelectorAll('.radio-label-name'));
    matchMaxWidth(divsArray, 120);
  }, []);

  const onChange = value => {
    setCheckedList(value);
    setActionData(value);
  };

  return (
    <Checkbox.Group value={checkedList} onChange={onChange}>
      {radioGroupVariables.map((item, index) => {
        return (
          <React.Fragment key={index}>
            <Checkbox value={item.id}>
              <div className={'radio-label'}>
                <span className="radio-label-name">{item.name}</span>
              </div>
            </Checkbox>

            {/* {Array.isArray(checkedList) &&
              checkedList.includes(item.id) &&
              renderValueInput(
                item,
                selectedData,
                subDrawerData,
                false,
                editingAction,
                setEditingAction,
              )} */}
          </React.Fragment>
        );
      })}
    </Checkbox.Group>
  );
};

export default CheckboxItem;
