import { useMemo } from 'react';
import { Select, NumberInput } from 'zent';
import { useAtom } from 'jotai';
import { editingActionAtom } from 'pages/work-flow/atoms';
import { TimeUnit, TimeMax } from 'pages/work-flow/constant';

type SyncParams = {
  time?: string;
  opt?: typeof TimeUnit[number];
};

const defaultUnitTime = {
  time: '1',
  opt: TimeUnit[0],
};

export default function UnitTime({ variable }) {
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [input, select, max] = useMemo(() => {
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;
    const { time, timeUnit } = actionFieldValueMaps4Execute[variable.code] || {
      time: '1',
      timeUnit: 1,
    };
    const opt = TimeUnit.find(t => t.key === timeUnit);
    const max = TimeMax[opt?.key!] || 0;

    return [time, opt, max];
  }, [editingAction]);

  const handleInputChange = event => {
    let value: string | number = +event;
    if (value < 0) {
      value = 0;
    }

    if (value > max) {
      value = max;
    }

    sync({ time: `${value}`, opt: select });
  };

  const handleSelectChange = opt => {
    sync({ opt, time: input });
  };

  const sync = ({ time = defaultUnitTime.time, opt = defaultUnitTime.opt }: SyncParams) => {
    const { code } = variable;

    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    actionFieldValueMaps4Execute[code] = { time, timeUnit: opt.key };
    actionFieldValueMaps4Display[code] = { time, timeUnit: opt };

    // @ts-ignore
    setEditingAction(prev => {
      return {
        ...prev,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  };

  if (+input > max) {
    sync({ time: `${max}`, opt: select });
  }

  return (
    <div className="delay-field">
      <NumberInput
        className="delay-field__item"
        type="number"
        placeholder="输入时间数值"
        value={input}
        min={0}
        max={max}
        decimal={0}
        onChange={handleInputChange}
      />
      <Select
        className="delay-field__item"
        value={select}
        options={TimeUnit}
        onChange={handleSelectChange}
      />
    </div>
  );
}
