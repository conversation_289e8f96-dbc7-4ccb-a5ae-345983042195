import React, { FC, useEffect, useState, useCallback } from 'react';
import style from './textAreaItem.m.scss';
import { Button, Pop } from 'zent';
import './zent.scss';
import { getListVariablesByTrigger } from 'pages/work-flow/api';
import { useAtom } from 'jotai';
import { flowAtom, editingActionAtom, textAreaInvalidTextAtom } from 'pages/work-flow/atoms';
const AddIcon = () => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="6" cy="6" r="5.04082" stroke="#4A4A4A" strokeWidth="1.1" />
    <rect x="5.44971" y="3.59668" width="1.1" height="4.8057" rx="0.4" fill="#4A4A4A" />
    <rect
      x="8.40283"
      y="5.44922"
      width="1.1"
      height="4.8057"
      rx="0.4"
      transform="rotate(90 8.40283 5.44922)"
      fill="#4A4A4A"
    />
  </svg>
);

const TextAreaItem: FC<any> = ({ variable, subDrawerData, selectedData, maxWordsLength }) => {
  const MAX_COUNT_LIMIT = maxWordsLength;

  const [textAreaValue, setTextAreaValue] = useState<string>(''); // 输入框的值
  const [variableList, setVariableList] = useState<any>([]);
  const [position, setPosition] = useState<any>(null);
  const [invalidText, setInvalidText] = useAtom(textAreaInvalidTextAtom);

  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [flowData, setFlowData] = useAtom(flowAtom);

  const setContent = useCallback((html, text) => {
    // 如果 variableList 为空，不执行后续操作
    if (!variableList.length) return;

    setTextAreaValue(text);
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const { code } = variable;
    const { content, contentVariables } = replaceVariablesWithIds(text, variableList);

    // 确保两个地方的content保持一致
    actionFieldValueMaps4Execute.content = content;
    actionFieldValueMaps4Execute.contentVariables = contentVariables;
    actionFieldValueMaps4Display[code!] = { html };

    setEditingAction(prev => ({
      ...prev, // 保留原有状态
      ...subDrawerData, // 更新子抽屉数据
      content, // 确保主content与execute中的content一致
      contentVariables,
      actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
      actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
    }));
  }, [variableList, editingAction, variable, subDrawerData]);

  useEffect(() => {
    // 处理已有数据展示
    const { content, contentVariables, actionFieldValueMaps4Display } = selectedData;
    const formattedDisplay = JSON.parse(actionFieldValueMaps4Display || '{}');
    const { code } = variable;
    const { html } = formattedDisplay[code!] || {};

    // 先获取变量列表
    getListVariablesByTrigger({
      page: 1,
      pageSize: 99,
      triggerId: flowData.triggerId,
      relatedComponentType: 3,
    }).then(res => {
      setVariableList(res.items);

      // 在获取到变量列表后再处理内容
      const textArea = document.querySelector('.' + style.textAreaInput) as HTMLTextAreaElement;
      if (html) {
        // 处理html显示
        const regex = /<span>(.*?)<\/span>/g;
        const formattedHtml = html.replace(regex, '<span contentEditable="false">$1</span>');
        textArea.innerHTML = formattedHtml;

        // 使用原始content和contentVariables，保持数据一致性
        setEditingAction(prev => ({
          ...prev,
          ...subDrawerData,
          content, // 使用原始content
          contentVariables, // 使用原始contentVariables
          actionFieldValueMaps4Execute: JSON.stringify({
            ...JSON.parse(prev?.actionFieldValueMaps4Execute || '{}'),
            content,
            contentVariables
          }),
          actionFieldValueMaps4Display: JSON.stringify({
            ...JSON.parse(prev?.actionFieldValueMaps4Display || '{}'),
            [code!]: { html: formattedHtml }
          })
        }));

        // 设置显示值
        const displayText = content.replace(/var\[(\d+)\]/g, (match, id) => {
          const variable = contentVariables.find(v => v.id === +id);
          return variable ? `{${variable.name}}` : match;
        });
        setTextAreaValue(displayText);
      } else if (content) {
        // 如果只有content，保持原始content，只处理显示
        const displayHtml = content.replace(/var\[(\d+)\]/g, (match, id) => {
          const variable = contentVariables.find(v => v.id === +id);
          return variable ? `<span contentEditable="false">{${variable.name}}</span>` : match;
        });
        textArea.innerHTML = displayHtml;

        const displayText = content.replace(/var\[(\d+)\]/g, (match, id) => {
          const variable = contentVariables.find(v => v.id === +id);
          return variable ? `{${variable.name}}` : match;
        });

        // 更新状态，保持数据一致性
        setEditingAction(prev => ({
          ...prev,
          ...subDrawerData,
          content, // 使用原始content
          contentVariables, // 使用原始contentVariables
          actionFieldValueMaps4Execute: JSON.stringify({
            ...JSON.parse(prev?.actionFieldValueMaps4Execute || '{}'),
            content,
            contentVariables
          }),
          actionFieldValueMaps4Display: JSON.stringify({ [code!]: { html: displayHtml } })
        }));
        setTextAreaValue(displayText);
      }
    });
  }, []);

  const insertVariable = variable => {
    // 在光标所在处插入变量
    const textArea = document.querySelector('.' + style.textAreaInput) as HTMLTextAreaElement;

    // 创建一个元素
    const span = document.createElement('span');
    // 插入的按钮不可编辑，设置contenteditable为false
    const html = `<span contentEditable="false">{${variable.name}}</span>`;
    span.innerHTML = html;
    // 将按钮插入
    position.insertNode(span);
    setContent(textArea.innerHTML, textArea.innerText);
  };

  const renderVaribleList = () => {
    return variableList.map(item => {
      return (
        <div className={style.variableItem} onClick={() => insertVariable(item)}>
          {item.name}
        </div>
      );
    });
  };

  const replaceVariablesWithIds = (text, variables) => {
    // 使用正则表达式匹配花括号内的变量名
    const regex = /\{([^}]*)\}/g;

    const contentVariables: any = [];
    // 使用map方法替换所有匹配的变量名
    const content = text.replace(regex, (match, variableName) => {
      const variable = variables.find(v => v.name === variableName);
      if (variable) contentVariables.push({ id: variable.id });
      return variable ? `var[${variable.id}]` : match;
    });
    return {
      content,
      contentVariables,
    };
  }

  const onTextAreaChange = e => {
    // 先把校验的文案清空
    setInvalidText('');
    // @ts-ignore
    const html = e.target.innerHTML;
    const text = e.target.innerText;
    setContent(html, text);
  };

  return (
    <div className={style.textAreaItem}>
      {/* 输入框部分 */}
      <div className={style.textArea}>
        <div
          contentEditable="true"
          className={style.textAreaInput}
          onBlur={e => {
            // @ts-ignore
            setPosition(window.getSelection().getRangeAt(0));
            onTextAreaChange(e);
          }}
          onInput={onTextAreaChange}
        ></div>
        {/* 操作栏 */}
        <div className={style.textAreaOperation}>
          {/* 左侧是"插入变量" 右侧是字数统计 */}
          <div className={style.textAreaOperationLeft}>
            {!!variableList.length && (
              <Pop
                content={renderVaribleList()}
                trigger="hover"
                position="bottom-center"
                containerSelector={'.' + style.textAreaOperationLeft}
              >
                <div className={style.insertVarible}>
                  <AddIcon />
                  <span>插入变量</span>
                </div>
              </Pop>
            )}
          </div>
          <div className={style.textAreaOperationRight}>
            <div className={style.textLengthLimit}>
              <span className={textAreaValue.length > MAX_COUNT_LIMIT ? style.invalidCount : null}>
                {textAreaValue.length}
              </span>{' '}
              / {MAX_COUNT_LIMIT}
            </div>
          </div>
        </div>
      </div>
      {variable.content}
      {/* 校验不通过的文案 */}
      {invalidText && <div className={style.invalidText}>{invalidText}</div>}
      {/* 有校验文案时不显示，反之显示 */}
      {!invalidText && <div className="variable-description">{variable.description}</div>}
    </div>
  );
};

export default TextAreaItem;
