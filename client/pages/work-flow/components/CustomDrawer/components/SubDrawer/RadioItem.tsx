import React, { FC, useEffect, useState } from 'react';
import { Radio, RadioGroup } from 'zent';
import { renderValueInput } from './fns';
import './style.scss';
import { editingActionAtom } from 'pages/work-flow/atoms';
import { useAtom } from 'jotai';
import matchMaxWidth from 'fns/matchMaxWidth';
import { parseJSON } from 'pages/work-flow/utils/flow';

const RadioItem: FC<any> = ({
  radioGroupVariables,
  selectedData,
  subDrawerData,
  variable,
  forceInit,
}) => {
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const { showAbleDependenciesVariables = [] } = parseJSON(variable.dynamicEndpointConfig);

  const defaultValue =
    showAbleDependenciesVariables.length > 0
      ? null
      : +JSON.parse(selectedData?.actionFieldValueMaps4Display || '{}')?.[variable.code]?.name ||
        radioGroupVariables[0].id;

  const [radioValue, setRadioValue] = useState(defaultValue);

  const setActionData = async value => {
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    const { code } = variable;

    actionFieldValueMaps4Execute[code!] = value;
    actionFieldValueMaps4Display[code!] = { name: value };

    // @ts-ignore
    setEditingAction(prev => {
      return {
        ...subDrawerData,
        actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
        actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
      };
    });
  };

  useEffect(() => {
    setRadioValue(defaultValue);
    // setActionData(defaultValue);

    const divsArray: HTMLElement[] = Array.from(document.querySelectorAll('.radio-label-name'));
    matchMaxWidth(divsArray, 120);
  }, []);

  useEffect(() => {
    if (forceInit && showAbleDependenciesVariables.length > 0) {
      const defaultValue = +parseJSON(editingAction?.actionFieldValueMaps4Display)?.[variable.code]
        ?.name;
      setRadioValue(defaultValue);
    }
  }, [forceInit]);

  const onRadioChange = e => {
    const { value } = e.target;
    setRadioValue(value);

    setActionData(value);
  };

  return (
    <RadioGroup value={radioValue} onChange={onRadioChange}>
      {radioGroupVariables.map((item, index) => {
        return (
          <React.Fragment key={index}>
            <Radio value={item.id}>
              <div className={'radio-label'}>
                <div className="radio-label-name">{item.name}</div>
              </div>
            </Radio>
            <div className="radio-label-ext">
              {radioValue === item.id &&
                renderValueInput(
                  item,
                  selectedData,
                  subDrawerData,
                  false,
                  editingAction,
                  setEditingAction,
                )}
            </div>
          </React.Fragment>
        );
      })}
    </RadioGroup>
  );
};

export default RadioItem;
