import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Form, FormStrategy, FormControl, Select, NumberInput, Notify } from 'zent';
import { useAtom, useAtomValue } from 'jotai';
import { cloneDeep } from 'lodash';
import { flowAtom, editingNode<PERSON>tom } from 'pages/work-flow/atoms';
import { graphAtom } from 'pages/work-flow/atoms/graph';
import {
  ENodeType,
  TimeUnit,
  TimeMax,
  BeforeOrAfter,
  BeforeOrAfterMap,
} from 'pages/work-flow/constant';
import { addChildNode } from 'pages/work-flow/fns/graph';
import { useCustomDrawer } from '../../useCustomDrawer';
import { getListVariablesByTrigger } from '../../../../api/index';

import './style.scss';

interface DelayContentProps {}

interface DelayContentRef {
  onConfirm: () => void;
}

const convert = value => {
  const { delayNode, delayTime } = value;
  const { node, type } = delayNode;
  const { input, select } = delayTime;
  const { key: variableId, text: variableIdText } = node;
  const { key: beforeOrAfter, text: beforeOrAfterText } = type;
  const { key: timeUnit, text: timeUnitText } = select;

  return {
    variableId,
    variableIdText,

    beforeOrAfter,
    beforeOrAfterText,

    timeUnit,
    timeUnitText,

    delayTime: input,
    delayTimeText: `${input}`,

    __origin: value, // 保存个引用 懒得再转了
  };
};

const createInitialValue = (data, flowData) => {
  const { delayTime, timeUnit, variableId, beforeOrAfter } = data;
  const unit = TimeUnit.find(u => u.key === timeUnit) || TimeUnit[0];

  const { triggerDefine } = flowData;
  const { delayVariables } = triggerDefine;

  const delayVariable = delayVariables.find(item => item.id === variableId);

  return {
    delayNode: {
      node: { key: variableId, text: delayVariable?.name },
      type: { key: beforeOrAfter, text: BeforeOrAfterMap[beforeOrAfter] },
    },
    delayTime: {
      input: delayTime,
      select: unit,
    },
  };
};

type DelayNodeOption = {
  node: {
    key: number;
    text: string;
    [k: string]: any;
  };
  type: typeof BeforeOrAfter[number];
};

type DelayTimeValue = {
  input: string;
  select: typeof TimeUnit[number];
};

const DelayNodeField = props => {
  const { name, label, placeholder, options } = props;
  const field = Form.useField<DelayNodeOption>(name) || {};
  const { node, type } = field.value || {};

  return (
    <FormControl label={label}>
      <div className="delay-field">
        <Select
          className="delay-field__large"
          placeholder={placeholder}
          value={node}
          options={options}
          onChange={value =>
            field.form.patchValue({
              [name]: {
                type,
                node: value,
              },
            })
          }
        />
        <Select
          className="delay-field__small"
          value={type}
          options={BeforeOrAfter}
          onChange={value =>
            field.form.patchValue({
              [name]: {
                node,
                type: value,
              },
            })
          }
        />
      </div>
    </FormControl>
  );
};

const DelayTimeField = props => {
  const { name, label, placeholder, options } = props;
  const field = Form.useField<DelayTimeValue>(name);
  const { input, select } = field.value || {};
  const max = TimeMax[select?.key] || 0;

  const handleInputChange = event => {
    let value = +event;
    if (value < 0) {
      value = 0;
    }

    if (value > max) {
      value = max;
    }

    field.form.patchValue({
      [name]: {
        select,
        input: `${value}`,
      },
    });
  };

  const handleSelectChange = value =>
    field.form.patchValue({
      [name]: {
        input,
        select: value,
      },
    });

  if (+input > max) {
    field.form.patchValue({
      [name]: {
        select,
        input: `${max}`,
      },
    });
  }

  return (
    <FormControl label={label}>
      <div className="delay-field">
        <NumberInput
          className="delay-field__item"
          type="number"
          placeholder={placeholder}
          value={input}
          min={0}
          max={max}
          decimal={0}
          onChange={handleInputChange}
        />
        <Select
          className="delay-field__item"
          value={select}
          options={options}
          onChange={handleSelectChange}
        />
      </div>
    </FormControl>
  );
};

const DelayContent = (props, ref) => {
  const form = Form.useForm(FormStrategy.View);

  const { getDrawerData } = useCustomDrawer();
  const { parentNode, isEditing } = getDrawerData();

  const [flowData, setFlowData] = useAtom(flowAtom);
  const graph = useAtomValue(graphAtom);
  const editingNode = useAtomValue(editingNodeAtom);

  const [delayNodeOptions, setDelayNodeOptions] = useState<DelayNodeOption[]>([]);

  const processFlowData = delay => {
    const newFlow = cloneDeep(flowData);

    if (isEditing) {
      const { index = -1 } = editingNode?.getData() || {};

      if (index > -1) {
        newFlow.chainRules![index].delayComponent = delay;
        setFlowData(newFlow);
      }
      return;
    }

    if (!newFlow.chainRules) {
      newFlow.chainRules = [
        {
          conditions: [],
          action: {
            name: '',
            variables: [],
          },
        },
      ];
    }

    newFlow.chainRules.push({
      conditions: [],
      delayComponent: delay,
      action: {
        name: '',
        variables: [],
      },
    });

    setFlowData(newFlow);
  };

  const processGraph = delay => {
    const nodeData = editingNode?.getData();
    const newNodeData = {
      ...nodeData,
      data: delay,
    };

    if (isEditing) {
      editingNode?.setData(newNodeData, { overwrite: true });
    } else {
      if (!graph) return Notify.error('未找到graph');
      addChildNode(parentNode, graph, {
        type: ENodeType.Delay,
        data: cloneDeep(delay),
        index: flowData.chainRules?.length || 0,
        parentNode,
      });
    }
  };

  const getListVariables = async () => {
    const { items } = await getListVariablesByTrigger({
      page: 1,
      pageSize: 99,
      triggerId: flowData.triggerId,
      relatedComponentType: 4,
    });

    const list = items.map(item => ({
      key: item.id,
      text: item.name,
      __origin: item, // ! 只是存一下原对象 不确定后面会不会用
    }));

    setDelayNodeOptions(list);
  };

  useImperativeHandle(ref, () => ({
    onConfirm: () => {
      const value = form.getValue();
      const delay = convert(value);

      processFlowData(delay);
      processGraph(delay);
    },
  }));

  useEffect(() => {
    let initialValue = {
      delayNode: {
        node: null,
        type: BeforeOrAfter[1],
      },
      delayTime: {
        input: '1',
        select: TimeUnit[0],
      },
    };

    if (isEditing) {
      const { data } = editingNode?.getData() || {};

      if (data && data.__origin) {
        initialValue = data.__origin;
      } else if (data) {
        // @ts-ignore
        initialValue = createInitialValue(data, flowData);
      }
    }

    form.initialize(initialValue);
    getListVariables();
  }, []);

  return (
    <div className="delay-content">
      <Form layout="horizontal" form={form}>
        <DelayNodeField
          name="delayNode"
          label="延迟节点："
          placeholder="选择延迟节点"
          options={delayNodeOptions}
        />
        <DelayTimeField
          name="delayTime"
          label="延迟时间："
          placeholder="输入时间数值"
          options={TimeUnit}
        />
      </Form>
    </div>
  );
};

export default forwardRef<DelayContentRef, DelayContentProps>(DelayContent);
