@import 'sass/vars/index.scss';

.delay-content {
  padding: 16px 24px 0;
  .label {
    color: #333;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }

  .zent-form-label {
    flex-basis: 80px !important;
  }

  .delay-field {
    display: flex;
    align-items: center;
    gap: 10px;

    & > span {
      font-weight: 600;
    }

    &__large {
      width: 165px;
    }

    &__small {
      width: 65px;
    }

    &__item {
      width: 115px;
    }
  }
}

.zent-drawer-backdrop {
  z-index: -1;
}
.zent-drawer-backdrop:first-of-type {
  z-index: 1000;
}
