import React, { useEffect, useState } from 'react';
import SearchForm from './components/SearchForm';
import { Button, Checkbox, Drawer, Grid, Icon, InfiniteScroller, Notify, Pop } from 'zent';
import { querySelectedData, searchData } from '../../../../api/workflow';
import formatMoney from '@youzan/utils/money/format';
import styles from './index.m.scss';
import { useInfinityList } from '../../../../hooks/useInfinityList';
import DetailList from './components/DetailList';
import { ItemSelectType, TYPE_DESC_MAP } from '../../constant';
import { flatGoodsGroupTree } from './utils';
import { useAtom } from 'jotai';
import { flowAtom } from 'pages/work-flow/atoms';
import { isRetailBranchStore, isRetailHqStore } from '@youzan/utils-shop';
import { cloneDeep } from 'lodash';

interface ItemSelectProps {
  type: ItemSelectType;
  value?: any;
  visible?: boolean;
  detailList?: any[];
  allowSelectSub?: boolean;
  onChange?: (value: number[]) => void;
  onConfirm?: (responseCover: any) => void;
  onClose?: () => void;
  extParam?: any;
  // 为零售场景特别新加的
  isRetailGoods?: boolean;
  // 最大可选择数量
  maxSelectedCount?: number;
}

const ItemSelect: React.FC<ItemSelectProps> = ({
  type,
  extParam = {},
  visible = true,
  value,
  allowSelectSub = true,
  onChange,
  onClose,
  onConfirm,
  isRetailGoods = false,
  maxSelectedCount = Infinity,
}) => {
  const initValue = value?.selectedMainIds || {};
  const [selectedMainIds, setSelectedMainIds] = useState<any>(value?.selectedMainIds || []);
  const [selectedSubIds, setSelectedSubIds] = useState<any>(
    // 不允许选择子项时，不接收selectedSubIds
    allowSelectSub ? value?.selectedSubIds || [] : [],
  );
  // const [list, setList] = useState<any>([]);

  const [flowData, setFlowData] = useAtom(flowAtom);
  const [filterParams, setFilterParams] = useState<any>({});
  const { loading, hasMore, list, loadMore, refresh } = useInfinityList({
    fetchList: searchData,
    defaultParams: {
      apiId: type,
      bizCode: flowData.triggerDefine?.code,
      extParam: JSON.stringify(extParam),
      filterParams: JSON.stringify(filterParams),
    },
    defaultPageSize: 20,
    filterParams,
  });
  const [innerVisible, setInnerVisible] = useState(visible);
  const [currentModel, setCurrentModel] = useState<'edit' | 'detail'>(
    value?.selectedMainIds?.length ? 'detail' : 'edit',
  );
  const isGoodsSelect =
    type === ItemSelectType.goods ||
    type === ItemSelectType.chainStoreGoods ||
    type === ItemSelectType.retailPurchaseGoods ||
    type === ItemSelectType.chainStoreSellGoods;

  // 选择商品不能大于maxSelectedCount
  const isSelectedToMaxNumber = selectedMainIds.length >= maxSelectedCount;

  const isSelectAll = (selectedMainIds, list) => {
    if (selectedMainIds.length !== list.length) {
      return false;
    }
    const allSkuIds = list.flatMap(item => item.skuInfos?.map(sku => sku.id) || [item.id]);

    return `${selectedMainIds}` === `${allSkuIds}`;
  };

  useEffect(() => {
    if (isGoodsSelect && allowSelectSub) {
      if (isSelectAll(selectedMainIds, list)) {
        // 全选情况
        const allSkuIds = list.flatMap(item => item.skuInfos?.map(sku => sku.id) || [item.id]);
        setSelectedSubIds([...new Set(allSkuIds)]);
      } else if (selectedMainIds.length === 0) {
        // 全部取消选中情况
        setSelectedSubIds([]);
      } else {
        // // 部分选中情况
        // ? 直接用 selectedMainIds 会有什么问题吗
        // const newSelectedSubIds = list
        //   .filter(item => selectedMainIds.includes(item.id))
        //   .flatMap(item => item.skuInfos?.map(sku => sku.id) || [item.id]);
        // setSelectedSubIds([...new Set(selectedMainIds)]);
      }
    }
  }, [selectedMainIds, list, isGoodsSelect, allowSelectSub]);

  const closeDrawer = (clearSelected = true) => {
    setInnerVisible(false);
    onClose && onClose();

    clearSelected && initValue.length === 0 && setSelectedMainIds([]);
    clearSelected && initValue.length === 0 && setSelectedSubIds([]);

    clearSelected && initValue.length > 0 && setSelectedMainIds(initValue);
  };

  const renderSubIds = ({ subInfos, targetId, isOptional = true, mergeSubIdToMain = false }) => {
    const disabled = isSelectedToMaxNumber && !selectedMainIds.includes(targetId);
    return (
      subInfos?.length > 1 &&
      subInfos.map(sub => (
        <div>
          <Checkbox
            disabled={isOptional === false || disabled}
            onChange={e => {
              console.log('==> ', subInfos);
              const { checked } = e.target;
              /* const selectedId = item.isNoSpecification
                ? [item.skuInfos[0].id]
                : selectedSkus.map(sku => sku.id); */
              if (mergeSubIdToMain) {
                setSelectedMainIds(
                  checked
                    ? [...selectedMainIds, sub.id]
                    : selectedMainIds.filter(i => i !== sub.id),
                );
              } else {
                const subIds = checked
                  ? [...selectedSubIds, sub.id]
                  : selectedSubIds.filter(i => i !== sub.id);
                setSelectedSubIds(subIds);
                // 取消选中的 判断是否是当前goodsId选中的最后一个sub 是的话取消goodsId的选中状态
                const isLastSub = subInfos.every(sub => !subIds.includes(sub.id));
                if (isLastSub) {
                  setSelectedMainIds(selectedMainIds.filter(i => i !== targetId));
                }
              }
            }}
            checked={(mergeSubIdToMain ? selectedMainIds : selectedSubIds).includes(sub.id)}
          >
            {sub.skuNo ? (
              <Pop
                trigger="hover"
                position={'right-center'}
                content={
                  <Grid
                    datasets={[sub]}
                    columns={[
                      {
                        title: '规格条码/编码',
                        name: 'skuNo',
                      },
                    ]}
                  />
                }
              >
                <span>
                  {sub.name}
                  <Icon type="info-circle-o" style={{ marginLeft: 2 }} />
                </span>
              </Pop>
            ) : (
              sub.name
            )}
            <span style={{ position: 'absolute', right: '-20px' }}>{sub.goodsCount}</span>
          </Checkbox>
        </div>
      ))
    );
  };

  const chainStoreColumns = [
    // 店铺名称 店铺分类 不可选原因 name storeTypeDesc notOptionalReason
    {
      title: '店铺名称',
      name: 'name',
    },
    {
      title: '店铺分类',
      width: '100px',
      name: 'storeTypeDesc',
    },
    {
      title: '不可选原因',
      name: 'notOptionalReason',
      width: '130px',
      // 为空时展示_
      bodyRender: _ => {
        const { notOptionalReason } = _;
        return notOptionalReason || '-';
      },
    },
  ];

  const warehouseColumns = [
    {
      title: '店铺名称',
      name: 'name',
    },
    {
      title: '不可选原因',
      name: 'notOptionalReason',
      width: '130px',
      bodyRender: _ => {
        const { notOptionalReason } = _;
        return notOptionalReason || '-';
      },
    },
  ];

  const goodsColumns = [
    {
      title: '商品名称',
      width: '220px',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price, id, isOptional } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <Pop trigger="hover" content={name} position="bottom-center">
                  <div className={styles.name}>{name}</div>
                </Pop>
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            {allowSelectSub && (
              <div className={styles.sku}>
                {renderSubIds({ subInfos: skuInfos, targetId: id, isOptional })}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '商品分组',
      name: 'groupNames',
      width: '180px',
      // 数组格式，展示为逗号分隔
      bodyRender: _ => {
        const { groupNames } = _;
        return groupNames?.join(',') || '';
      },
    },
    {
      title: '库存',
      name: 'stockNum',
    },
    {
      title: '不可选原因',
      name: 'notOptionalReason',
      width: '130px',
      // 为空时展示_
      bodyRender: _ => {
        const { notOptionalReason } = _;
        return notOptionalReason || '-';
      },
    },
  ];

  const chainSellGoodsColumns = [
    {
      title: '商品名称',
      width: '220px',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price, id, isOptional } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <Pop trigger="hover" content={name} position="bottom-center">
                  <div className={styles.name}>{name}</div>
                </Pop>
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            {allowSelectSub && (
              <div className={styles.sku}>
                {renderSubIds({ subInfos: skuInfos, targetId: id, isOptional })}
              </div>
            )}
          </div>
        );
      },
    },
    !isRetailBranchStore && {
      title: '商品分类',
      name: 'categoryName',
      width: '180px',
      // 数组格式，展示为逗号分隔
      bodyRender: _ => {
        const { categoryName } = _;
        return categoryName;
      },
    },
    !isRetailHqStore &&
      !isRetailBranchStore && {
        title: '库存',
        name: 'stockNum',
      },
    {
      title: '不可选原因',
      name: 'notOptionalReason',
      width: '130px',
      // 为空时展示_
      bodyRender: _ => {
        const { notOptionalReason } = _;
        return notOptionalReason || '-';
      },
    },
  ];

  const chainGoodsColumns = [
    {
      title: '商品名称',
      width: '220px',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price, id, isOptional } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <Pop trigger="hover" content={name} position="bottom-center">
                  <div className={styles.name}>{name}</div>
                </Pop>
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            {allowSelectSub && (
              <div className={styles.sku}>
                {renderSubIds({ subInfos: skuInfos, targetId: id, isOptional })}
              </div>
            )}
          </div>
        );
      },
    },
    !isRetailBranchStore && {
      title: '商品分类',
      name: 'categoryName',
      width: '180px',
      // 数组格式，展示为逗号分隔
      bodyRender: _ => {
        const { categoryName } = _;
        return categoryName;
      },
    },
    !isRetailHqStore &&
      !isRetailBranchStore && {
        title: '库存',
        name: 'stockNum',
      },
    {
      title: '不可选原因',
      name: 'notOptionalReason',
      width: '130px',
      // 为空时展示_
      bodyRender: _ => {
        const { notOptionalReason } = _;
        return notOptionalReason || '-';
      },
    },
  ];

  const couponColumns = [
    {
      title: '优惠券名称',
      name: 'name',
    },
    {
      title: '类型',
      name: 'cardTypeDesc',
    },
    {
      title: '优惠内容',
      name: 'formativeContext',
    },
    {
      title: '剩余库存',
      name: 'stockNum',
    },
  ];

  const couponCodeColumns = [
    {
      title: '优惠码名称',
      name: 'name',
    },
    {
      title: '类型',
      name: 'typeDesc',
    },
    {
      title: '优惠内容',
      name: 'formativeContext',
    },
    {
      title: '剩余库存',
      name: 'stockNum',
    },
  ];

  const presentColumns = [
    {
      title: '赠品名称',
      name: 'name',
      bodyRender: _ => {
        const { name, picture } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.infoOnlyName}>
                <div className={styles.name}>{name}</div>
                {/* <div className={styles.price}>￥{formatMoney(price)}</div> */}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: '剩余库存',
      name: 'stockNum',
    },
  ];

  const goodsGroupColumns = [
    {
      title: '分组名称',
      name: 'name',
      bodyRender: _ => {
        const { name, secondGroups } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <div className={styles.infoOnlyName}>
                <div className={styles.goodsGroupName}>{name}</div>
              </div>
            </div>
            <div className={styles.sku}>
              {renderSubIds({ subInfos: secondGroups, targetId: _.id, mergeSubIdToMain: true })}
            </div>
          </div>
        );
      },
    },
    {
      title: '当前商品总数',
      name: 'goodsCount',
    },
  ];

  const yzCloudClientColumns = [
    {
      title: '应用名称',
      name: 'name',
    },
  ];

  const retailPurchaseColumns = [
    {
      title: '商品名称',
      width: '300px',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price, id, isOptional, spuNo } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <Pop trigger="hover" content={name} position="bottom-center">
                  <div className={styles.name}>{name}</div>
                </Pop>
                {spuNo && <div className={styles.spuNo}>{spuNo}</div>}
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            {allowSelectSub && (
              <div className={styles.sku}>
                {renderSubIds({ subInfos: skuInfos, targetId: id, isOptional })}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '库存',
      name: 'stockNum',
    },
    {
      title: '状态',
      name: 'lifeCycleName',
    },
    {
      title: '不可选原因',
      name: 'notOptionalReason',
      width: '130px',
      // 为空时展示_
      bodyRender: _ => {
        const { notOptionalReason } = _;
        return notOptionalReason || '-';
      },
    },
  ];

  const retailShopSaleChannelColumns = [
    {
      title: '渠道名称',
      name: 'name',
    },
  ];

  const wxxdShopSelectorColumns = [
    {
      title: '店铺名称',
      name: 'name',
    },
  ];
  const columnMap = {
    [ItemSelectType.goods]: goodsColumns,
    [ItemSelectType.coupon]: couponColumns,
    [ItemSelectType.couponCode]: couponCodeColumns,
    [ItemSelectType.present]: presentColumns,
    [ItemSelectType.goodsGroup]: goodsGroupColumns,
    [ItemSelectType.chainStoreSubShop]: chainStoreColumns,
    [ItemSelectType.warehouse]: warehouseColumns,
    [ItemSelectType.chainStoreSellGoods]: chainSellGoodsColumns,
    [ItemSelectType.chainStoreGoods]: chainGoodsColumns,
    [ItemSelectType.yzCloudClient]: yzCloudClientColumns,
    [ItemSelectType.retailPurchaseGoods]: retailPurchaseColumns,
    [ItemSelectType.retailShopSaleChannel]: retailShopSaleChannelColumns,
    [ItemSelectType.wxxdShopSelector]: wxxdShopSelectorColumns,
  };

  const currentColumns = columnMap[type];

  useEffect(() => {
    setInnerVisible(visible);
  }, [visible]);

  useEffect(() => {
    refresh({ apiId: type });
  }, [type]);

  // useEffect(() => {
  //  // 外部value变更的时候，更新是否展示详情页
  //  if (!value.selectedMainIds.length) {
  //    setCurrentModel('edit');
  //  }
  // }, [value]);

  return currentModel === 'detail' ? (
    <DetailList
      type={type}
      visible={innerVisible}
      onEdit={() => {
        setInnerVisible(true);
        setCurrentModel('edit');
      }}
      listIds={selectedMainIds}
      listSubIds={selectedSubIds}
      extParam={extParam}
      onClose={() => {
        setInnerVisible(false);
        onClose && onClose();
      }}
    />
  ) : (
    <Drawer
      title={
        <>
          {/* 增加一个返回按钮+标题 */}
          <div className={styles.headerTitle}>
            <Icon
              type="left"
              style={{ fontSize: 30, cursor: 'pointer', fontWeight: 500 }}
              onClick={() => closeDrawer()}
            />
            <span>{`${TYPE_DESC_MAP[type]}范围`}</span>
          </div>
        </>
      }
      placement="right"
      width={682}
      visible={innerVisible}
      onClose={() => {
        setInnerVisible(false);
        onClose && onClose();
      }}
      maskClosable
      closeBtn={<Icon type="close" style={{ fontWeight: 600, fontSize: 22 }} />}
    >
      <div className={styles.selectContainer}>
        <SearchForm
          type={type}
          onChange={value => {
            setFilterParams(value);
          }}
        ></SearchForm>
        <InfiniteScroller
          hasMore={hasMore}
          skipLoadOnMount
          loadMore={loadMore}
          className={styles.list}
        >
          <Grid
            className={isGoodsSelect ? styles.includeImage : styles.includeSub}
            loading={loading}
            columns={currentColumns}
            autoStick
            rowKey={'id'}
            datasets={list}
            selection={{
              selectedRowKeys: selectedMainIds,
              onSelect: ids => {
                // 最多选择maxSelectedCount个商品，如果超过了则默认选择前maxSelectedCount个，并给出提示
                const selectedIds = ids.slice(0, maxSelectedCount);
                setSelectedMainIds(selectedIds);

                // 如果是商品选择且允许选择子项，需要同步更新 selectedSubIds
                if (isGoodsSelect && allowSelectSub) {
                  const newSelectedSubIds = list
                    .filter(item => selectedIds.includes(item.id))
                    .flatMap(item =>
                      item.isNoSpecification
                        ? [item.skuInfos[0].id]
                        : item.skuInfos?.map(sku => sku.id) || [],
                    );
                  setSelectedSubIds([...new Set(newSelectedSubIds)]);
                }

                if (ids.length > maxSelectedCount) {
                  Notify.warn('最多支持指定' + maxSelectedCount + '个商品', 2000);
                }
              },
              getSelectionProps: record => {
                return {
                  disabled:
                    record.isOptional === false ||
                    // 已选达到最大数量并且当前商品没选中时不可编辑
                    (isSelectedToMaxNumber && !selectedMainIds.includes(record.id)),
                };
              },
            }}
          ></Grid>
        </InfiniteScroller>
        <div className={styles.footer}>
          {currentModel === 'edit' && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  console.log('isRetailGoods is ', isRetailGoods);
                  if (isRetailGoods) {
                    // 创建新的对象，键为SPU ID，值为对应的SKU ID数组
                    const spuSkuMap = {};

                    // 遍历 list 来建立 SPU 和 SKU 之间的关系
                    list.forEach(item => {
                      const selectedSkus = item.skuInfos.filter(sku =>
                        selectedSubIds.includes(sku.id),
                      );
                      if (selectedSkus.length > 0 || selectedMainIds.includes(item.id)) {
                        // 如果这个 SPU 被选中，或者它的任何 SKU 被选中
                        spuSkuMap[item.id] = item.isNoSpecification
                          ? [item.skuInfos[0].id]
                          : selectedSkus.map(sku => sku.id);
                      }
                    });

                    // 更新 selectedMainIds，确保包含所有选中 SKU 的 SPU
                    const updatedSelectedMainIds = [
                      ...new Set([...selectedMainIds, ...Object.keys(spuSkuMap).map(Number)]),
                    ];

                    onConfirm?.({
                      selectedNames: list
                        .filter(i => updatedSelectedMainIds.includes(i.id))
                        .map(i => i.name),
                      selectedMainIds: updatedSelectedMainIds,
                      selectedSubIds: isGoodsSelect ? selectedSubIds : undefined,
                      selected: list
                        .filter(i => updatedSelectedMainIds.includes(i.id))
                        .map(item => ({ name: item.name, id: item.id })),
                      spuSkuMap, // 添加新的 spuSkuMap 对象
                    });
                  } else {
                    const params = {
                      // 商品分组时要打平计算。。。。商品分组虽然有二级分组，但是与上一级无本质关系
                      selectedNames: (type === ItemSelectType.goodsGroup
                        ? flatGoodsGroupTree(list)
                        : list
                      )
                        .filter(
                          i =>
                            selectedMainIds.includes(i.id) ||
                            i.skuIds?.some(j => selectedSubIds.includes(j)),
                        )
                        .map(i => i.name),
                      selectedMainIds,
                      selectedSubIds: isGoodsSelect ? selectedSubIds : undefined,
                      selected: list
                        .filter(
                          i =>
                            selectedMainIds.includes(i.id) ||
                            i.skuIds?.some(j => selectedSubIds.includes(j)),
                        )
                        .map(item => ({ name: item.name, id: item.id })),
                    };
                    onConfirm?.(params);
                  }
                  closeDrawer(false);
                }}
              >
                确定
              </Button>
              <Button onClick={() => closeDrawer()}>取消</Button>
            </>
          )}
        </div>
      </div>
    </Drawer>
  );
};

export default ItemSelect;

const ItemSelectField = ({
  type,
  outputKey,
  value,
  onChange,
  subType,
  disabled,
  onError,
  extParam,
  isRetailGoods = false,
  maxSelectedCount = Infinity,
}) => {
  const [showItemSelect, setShowItemSelect] = useState(false);
  const [fieldTitle, setFieldTitle] = useState('');
  const [filedItemCountText, setFieldItemCountText] = useState('');
  const onSelected = value => {
    const { selectedNames = [], selectedMainIds, selectedSubIds, selected, spuSkuMap } = value;
    if (isRetailGoods) {
      onChange({
        [outputKey]: spuSkuMap,
        selected,
      });
    } else {
      onChange({
        [outputKey]: selectedSubIds?.length ? selectedSubIds : selectedMainIds,
        selected,
      });
    }
    setFieldTitle(`${selectedNames.length ? selectedNames[0] : ''}`);
    setFieldItemCountText(`${selectedNames.length > 1 ? ` 等${selectedNames.length}个` : ''}`);
  };

  useEffect(() => {
    const { selectedMainIds } = value;
    // 有mainIds的时候需要回显选择项
    if (selectedMainIds.length) {
      querySelectedData({
        searchDataType: type,
        ids: selectedMainIds.slice(0, 2).join(','),
        extParam: JSON.stringify(extParam),
      }).then(item => {
        const selectedNames = item.map(i => i.name);
        setFieldTitle(`${selectedNames.length ? selectedNames[0] : ''}`);
        setFieldItemCountText(
          `${selectedMainIds.length > 1 ? ` 等${selectedMainIds.length}个` : ''}`,
        );
      });
    } else {
      setFieldTitle('');
      setFieldItemCountText('');
    }
  }, [value]);

  return (
    <>
      <div
        onClick={() => {
          if (disabled) {
            onError && onError();
            return;
          }
          setShowItemSelect(true);
        }}
        className={`${styles.itemSelectField} ${fieldTitle ? styles.value : ''} ${
          disabled ? styles.disabled : ''
        }`}
      >
        <div className={styles.title}>{fieldTitle || `点击选择${TYPE_DESC_MAP[type] || ''}`}</div>
        <div className={styles.count}> {filedItemCountText}</div>
      </div>
      {showItemSelect && (
        <ItemSelect
          type={type}
          visible={showItemSelect}
          value={value}
          onClose={() => setShowItemSelect(false)}
          isRetailGoods={isRetailGoods}
          onConfirm={onSelected}
          extParam={extParam}
          allowSelectSub={subType !== 'goodsId'}
          maxSelectedCount={maxSelectedCount}
        />
      )}
    </>
  );
};

export { ItemSelectField };
