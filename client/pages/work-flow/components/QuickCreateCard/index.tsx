import { BlockLoading, Button, Icon, Pop } from 'zent';
import { useFlowCreate } from '../../../../hooks/useFlowCreate';
import styles from './index.m.scss';
import jumpTo from '../../../../fns/jumpTo';
import React, { useState } from 'react';

export enum QuickCreateCardType {
  New,
  Template,
  More,
}

const QuickCreateCard = ({
  type = QuickCreateCardType.Template,
  title = '',
  desc = '',
  templateId = '',
  url = '',
  disabled = false,
  ...rest
}) => {
  const { quickCreate } = useFlowCreate({ templateId, flowName: title });
  const [loading, setLoading] = useState(false);

  const create = () => {
    if (disabled) return;
    setLoading(true);
    quickCreate(url, true).finally(() => setLoading(false));
  };

  const renderContent = (type: QuickCreateCardType) => {
    switch (type) {
      case QuickCreateCardType.New:
        return (
          <div className={styles.newCreate} onClick={create}>
            <Icon type="plus-circle-o" style={{ fontSize: 16, color: 'black', fontWeight: 500 }} />
            新建自动任务
          </div>
        );
      case QuickCreateCardType.Template:
        return (
          <>
            <div className={styles.title}>{title}</div>
            <div className={styles.desc}>{desc}</div>
            <Button type="text" disabled={disabled} onClick={create}>
              快速创建
            </Button>
          </>
        );
      case QuickCreateCardType.More:
        return (
          <div
            className={styles.newCreate}
            onClick={() =>
              jumpTo({
                url: '/v4/jiawo/work-flow/templates',
              })
            }
          >
            更多任务模板
          </div>
        );
      default:
        return null;
    }
  };

  return disabled ?
    <BlockLoading loading={loading} icon="circle">
      <Pop trigger="hover" content={<div>升级更高版本后可用，<a href="https://store.youzan.com/v4/subscribe/pc-order/software/choose#/">去升级</a></div>}>
        <div className={styles.createCardContainer + (disabled ? ' ' + styles.disabled : '')} {...rest}>
          {renderContent(type)}
        </div>
      </Pop>
    </BlockLoading> :
    <BlockLoading loading={loading} icon="circle">
      <div className={styles.createCardContainer + (disabled ? ' ' + styles.disabled : '')} {...rest}>
        {renderContent(type)}
      </div>
    </BlockLoading>
};

export default QuickCreateCard;
