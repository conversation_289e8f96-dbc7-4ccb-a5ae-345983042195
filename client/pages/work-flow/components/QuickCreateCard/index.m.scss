.create-card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 4px;
  background: #f2f7fe;
  min-width: 252px;
  max-width: 400px;
  min-height: 132px;
  padding: 0 16px;
  box-sizing: border-box;
  .new-create {
    line-height: 20px;
    font-weight: 500;
    padding: 50px;
    cursor: pointer;
    i {
      margin-right: 4px;
      font-weight: 500;
    }
  }

  .title {
    color: #333;
    font-weight: 500;
    line-height: 20px;

    i {
      margin-right: 4px;
    }
  }

  .desc {
    color: #999;
    font-size: 12px;
    line-height: 20px;
    height: 40px;
    display: flex;
    flex-direction: column;
    text-align: center;
  }
}

.disabled {
  background-color: #f7f7f7;
  cursor: not-allowed;

  .new-create {
    color: #ccc;
    cursor: not-allowed;
  }
}
