import React from 'react';
import './style.scss';

const ResponsiveCurlyBrace = () => {
  return (
    <div className="box">
      {/* <svg width="5" height="2" viewBox="0 0 5 2" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.5 4.83936C0.5 2.35407 2.51472 0.339355 5 0.339355L12 0.339356C12.2761 0.339356 12.5 0.563213 12.5 0.839355C12.5 1.1155 12.2761 1.33936 12 1.33936L5 1.33936C3.067 1.33936 1.5 2.90636 1.5 4.83936C1.5 5.1155 1.27614 5.33936 1 5.33936C0.723858 5.33936 0.5 5.1155 0.5 4.83936Z"
          fill="#999999"
        />
      </svg> */}
      <img src="https://img01.yzcdn.cn/upload_files/2024/03/07/FoufS35E-ct2wXoT0xPgfUxtKUer.png" />

      <div className="shu"></div>
      <div className="text">且</div>
      <div className="shu"></div>
      <img src="https://img01.yzcdn.cn/upload_files/2024/03/07/FvV1aa9mVuOWgzUoyzapeSqK6VaK.png" />
    </div>
  );
};

export default ResponsiveCurlyBrace;
