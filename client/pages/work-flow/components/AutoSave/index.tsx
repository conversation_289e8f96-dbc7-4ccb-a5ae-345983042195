import { useAtom } from 'jotai';
import { autoSaveVisibleAtom } from 'pages/work-flow/atoms';
import { useEffect } from 'react';

const AutoSave = () => {
  const [autoSaveVisible, setAutoSaveVisible] = useAtom(autoSaveVisibleAtom);

  useEffect(() => {
    if (autoSaveVisible) {
      setTimeout(() => {
        setAutoSaveVisible(false);
      }, 5000);
    }
  }, [autoSaveVisible]);

  // 渲染自动保存的语句
  const renderAutoSaveMsg = () => {
    return `最新保存时间${
      // 当前时间 hh:mm:ss
      new Date().toLocaleTimeString().split(' ')[0]
    }`;
  };

  return autoSaveVisible ? <p className="auto-save">{renderAutoSaveMsg()}</p> : <></>;
};

export default AutoSave;
