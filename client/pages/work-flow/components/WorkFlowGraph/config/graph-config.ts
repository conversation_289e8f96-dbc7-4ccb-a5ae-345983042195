import { Options, Edge } from '@antv/x6';

export const getGraphConfig: () => Partial<Options.Manual> = () => {
  return {
    container: document.getElementById('graph-container') || undefined,
    //   width: 1600,
    //   height: 1200,
    autoResize: true,
    background: {
      color: '#f7f7f7',
    },
    grid: true,
    // 可以拖拽
    panning: {
      enabled: true,
    },
    mousewheel: {
      enabled: true,
      modifiers: ['ctrl', 'meta'],
      // guard: createMousewheelGuard(),
    },
    connecting: {
      // 自动吸附
      // snap: {
      //   radius: 50,
      // },
      // 是否允许连接到画布空白位置的点，默认为 true
      allowBlank: false,
      // 是否允许在相同的起始节点和终止之间创建多条边，默认为 true
      allowMulti: false,
      // 是否允许创建循环连线，即边的起始节点和终止节点为同一节点，默认为 true
      allowLoop: false,
      // 是否允许边链接到另一个边，默认为 true
      allowEdge: false,
      // 是否允许边链接到节点（非节点上的链接桩），默认为 true
      allowNode: true,

      highlight: true,

      // https://x6.antv.antgroup.com/api/registry/connector
      // connector: 'rounded',
      connector: {
        name: 'rounded',
        args: {
          radius: 20,
        },
      },

      // https://x6.antv.antgroup.com/api/registry/router
      router: {
        name: 'manhattan',
        args: {
          startDirections: 'right',
          endDirections: 'left',
          padding: 20,
        },
      },

      // https://x6.antv.antgroup.com/api/registry/node-anchor
      anchor: {
        name: 'topRight',
        args: {
          dy: 16,
        },
      },
      // https://x6.antv.antgroup.com/api/registry/connection-point
      connectionPoint: {
        name: 'boundary',
        args: {
          // sticky: true,
        },
      },

      // 定义边的样式
      /* createEdge() {
              return new Shape.Edge(EdgeAgent.createEdgeMeta());
            }, */

      // 在「连接时」做规则校验，如果校验不通过（返回 false），则不会进行连接
      // validateConnection(args) {
      //   console.log('validate Connection called');
      //   // eslint-disable-next-line @typescript-eslint/no-this-alias
      //   const graph = this;
      //   const { sourceCell, targetCell, targetPort } = args;

      //   /* ------------------- 只允许将链接桩连接到节点上 ------------------- */
      //   if (!targetCell?.isNode() || !sourceCell?.isNode() || targetPort) {
      //     return false;
      //   }

      //   /* ------------------- 不允许连接到 port node 上 ------------------- */

      //   if (targetCell.getParent()) {
      //     return false;
      //   }

      //   /* ------------------- DAG 校验 ------------------- */

      //   // 获取所有前序节点
      //   const predecessors = graph.getPredecessors(sourceCell);
      //   // 前序节点不能包括目标节点
      //   const isDag = !predecessors.includes(targetCell);

      //   if (!isDag) {
      //     return false;
      //   }

      //   return true;
      // },

      // 在「连接后」做规则校验，如果校验不通过（返回 false），edge 将会被移除
      validateEdge() {
        console.log('validate edge called');
        // const graph = this;
        // return validateEdge(graph, args /* getPlanType */);
        return true;
      },
    },
  };
};

export const getAddNodeEdgeConfig = ({ parentNode, childNode }): Edge.Metadata => {
  return {
    source: {
      cell: parentNode,
      anchor: {
        name: 'right',
      },
      connectionPoint: 'anchor',
    },
    target: {
      cell: childNode,
      anchor: {
        name: 'left',
      },
      connectionPoint: 'anchor',
    },
    // connector: 'smooth',
    attrs: {
      line: {
        stroke: '#155BD4',
        strokeWidth: 2,
        targetMarker: {
          name: 'classic',
          size: 7,
        },
      },
    },
    anchor: {
      source: 'bottomRight', // 源节点的锚点
      target: 'left', // 目标节点的锚点
      name: 'midSide',
      args: {
        dx: 10,
      },
    },
    // connector: 'smooth',
    // connectionPoint: 'boundary',
    connectionPoint: 'anchor',
  };
};
