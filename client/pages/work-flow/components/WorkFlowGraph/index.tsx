import { useState, useEffect, FC, useRef } from 'react';
import { Graph, Node } from '@antv/x6';
import { ENodeType } from 'pages/work-flow/constant';
import { getGraphConfig } from './config/graph-config';
import {
  createNode,
  getGraphData,
  registerActionNode,
  registerConditionNode,
  registerTriggerNode,
  registerDelayNode,
} from 'pages/work-flow/fns/graph';
import { useCustomDrawer } from '../CustomDrawer/useCustomDrawer';
import { Notify } from 'zent';
import { useAtom, useAtomValue } from 'jotai';
import { flowAtom, autoSaveVisibleAtom, graphAtom, isFlowEditingAtom } from 'pages/work-flow/atoms';
import { saveDraft } from 'api/workflow';
import { debounce } from 'lodash';
import { getFlowDetail } from 'pages/work-flow/api';
import { convertFormDataToGraphData } from 'pages/work-flow/fns/formToGraph';
import { Locker } from '../../../../fns/Locker';
import { AUTO_SAVE_DRAFT_LOCK } from '../../constant/lock';
import { formatCondition, formatConditionWhenGet } from '../../utils/flow';

// 注册四种自定义节点
registerTriggerNode();
registerConditionNode();
registerActionNode();
registerDelayNode();

const locker = Locker.getInstance();
const lockInstance = locker.registe(AUTO_SAVE_DRAFT_LOCK);

const WorkFlowGraph: FC = () => {
  const [graph, setGraph] = useAtom(graphAtom);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [nodeName, setNodeName] = useState('');
  const { showDrawer } = useCustomDrawer();
  const [flowData, setFlowData] = useAtom(flowAtom);
  const graphFromAtom = useAtomValue(graphAtom);
  const [autoSaveVisible, setAutoSaveVisible] = useAtom(autoSaveVisibleAtom);
  const [isFlowEditing, setIsFlowEditing] = useAtom(isFlowEditingAtom);

  const flowDataRef = useRef(flowData);
  const isFlowEditingRef = useRef(isFlowEditing);

  useEffect(() => {
    flowDataRef.current = flowData;
  }, [flowData]);

  const autoSave = debounce(graph => {
    if (isFlowEditingRef.current) return;
    if (!graph) return Notify.error('请先绘制流程图');
    if (lockInstance.isLocking()) return;
    lockInstance.lock();
    const graphData = getGraphData(graph);
    const { chainRules } = flowDataRef.current;
    const formattedChainRules = chainRules && formatCondition(chainRules);

    // 保存草稿
    saveDraft({
      ...flowDataRef.current,
      chainRules: formattedChainRules,
      canvasComponents: graphData,
    })
      .then(res => {
        setAutoSaveVisible(true);

        if (!flowDataRef.current.id) {
          // 如果没有flowId，则将返回的id设为当前画布的flowId
          setFlowData({
            ...flowDataRef.current,
            id: res,
          });
          const newUrl = window.location.origin + window.location.pathname + `?flowId=${res}`;
          history.replaceState({ flowId: res }, '', newUrl);
        }
      })
      .finally(() => {
        lockInstance.unLock();
      });
  }, 2000);

  /* const registerBtnTool = (graph: Graph) => {
    Graph.registerNodeTool(
      'my-btn',
      {
        inherit: 'button', // 继承于 button 工具
        markup: [
          // 按钮的标记
          {
            tagName: 'circle',
            selector: 'button',
            attrs: {
              r: 10, // 半径
              fill: 'rgb(21,91,212)', // 填充色
              // 边框宽度
              strokeWidth: 2,
              stroke: '#ccc', // 描边色
              cursor: 'pointer', // 鼠标样式
            },
          },
          {
            tagName: 'text',
            textContent: '+', // 文本内容
            selector: 'icon',
            attrs: {
              fill: '#fff', // 文本颜色
              fontSize: 14, // 字体大小
              textAnchor: 'middle', // 水平对齐方式
              pointerEvents: 'none', // 禁止文本响应鼠标事件
              y: '0.3em', // 垂直偏移量
            },
          },
        ],

        onClick(node) {
          const { cell } = node;
          // 点击事件处理函数，参数为当前节点对象
          addChildNode(cell, graph, ); // 调用添加子节点的函数，传入 graph 对象
        },
      },
      true,
    );
  }; */

  const initial = () => {
    // 初始化画布
    const newGraph = new Graph(getGraphConfig());

    // 创建初始节点
    const initialNode = newGraph.addNode(
      createNode({
        data: { type: ENodeType.Trigger },
        x: 100,
        y: 100,
        graph: newGraph,
      }),
    );

    newGraph.addNode(initialNode);
    setGraph(newGraph);

    return newGraph;
  };

  // 点击节点，根据type类型弹出不同的编辑框
  const onEditNode = (node, type) => {
    const { data, index } = node.getData();
    showDrawer({
      type,
      conditionIdx: index,
      isEdit: true,
      parentNode: null,
      currentNode: node,
      actionIdx: index,
    });
  };

  useEffect(() => {
    const graph = initial();
    // 检测url上是否有id，如果有则请求数据
    // 从url上获取id，然后请求数据
    const url = new URL(window.location.href);
    const flowId = url.searchParams.get('flowId');
    if (flowId) {
      // 请求数据
      getFlowDetail({ flowId })
        .then(res => {
          const { canvasComponents, ...others } = res;
          const { status, chainRules } = others;
          others.chainRules = formatConditionWhenGet(chainRules);

          // 设置启用状态
          if (status === 1) {
            setIsFlowEditing(true);
            isFlowEditingRef.current = true;
          }

          // FIXME: 这里不再使用canvasComponents，而是直接使用flowData做转换，原因是在手动点击保存时，会删除chainRules中的空数据，但是没有处理canvasComponents，导致节点存储的index不准确，编辑时无法正确改动或删除
          // FIXME: 不使用canvasComponents的缺点是，无法保存用户自由拖动的节点位置。
          // if (canvasComponents) {
          // graph.fromJSON(JSON.parse(canvasComponents));
          // } else {
          const graphData = convertFormDataToGraphData({ ...others });
          graph.fromJSON(graphData);
          // }
          // @ts-ignore
          setFlowData({ ...others });
        })
        .catch(err => {
          Notify.error(err);
        });
    } else {
      // 一进来先弹出trigger的编辑框
      const nodes = graph.getNodes();
      onEditNode(nodes[0], ENodeType.Trigger);
    }

    // 点击节点弹出输入框修改节点名称
    graph.on('node:click', params => {
      const { node, e } = params;

      // 如果点击的是svg或者path，则不弹出 这是为了避免点击加号按钮时弹出抽屉，如果有更好的方法可以改掉这里
      if (
        e.target.outerHTML.startsWith('<path') ||
        e.target.outerHTML.startsWith('<svg') ||
        e.target.outerHTML.startsWith('<circle') ||
        e.target.outerHTML.startsWith('<rect')
        // ||
        // 或者className为action-filed => 是action的选中项也不触发
        // 0626新增：trigger同样逻辑
        /* ['trigger-field', 'trigger-shrink-span', 'action-field', 'action-shrink-span'].includes(
          e.target.className,
        ) */
      ) {
        return;
      }
      // setSelectedNode(node);
      // setNodeName(node.attr('label/text'));
      const { type } = node.getData();
      console.log('node data is ', node.getData())
      onEditNode(node, type);
    });

    // graph.on('node:changed', ({ cell, options }) => {
    //  /**
    //   * 节点内容变更会导致高度变更，为了避免高度变更导致节点重叠，需要重新布局
    //   * 尽可能减少变更内容，只变更会重叠的node
    //   * 假如a在上面  b在下面，a的高度变化会导致b的位置变化，那么只需要重新布局b
    //   */
    //  const autoLayout = () => {
    //  }
    // });

    graph.on('node:added', ({ cell, index, options }) => {
      console.log('cell:added 触发了', cell, index, options);
      autoSave(graph);
    });

    graph.on('cell:changed', ({ cell, options }) => {
      console.log('cell:changed 触发了', cell, options, flowData);
      autoSave(graph);
    });

    /* graph.getNodes().forEach(node => {
      node.on('change:data', () => {
        console.log('节点数据变化:', node);
      });
    });
    // 监听节点变化事件
    graph.on('change:data', ({ cell, options }) => {
      // 确保是同一个节点
      // const newData = cell.getData();
      console.log('节点数据变化:');
    }); */
  }, []);

  // 处理节点名称输入
  const handleNodeNameChange = event => {
    setNodeName(event.target.value);
  };

  // 更新节点名称
  const updateNodeName = () => {
    if (selectedNode && nodeName) {
      selectedNode?.attr('label/text', nodeName);
      setNodeName('');
      setSelectedNode(null);
    }
  };

  return (
    <div style={{ width: '100%', height: 'calc(100vh - 54px - 70px - 32px - 70px)' }}>
      <div id="graph-container"></div>
      {selectedNode && (
        <div className="modal">
          <input
            type="text"
            value={nodeName}
            onChange={handleNodeNameChange}
            placeholder="输入节点名称"
          />
          <button onClick={updateNodeName}>确认</button>
        </div>
      )}
    </div>
  );
};

export default WorkFlowGraph;
