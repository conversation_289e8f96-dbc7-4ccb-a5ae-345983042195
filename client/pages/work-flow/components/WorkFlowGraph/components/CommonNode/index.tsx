import React, { Children, FC, ReactNode, useEffect, useState } from 'react'; // 导入React库和一些类型定义
import './style.scss'; // 导入CSS样式文件
import {
  ENodeType,
  addNodeListMap,
  bgMap,
  leftIconMap,
  nodeThemeColorMap,
  rightIconMap,
  typeNameMap,
} from 'pages/work-flow/constant/node'; // 导入节点相关的常量映射
import { Graph, Node } from '@antv/x6'; // 导入图形库中的Graph和Node类型
import { Notify, Pop } from 'zent'; // 导入Pop组件，用于创建弹出内容
import { useCustomDrawer } from 'pages/work-flow/components/CustomDrawer/useCustomDrawer';
import { IDrawerData } from 'pages/work-flow/constant';
import { useAtom } from 'jotai';
import { flowAtom } from 'pages/work-flow/atoms';
import { v4 as uuidv4 } from 'uuid';
import cn from 'classnames';
import { DeleteIcon, InvalidTipsIcon } from 'svg';
import { cloneDeep } from 'lodash';
import { convertFormDataToGraphData } from 'pages/work-flow/fns/formToGraph';
import { renderIconByType } from 'pages/work-flow/fns';

// 生成唯一的节点 ID
function generateNodeId(): string {
  return `commonNode-${uuidv4()}`;
}

// 创建一个函数组件CommonNode，它接收一个props对象，包含node（节点）、graph（图形）、onAddNode（添加节点的回调函数）和children（子元素）
const CommonNode: FC<{
  node: Node; // 节点对象
  graph: Graph; // 图形对象
  onAddNode?: (type: ENodeType) => void; // 添加节点的回调函数，可选
  invalidTipsArr?: string[];
}> = ({ node, graph, children, onAddNode, invalidTipsArr }) => {
  // const [invalidTipsArr, setInvalidTipsArr] = useState<string[]>([]);
  const { showDrawer } = useCustomDrawer();
  const [flowData, setFlowData] = useAtom(flowAtom);

  const childId = generateNodeId();

  // 私有方法，用于获取节点的类型
  const getNodeType = (): ENodeType => {
    const nodeData = node.getData(); // 获取节点的数据
    return nodeData ? nodeData.type : ''; // 如果nodeData存在，返回其type属性，否则返回null
  };

  // 根据type和是否校验通过 渲染不同的背景图
  const renderBgByType = () => {
    const type = getNodeType();
    const isValid = !(invalidTipsArr && invalidTipsArr.length);

    const bg = bgMap[type][isValid ? 'valid' : 'invalid'];
    return { backgroundImage: `url(${bg})` };
  };

  // 渲染节点名称的方法，根据节点类型从typeNameMap中获取名称
  const renderNameByType = (): string => {
    const type = getNodeType();
    return typeNameMap[type] || ''; // 如果type存在，返回对应的名称，否则返回空字符串
  };

  // 渲染节点上图标，根据所选的type渲染不同的图标
  const renderNodeIcon = () => {
    const type = getNodeType();
    const { data: { groupCode } = { groupCode: '' } } = node.getData();
    return renderIconByType({ type, groupCode });
  };

  // 渲染左侧图标的方法，根据节点类型从leftIconMap中获取图标组件
  const renderLeftIcon = (): ReactNode => {
    const type = getNodeType();
    const IconComponent = leftIconMap[type]; // 获取对应的图标组件
    if (!IconComponent) {
      // 如果没有找到对应的图标组件，返回null
      return null;
    }
    return <IconComponent />; // 返回图标组件
  };

  // 渲染右侧图标的方法，逻辑与左侧图标类似
  const renderRightIcon = (): ReactNode => {
    const type = getNodeType();
    const IconComponent = rightIconMap[type];
    if (type === ENodeType.Action) {
      const { supportFlowConcatenation = false } = node.getData().data || {};
      if (!supportFlowConcatenation) {
        return null;
      }
    }
    if (!IconComponent) {
      return null;
    }
    return <IconComponent />;
  };

  useEffect(() => {
    const child = document.getElementById(childId);
    const nodeHeight = child?.offsetHeight || 92;
    node.resize(276, nodeHeight);
  }, [node.getData()]);

  // 渲染添加节点的列表的方法
  const renderAddNodeList = (): ReactNode => {
    const type = getNodeType();
    const { data: nodeData, index } = node.getData();
    const { supportDelay } = nodeData || {};
    const list = addNodeListMap[type].filter(item => {
      // 如果是action节点，根据node.getData().data.supportFlowConcatenation判断是否支持流程拼接
      if (type === ENodeType.Action) {
        return nodeData.supportFlowConcatenation;
      }

      // 是否支持设置延迟节点
      if (item.type === ENodeType.Delay && !supportDelay) {
        return false;
      }
      return true;
    }); // 根据节点类型从addNodeListMap中获取列表

    let disableAction = false;
    let disableCondition = false;
    // 如果是condition节点，需要判断后续是否已经有了action节点，如果有，则添加操作置灰
    if (type === ENodeType.Condition) {
      disableAction = !!flowData.chainRules?.[index]?.action?.name;
    }

    // 判断action是否已经串联，不支持串联的action再添加多个
    // if (type === ENodeType.Action) {
    // disableAction = !!flowData.chainRules?.[index]?.seriesActions?.name;
    // }

    if (type === ENodeType.Delay) {
      const rule = flowData.chainRules?.[index];
      if (rule) {
        const shouldDisable = !!(rule.action?.name || rule.conditions.length);
        disableCondition = shouldDisable;
        disableAction = shouldDisable;
      }
    }

    return list
      .map(item => {
        if (
          (item.type === ENodeType.Action && disableAction) ||
          (item.type === ENodeType.Condition && disableCondition)
        ) {
          return {
            ...item,
            disabled: true,
          };
        }
        return item;
      })
      .map(item => {
        return (
          <div
            className={cn('add-node-item', { disabled: item.disabled })}
            onClick={() => {
              if (item.disabled) return;
              // 校验是否有trigger信息
              if (!flowData?.triggerId) return Notify.error('请先选择触发器');

              const { index } = node.getData();
              const { type } = item;
              const drawerData: IDrawerData = {
                type: item.type,
                isEdit: false,
                parentNode: node,
                conditionIdx: null,
                actionIdx: null,
                currentNode: null,
              };
              // 新增条件的情况，一定是flow新增一条链路，所以不用管

              // 新增操作的情况，需要判断parentNode是trigger还是condition，如果是trigger则视为新增链路，是condition则需要记录当前链路的索引
              if (type === ENodeType.Action) {
                drawerData.actionIdx = index;
              }

              // 如果是新增操作，需要判断parentNode是否是condition，这个condition是否已经有action，如果有action则不能继续添加action节点

              showDrawer(drawerData); // 显示抽屉
              // onAddNode?.(type); // 当列表项被点击时，调用onAddNode回调函数
            }}
          >
            {item.disabled ? (
              <Pop
                trigger="hover"
                position="right-center"
                content={
                  item.type === ENodeType.Condition ? (
                    <p>一个条件无法添加多条操作</p>
                  ) : item.type === ENodeType.Action ? (
                    <p>一个操作无法添加多条操作</p>
                  ) : (
                    <p>无法添加</p>
                  )
                }
                // containerSelector="#js-react-container"
                // className="common-node-pop"
              >
                {item.label}
              </Pop>
            ) : (
              item.label
            )}
          </div>
        );
      });
  };

  // 获取节点主题色的方法，根据节点类型从nodeThemeColorMap中获取颜色
  const getThemeColor = (): string => {
    const type = getNodeType();
    return nodeThemeColorMap[type] || '#333'; // 如果type存在，返回对应的颜色，否则返回默认颜色
  };

  // 渲染
  const renderInvalidTips = (): ReactNode => {
    return (
      <div>
        {invalidTipsArr?.map(item => {
          return <p>{item}</p>;
        })}
      </div>
    );
  };

  // 删除节点
  const deleteNode = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    const newFlowData = cloneDeep(flowData);
    const { type, index, parentNode } = node.getData();
    if ([ENodeType.Delay].includes(type) && newFlowData.chainRules) {
      // newFlowData.chainRules[index] = { conditions: [], action: { name: '', variables: [] } };
      newFlowData.chainRules.splice(index, 1);
    } else if (type === ENodeType.Condition && newFlowData.chainRules) {
      // 如果是condition 判断有没有 delay 节点 保留 delay 删除其它
      const rule = newFlowData.chainRules?.[index];
      if (rule && rule.delayComponent) {
        newFlowData.chainRules[index] = {
          ...rule,
          conditions: [],
          action: { name: '', variables: [] },
        };
        delete newFlowData.chainRules[index].seriesActions;
      } else if (rule) {
        newFlowData.chainRules.splice(index, 1);
      }
    } else if (type === ENodeType.Action && newFlowData.chainRules) {
      // 新增逻辑，因为这次支持action串联action  所以要先判断是否是action的最后一个节点
      // 说明当前节点是串联action的最后一个节点
      // 应当删除seriesActions
      // 更新逻辑：不需要判断parentNode的type了，因为无论是action还是串联的action，都需要删除seriesActions
      if (parentNode.data?.type === 'action' || parentNode.getData?.()?.type === 'action') {
        delete newFlowData.chainRules[index].seriesActions;
      } else if (
        !newFlowData.chainRules[index]?.conditions?.every(item => item.expressions.length)
      ) {
        // 直接trigger连action
        newFlowData.chainRules.splice(index, 1);
      } else {
        newFlowData.chainRules[index]
          ? (newFlowData.chainRules[index].action = { name: '', variables: [] })
          : null;
      }
    }
    setFlowData(newFlowData);

    const graphData = convertFormDataToGraphData(newFlowData);
    graph?.fromJSON(graphData);
  };

  const renderCommonNode = () => {
    return (
      <div
        className={cn(
          'custom-node',
          invalidTipsArr && invalidTipsArr?.length ? 'invalid-node' : '',
        )}
        style={renderBgByType()}
        id={childId}
      >
        {/* 左侧图标 */}
        <div className="left-icon">{renderLeftIcon()}</div>

        {/* 右侧图标 */}
        <div className="right-icon">
          {/* Pop组件，用于创建弹出内容 */}
          <Pop
            trigger="hover"
            position="right-center"
            content={renderAddNodeList()}
            containerSelector="#js-react-container"
            className="common-node-pop"
          >
            {/* Pop组件的内容，即右侧图标 */}
            <div style={{ height: '16px' }}>{renderRightIcon()}</div>
          </Pop>
        </div>

        {/* 右上角图标 */}
        <div className="right-top-icons">
          {/* 未通过校验的tips的icon */}
          {/* <div className="invalid-tips">
        <InvalidTipsIcon />
      </div> */}
          {node.getData().type !== ENodeType.Trigger && (
            <div className="delete-icon" onClick={e => deleteNode(e)}>
              <DeleteIcon width={16} height={16} />
            </div>
          )}
        </div>

        {/* 节点名称 */}
        <div className="name" style={{ color: getThemeColor() }}>
          {renderNameByType()}
          {renderNodeIcon()}
        </div>
        {/* 节点的子元素 */}
        {/* @ts-ignore */}
        <div>{children}</div>
      </div>
    );
  };

  // 返回节点的渲染结果
  return invalidTipsArr?.length ? (
    <Pop
      trigger="hover"
      position="bottom-center"
      content={invalidTipsArr?.length ? renderInvalidTips() : null}
      className="invalid-node-pop"
    >
      {renderCommonNode()}
    </Pop>
  ) : (
    renderCommonNode()
  );
};

export default CommonNode; // 导出CommonNode组件
