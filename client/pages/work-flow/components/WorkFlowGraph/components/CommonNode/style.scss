@use 'sass/vars/index.scss' as *;

// 创建一个混合，包含共同的样式
@mixin icon-common {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

// 使用混合来定义左图标和右图标的样式
.custom-node {
  width: 248px;
  // height: 92px;
  box-sizing: border-box;
  // background-color: #fff;
  background-color: transparent;
  margin: 0 16px;
  padding: 20px 24px;
  position: relative;

  border-radius: 8px;

  cursor: pointer;

  // background-image: url(https://img01.yzcdn.cn/upload_files/2024/03/07/FhKYAxacjOJjHkSzI8oI7na9-wAa.png);
  background-position: center center;
  background-size: 100% auto;

  .left-icon {
    @include icon-common;
    left: -12px;
  }

  .right-top-icons {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(7px, -8px);

    display: flex;
    align-items: center;

    .invalid-tips {
      display: none;
    }

    .delete-icon {
      width: 16px;
      height: 16px;
      // border: 4px solid #f7f7f7;
      border-radius: 50%;

      margin-left: 8px;
      display: none;
    }
  }

  .right-icon {
    @include icon-common;
    right: -10px;
    cursor: pointer;
  }

  .name {
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px; /* 183.333% */

    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &:hover {
    .delete-icon {
      display: block;
    }
  }
}

.invalid-node {
  border-top: 2px solid #d42f15;
  border-bottom: 2px solid #d42f15;
  // background-image: url(https://img01.yzcdn.cn/upload_files/2024/03/07/Fhxg-FF_HHDBINZy4eMIRj_XYxFM.png);

  .invalid-tips {
    display: block !important;
    // position: absolute;

    // top: 0;
    // right: 0;

    width: 16px;
    height: 16px;

    // background-image: url(https://img01.yzcdn.cn/upload_files/2024/02/29/Fscl-XybWml6AmWaeLUUkPjj41rw.png);
    // background-size: 100% 100%;
    // border: 4px solid #f7f7f7;
    border-radius: 50%;
  }
}
.common-node-pop {
  border-radius: 8px !important;
  z-index: 999 !important;
  .zent-pop-v2-inner {
    padding: 8px !important;
    border-radius: 8px !important;
  }
  .add-node-item {
    padding: 8px 16px;
    width: 184px;

    color: #333;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */

    border-radius: 4px;
    cursor: pointer;
    text-align: left;
    &:hover {
      background-color: $gray-color;
    }
  }
  .disabled {
    cursor: not-allowed;
    color: #ccc;
  }
}

// 这一步是为了覆写全局的body上有一个min-width:1000px 的样式，没时间查是哪里设置的了，先这样强制覆盖
foreignObject {
  body {
    min-width: 268px !important;
  }
}

.invalid-node-pop {
  color: #fff !important;

  .zent-pop-v2-inner {
    background-color: #333 !important;
    padding: 4px !important;

    p {
      color: #fff !important;
    }
  }
  .zent-pop-v2-arrow {
    background-color: #333 !important;
  }
}

.x6-node foreignObject > body {
  position: fixed;
}
