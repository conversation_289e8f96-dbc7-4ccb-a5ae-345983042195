.condition-node {
  margin-top: 8px;
  color: #333;
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  width: 100%;

  .condition-group {
    padding: 12px 16px;
    background: #f7f7f7;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;

    .condition-index {
      color: #999;
      font-feature-settings: 'clig' off, 'liga' off;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }

    .condition-value {
      color: #333;
      font-feature-settings: 'clig' off, 'liga' off;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      display: flex;
      align-items: center;
      margin-top: 8px;

      &:first-child {
        margin-top: 0;
      }

      .or {
        color: #999;
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-right: 12px;
      }

      .icon-box {
        margin-right: 8px;
        width: 20px;
        height: 20px;
      }

      .field {
        word-break: break-all;
        //display: flex;
        max-width: 100%;
        flex: 1 1 auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .shrink-span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex-shrink: 1;
        }
      }

      .narrow-field {
        max-width: calc(100% - 24px);
      }
    }

    margin-top: 12px;

    &:first-child {
      margin-top: 8px;
    }
  }
}

.pop-content {
  max-width: 600px;
}
