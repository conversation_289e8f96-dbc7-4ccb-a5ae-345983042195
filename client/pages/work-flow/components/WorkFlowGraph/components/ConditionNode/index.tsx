import { FC, useEffect } from 'react';
import './style.scss';
import CommonNode from '../CommonNode';
import { Graph, Node } from '@antv/x6';
import { GiftsIcon } from 'svg';
import { ICondition, IExpressions, TimeUnitMap } from 'pages/work-flow/constant';
import { useAtomValue } from 'jotai';
import { flowAtom } from 'pages/work-flow/atoms';
import { defaultUnitTime } from 'pages/work-flow/constant';
import withCommonNode from 'pages/work-flow/HOC/withCommonNode';
import classnames from 'classnames';
import { Pop } from 'zent';
import { formatDateToStandard } from '../../../../../../fns/formatDate';

const isDefined = (value: any) => {
  return value !== undefined && value !== null;
};

const ConditionNode: FC<{
  node: Node;
  graph: Graph;
  // setInvalidTipsArr: (any) => any;
  // @ts-ignore
}> = ({ node, graph, setInvalidTipsArr }) => {
  const conditions = node.getData().data as ICondition[];
  // FIXME: 不要删除这句，使用flowAtom使conditionNode可以在节点数据变化时重新渲染。很傻逼 但是时间紧迫我找不到不重新渲染的具体原因，actionNode是正常的，conditionNode就不渲染
  useAtomValue(flowAtom);

  const handleValidCheck = () => {
    // 使用 every 方法检查 conditions 数组
    const isValid = conditions.every(condition => {
      // 检查 condition 是否有 expressions 属性且 expressions 数组不为空
      if (!condition.expressions || !condition.expressions.length) return false;

      // 对每个 expression 进行有效性检查
      return condition.expressions.every(expression => {
        // 检查 expression 是否有 variable 和 operator 属性
        if (!expression.variable || !expression.operator) return false;

        // 如果 expression 没有 openField 属性，检查 fieldValueMaps4Execute 属性是否存在且非空

        if (
          !expression.openField &&
          (!expression.fieldValueMaps4Execute || expression.fieldValueMaps4Execute.length === 0) &&
          (!expression.restrictedFields || !expression.restrictedFields.length) &&
          (!isDefined(expression.timeValue?.time) || !isDefined(expression.timeValue?.timeUnit))
        )
          return false;

        // 如果所有检查都通过，返回 true
        return true;
      });
    });

    // 返回整个 conditions 数组的有效性检查结果
    return isValid;
  };

  useEffect(() => {
    const isNodeValid = handleValidCheck();
    if (!isNodeValid) {
      setInvalidTipsArr(prev => {
        return ['流程配置不完整，请检查'];
      });
    } else {
      setInvalidTipsArr([]);
    }
  }, [conditions]);

  const renderExpression7Text = (operator, openField, timeValue) => {
    // const { description } = operator;
    // const { time, timeUnit } = timeValue;
    // return `${description} ${time || defaultUnitTime.time} ${
    //   TimeUnitMap[timeUnit || defaultUnitTime.opt.key]
    // }；`;

    let field = {
      time: defaultUnitTime.time,
      timeUnit: defaultUnitTime.opt.key,
    };

    try {
      // ? https://jira.qima-inc.com/browse/CSWT-164661
      field = timeValue || JSON.parse(openField);
    } catch (error) {}

    const { time, timeUnit } = field;

    const { description } = operator;
    return `${description} ${time} ${TimeUnitMap[timeUnit]}；`;
  };

  // 渲染正确的操作符 + 值
  const renderFieldValue = (expression: IExpressions) => {
    const {
      operator,
      openField = '',
      restrictedFields,
      fieldValueMaps4Display = '{}',
      variable: { valueSource, description, code } = {},
      timeValue,
    } = expression;

    if (valueSource === 1) {
      if (!operator?.name && !openField) return '';

      return `${operator?.name} ${openField}；`;
    }
    if (valueSource === 2) {
      if (!operator?.name && !restrictedFields?.length) return '';
      return (
        <>
          {operator?.name || ''}&nbsp;
          <span className="shrink-span">{restrictedFields?.[0]?.name}</span>
          {restrictedFields?.length && restrictedFields.length > 1
            ? `等${restrictedFields.length}个`
            : ''}
        </>
      );
    }
    if (valueSource === 3) {
      if (!operator?.name && !JSON.parse(fieldValueMaps4Display)?.name) return '';
      const parsedFieldValueMaps = JSON.parse(fieldValueMaps4Display);

      return (
        <>
          {operator?.name}&nbsp;
          <span className="shrink-span">
            {parsedFieldValueMaps?.[code!] ? parsedFieldValueMaps?.[code!]?.[0]?.name : ''}
          </span>{' '}
          {parsedFieldValueMaps?.[code!]?.length > 1
            ? `等${parsedFieldValueMaps?.[code!].length}个`
            : ''}
        </>
      );
    }
    if (valueSource === 4) {
      if (!operator?.name && !openField) return '';
      return `${operator?.name} ${openField ? formatDateToStandard(openField) : ''}；`;
    }
    if (valueSource === 7) {
      return renderExpression7Text(operator, openField, timeValue);
    }
  };

  const renderCompleteFieldValue = (expression: IExpressions) => {
    const {
      operator,
      openField = '',
      restrictedFields,
      fieldValueMaps4Display = '{}',
      variable: { valueSource, description, code } = {},
      timeValue,
    } = expression;
    if (valueSource === 1) {
      if (!operator?.name && !openField) return '';

      return `${operator?.name} ${openField}；`;
    }
    if (valueSource === 2) {
      if (!operator?.name && !restrictedFields?.length) return '';
      let result = operator?.name + ' ' || '';
      restrictedFields?.forEach((item, index) => {
        if (index === 0) {
          result += item.name;
        } else {
          result += '、' + item.name;
        }
      });
      return result + '；';
    }
    if (valueSource === 3) {
      if (!operator?.name && !JSON.parse(fieldValueMaps4Display)?.name) return '';
      let result = '';
      // result += description + '：';
      const parsedFieldValueMaps = JSON.parse(fieldValueMaps4Display);
      try {
        result += parsedFieldValueMaps?.[code!]
          ? parsedFieldValueMaps?.[code!]?.map(item => item.name).join('、')
          : '';
      } catch (error) {
        result += '';
      }
      return operator?.name + ' ' + result + '；';
    }
    if (valueSource === 4) {
      if (!operator?.name && !openField) return '';
      return `${operator?.name} ${openField ? formatDateToStandard(openField) : ''}；`;
    }
    if (valueSource === 7) {
      return renderExpression7Text(operator, openField, timeValue);
    }
  };

  const renderField = (expression: IExpressions, expressionIndex) => {
    const { variable, operator } = expression;
    return (
      <Pop
        trigger="hover"
        content={
          <div className="pop-content">
            {variable?.name + ' ' + renderCompleteFieldValue(expression)}
          </div>
        }
      >
        <div className={classnames('field', expressionIndex >= 1 ? 'narrow-field' : '')}>
          {variable?.name} {renderFieldValue(expression)}
        </div>
      </Pop>
    );
  };

  return (
    // <CommonNode node={node} graph={graph}>
    <div className="condition-node">
      {/* 条件节点独有的条件渲染 */}
      {conditions
        ?.filter(item => item.expressions.filter(item => item.variable.name).length)
        .map((item, index) => {
          return (
            <div className="condition-group" key={index + '' + new Date().toDateString()}>
              <div className="condition-index">条件组{index + 1}</div>
              {item.expressions
                .filter(item => item.variable.name)
                .map((expression, expressionIndex) => {
                  return (
                    <div
                      className="condition-value"
                      key={expressionIndex + '' + new Date().toDateString()}
                    >
                      {expressionIndex !== 0 && <span className="or">且</span>}
                      {/* <div className="icon-box">
                        <GiftsIcon mainColor={nodeThemeColorMap.condition} />
                      </div> */}
                      {renderField(expression, expressionIndex) || ''}
                    </div>
                  );
                })}
            </div>
          );
        })}
    </div>
    // </CommonNode>
  );
};

export default withCommonNode(ConditionNode);
