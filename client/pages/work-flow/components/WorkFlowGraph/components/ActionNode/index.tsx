import { FC, useEffect } from 'react';
import './style.scss';
import { Graph, Node } from '@antv/x6';
import { EValueSource, IAction } from 'pages/work-flow/constant';
import { Icon, Pop } from 'zent';
import withCommonNode from 'pages/work-flow/HOC/withCommonNode';
import { useCustomDrawer } from '../../../CustomDrawer/useCustomDrawer';
import { useAtomValue } from 'jotai';
import { editingActionAtom } from '../../../../atoms';
import { filteredVariableList, getHiddenCodes } from 'pages/work-flow/fns';

const ActionNode: FC<{
  node: Node;
  graph: Graph;
  // @ts-ignore
}> = ({ node, graph, setInvalidTipsArr }) => {
  const nodeData: IAction = node.getData().data;
  const { showSubDrawer } = useCustomDrawer();
  const editingAction = useAtomValue(editingActionAtom);

  const { name, actionFieldValueMaps4Execute, variables } = nodeData;

  const isValidAction = () => {
    if (!name) return false;
    if (!variables?.length) return true;
    if (!actionFieldValueMaps4Execute) return false;

    const parsedFields = JSON.parse(actionFieldValueMaps4Execute || '{}');

    const hiddenCodes = getHiddenCodes({ fields: parsedFields, variables });

    return variables.every(variable => {
      const { code, isRequired, valueSource } = variable;
      if (!code || !isRequired) return true;

      // 变量联动隐藏时，不校验
      if (hiddenCodes.includes(code)) {
        return true;
      }

      if (valueSource === EValueSource.RADIO) {
        const selectedId = parsedFields[code];
        const selectedVariable = variables.find(v => v.id === selectedId);
        if (!selectedVariable?.code) return false;

        const selectedValue = parsedFields[selectedVariable.code];
        return isValidValue(selectedValue);
      }
      return isValidValue(parsedFields[code]);
    });
  };

  const isValidValue = (value: any): boolean => {
    if (Array.isArray(value)) return value.length > 0;
    return value !== undefined && value !== null && value !== '';
  };

  useEffect(() => {
    const isNodeValid = isValidAction();
    if (!isNodeValid) {
      setInvalidTipsArr(prev => {
        return ['流程配置不完整，请检查'];
      });
    } else {
      setInvalidTipsArr([]);
    }
  }, [nodeData]);

  const renderActionValue = action => {
    const { actionFieldValueMaps4Display = '{}', variables, type } = action;
    const filtered = filteredVariableList(variables);
    return (
      <>
        {filtered?.map(item => {
          const extension = JSON.parse(item?.extension || '{}');
          const { operatorDesc, unitDesc } = extension?.variablePattern?.styleTemplate || {};
          if (item.valueSource === 1) {
            return (
              <>
                <span className="action-shrink-span">
                  {JSON.parse(actionFieldValueMaps4Display || '{}')?.[item.code!]?.name || ''}
                </span>
              </>
            );
          }

          if (type === 'retailGoodsTaskUp' || type === 'retailGoodsTaskDown') {
            return operatorDesc;
          }

          return (
            <>
              &nbsp;{`${operatorDesc || '给'}`}&nbsp;
              <span className="action-shrink-span">
                {JSON.parse(actionFieldValueMaps4Display || '{}')?.[item.code!]
                  ? JSON.parse(actionFieldValueMaps4Display)[item.code][0]?.name
                  : ''}
              </span>
              {JSON.parse(actionFieldValueMaps4Display || '{}')?.[item.code]?.length > 1
                ? `等${JSON.parse(actionFieldValueMaps4Display)[item.code].length}${unitDesc ||
                    '人'}`
                : ''}
            </>
          );
        })}
      </>
    );
  };

  const renderCompleteFieldValue = action => {
    const { actionFieldValueMaps4Display = '{}', variables } = action;

    let result = '';
    // 为了处理表单项展示顺序与拼接顺序不一致所以新增了index排序用
    // variables?.sort((a, b) => {
    //  // 根据extension中的?.variablePattern?.styleTemplate?.index 排序，extension是json字符串
    //  const aIndex = JSON.parse(a.extension || '{}')?.variablePattern?.styleTemplate?.index || 0;
    //  const bIndex = JSON.parse(b.extension || '{}')?.variablePattern?.styleTemplate?.index || 0;
    //  // 从大到小排序
    //  return bIndex - aIndex;
    // });
    variables?.forEach(item => {
      const extension = JSON.parse(item?.extension || '{}');
      const { operatorDesc, index } = extension?.variablePattern?.styleTemplate || {};

      // result += item.name + '：';
      if (item.valueSource === 1) {
        result +=
          ' ' + (JSON.parse(actionFieldValueMaps4Display || '{}')?.[item.code!]?.name || '');
      } else if (item.valueSource === 5) {
      } else {
        result += ` ${operatorDesc || '给'} `;
        result += JSON.parse(actionFieldValueMaps4Display || '{}')?.[item.code!]
          ? JSON.parse(actionFieldValueMaps4Display)
              [item.code].map?.(item => item.name)
              .join('、')
          : '';
      }
    });

    return result;
  };

  // 是不需要渲染完整语句的特殊场景
  const isBlackList = type => {
    const blackTypeList = [
      'autoPurchaseIn',
      'autoStockLossOut',
      'AutoCreateApplyOrder',
      'autoCreateApplyOrder',

      'AutoShelfLifeLossOut',
      'autoShelfLifeLossOut',

      'AutoAddSpotStockNum',
      'autoAddSpotStockNum', // 补现货库存

      'AutoAddPlanStockNum',
      'autoAddPlanStockNum',

      // 自动新建采购订单
      'AutoCreatePurchaseOrder',
      'autoCreatePurchaseOrder',

      // 自动新建采购入库单
      'AutoPurchaseInV2',
      'autoPurchaseInV2',

      // 自动新建采购退货单
      'AutoCreatePurchaseReturnOrder',
      'autoCreatePurchaseReturnOrder',

      'autoStocktaking', // 自动新建日常盘点单
      'salesAndInventoryForecast', // 动销预测

      // 商品上下架
      'retailGoodsTaskUp',
      'retailGoodsTaskDown',

      // 以上是门店场景
      'actionCountOrderWithConditions',

      // 零售统计发货订单
      'retailCountUnshippedOrder',

      // 快递订单支持自动发货项目
      'autoSendGoods',
      'filterPendingOrdersForShipment',

      // 获取自定义看板
      'AutoGetCustomKanban',
      'autoGetCustomKanban'
      
    ];
    return blackTypeList.includes(type);
  };

  const renderAction = () => {
    const { data: action } = node.getData();

    // node.resize(276, 192)

    let actionName = action.name;

    // 自动加小费
    if (action.type === 'increaseTips') {
      actionName += '（元）';
    }

    const renderPopContent = () => {
      if (isBlackList(action.type)) {
        return actionName;
      }
      return actionName + ' ' + renderCompleteFieldValue(action);
    };

    return (
      <Pop trigger="hover" content={<div className="pop-content">{renderPopContent()}</div>}>
        {/* 加的without-sub是因为当选了没有一个二级选项的时候要唤起一级drawer */}
        {/* 加了className之后不会被外层监听排除（on('node:click')） */}
        <p
          className={`action-field${action?.variables?.length ? '' : ' without-sub'}`}
          onClick={e => {
            const { index } = node.getData();
            // 判断是否是可选的二级，是的话弹出二级的drawer
            action?.variables?.length &&
              showSubDrawer({
                data: action,
                selected: { ...editingAction, ...action },
                parentNode: null,
                isEdit: true,
                actionIdx: index,
                conditionIdx: index,
                currentNode: node,
                // groupCode: action.groupCode,
              });
          }}
        >
          {actionName}
          {!isBlackList(action.type) && renderActionValue(action)}
        </p>
      </Pop>
    );
  };

  const renderTips = () => {
    const { data: action } = node.getData();
    return (
      action.description && (
        <Pop
          trigger="hover"
          content={
            <div
              style={{ whiteSpace: 'pre-wrap' }}
              dangerouslySetInnerHTML={{ __html: action.description }}
            ></div>
          }
          style={{ maxWidth: 300 }}
        >
          <Icon type="info-circle-o" />
        </Pop>
      )
    );
  };

  return (
    <div className="trigger-node">
      {/*  <div className="icon-box">
        <GiftsIcon mainColor={nodeThemeColorMap.action} />
      </div> */}
      {renderAction()}
      {renderTips()}
    </div>
  );
};

export default withCommonNode(ActionNode);
