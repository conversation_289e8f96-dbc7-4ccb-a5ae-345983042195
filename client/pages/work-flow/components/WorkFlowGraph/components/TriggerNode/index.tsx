import { FC, useEffect } from 'react';
import './style.scss';
import CommonNode from '../CommonNode';
import { Graph, Node } from '@antv/x6';
import { GoodsIcon } from 'svg';
import { TriggerType, nodeThemeColorMap } from 'pages/work-flow/constant';
import { useAtom } from 'jotai';
import { flowAtom } from 'pages/work-flow/atoms/flow';
import withCommonNode from 'pages/work-flow/HOC/withCommonNode';
import { Icon, Pop } from 'zent';
import { triggerSubDrawerVisible } from 'pages/work-flow/atoms';

const TriggerNode: FC<{
  node: Node;
  graph: Graph;
  // setInvalidTipsArr: (any) => any;
  // @ts-ignore
}> = ({ node, graph, setInvalidTipsArr }) => {
  const [flow, setFlow] = useAtom(flowAtom);
  const [showSubDrawer, setShowSubDrawer] = useAtom(triggerSubDrawerVisible);

  const handleValidCheck = () => {
    const { type, subjectDetail } = flow.triggerDefine || {};
    if (!flow.triggerDefine?.name) {
      return false;
    }
    if (type === TriggerType.SCHEDULED && !subjectDetail) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    const isNodeValid = handleValidCheck();

    if (!isNodeValid) {
      setInvalidTipsArr(prev => {
        return ['流程配置不完整，请检查'];
      });
    } else {
      setInvalidTipsArr([]);
    }
  }, [flow]);

  const renderTips = () => {
    return (
      flow?.triggerDefine?.description && (
        <Pop
          trigger="hover"
          content={flow?.triggerDefine?.description || ''}
          style={{ maxWidth: 300 }}
        >
          <Icon type="info-circle-o" />
        </Pop>
      )
    );
  };

  return (
    // <CommonNode node={node} graph={graph}>
    <div className="trigger-node">
      {flow.triggerDefine?.name ? (
        <div
          className="trigger-field"
          onClick={e => {
            e.stopPropagation();
            console.log(node.getData());
            const { data } = node.getData();
            const { code } = data;
            if (code === 'scheduled') {
              // 打开定时任务子抽屉
              // FIXME: 这里太偷懒了，直接用把子抽屉状态放在全局，而且trigger的子抽屉仅适用于定时任务，回头改一下
              setShowSubDrawer(true);
            }
          }}
        >
          {/* <GoodsIcon mainColor={nodeThemeColorMap.trigger} /> */}
          <span className="trigger-shrink-span">{flow.triggerDefine?.name}&nbsp;</span>
          {renderTips()}
        </div>
      ) : (
        <span className="no-data">点击选择业务场景</span>
      )}
    </div>
    // </CommonNode>
  );
};

export default withCommonNode(TriggerNode);
