import { FC } from 'react';
import { Graph, Node } from '@antv/x6';
import { useAtomValue, useAtom } from 'jotai';
import { flowAtom } from 'pages/work-flow/atoms';
import { TimeUnitMap, BeforeOrAfterMap } from 'pages/work-flow/constant';
import withCommonNode from 'pages/work-flow/HOC/withCommonNode';

const getVariableIdText = (flowData, variableId) => {
  const { triggerDefine } = flowData;
  const { delayVariables } = triggerDefine;

  return delayVariables.find(item => item.id === variableId)?.name || variableId;
};

const DelayNode: FC<{
  node: Node;
  graph: Graph;
  // setInvalidTipsArr: (any) => any;
  // @ts-ignore
}> = ({ node }) => {
  const [flowData] = useAtom(flowAtom);
  const { data } = node.getData();
  const {
    variableId,
    variableIdText,
    beforeOrAfter,
    beforeOrAfterText,
    delayTime,
    timeUnit,
  } = data;

  // FIXME: 不要删除这句，使用flowAtom使conditionNode可以在节点数据变化时重新渲染。很傻逼 但是时间紧迫我找不到不重新渲染的具体原因，actionNode是正常的，conditionNode就不渲染
  useAtomValue(flowAtom);

  return (
    <div className="condition-node">
      {variableIdText || getVariableIdText(flowData, variableId)}
      {beforeOrAfterText || BeforeOrAfterMap[beforeOrAfter]} {delayTime}
      {TimeUnitMap[timeUnit]}
    </div>
  );
};

export default withCommonNode(DelayNode);
