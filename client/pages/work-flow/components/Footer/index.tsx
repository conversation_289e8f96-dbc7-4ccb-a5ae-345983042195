import React, { useState } from 'react';
import { But<PERSON>, Notify, Sweetalert } from 'zent';
import './style.scss';
import { useAtom, useAtomValue } from 'jotai';
import { flowAtom, graphAtom, isFlowEditingAtom } from 'pages/work-flow/atoms';
import { getGraphData } from 'pages/work-flow/fns/graph';
import { createFlow, editFlow, saveDraft } from 'api/workflow';
import jumpTo from 'fns/jumpTo';
import { cloneDeep, isEmpty } from 'lodash';
import { Locker } from '../../../../fns/Locker';
import { AUTO_SAVE_DRAFT_LOCK } from '../../constant/lock';
import { formatCondition } from '../../utils/flow';
import { useNavigate } from 'react-router';

const locker = Locker.getInstance();
const lockInstance = locker.registe(AUTO_SAVE_DRAFT_LOCK);

const Footer = () => {
  const [flowData, setFlowData] = useAtom(flowAtom);
  const graph = useAtomValue(graphAtom);
  const [isSaving, setIsSaving] = useState(false);
  const navigate = useNavigate();

  const isFlowEditing = useAtomValue(isFlowEditingAtom);

  const getParams = (isDraft = true) => {
    if (!graph) return Notify.error('请先绘制流程图');
    const graphData = getGraphData(graph);
    console.log('flowData is ', flowData);
    const filteredFlowData = cloneDeep(flowData);
    if (!isDraft) {
      filteredFlowData.chainRules = filteredFlowData.chainRules?.filter(
        rule => !!(rule.action.id || rule.conditions.length || rule.delayComponent),
      );
    }
    filteredFlowData.chainRules &&
      (filteredFlowData.chainRules = formatCondition(filteredFlowData.chainRules));
    return {
      ...filteredFlowData,
      canvasComponents: graphData,
    };
  };

  const jumpToList = () => {
    setTimeout(() => {
      jumpTo({
        url: '/v4/jiawo/work-flow/list',
        needNewWindow: false,
      });
    }, 1000);
  };

  const saveAndUse = async () => {
    if (isSaving) return;
    const doEditFlow = () => {
      editFlow(getParams(false))
        .then(res => {
          Notify.success('编辑成功');
          jumpToList();
        })
        .catch(err => {
          Notify.error(err.msg || err || '保存失败');
        })
        .finally(() => {
          setIsSaving(false);
          lockInstance.unLock();
        });
    };

    setIsSaving(true);
    if (lockInstance.isLocking()) {
      // 正在自动保存草稿 loading 1s后保存（其实更好的做法是等自动保存草稿后抛出事件，这里监听事件，但是没必要）
      await Locker.sleep(1000);
      doEditFlow();
      return;
    }
    lockInstance.lock();

    const params = getParams(false);
    if (flowData.id) {
      doEditFlow();
      return;
    }
    createFlow(params)
      .then(res => {
        Notify.success('保存并启用成功');
        jumpToList();
      })
      .catch(err => {
        Notify.error(err.msg || err || '保存失败');
      })
      .finally(() => {
        setIsSaving(false);
        lockInstance.unLock();
      });
  };

  const saveToDraft = () => {
    if (lockInstance.isLocking()) {
      return;
    }
    if (isSaving) return;
    setIsSaving(true);

    const params = getParams(false);
    saveDraft(params)
      .then(res => {
        Notify.success('保存草稿成功');

        if (!flowData.id) {
          setFlowData({
            ...flowData,
            id: res,
          });
        }
        jumpToList();
      })
      .catch(err => {
        Notify.error(err.msg || err || '保存失败');
      })
      .finally(() => setIsSaving(false));
  };

  function checkNeedShowAlert() {
    const chainRules = flowData?.chainRules || [];
    const needShowAlertRule = chainRules.find(rule => rule.action?.isEnableNeedSecondaryConfirm);
    return {
      needShowAlert: !isEmpty(needShowAlertRule),
      description: needShowAlertRule?.action?.description || '',
    };
  }

  function handleSaveAndUse() {
    const { needShowAlert, description } = checkNeedShowAlert();
    if (needShowAlert) {
      Sweetalert.confirm({
        content: <p style={{ maxWidth: '560px' }}>{description}</p>,
        onConfirm: saveAndUse,
      });
    } else {
      saveAndUse();
    }
  }

  return (
    <div className="graph-footer">
      {isFlowEditing ? (
        <>
          <Button type="primary" loading={isSaving} onClick={saveAndUse}>
            保存
          </Button>
          <Button
            onClick={() => {
              navigate('/list');
            }}
          >
            取消
          </Button>
        </>
      ) : (
        <>
          <Button type="primary" loading={isSaving} onClick={handleSaveAndUse}>
            保存并启用
          </Button>
          <Button loading={isSaving} onClick={saveToDraft}>
            保存为草稿
          </Button>
        </>
      )}
    </div>
  );
};

export default Footer;
