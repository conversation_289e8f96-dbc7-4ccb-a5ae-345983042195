.react-basic-app {
  display: flex;
  padding: 0;
  font-family: sans-serif;

  .app-content {
    flex: 1;
    height: 240px;
    margin-right: 8px;
    margin-left: 8px;
    box-shadow: 0 0 10px 1px #e9e9e9;
  }

  .react-node {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    border: 1px solid #8f8f8f;
    border-radius: 6px;
  }

  // test
  body > div {
    background-color: #eee;
    width: 100px!important;
  }
}
