import React, { FC, useEffect, useState } from 'react';
import './style.scss';
import { flowAtom } from 'pages/work-flow/atoms';
import { useAtom } from 'jotai';
import { Input, Notify } from 'zent';
import AutoSave from '../AutoSave';

const WorkFlowName: FC = () => {
  const [flowName, setFlowName] = useState('');
  const [flowData, setFlowData] = useAtom(flowAtom);

  useEffect(() => {
    if (flowData.name !== flowName) {
      setFlowName(flowData.name || '');
    }
  }, [flowData.name]);

  const onFlowNameChange = val => {
    // setFlowData(prev => ({ ...prev, name: val }));
    setFlowName(val);
  };

  // 失焦保存
  const onBlurSaveFlowName = () => {
    setFlowData(prev => ({ ...prev, name: flowName }));
    // return Notify.success('修改流程名称成功');
  };

  // 从url参数中获取flowName
  const getFlowNameFromUrl = () => {
    const url = new URL(window.location.href);
    return url.searchParams.get('flowName');
  };

  const getFlowIdFromUrl = () => {
    const url = new URL(window.location.href);
    return url.searchParams.get('flowId');
  };

  useEffect(() => {
    const name = getFlowNameFromUrl() || '';
    const flowId = getFlowIdFromUrl() || '';
    if (flowId) return;
    setFlowName(name);
    setFlowData(prev => ({ ...prev, name }));
  }, []);

  return (
    <div className="work-flow-name">
      <Input
        className="name-input"
        value={flowName}
        onChange={e => onFlowNameChange(e.target.value)}
        onBlur={onBlurSaveFlowName}
      />
      <AutoSave />
    </div>
  );
};

export default WorkFlowName;
