import { useAtom, useAtomValue } from 'jotai';
import {
  editingNode<PERSON>tom,
  editingTrigger<PERSON>tom,
  flowAtom,
  graphAtom,
  isFlowEditingAtom,
  triggerSubDrawerVisible,
} from 'pages/work-flow/atoms';
import {
  ENodeType,
  IFlow,
  ScheduledTaskField,
  TRIGGER_TYPE_TO_SUBJECT_MAP,
  TriggerType,
  typeNameMap,
  widthByType,
} from 'pages/work-flow/constant';
import react, { useCallback, useEffect, useState } from 'react';
import { Button, Drawer, Notify } from 'zent';
import ScheduleTaskConfig, {
  defaultWeekValue,
} from '../CustomDrawer/components/TriggerContent/components/ScheduledTaskConfig';
import { useCustomDrawer } from '../CustomDrawer/useCustomDrawer';
import { convertFormDataToGraphData } from 'pages/work-flow/fns/formToGraph';

const TriggerSubDrawer = () => {
  const [selectedTrigger, setSelectedTrigger] = useAtom(editingTriggerAtom);
  const [showSubDrawer, setShowSubDrawer] = useAtom(triggerSubDrawerVisible);
  const [subjectDetail, setSubjectDetail] = useState<ScheduledTaskField>({
    repeatRule: {
      startTime: '',
      cycleRule: '',
      endTime: '',
      endType: 1,
      weekDays: defaultWeekValue,
    },
  });
  const isFlowEditing = useAtomValue(isFlowEditingAtom);
  const [flowData, setFlowData] = useAtom(flowAtom);
  const editingNode = useAtomValue(editingNodeAtom);
  const graph = useAtomValue(graphAtom);

  const { getDrawerData, hideDrawer } = useCustomDrawer();

  useEffect(() => {
    if (flowData.triggerDefine && !selectedTrigger?.id) {
      setSelectedTrigger(flowData.triggerDefine);
    }
  }, [showSubDrawer]);

  const onConfirm = (trigger?) => {
    const editingTrigger = trigger || selectedTrigger;
    if (isFlowEditing && editingTrigger?.type !== TriggerType.SCHEDULED)
      return Notify.error('已启用的工作流不能修改业务场景');

    // 将 editingTriggerAtom 给 flowAtom
    const newFlow: IFlow = {
      ...flowData,
      triggerId: editingTrigger?.id,
      scenes: editingTrigger?.name,
      triggerDefine: editingTrigger || undefined,
      // 触发器也有二级选择，判断上次的跟这次选的是不是同一个触发器，不是的话清空，反之保留
      chainRules: flowData.triggerId !== editingTrigger?.id ? [] : flowData.chainRules,
    };
    setFlowData(newFlow);
    editingNode?.setData({ data: { triggerDefine: editingTrigger } }, { overwrite: false });
    const graphData = convertFormDataToGraphData({ ...newFlow });
    graph?.fromJSON(graphData);
  };

  const onSubmitSubConfig = () => {
    const { startTime, endTime, cycleRule, endType, weekDays } = subjectDetail.repeatRule;
    if (!startTime) {
      Notify.error('请选择开始时间');
      return;
    }
    if (!cycleRule) {
      Notify.error('请选择循环规律');
      return;
    }
    if (endType === 1 && !endTime) {
      Notify.error('请选择结束时间');
      return;
    }
    if (new Date(startTime) > new Date(endTime)) {
      Notify.error('结束时间不能早于开始时间');
      return;
    }

    // 如果是周循环，需要判断是否选择了周几
    if (cycleRule === 'week' && !weekDays?.length) {
      Notify.error('请选择星期几');
      return;
    }
    // 如果不是周循环，清空周几
    if (cycleRule !== 'week') {
      subjectDetail.repeatRule.weekDays = [];
    }
    const editedTrigger = {
      ...selectedTrigger,
      subject: TRIGGER_TYPE_TO_SUBJECT_MAP[selectedTrigger?.type || ''],
      subjectDetail: JSON.stringify(subjectDetail),
    };
    setShowSubDrawer(false);
    onConfirm(editedTrigger);
    // @ts-ignore
    setSelectedTrigger(editedTrigger);
    hideDrawer();
  };
  const parseSubjectDetail = (detail: string | undefined): ScheduledTaskField => {
    let defaultValue = {
      repeatRule: {
        startTime: '',
        cycleRule: '',
        endTime: '',
        endType: 1,
        weekDays: defaultWeekValue,
      },
    } as ScheduledTaskField;

    if (detail) {
      try {
        defaultValue = JSON.parse(detail);
        if (defaultValue.repeatRule.weekDays?.length === 0) {
          defaultValue.repeatRule.weekDays = defaultWeekValue;
        }
      } catch (error) {
        console.error('Failed to parse subjectDetail:', error);
      }
    }

    return defaultValue;
  };

  const renderSubConfig = useCallback(() => {
    const parsedSubjectDetail = parseSubjectDetail(flowData?.triggerDefine?.subjectDetail);
    switch (selectedTrigger?.type) {
      case TriggerType.SCHEDULED:
        return (
          <ScheduleTaskConfig
            value={parsedSubjectDetail}
            onChange={value => {
              setSubjectDetail(value);
            }}
          />
        );
      default:
    }
  }, [selectedTrigger, flowData]);

  return (
    <Drawer
      title={typeNameMap[ENodeType.Trigger] || ''}
      visible={showSubDrawer}
      placement="right"
      width={widthByType[ENodeType.Trigger] || 520}
      onClose={() => setShowSubDrawer(false)}
      footer={
        <div style={{ textAlign: 'center' }}>
          <Button type="primary" onClick={onSubmitSubConfig}>
            确定
          </Button>
          <Button onClick={() => setShowSubDrawer(false)}>取消</Button>
        </div>
      }
    >
      {renderSubConfig()}
    </Drawer>
  );
};

export default TriggerSubDrawer;
