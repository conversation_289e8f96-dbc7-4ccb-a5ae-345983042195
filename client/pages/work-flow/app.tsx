import { hot } from 'react-hot-loader/root';
import RouterRegister from '../../components/RouterRegister';
import { routes } from './routes';
import { useEffect } from 'react';
import { renderNav } from './render-nav';

const App = () => {
  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    const finalPathName = pathArr[pathArr.length - 1];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  return (
    <div>
      {/* 用于创造一个干净环境的画布测试功能，验证问题 */}
      {/* <TestGraph /> */}
      <RouterRegister routes={routes}></RouterRegister>
    </div>
  );
};

export default hot(App);
