import { atom } from 'jotai';
import { ENodeType, IAction, IActionItem, ICondition, ITrigger } from '../constant';
import { Node } from '@antv/x6';

export const drawerVisibleAtom = atom<boolean>(false);
export const drawerTypeAtom = atom<ENodeType>(ENodeType.Trigger);

// 如果是从已存在的节点上点击的，需要存储当前点击的节点
export const drawerConditionIdxAtom = atom<number | null>(null);
// 第一种情况：从已存在的action节点上点击进去，存储当前节点在整个flow链路的索引。
// 第二种情况：从已有的condition新增的节点，action信息需要保存在已存在的链路中，也需要这个索引
// 不存在draerActionIdxAtom的情况下，表示是从trigger节点直接新增的action，在chainrules中直接新增
// export const drawerActionIdxAtom = atom<number | null>(null);

// 正在编辑状态下的trigger 用于存放正在编辑状态中的trigger
export const editingTriggerAtom = atom<ITrigger | null>(null);
// 正在编辑状态下的condition 用于存放正在编辑状态中的condition
export const editingConditionAtom = atom<ICondition[] | null>(null);
// 正在编辑状态下的action 用于存放正在编辑状态中的action
export const editingActionAtom = atom<IAction | null>(null);

// 正在编辑中的node
export const editingNodeAtom = atom<Node | null>(null);

export const isEditingAtom = atom<boolean>(false);

// 从已经选择过的节点点击详情，直接打开二级抽屉(比如选择了一个操作人是谁，直接点击这一行需要打开
// 联系人选择页，而不是再选一次action)
export const subDrawerVisibleAtom = atom<boolean>(false);
// 二级抽屉data
export const subDrawerDataAtom = atom<Nullable<IActionItem>>(null);
// 二级抽屉selectedData
export const subDrawerSelectedAtom = atom<Nullable<IActionItem>>(null);

// 二级抽屉的文本域校验错误信息
export const textAreaInvalidTextAtom = atom<string>('');

// trigger子抽屉是否展示
export const triggerSubDrawerVisible = atom<boolean>(false);

export const isSubDrawerLoading = atom<boolean>(false);
