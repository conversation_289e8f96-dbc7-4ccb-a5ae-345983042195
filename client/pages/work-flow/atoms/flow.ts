import { atom } from 'jotai';
import { FlowStatusEnum, IFlow, Subject, TriggerType } from '../constant';

export const flowAtom = atom<IFlow>({
  name: '',
  scenes: '',
  triggerId: 0,
  triggerDefine: {
    code: '',
    description: '',
    id: 0,
    name: '',
    obtainPermissionWay: '',
    obtainPermissionNotice: '',
    unusableReason: '',
    canUse: true,
    subject: Subject.Empty,
    subjectDetail: '',
    type: TriggerType.EVENT,
  },
  status: FlowStatusEnum.DISABLE,
  chainRules: [],
});

export const isFlowEditingAtom = atom<boolean>(false);
