.dashboard-container {
  display: flex;
  flex-direction: column;
  margin-top: 40px;
  padding: 0 64px;

  .overview {
    position: relative;

    .text {
      font-size: 24px;
      font-weight: 500;
      line-height: 40px;

      .underline {
        text-decoration: underline;
      }
    }

    .nav {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
      color: #333333;
      display: flex;
      flex-direction: row;

      .nav-item {
        cursor: pointer;
      }
    }
  }

  .dashboard {
    margin-top: 40px;
    background: #ffffff;
    display: flex;
    flex-direction: row;

    .dashboard-item {
      padding: 32px 16px 32px 32px;
      flex: 1;

      .dashboard-icon {
        height: 32px;
      }

      .dashboard-info {
        margin-top: 12px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;

        .dashboard-data {
          margin: 0 12px;

          .dashboard-num {
            font-family: Avenir;
            font-size: 24px;
            font-weight: 500;
            line-height: 24px;
            position: relative;
            top: 1px;
          }
        }
      }
    }
  }

  .agent-block {
    margin-top: 48px;

    .agent-title {
      font-size: 24px;
      font-weight: 500;
      line-height: 40px;
    }
    
    .agent-description {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #999;
      margin-top: 8px;
    }

    .agent-list {
      margin-top: 24px;
      // margin-bottom: 48px;
      
      .agent-row {
        margin-bottom: 20px;
        display: flex;
        gap: 20px;
        justify-content: flex-start;
        
        &:last-child {
          margin-bottom: 0;
        }
      }

      .agent-item {
        height: 116px;
        box-sizing: border-box;
        border-radius: 8px;
        padding: 20px;
        background-color: #ffffff;
        flex-basis: calc(100% / 4 - 20px);
        display: flex;
        gap: 12px;

        // margin-left: 20px;
        &:hover {
          cursor: pointer;
        }
        
        .agent-icon {
          width: 44px;
          height: 44px;
          border-radius: 4px;
          overflow: hidden;
          flex-shrink: 0;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .agent-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .agent-item-title {
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          height: 24px;
          display: flex;
          flex-direction: row;

          .name {
            flex: 1;
          }

          .status {
            font-size: 12px;
            font-weight: 500;
            line-height: 24px;

            .status-name {
              vertical-align: middle;
            }

            &.running {
              color: #45a110;
            }

            &.pending {
              color: #ccc;
            }
            
            &.betaTesting {
              color: #ccc;
            }

            &.preparing {
              color: #155bd4;
              display: flex;
              align-items: center;
              opacity: 1;
              gap: 4px;

              .loading-icon {
                width: 16px;
                height: 16px;
                animation: spin 1s linear infinite;
              }

              @keyframes spin {
                from {
                  transform: rotate(0deg);
                }
                to {
                  transform: rotate(360deg);
                }
              }
            }
          }
        }

        .agent-item-desc {
          margin-top: 4px;
          font-size: 14px;
          font-weight: 400;
          line-height: 24px;
          opacity: 0.3;
        }
      }

      .agent-item-preparing {
        background-color: #f1f1f1;

        .agent-item-title {
          color: #999;
        }
        .agent-item-desc {
          color: #aaa;
        }
        .status {
          color: #333333;
          opacity: 0.3;
        }

        &:hover {
          cursor: not-allowed;
        }
      }
      
      .sub-business-type-block {
        margin-bottom: 20px;
        
        &:last-child .agent-row:last-child {
          margin-bottom: 0;
        }
        
        .sub-business-type-title {
          font-size: 18px;
          font-weight: 500;
          line-height: 28px;
          margin-bottom: 16px;
          color: #333;
          position: relative;
          padding-left: 12px;
          
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #155bd4;
            border-radius: 2px;
          }
        }
      }
    }

    .no-more {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      flex: 1;
      text-align: center;
      opacity: 0.4;
    }
  }
}

:global {
  .app-inner {
    background-color: transparent !important;
    padding: 0 !important;
  }

  .zent-badge-dot {
    vertical-align: middle !important;
  }
}
