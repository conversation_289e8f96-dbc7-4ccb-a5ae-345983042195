import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { hot } from 'react-hot-loader/root';
import styles from './index.m.scss';
import Divider from './components/Divider/Divider';
import jumpTo from 'fns/jumpTo';
import Monitor from 'svg/Monitor';
import Text from 'svg/Text';
import Image from 'svg/Image';
import { getAgentData, getMyAgentList, checkAndInitAgent } from './api';
import { Badge, BlockLoading, ClampLines, Notify, LayoutRow as Row, LayoutCol as Col } from 'zent';
import cn from 'classnames';
import { splitArrayIntoChunks } from 'pages/collect/app';
import { navigateTo } from 'fns/link';
import { renderNav } from './render-nav';
import { checkAbilityValid } from '@youzan/utils-shop';

interface IItemProps {
  icon: ReactNode;
  prefix: string;
  num: number;
  unit: string;
  suffix: string;
}

// 定义智能体接口
interface IAgentInstanceResponse {
  /** 智能体唯一编码  如果是以有赞应用市场插件形式上架的智能体，code为 固定前缀 + 应用市场appId */
  code?: string;
  /** 智能体图标 */
  icon?: string;
  /** 智能体描述 */
  description?: string;
  /** 智能体更新时间 */
  updateTime?: string;
  /** 智能体类型  <p>  取值：  taskFlow: taskFlow类智能体  aigc_text：文案创作  aigc_image：图片生成  business_report：经营报告推送  out_link：链接形式接入的智能体 */
  type?: string;
  /** 智能体跳转链接，type=out_link时有效 */
  url?: string;
  /** 智能体目标（副标题） */
  objective?: string;
  /** 子业务分类，可能为空  取值：  xiaohongshu_hosted: 小红书托管  wechat_hosted: 微信托管 */
  subBusinessType?: string;
  /** 智能体创建时间 */
  createTime?: string;
  /** 智能体名称 */
  name?: string;
  /** 智能体擅长领域 */
  competenceFields?: string[];
  /** 智能体实例id (我的智能体id) */
  id?: number;
  /** 业务分类  取值：  hosted: 托管类  agent: 智能体类  third_party: 三方应用类 */
  businessType?: string;
  /** 智能体运行状态  取值：  -1: 无状态（不做展示）  0: 未运行  1: 运行中  2: 准备中  3: 已下线 */
  status?: number;
  /** 智能体序列号 */
  sequence?: number;
}

// 业务类型配置
const BUSINESS_TYPE_CONFIG = {
  hosted: {
    title: '智能托管',
    description: '全流程自动托管，你只需要接单发货就够啦！',
  },
  agent: {
    title: '智能体',
    description: 'AI智能矩阵，定制经营建议，提升经营效率，助力业绩增长。',
  },
  third_party: {
    title: '第三方应用',
    description: '更多智能应用，满足个性化经营需求。',
  },
};

// 子业务类型映射
const SUB_BUSINESS_TYPE_MAP = {
  xiaohongshu_hosted: '小红书托管',
  wechat_hosted: '微信托管',
};

const DashboardItem = (props: IItemProps) => {
  const { icon, prefix, num, unit, suffix } = props;
  return (
    <div className={styles.dashboardItem}>
      <div className={styles.dashboardIcon}>{icon}</div>
      <div className={styles.dashboardInfo}>
        {prefix}
        <span className={styles.dashboardData}>
          <span className={styles.dashboardNum}>{num}</span> {unit}
        </span>
        {suffix}
      </div>
    </div>
  );
};

const App = () => {
  const [loading, setLoading] = useState(false);
  const [workDay, setWorkDay] = useState(0);
  const [profit, setProfit] = useState(0);
  const [workload, setWorkLoad] = useState(0);
  const [taskCount, setTaskCount] = useState(0);
  const [textCount, setTextCount] = useState(0);
  const [imageCount, setImageCount] = useState(0);
  const [agentListByBusinessType, setAgentListByBusinessType] = useState<
    Record<
      string,
      { agents: IAgentInstanceResponse[]; subTypes: Record<string, IAgentInstanceResponse[]> }
    >
  >({});

  const goCollect = useCallback(() => {
    jumpTo({ url: '/v4/jiawo/collect/index' });
  }, []);
  const goFlow = useCallback(() => {
    jumpTo({ url: '/v4/jiawo/flow/index' });
  }, []);
  const goTaskFlow = useCallback(id => {
    navigateTo(`/v4/jiawo/task-flow/${id}`);
    // jumpTo({ url: `/v4/jiawo/task-flow/${id}`, needNewWindow: false });
  }, []);
  const goText = useCallback(id => {
    navigateTo(`/v4/jiawo/aigc/text/${id}`);
    // jumpTo({ url: `/v4/jiawo/aigc/text/${id}`, needNewWindow: false });
  }, []);
  const goPicture = useCallback(id => {
    navigateTo(`/v4/jiawo/aigc/image/${id}`);
    // jumpTo({ url: `/v4/jiawo/aigc/image/${id}`, needNewWindow: false });
  }, []);

  const goOutLink = url => {
    navigateTo(url);
  };

  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    const finalPathName = pathArr[pathArr.length - 1];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  useEffect(() => {
    setLoading(true);

    // 异步调用checkAndInitAgent，不阻塞其他接口
    checkAndInitAgent({})
      .then(res => {
        // 如果needRefresh为true，刷新页面
        if (res?.needRefresh) {
          window.location.reload();
        }
      })
      .catch(error => {
        console.error('checkAndInitAgent error:', error);
      });

    // 并行执行其他接口，不等待checkAndInitAgent完成
    Promise.all([
      getAgentData().then(res => {
        const dimension = res.dataItems.reduce((acc, current) => {
          return {
            ...acc,
            [current.dimension]: current.value,
          };
        }, {});
        setWorkDay(res.workDays || 0);
        setProfit(dimension.profit || 0);
        setWorkLoad(dimension.workload || 0);
        setTaskCount(dimension.taskCount || 0);
        setTextCount(dimension.textCount || 0);
        setImageCount(dimension.imageCount || 0);
      }),
      getMyAgentList().then(res => {
        let newRes = res;

        if (
          // @ts-ignore
          !checkAbilityValid('image_ad_rotation_mode_ability') &&
          // @ts-ignore
          !checkAbilityValid('xhs_independent_ability')
        ) {
          const XHS_SHOWCASE_AGENT_ID = '小红书智能晒图打卡'; // 小红书智能晒图打卡
          const XHS_CONTENT_ASSISTANT_ID = '小红书内容助手'; // 小红书内容助手
          newRes = newRes.filter(item => {
            return ![XHS_SHOWCASE_AGENT_ID, XHS_CONTENT_ASSISTANT_ID].includes(item.name);
          });
        }

        // 按业务类型和子业务类型分组
        const agentsByBusinessType: Record<
          string,
          {
            agents: IAgentInstanceResponse[];
            subTypes: Record<string, IAgentInstanceResponse[]>;
          }
        > = {};

        newRes.forEach((agent: IAgentInstanceResponse) => {
          if (agent.status === 3) return; // 跳过已下线的智能体

          const businessType = agent.businessType || 'agent'; // 默认为智能体类型

          if (!agentsByBusinessType[businessType]) {
            agentsByBusinessType[businessType] = {
              agents: [],
              subTypes: {},
            };
          }

          // 处理子业务类型
          if (agent.subBusinessType) {
            if (!agentsByBusinessType[businessType].subTypes[agent.subBusinessType]) {
              agentsByBusinessType[businessType].subTypes[agent.subBusinessType] = [];
            }
            agentsByBusinessType[businessType].subTypes[agent.subBusinessType].push(agent);
          } else {
            // 没有子业务类型的加入主列表
            agentsByBusinessType[businessType].agents.push(agent);
          }
        });

        setAgentListByBusinessType(agentsByBusinessType);
      }),
    ])
      .then(() => {
        setLoading(false);
      })
      .catch(error => {
        Notify.error(error || '服务端异常');
        setLoading(false);
      });
  }, []);

  // 渲染智能体项
  const renderAgentItem = (agent: IAgentInstanceResponse) => {
    const noStatus = agent.status === -1;
    const isRunning = agent.status === 1;
    const isPending = agent.status === 0;
    const isPreparing = agent.status === 2;
    // 内测状态
    const isBetaTesting = agent.status === -2;

    const runningColor = '#45A110';
    const pendingColor = '#ccc';
    const betaTestingColor = '#ccc';
    const runningText = '运行中';
    const pendingText = '待启用';
    const preparingText = '准备中';
    const betaTestingText = '内测';

    // 默认图标
    const defaultIcon =
      'https://img01.yzcdn.cn/upload_files/2023/05/16/FvVDTBaQNvBzNIzxNfBKDXYXAHYg.png';

    return (
      <div
        key={agent.id}
        className={cn(styles.agentItem, {
          [styles.agentItemPreparing]: isPreparing,
        })}
        onClick={() => {
          if (isPreparing) {
            return;
          }
          if (agent.type === 'aigc_text') {
            goText(agent.id);
            return;
          }
          if (agent.type === 'aigc_image') {
            goPicture(agent.id);
            return;
          }
          if (agent.type === 'taskFlow' || agent.type === 'business_report') {
            goTaskFlow(agent.id);
            return;
          }

          if (agent.type === 'out_link') {
            goOutLink(agent.url);
          }
        }}
      >
        <div className={styles.agentIcon}>
          <img src={agent.icon || defaultIcon} alt={agent.name} />
        </div>
        <div className={styles.agentContent}>
          <div className={styles.agentItemTitle}>
            <div className={styles.name}>
              <ClampLines lines={1} popWidth={400} showPop={false} text={agent.name} />
            </div>
            {isPreparing ? (
              <div
                className={cn(styles.status, {
                  [styles.preparing]: isPreparing,
                })}
              >
                <img
                  src="https://img01.yzcdn.cn/upload_files/2025/04/15/FnkBjw1b4PYSR0xoPcPeYjn3fC5G.png"
                  className={styles.loadingIcon}
                />
                <span className={styles.statusName}>{preparingText}</span>
              </div>
            ) : isBetaTesting ? (
              <div
                className={cn(styles.status, {
                  [styles.betaTesting]: isBetaTesting,
                })}
              >
                <Badge
                  style={{
                    background: betaTestingColor,
                  }}
                  dot
                />
                <span className={styles.statusName}>{betaTestingText}</span>
              </div>
            ) : (
              agent.type !== 'aigc_text' &&
              agent.type !== 'aigc_image' &&
              !noStatus && (
                <div
                  className={cn(styles.status, {
                    [styles.running]: isRunning,
                    [styles.pending]: isPending,
                  })}
                >
                  <Badge
                    style={{
                      background: isRunning ? runningColor : isPending ? pendingColor : '',
                    }}
                    dot
                  />
                  <span className={styles.statusName}>
                    {isRunning ? runningText : isPending ? pendingText : '-'}
                  </span>
                </div>
              )
            )}
          </div>
          <div className={styles.agentItemDesc}>
            <ClampLines lines={2} popWidth={400} showPop={false} text={agent.objective} />
          </div>
        </div>
      </div>
    );
  };

  // 渲染智能体列表
  const renderAgentList = (agents: IAgentInstanceResponse[]) => {
    // 按sequence字段倒序排序，数字越大越排在前面
    const sortedAgents = [...agents].sort((a, b) => {
      const sequenceA = a.sequence || 0;
      const sequenceB = b.sequence || 0;
      return sequenceB - sequenceA; // 倒序排列
    });

    const chunks = splitArrayIntoChunks(sortedAgents, 4);
    return chunks.map((chunk, chunkIndex) => (
      <Row key={`chunk-${chunkIndex}`} className={styles.agentRow}>
        {chunk.map(agent => renderAgentItem(agent))}
      </Row>
    ));
  };

  // 对子分类进行排序（仅对托管类有效）
  const renderSubTypes = (
    businessType: string,
    subTypes: Record<string, IAgentInstanceResponse[]>,
  ) => {
    if (businessType !== 'hosted' || !Object.keys(subTypes).length) {
      return Object.entries(subTypes).map(([subType, subTypeAgents]) => (
        <div key={subType} className={styles.subBusinessTypeBlock}>
          {renderAgentList(subTypeAgents)}
        </div>
      ));
    }

    // 智能托管的子分类按照指定顺序渲染
    const sortedSubTypes: React.ReactNode[] = [];

    // 1. 微信托管
    if (subTypes.wechat_hosted) {
      sortedSubTypes.push(
        <div key="wechat_hosted" className={styles.subBusinessTypeBlock}>
          {renderAgentList(subTypes.wechat_hosted)}
        </div>,
      );
    }

    // 2. 小红书托管
    if (subTypes.xiaohongshu_hosted) {
      sortedSubTypes.push(
        <div key="xiaohongshu_hosted" className={styles.subBusinessTypeBlock}>
          {renderAgentList(subTypes.xiaohongshu_hosted)}
        </div>,
      );
    }

    // 3. 其他子分类
    Object.entries(subTypes).forEach(([subType, subTypeAgents]) => {
      if (subType !== 'wechat_hosted' && subType !== 'xiaohongshu_hosted') {
        sortedSubTypes.push(
          <div key={subType} className={styles.subBusinessTypeBlock}>
            {renderAgentList(subTypeAgents)}
          </div>,
        );
      }
    });

    console.log('sortedSubTypes', sortedSubTypes);

    return sortedSubTypes;
  };

  // 渲染每个业务类型的智能体块
  const renderBusinessTypeBlock = () => {
    // 定义业务类型的排序顺序
    const businessTypeOrder = ['hosted', 'agent', 'third_party'];

    // 对业务类型进行排序
    const sortedBusinessTypes = Object.entries(agentListByBusinessType).sort(([typeA], [typeB]) => {
      const indexA = businessTypeOrder.indexOf(typeA);
      const indexB = businessTypeOrder.indexOf(typeB);
      // 如果某个类型不在预设顺序中，则放到最后
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    });

    return sortedBusinessTypes.map(([businessType, { agents, subTypes }]) => {
      const config = BUSINESS_TYPE_CONFIG[businessType] || {
        title: '智能体',
        description: '',
      };

      return (
        <div key={businessType} className={styles.agentBlock}>
          <div className={styles.agentTitle}>{config.title}</div>
          {config.description && (
            <div className={styles.agentDescription}>{config.description}</div>
          )}
          <div className={styles.agentList}>
            {/* 特殊处理智能托管类型，先渲染子分类再渲染主分类 */}
            {businessType === 'hosted' ? (
              <>
                {/* 先渲染子类型 */}
                {renderSubTypes(businessType, subTypes)}
                {/* 再渲染主分类智能体 */}
                {agents.length > 0 && renderAgentList(agents)}
              </>
            ) : (
              <>
                {/* 其他业务类型保持原有渲染顺序：先主分类，后子分类 */}
                {agents.length > 0 && renderAgentList(agents)}
                {renderSubTypes(businessType, subTypes)}
              </>
            )}
          </div>
        </div>
      );
    });
  };

  return (
    <BlockLoading loading={loading} icon="youzan">
      <div className={styles.dashboardContainer}>
        <div className={styles.overview}>
          <div className={styles.text}>
            <div>早上好 ☕️，</div>
            <div>
              看看目前的工作成果，在过去的 <span className={styles.underline}>{workDay} 天</span>
            </div>
          </div>
          <div className={styles.nav}>
            <span className={styles.navItem} onClick={goCollect}>
              收藏
            </span>
            <Divider padding={16} />
            <span className={styles.navItem} onClick={goFlow}>
              工单
            </span>
          </div>
        </div>
        <div className={styles.dashboard}>
          {/* <DashboardItem icon={<AI />} prefix="带来了" num={profit} unit="元" suffix="的收益" />
          <Divider padding={8} height={40} /> */}
          <DashboardItem
            icon={<Monitor />}
            prefix="自动执行操作"
            num={taskCount}
            unit="次"
            suffix=""
          />
          <Divider padding={8} height={40} />
          <DashboardItem icon={<Text />} prefix="生成" num={textCount} unit="条" suffix="文案" />
          <Divider padding={8} height={40} />
          <DashboardItem icon={<Image />} prefix="生成" num={imageCount} unit="张" suffix="图片" />
        </div>

        {/* 根据业务类型和子业务类型渲染智能体 */}
        {renderBusinessTypeBlock()}
      </div>
    </BlockLoading>
  );
};

export default hot(App);
