import { hot } from 'react-hot-loader/root';

import { useEffect } from 'react';
import { renderNav } from './render-nav';
import WorkFlowIntro from './index.tsx';

const App = () => {
  useEffect(() => {
    const { search } = location;
    const pathArr = location.pathname.split('/');
    const finalPathName = pathArr[pathArr.length - 1];
    renderNav({ path: finalPathName, search });
  }, [location.pathname]);

  return (
    <div>
      {/* 用于创造一个干净环境的画布测试功能，验证问题 */}
      {/* <TestGraph /> */}
      <WorkFlowIntro />
    </div>
  );
};

export default hot(App);
