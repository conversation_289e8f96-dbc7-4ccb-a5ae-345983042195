import React from 'react';

import { useNavigate } from 'react-router-dom';
import styles from './index.m.scss';
import { Button } from 'zent';

const WorkFlowIntro: React.FC = () => {
    const handleStart = () => {
        window.location.href = 'https://store.youzan.com/v4/subscribe/pc-order/software/choose#/';
    };

    return (
        <div className={styles.container}>
            <div className={styles.content}>
                <h1 className={styles.title}>简单几步，开启自动化经营体验</h1>
                <p className={styles.subtitle}>重复性工作流程的自动化，提升运营管理效率</p>

                <div className={styles.features}>
                    <div className={styles['feature-item']}>
                        <span>数十个最佳实践模板，开箱即用</span>
                    </div>
                    <div className={styles['feature-item']}>
                        <span>自定义编排任务，灵活组合</span>
                    </div>
                    <div className={styles['feature-item']}>
                        <span>库存预警、售后自定处理、评价自动回复</span>
                    </div>
                </div>

                <div className={styles['upgrade-btn-wrapper']}>
                    <Button type="primary" className={styles['upgrade-btn']} onClick={handleStart}>立即升级</Button>
                    <p className={styles['upgrade-tip']}>升级更高版本后可用</p>
                </div>

                {/* <div className={styles['use-btn-wrapper']}>
                    <Button type="primary" className={styles['template-create-btn']}>从模板创建</Button>
                    <Button type="default" className={styles['custom-create-btn']}>自定义创建</Button>
                </div> */}
            </div>
            <img
                src="https://img01.yzcdn.cn/upload_files/2024/12/09/FrXWMqJIe1YiWht1bf0Bb9zt8yeC.png"
                className={styles.illustration}
            />
        </div>
    );
};

export default WorkFlowIntro;
