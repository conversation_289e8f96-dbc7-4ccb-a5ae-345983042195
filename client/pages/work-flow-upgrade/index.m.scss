:global {
    .app-inner {
        background-color: unset !important;
        padding: 0 !important;
    }
}

.container {
    min-height: calc(100vh - 68px - 48px - 28px - 6px);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    gap: 48px;

    .content {
        width: 400px;
    }

    .title {
        font-family: PingFang SC;
        font-size: 24px;
        font-weight: 500;
        line-height: 40px;

        color: #333;
        text-align: left;
    }

    .subtitle {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;

        color: #333;
        margin-top: 4px;
    }

    .features {
        margin-top: 36px;
        width: 100%;
        padding: 16px 0;

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            text-align: left;
            line-height: 28px;

            &::before {
                content: '';
                display: inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #1b67ff;
                margin-right: 12px;
            }

            span {
                font-size: 14px;
                color: #1f2329;
            }
        }
    }

    .upgrade-btn-wrapper {
        margin-top: 36px;
        display: flex;
        // flex-direction: column;
        align-items: center;

        .upgrade-btn {
            // margin-bottom: 12px;
        }

        .upgrade-tip {
            color: #155BD4;
            margin-left: 12px;

            font-family: PingFang SC;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;

            cursor: pointer;
        }
    }

    .illustration {
        // margin-top: 40px;
        // max-width: 400px;
        width: 294px;
        height: 294px;
    }
}