import React from 'react';
import ReactDOM from 'react-dom';
import { Breadcrumb } from 'zent';
// import args from '@youzan/utils/url/args';
interface BaseBread {
  name: string;
  href?: string;
}

const baseBreads: BaseBread[] = [
  {
    name: '应用中心',
    href: '/v4/apps/new-app-center',
  },
];

let navMap: BaseBread[] = [];

export const renderNav = ({ path, search }): void => {
  if (path === 'list') {
    navMap = [{ name: '自动任务' }];
  } else if (path === 'graph') {
    navMap = [
      { name: '自动任务', href: '/v4/jiawo/work-flow/list' },
      { name: search.includes('flowId') ? '编辑' : '新建' },
    ];
  } else if (path === 'templates') {
    navMap = [{ name: '自动任务', href: '/v4/jiawo/work-flow/list' }, { name: '更多模板' }];
  }

  // 不能直接修改 baseBreads, 因为 SPA 下, 这个变量会继续保持下去
  let breads = baseBreads.map(obj => ({ ...obj }));
  breads = breads.concat(navMap);

  const CustomNav = () => {
    return (
      <div>
        {breads.map(item => {
          return item.href ? <a href={item.href}>{item.name}</a> : <span>{item.name}</span>;
        })}
      </div>
    );
  };

  ReactDOM.render(<CustomNav />, document.getElementById('thirdbar-nav'));
};
