export interface IndicatorValueTrend {
  currentDay: string;
  indicatorValue: number;
  sortCurrentDay: number;
}

export interface IndicatorDataModel {
  displayFormat: string;
  indicatorAlias: string;
  indicatorId: number;
  indicatorValue: number;
  indicatorValueOfLastPeriod: number;
  indicatorValueTrend: IndicatorValueTrend[];
  valueRateOfLastPeriod: number;
}

export interface CurrentTimeRange {
  endDay: string;
  startDay: string;
}

export interface MomTimeRange {
  endDay: string;
  startDay: string;
}

export interface IndicatorDataDTO {
  currentTimeRange: CurrentTimeRange;
  indicatorDataModelList: IndicatorDataModel[];
  momTimeRange: MomTimeRange;
}

export interface TimeModel {
  dateType: number;
  endDay: string;
  startDay: string;
}

export interface IIndicatorData {
  foundMetric: boolean;
  indicatorDataDTO: IndicatorDataDTO;
  timeModel?: TimeModel;
  currentTimeRange?: object;
}

export interface IndicatorPanelProps {
  data: IIndicatorData;
}

export interface IIdicatorObj {
  indicatorAlias: string;
  indicatorDefinition: string;
  displayFormat?: string;
  ratio?: any;
}

export interface IIdicator {
  name: string;
  key: string;
  value: string | number;
  formattedValue: string;
  indicator: IIdicatorObj;
}
