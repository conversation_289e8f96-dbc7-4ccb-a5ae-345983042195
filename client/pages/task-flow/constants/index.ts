import { IFlow, ITask } from 'pages/work-flow/constant';

export interface ISkill {
  description: string;
  name: string;
  type: string;
  enable: boolean;
  initialized: boolean;
  id: number;
  skillTemplateId: number;
  configurable: boolean;
  config: {
    fields?: any[];
    values?: any;
    flowTemplate: IFlow;
    flowRule?: IFlow;
    containsCondition: boolean;
    actionSelectable?: boolean;
    actionList?: any[];
  };
}

export interface IDataOverview {
  title: string;
  content: string;
}

export interface ILog {
  executeTime: string;
  status: string;
  content: string;
  result: string;
}

export interface ILogColumn {
  title: string;
  name: string;
  width?: number;
}

export enum EAgentType {
  TASK_FLOW = 'taskFlow',
  AIGC_TEXT = 'aigc_text',
  AIGC_IMAGE = 'aigc_image',
  BUSINESS_REPORT = 'business_report',
}
