export interface IReportData {
    [key: string]: {
        yoyRate?: number;
        dimensionColumn?: boolean;
        momValue?: number;
        yoyValue?: number;
        momRate?: number;
        currentValue?: number;
        isDeleted?: boolean;
    };
}

export interface ITimeRange {
    dateType?: string;
    dateTypeCode?: number;
    startDay?: number;
    endDay?: number;
}

export interface IReport {
    suggests?: {
        [key: number]: string;
    };
    reportId?: number;
    reportName: string;
    yoyTimeRange: ITimeRange;
    currentTimeRange?: ITimeRange;
    momTimeRange?: ITimeRange;
    moduleName?: string;
    reportData?: IReportData;
    moduleId?: number;
    key?: number;
}

export interface IReportPosData {
    contentPartsElemId?: number;
    dataModelElemId?: number;
    isFavorite?: '1' | '0';
    msgFavoriteId?: string;
    messageId?: string;
    index?: number;
}

export interface IComponentInfo {
    data: any;
    requiredKey: any[];
}


export enum IProgressStatusEnum {
    /** 消息失败或超时 */
    // failed = -1,
    /** 消息处理中 */
    init = 0,
    /** 消息处理完成 */
    done = 1,

    doing = 2,
}