export interface Item {
  displayName: string;
  url?: string;
  value: string;
}

export interface FixedDefinition {
  actionInfo: {
    name: string;
    detail?: {
      rewriteText?: string;
      sendText: string;
      sessionId?: string;
      detail?: any;
      sendImmediately?: boolean;
    };
  }; // 应该是一个数组，但这里可能是 typo，所以用 string 代替
  elementKey: string;
  elementType?: string;
  rawDataPointer?: string;
}

export interface InteractDefinition {
  defType: 'fixed';
  fixedDefinitions: FixedDefinition[];
}

export interface Data {
  items: Item[];
}

// 完整的接口定义
export interface InteractiveListProps {
  title?: string;
  canFeedback?: boolean;
  data: Data | string;
  feedbackValue: number;
  interactDefinition: InteractDefinition;
  segmentId: string;
  type: 'list' | 'text';

  // 前端会新增的字段，用于根据不同场景展示不同样式
  errorStyle?: boolean;
  sceneType?: string;
}
