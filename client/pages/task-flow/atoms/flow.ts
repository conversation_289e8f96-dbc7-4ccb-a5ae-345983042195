import { atom } from 'jotai';
import {
  FlowStatusEnum,
  IAction,
  ICondition,
  IFlow,
  Subject,
  TriggerType,
} from 'pages/work-flow/constant';
import { Node } from '@antv/x6';

export const editingSeriesActionsAtom = atom<IAction[]>([]);

export const flowAtom = atom<IFlow>({
  name: '',
  scenes: '',
  triggerId: 0,
  triggerDefine: {
    code: '',
    description: '',
    id: 0,
    name: '',
    obtainPermissionWay: '',
    obtainPermissionNotice: '',
    unusableReason: '',
    canUse: true,
    subject: Subject.Empty,
    subjectDetail: '',
    type: TriggerType.EVENT,
  },
  status: FlowStatusEnum.DISABLE,
  chainRules: [],
});

export const editingConditionAtom = atom<ICondition[] | null>(null);

export const editingActionAtom = atom<IAction | null>(null);

export const drawerConditionIdxAtom = atom<number | null>(null);

export const editingNodeAtom = atom<Node | null>(null);

// 二级抽屉的文本域校验错误信息
export const textAreaInvalidTextAtom = atom<string>('');
