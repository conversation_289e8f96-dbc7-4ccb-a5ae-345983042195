import React, { Component } from 'react';
import { Steps } from 'zent';
import './index.scss';

class StepBar extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    let { current } = this.props;
    return (
      <div>
        <Steps className="bar" current={current} status="process">
          <Steps.Step title="自动学习" />
          <Steps.Step title="一键开店" />
          <Steps.Step title="预览方案" />
          <Steps.Step title="正式启用" />
        </Steps>
      </div>
    );
  }
}

export default StepBar;
