import { Alert } from 'zent';
import './index.scss';

const WxxdTips = ({ index, tip }) => {
  return (
    <Alert type="warning" className="wxxd-agent-tips">
      <p>
        <span className="wxxd-agent-tips__message">{tip.message}</span>
        {tip.actions &&
          tip.actions.map(action => (
            <span
              className="wxxd-agent-tips__action"
              key={action.label}
              onClick={() => action.onClick(index)}
            >
              {action.label}
            </span>
          ))}
      </p>
    </Alert>
  );
};

export default WxxdTips;
