import React, { useState, useEffect, useMemo } from "react";
import "./index.scss";

const Typewriter = ({ text, width }) => {
  const [currentText, setCurrentText] = useState("");

  // 提前计算好字符数组
  const characters = useMemo(() => text.split(""), [text]);

  useEffect(() => {
    // 当text变化时清空状态
    setCurrentText("");
    let index = 0;
    const interval = setInterval(() => {
      if (index < characters.length) {
        setCurrentText((prev) => prev + characters[index]);
        index++;
      } else {
        clearInterval(interval);
      }
    }, 50); // 调整动画速度（单位：毫秒）

    return () => {
      clearInterval(interval); // 清理计时器
    };
  }, [text, characters]);

  return (
    <div
      className="typewriter-container"
      style={{ maxWidth: width || "100%" }} // 设置最大宽度
    >
      <div className="typewriter">{currentText}</div>
    </div>
  );
};

export default Typewriter;
