import React from 'react';
import { Collapse } from 'zent';
import FormBuilder from '../form-builder';
import styles from './index.m.scss';
import WxxdTips from '../../WxxdTips';

/**
 * 表单分组组件
 * @param {Object} props - 组件属性
 * @param {string} props.title - 分组标题
 * @param {Array} props.fields - 字段配置数组
 * @param {Object} props.formData - 表单数据
 * @param {Function} props.onChange - 表单值变更回调
 * @param {boolean} props.isEditing - 是否编辑模式
 * @param {boolean} props.collapsible - 是否可折叠
 * @param {boolean} props.defaultOpen - 默认是否展开
 */
const FormSection = props => {
  const {
    title,
    tip = '',
    fields = [],
    formData = {},
    onChange,
    collapsible = false,
    defaultOpen = true,
    drawer = false,
    isEditing = true,
    disabledMap = {},
  } = props;

  const content = (
    <FormBuilder
      fields={fields}
      formData={formData}
      onChange={onChange}
      isEditing={isEditing}
      drawer={drawer}
      disabledMap={disabledMap}
    />
  );

  return (
    <div className={styles.formSection}>
      {title && <div className={styles.sectionTitle}>{title}</div>}
      {tip && <WxxdTips className={styles.sectionTip} tip={{ message: tip }} />}
      <div className={styles.sectionContent}>{content}</div>
    </div>
  );
};

export default FormSection;
