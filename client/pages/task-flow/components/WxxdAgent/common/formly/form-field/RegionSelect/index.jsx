import { useState, useEffect, memo } from 'react';
import { RegionSelect } from '@youzan/react-components';
import { getRegionModelById } from 'pages/task-flow/components/WxxdAgent/api';

function RegionSelectV2({ value, onChange, disabled, ...extraProps }) {
  const [province, city] = value;
  const [code, setCode] = useState(city || province);
  const [regionText, setRegionText] = useState('-');

  const getRegionText = code => {
    getRegionModelById({ regionId: code }).then(res => {
      setRegionText(`${res.provinceName}/${res.cityName}`);
    });
  };

  useEffect(() => {
    if (disabled === true) {
      getRegionText(code);
    }
  }, []);

  return disabled ? (
    <p>{regionText}</p>
  ) : (
    <RegionSelect
      value={code}
      changeOnSelect={false}
      onChange={v => {
        onChange([v.province_id, v.city_id]);
        setCode(v.city_id || v.province_id);
        setRegionText(`${v.province}/${v.city}`);
      }}
      disabled={disabled}
      stage="2"
      {...extraProps}
    />
  );
}

export default memo(RegionSelectV2);
