import { useState, useCallback, memo } from 'react';
import { BlockLoading, Icon, ImageUpload, Notify } from 'zent';

import styles from './image.m.scss';
import { getUploadToken } from 'pages/task-flow/components/WxxdAgent/api';

const ImageUploader = memo(props => {
  const {
    image = [],
    onChange,
    multiple = false,
    maxAmount = 999,
    maxSize,
    tips,
    style,
    disabled = false,
  } = props;

  const [urls, setUrls] = useState(Array.isArray(image) ? image : [image]);
  const [loading, setLoading] = useState(false);

  const onUploadChange = files => {};

  const onUpload = useCallback(
    (file, report) => {
      return new Promise(async resolve => {
        try {
          setLoading(true);
          const res = await getUploadToken();
          const uploadToken = res.token;
          const formData = new FormData();
          formData.append('file', file);
          formData.append('token', uploadToken);

          fetch('https://upload.qiniup.com/', {
            method: 'POST',
            body: formData,
          })
            .then(response => response.json())
            .then(data => {
              if (data.code === 0) {
                const fileUrl = data.data.attachment_url;
                const newUrls = [...urls, fileUrl];
                setUrls(newUrls);
                onChange(multiple ? newUrls : fileUrl);
                resolve(fileUrl);
              } else {
                throw new Error('上传失败');
              }
            })
            .catch(error => {
              console.error('上传失败:', error);
              Notify.error('图片上传失败');
              resolve();
            })
            .finally(() => {
              setLoading(false);
            });
        } catch (error) {
          console.error('上传过程发生错误:', error);
          Notify.error('上传过程发生错误');
          setLoading(false);
          resolve();
        }
      });
    },
    [onChange, multiple],
  );

  const handleDeleteUrl = useCallback(
    index => {
      const newUrls = [...urls];
      newUrls.splice(index, 1);
      setUrls(newUrls);
      onChange(multiple ? newUrls : newUrls[0] || '');
    },
    [urls, onChange, multiple],
  );

  return (
    <div style={style}>
      <div className={styles.imageContainer}>
        {urls.length === 0 && disabled && <p>-</p>}

        {urls.map((url, index) => {
          return (
            <div key={`${url}-${index}`} className={styles.imagePreview}>
              <img
                className={styles.imagePreviewImg}
                src={url}
                style={{ width: 'auto', height: 80 }}
                alt=""
              />
              {!disabled && (
                <div className={styles.imageDelete} onClick={() => handleDeleteUrl(index)}>
                  <Icon type="close-circle" className={styles.imageDeleteIcon} />
                </div>
              )}
            </div>
          );
        })}

        {!disabled &&
        ((!multiple && urls.length === 0) || (multiple && urls.length < maxAmount)) ? (
          <>
            <BlockLoading loading={loading}>
              <ImageUpload
                key={`${urls.length}-${maxAmount}`}
                disabled={disabled}
                multiple={multiple}
                onChange={onUploadChange}
                onUpload={onUpload}
                maxSize={maxSize}
                maxAmount={multiple ? maxAmount : 1}
                fileList={[]}
                accept="image/png, image/jpeg"
              />
            </BlockLoading>
            <p className={styles.tips}>{tips}</p>
          </>
        ) : null}
      </div>
    </div>
  );
});

export default ImageUploader;
