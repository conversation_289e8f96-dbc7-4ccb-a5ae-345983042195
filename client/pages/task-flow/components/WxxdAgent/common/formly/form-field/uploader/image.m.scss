.imageContainer {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 10px;
}

.imagePreview {
  position: relative;
  height: 80px;
  background-size: cover;
  background-position: center center;

  &:hover {
    .imageDelete {
      display: block;
    }
  }
}

.imagePreviewImg {
  width: auto;
  height: 80px;
  padding: 4px;
  box-sizing: border-box;
}

.imageDelete {
  position: absolute;
  top: -10px;
  right: -7px;
  cursor: pointer;
  display: none;
}

.imageDeleteIcon {
  font-size: 20px;
  color: #999;
}

.tips {
  color: #999;
  font-size: 12px;
  line-height: 18px;
  margin-top: 4px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  min-width: 80px;
  border: 1px dashed #ddd;
  border-radius: 4px;
}
