import { InfiniteScroller, InlineLoading, Select } from 'zent';
import { useCallback, useState, useEffect } from 'react';
import { querySettleBankList } from 'pages/task-flow/components/WxxdAgent/api';
import { debounce, isArray, isObject } from 'lodash';
import styles from './index.m.scss';
function BankSelect({ value, onChange, disabled, placeholder }) {
  const [currentValue, setCurrentValue] = useState(
    isArray(value) ? { text: value[0], key: value[1] } : {},
  );
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const loadOptions = useCallback(async () => {
    const res = await querySettleBankList();
    const newOptions = res.map(item => ({
      key: item.bankCode,
      text: item.bankName,
    }));

    setOptions(newOptions);
  }, []);

  // 处理浮层打开
  const handleOpenChange = useCallback(
    async isOpen => {
      setOpen(isOpen);
      if (isOpen && !options.length) {
        setLoading(true);
        await loadOptions();
        setLoading(false);
      }
    },
    [loadOptions, options.length],
  );

  const handleSelect = useCallback(
    selectedValue => {
      setCurrentValue(selectedValue);
      onChange?.([selectedValue.text, selectedValue.key]);
    },
    [onChange],
  );

  const renderOptionList = useCallback((optionList, renderOption) => {
    return (
      <InfiniteScroller
        skipLoadOnMount
        className="brand-select-scroller"
        loader={
          <div className="loading-text">
            <InlineLoading
              iconSize={18}
              loading
              icon="circle"
              iconText="加载中…"
              textPosition="right"
              colorPreset="grey"
            />
          </div>
        }
        threshold={3}
      >
        {optionList.map((item, index) => renderOption(item, index))}
      </InfiniteScroller>
    );
  }, []);

  // 初始化时如果有 value，需要获取对应的选项数据
  useEffect(() => {
    if (value && !options.length) {
      loadOptions();
    }
  }, [value, options.length, loadOptions]);

  return (
    <Select
      className={styles.bankSelect}
      clearable
      value={currentValue}
      options={options}
      placeholder={placeholder || '请选择'}
      loading={loading}
      open={open}
      disabled={disabled}
      onOpenChange={handleOpenChange}
      renderOptionList={renderOptionList}
      onChange={handleSelect}
    />
  );
}

export default BankSelect;
