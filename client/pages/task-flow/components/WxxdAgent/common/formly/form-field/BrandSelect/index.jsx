import { InfiniteScroller, InlineLoading, Select } from 'zent';
import { useCallback, useState, useEffect } from 'react';
import { brandDictionarySearch } from 'pages/task-flow/components/WxxdAgent/api';
import { debounce, isObject } from 'lodash';
import './index.scss';

function BrandSelect({ value, onChange, disabled, placeholder }) {
  const [params, setParams] = useState({
    nextKey: 0,
    outChannel: 1,
    pageSize: 20,
    searchName: '',
  });
  const [currentValue, setCurrentValue] = useState(isObject(value) ? value : { text: value });
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [keyword, setKeyword] = useState('');

  const loadOptions = useCallback(
    async (newParams = {}, shouldReset = false) => {
      const mixedParams = { ...params, ...newParams };

      const res = await brandDictionarySearch(mixedParams);
      const { nextKey, brandDTOList } = res;

      const newOptions = brandDTOList.map(item => ({
        key: item.brandId,
        text: item.chName || item.enName,
      }));

      setOptions(prev => (shouldReset ? newOptions : [...prev, ...newOptions]));
      setHasMore(brandDTOList.length === mixedParams.pageSize);
      setParams(prev => ({ ...prev, nextKey }));
    },
    [params],
  );

  // 处理浮层打开
  const handleOpenChange = useCallback(
    async isOpen => {
      setOpen(isOpen);
      if (isOpen && !options.length) {
        setLoading(true);
        await loadOptions({ nextKey: 0 }, true);
        setLoading(false);
      }
    },
    [loadOptions, options.length],
  );

  // 滚动加载
  const loadMore = useCallback(
    async closeLoading => {
      await loadOptions();
      closeLoading?.();
    },
    [loadOptions],
  );

  const debouncedSearch = useCallback(
    debounce(searchKeyword => {
      setParams(prev => ({ ...prev, nextKey: 0, searchName: searchKeyword }));
      loadOptions({ nextKey: 0, searchName: searchKeyword }, true);
    }, 300),
    [loadOptions],
  );

  const handleSearch = useCallback(
    searchKeyword => {
      setKeyword(searchKeyword);
      debouncedSearch(searchKeyword);
    },
    [debouncedSearch],
  );

  const handleSelect = useCallback(
    selectedValue => {
      setCurrentValue(selectedValue);
      onChange?.(selectedValue.text);
    },
    [onChange],
  );

  const renderOptionList = useCallback(
    (optionList, renderOption) => {
      return (
        <InfiniteScroller
          hasMore={hasMore}
          loadMore={loadMore}
          skipLoadOnMount
          className="brand-select-scroller"
          loader={
            <div className="loading-text">
              <InlineLoading
                iconSize={18}
                loading
                icon="circle"
                iconText="加载中…"
                textPosition="right"
                colorPreset="grey"
              />
            </div>
          }
          threshold={3}
        >
          {optionList.map((item, index) => renderOption(item, index))}
        </InfiniteScroller>
      );
    },
    [hasMore, loadMore],
  );

  // 初始化时如果有 value，需要获取对应的选项数据
  useEffect(() => {
    if (value && !options.length) {
      loadOptions({ nextKey: 0 }, true);
    }
  }, [value, options.length, loadOptions]);

  return (
    <Select
      clearable
      value={currentValue}
      options={options}
      placeholder={placeholder || '请选择'}
      loading={loading}
      open={open}
      keyword={keyword}
      disabled={disabled}
      onOpenChange={handleOpenChange}
      renderOptionList={renderOptionList}
      onKeywordChange={handleSearch}
      onChange={handleSelect}
    />
  );
}

export default BrandSelect;
