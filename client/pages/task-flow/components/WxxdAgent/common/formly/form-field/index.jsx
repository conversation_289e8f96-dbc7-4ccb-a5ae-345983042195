import React from 'react';
import { Input, Select, Radio, DatePicker, Checkbox, Switch, Icon } from 'zent';
import ImageUploader from './uploader/image';
import styles from './index.m.scss';
import { FieldType } from './fieldType';
import BrandSelect from './BrandSelect';
import BankSelect from './BankSelect';
import RegionSelectV2 from './RegionSelect';

/**
 * 通用表单字段组件
 * @param {Object} props - 组件属性
 * @param {string} props.type - 字段类型: 'text', 'textarea', 'number', 'date', 'select', 'radio', 'checkbox', 'switch', 'image'
 * @param {string} props.label - 字段标签
 * @param {string} props.field - 字段名称
 * @param {any} props.value - 字段值
 * @param {boolean} props.required - 是否必填
 * @param {boolean} props.isEditing - 是否编辑
 * @param {Function} props.onChange - 值变更回调
 * @param {Object} props.options - 选项配置，用于select/radio/checkbox类型
 * @param {string} props.placeholder - 占位文本
 * @param {string} props.tips - 提示文本
 * @param {Object} props.validators - 验证规则
 * @param {number} props.maxLength - 最大长度
 * @param {any} props.extraProps - 传递给具体组件的额外属性
 */
const FormField = props => {
  const {
    type = 'text',
    label = '',
    field = '',
    value,
    required = false,
    isEditing = true,
    disabledMap = {},
    onChange,
    options = [],
    placeholder = `请输入${label}`,
    tips = '',
    validators = {},
    maxLength,
    extraProps = {},
    customComponent = any,
    drawer = false,
  } = props;

  // 处理值变更
  const handleChange = val => {
    let fieldValue = val;

    // 根据不同组件类型处理返回值
    if (type === FieldType.TEXT) {
      fieldValue = val.target.value;
    } else if (type === FieldType.RADIO) {
      fieldValue = val.target.value;
    } else if (type === FieldType.SELECT) {
      fieldValue = val.key;
    } else if (
      type === FieldType.DATE_RANGE_PICKER ||
      type === FieldType.CARD_IMAGE_UPLOADER ||
      type === FieldType.BANK_SELECT ||
      type === FieldType.REGION_SELECT
    ) {
      fieldValue = [val[0], val[1]];
    }

    // 调用外部onChange
    onChange && onChange(fieldValue, field);
  };

  const disabled = disabledMap[field];

  // 根据类型渲染不同的表单控件
  const renderField = () => {
    switch (type) {
      case FieldType.TEXT:
        return isEditing ? (
          <Input
            className={styles.input}
            value={value}
            onChange={handleChange}
            placeholder={placeholder}
            {...extraProps}
            disabled={disabled}
          />
        ) : (
          <p>{value || '-'}</p>
        );

      case FieldType.RADIO:
        return isEditing ? (
          <Radio.Group value={value} onChange={handleChange} disabled={disabled}>
            {options.map(option => (
              <Radio key={option.key} value={option.key}>
                {option.text}
              </Radio>
            ))}
          </Radio.Group>
        ) : (
          <p>{options.find(option => option.key === value)?.text || '-'}</p>
        );

      case FieldType.SELECT:
        return isEditing ? (
          <Select
            value={options.find(option => option.key === value)}
            onChange={handleChange}
            options={options}
          />
        ) : (
          <p>{options.find(option => option.key === value)?.text || '-'}</p>
        );

      case FieldType.IMAGE_UPLOADER:
        return (
          <ImageUploader
            maxSize={5 * 1024 * 1024}
            image={value}
            tips={tips}
            disabled={disabled || !isEditing}
            onChange={url => handleChange(url)}
          />
        );

      case FieldType.DATE_RANGE_PICKER:
        const isEndDatePermanently = value[1] === '长期';
        return isEditing ? (
          <>
            <DatePicker
              width={150}
              value={value[0]}
              onChange={v => handleChange([v, value[1]])}
              placeholder="开始日期"
              disabled={disabled}
            />
            <span className={styles.dateSeparator}>至</span>
            {isEndDatePermanently ? (
              <div className={styles.DatePermanently}>
                <span>长期有效</span>
                <Icon type="calendar-o" className={styles.calendarIcon} />
              </div>
            ) : (
              <DatePicker
                width={150}
                value={value[1]}
                onChange={v => handleChange([value[0], v])}
                placeholder="结束日期"
                disabled={disabled}
              />
            )}
            <Switch
              disabled={disabled}
              size="small"
              className={styles.DatePermanentlySwitch}
              checked={isEndDatePermanently}
              onChange={checked => {
                handleChange([value[0], checked ? '长期' : '']);
              }}
            />
            长期有效
          </>
        ) : (
          <>
            <p>{value[0]}</p>
            <span className={styles.dateSeparator}>至</span>
            <p>{isEndDatePermanently ? '长期有效' : value[1]}</p>
          </>
        );

      case FieldType.CARD_IMAGE_UPLOADER:
        return (
          <div className={styles.personCard}>
            <ImageUploader
              maxSize={5 * 1024 * 1024}
              maxAmount={1}
              disabled={disabled || !isEditing}
              onChange={url => handleChange([url, value[1]])}
              image={value[0]}
              tips="请上传证件正面"
            />

            <ImageUploader
              style={{ marginLeft: 16 }}
              maxSize={5 * 1024 * 1024}
              maxAmount={1}
              disabled={disabled || !isEditing}
              onChange={url => handleChange([value[0], url])}
              image={value[1]}
              tips="请上传证件背面"
            />
          </div>
        );

      case FieldType.SWITCH:
        return isEditing ? (
          <Switch checked={!!value} onChange={handleChange} disabled={disabled} {...extraProps} />
        ) : (
          <p>{value ? '是' : '否'}</p>
        );

      case FieldType.REGION_SELECT:
        return (
          <RegionSelectV2
            value={value}
            changeOnSelect={false}
            onChange={handleChange}
            disabled={disabled || !isEditing}
            stage="2"
            {...extraProps}
          />
        );

      case FieldType.MULTI_IMAGE_UPLOADER:
        return (
          <div className={styles.uploadContent}>
            <ImageUploader
              maxSize={10 * 1024 * 1024}
              multiple
              disabled={disabled || !isEditing}
              image={value || []}
              tips={tips}
              {...extraProps}
              onChange={url => handleChange(url)}
            />
          </div>
        );

      case FieldType.BRAND_SELECT:
        return isEditing ? (
          <BrandSelect value={value} onChange={handleChange} disabled={disabled} {...extraProps} />
        ) : (
          <p>{value || '-'}</p>
        );

      case FieldType.BANK_SELECT:
        return isEditing ? (
          <BankSelect value={value} onChange={handleChange} disabled={disabled} {...extraProps} />
        ) : (
          <p>{value[0] || '-'}</p>
        );

      default:
        return customComponent ? customComponent : null;
    }
  };

  return (
    <div className={styles.formItem}>
      {label && (
        <div className={`${styles.formLabel} ${drawer && styles.drawer}`}>
          {required && <span className={styles.required}>*</span>}
          <span>{label}：</span>
        </div>
      )}
      <div className={styles.formContent}>{renderField()}</div>
    </div>
  );
};

export default FormField;
