.formContainer {
  position: relative;
  padding: 16px;
}

.formItem {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.formLabel {
  flex: 1;
  max-width: 130px;
  height: 30px;
  text-align: right;
  margin-right: 8px;
  font-size: 14px;
  color: #333;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  &.drawer {
    max-width: 150px;
  }
}

.required {
  color: #d41f15;
  margin-right: 4px;
}

.formContent {
  min-height: 30px;
  max-width: 500px;
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
}

.uploadContent {
  flex: 1;
}

.drawerFooter {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  gap: 8px;
}

.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.editIcon {
  width: 20px;
  height: 20px;
}

.dateSeparator {
  padding: 0 2px;
}

.input {
  width: 317px;
}

.DatePermanently {
  border-color: #e0e0e0;
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
  background-color: #fff;
  height: 32px;
  width: 150px;
  padding: 0 10px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
}
.calendarIcon {
  font-size: 18px;
  color: #ccc;
}
.DatePermanentlySwitch {
  margin-left: 8px;
  margin-right: 4px;
}

.personCard {
  display: flex;
  flex-direction: row;
}
