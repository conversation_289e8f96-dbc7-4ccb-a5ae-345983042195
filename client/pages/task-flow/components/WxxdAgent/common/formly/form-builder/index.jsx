import React, { useState, useEffect } from 'react';
import { Button } from 'zent';
import <PERSON>Field from '../form-field';
import { FieldType } from '../form-field/fieldType';
import styles from './index.m.scss';
import { set, get } from 'lodash-es';

/**
 * 通用表单生成器
 * @param {Object} props - 组件属性
 * @param {Array} props.fields - 字段配置数组
 * @param {Object} props.formData - 表单数据
 * @param {Function} props.onChange - 表单值变更回调
 * @param {boolean} props.isEditing - 是否编辑模式
 * @param {Function} props.onSubmit - 提交回调
 * @param {Object} props.submitButtonProps - 提交按钮属性
 */
const FormBuilder = props => {
  const {
    fields = [],
    formData = {},
    onChange,
    onSubmit,
    submitButtonProps = {},
    drawer = false,
    isEditing = true,
    disabledMap = {},
  } = props;

  // 处理表单字段值变更
  const handleFieldChange = (value, field) => {
    const newFormData = { ...formData };
    if (
      field.type === FieldType.DATE_RANGE_PICKER ||
      field.type === FieldType.CARD_IMAGE_UPLOADER ||
      field.type === FieldType.BANK_SELECT ||
      field.type === FieldType.REGION_SELECT
    ) {
      set(newFormData, `${field.keyPath}.${field.keys[0]}`, value[0]);
      set(newFormData, `${field.keyPath}.${field.keys[1]}`, value[1]);
    } else {
      set(newFormData, field.keyPath, value);
    }

    if (field.otherKeyPaths) {
      // 不同字段相同内容
      field.otherKeyPaths.forEach(key => {
        set(newFormData, `${key}`, value);
      });
    }

    onChange && onChange(newFormData);
  };

  // 获取字段值（支持嵌套路径）
  const getFieldValue = field => {
    if (!field.keyPath) return undefined;

    if (
      field.type === FieldType.DATE_RANGE_PICKER ||
      field.type === FieldType.REGION_SELECT ||
      field.type === FieldType.BANK_SELECT ||
      field.type === FieldType.CARD_IMAGE_UPLOADER
    ) {
      return [
        get(formData, `${field.keyPath}.${field.keys[0]}`),
        get(formData, `${field.keyPath}.${field.keys[1]}`),
      ];
    }

    return get(formData, field.keyPath);
  };

  // 处理表单提交
  const handleSubmit = e => {
    e && e.preventDefault();
    onSubmit && onSubmit(formData);
  };

  return (
    <div className={styles.formBuilder}>
      <div className={styles.formFields}>
        {fields.map((field, index) => {
          // 处理条件显示
          if (field.condition && !field.condition(formData)) {
            return null;
          }

          return (
            <FormField
              key={`${field.field || index}`}
              drawer={drawer}
              type={field.type}
              label={field.label}
              field={field.field}
              value={getFieldValue(field)}
              required={field.required}
              isEditing={isEditing}
              disabledMap={disabledMap}
              onChange={value => handleFieldChange(value, field)}
              options={field.options}
              placeholder={field.placeholder}
              tips={field.tips}
              validators={field.validators}
              maxLength={field.maxLength}
              extraProps={field.extraProps || {}}
              customComponent={field.customComponent || {}}
            />
          );
        })}
      </div>
    </div>
  );
};

export default FormBuilder;
