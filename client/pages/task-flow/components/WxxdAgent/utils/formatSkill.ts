import { get } from 'lodash';
import { SkillTemplateIdMap } from 'pages/task-flow/components/WxxdAgent/constant';

function needFormatSkillDesc(skill: any) {
  return [SkillTemplateIdMap.GoodsSoldoutReminder, SkillTemplateIdMap.GoodsAutoStock].includes(
    skill.skillTemplateId,
  );
}

/**
 * 将流程模板格式化为人类可读的描述
 * @param flowTemplate 流程模板数据
 * @returns 格式化后的描述JSON对象
 */
function formatSkillDesc(skill: any) {
  if (
    [SkillTemplateIdMap.GoodsSoldoutReminder, SkillTemplateIdMap.GoodsAutoStock].includes(
      skill.skillTemplateId,
    )
  ) {
    const chainRule =
      get(skill, 'config.flowRule.chainRules[0]') ||
      get(skill, 'config.flowTemplate.chainRules[0]');
    // 处理链式规则
    const condition = formatConditions(chainRule.conditions);
    const action = formatAction(chainRule.action);

    return {
      condition: condition?.length > 25 ? condition.slice(0, 25) + '...' : condition,
      action: action,
    };
  }

  return {
    condition: '',
    action: skill.description,
  };
}

/**
 * 格式化条件表达式
 * @param conditions 条件数组
 * @returns 格式化后的条件描述
 */
function formatConditions(conditions: any[]) {
  if (!conditions || conditions.length === 0) {
    return '';
  }

  // 处理每组条件（条件组之间是OR关系）
  const conditionGroups = conditions
    .map((conditionGroup: any) => {
      if (!conditionGroup.expressions || conditionGroup.expressions.length === 0) {
        return '';
      }

      // 处理每个表达式（表达式之间是AND关系）
      const expressions = conditionGroup.expressions
        .map((expression: any) => {
          if (!expression.variable || !expression.operator) {
            return '';
          }

          const variableName = expression.variable.name;
          const operatorSymbol = expression.operator.description || expression.operator.name || '';

          // 处理不同类型的值
          let value = '';

          // 开放字段（用户输入的值）
          if (expression.openField !== undefined && expression.openField !== '') {
            value = expression.openField;
          }
          // 包含关系，处理多选值
          else if (
            expression.operator.symbol === 'in' ||
            expression.operator.symbol === 'anyMatch'
          ) {
            // 处理显示映射
            if (expression.fieldValueMaps4Display) {
              try {
                const displayMap = JSON.parse(expression.fieldValueMaps4Display);
                const key = expression.variable.code;

                if (displayMap[key] && Array.isArray(displayMap[key])) {
                  // 商品范围等多选情况
                  value = displayMap[key].map((item: any) => item.name).join('、');
                } else if (displayMap[key]) {
                  value = displayMap[key].toString();
                }
              } catch (error) {
                value = '解析错误';
              }
            }
            // 处理受限字段（预定义的选项）
            else if (expression.restrictedFields && expression.restrictedFields.length > 0) {
              value = expression.restrictedFields.map((field: any) => field.name).join('、');
            }
          }

          return `${variableName}${operatorSymbol}${value}`;
        })
        .filter(Boolean)
        .join('，');

      return expressions;
    })
    .filter(Boolean)
    .join('或');

  return conditionGroups;
}

/**
 * 格式化动作
 * @param action 动作对象
 * @returns 格式化后的动作描述
 */
function formatAction(action: any) {
  if (!action) {
    return '无效的动作';
  }

  let result = '';

  // 处理动作参数
  if (action.actionFieldValueMaps4Execute) {
    try {
      const displayMap = JSON.parse(action.actionFieldValueMaps4Execute);

      // 针对补库存动作特殊处理
      if (action.code === 'AddGoodsStock' && displayMap.replenishStock) {
        return `自动补${displayMap.replenishStock}库存`;
      } else if (action.code === 'SendChannelSMS' && displayMap.receiver) {
        return `发送消息提醒给${displayMap.receiver.length}人`;
      }

      // 通用处理逻辑
      Object.keys(displayMap).forEach(key => {
        const variable = action.variables?.find((v: any) => v.code === key);
        if (variable) {
          const value = displayMap[key];
          result = `${action.name}${value}`;
        }
      });
    } catch (error) {
      result = action.name || '未知动作';
    }
  } else {
    result = action.name || '未知动作';
  }

  return result;
}

export { needFormatSkillDesc, formatSkillDesc };
