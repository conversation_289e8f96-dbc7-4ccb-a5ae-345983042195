import { SkillTemplateIdMap } from '../constant';
import { setDay, addWeeks, startOfWeek, setHours, setMinutes, format } from 'date-fns';
import { findFirst20Admin, queryShopBindAccount } from '../api';

/** 微信小店操作符 */
const wxShopOperator = {
  operator: {
    compareType: 1,
    description: '包含',
    id: 7,
    name: '包含',
    symbol: 'in',
  },
  variable: {
    code: 'xdShopIds',
    description: '请选择小店范围',
    dynamicEndpointConfig:
      '{"apiId":"wxxdShopSelector","responseCovert":"id","isMultiSelect":true,"extParam":{},"componentStyle":"popUpSelection"}\n',
    extension: '',
    id: 428,
    isRequired: true,
    name: '微信小店',
    needPermissionVerification: false,
    type: 2,
    validateRule: '',
    valueSource: 3,
  },
};

const itemDeliveryOperator = {
  operator: {
    compareType: 1,
    description: '包含',
    id: 9,
    name: '包含',
    symbol: 'anyMatch',
  },
  variable: {
    code: 'itemDeliveryTypes',
    description: '请选择发货状态',
    dynamicEndpointConfig: '',
    extension: '',
    id: 387,
    isRequired: true,
    name: '发货状态',
    needPermissionVerification: false,
    type: 3,
    validateRule: '',
    valueSource: 2,
  },
  restrictedFields: [
    {
      description: '未发货',
      id: 177,
      name: '未发货',
      value: '1',
      text: '未发货',
      key: 177,
    },
  ],
};

async function generateDefaultSkillConfig(skills: any[]) {
  const adminList20 = await findFirst20Admin();
  const adminList = adminList20?.items?.slice(0, 10);

  const adminList4Display = adminList?.map(staff => {
    return {
      id: staff.adminId,
      name: staff.name,
    };
  });
  const adminList4Execute = adminList?.map(staff => staff.adminId);

  const defaultAdminListActionFieldValueMaps4Display = JSON.stringify({
    msgChannel: [
      {
        description: 'App消息渠道',
        id: 341,
        name: 'App消息',
        value: '6',
        disabled: false,
        key: 341,
        text: 'App消息',
      },
    ],
    receiver: adminList4Display,
  });
  const defaultAdminListActionFieldValueMaps4Execute = JSON.stringify({
    msgChannel: [341],
    receiver: adminList4Execute,
  });

  const chainRulesConfig = {
    // 商品库存缺货自动补货
    [SkillTemplateIdMap.GoodsAutoStock]: {
      flowRule: {
        chainRules: [
          {
            action: {
              actionFieldValueMaps4Display: '{"replenishStock":{"name":"5"}}',
              actionFieldValueMaps4Execute: '{"replenishStock":"5"}',
              code: 'AddGoodsStock',
              description: '仅实物商品、虚拟商品、电子卡券、海淘商品支持补库存',
              id: 4,
              name: '补商品库存',
              supportFlowConcatenation: 0,
              type: 'addGoodsStock',
              variables: [
                {
                  code: 'replenishStock',
                  description: '请输入库存数量',
                  dynamicEndpointConfig: '',
                  extension: '',
                  id: 6,
                  isRequired: true,
                  name: '补库存数量',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '^[1-9]+[0-9]*$',
                  valueSource: 1,
                },
              ],
            },
            seriesActions: [],
            conditions: [
              {
                expressions: [
                  {
                    openField: '20',
                    operator: {
                      compareType: 1,
                      description: '小于',
                      id: 1,
                      name: '小于',
                      symbol: '<',
                    },
                    variable: {
                      code: 'stockNum',
                      description: '请输入库存数量',
                      dynamicEndpointConfig: '',
                      extension: '',
                      id: 8,
                      isRequired: true,
                      name: '商品库存',
                      needPermissionVerification: false,
                      type: 1,
                      validateRule: '^[0-9]+[0-9]*$',
                      valueSource: 1,
                    },
                  },
                ],
                rule: 1,
              },
            ],
          },
        ],
      },
    },
    // 商品库存小于20：发送消息提醒给10人
    [SkillTemplateIdMap.GoodsSoldoutReminder]: {
      flowRule: {
        chainRules: [
          {
            action: {
              actionFieldValueMaps4Display: defaultAdminListActionFieldValueMaps4Display,
              actionFieldValueMaps4Execute: defaultAdminListActionFieldValueMaps4Execute,
              code: 'SendChannelSMS',
              description: '根据渠道变量分发',
              id: 36,
              name: '发送消息提醒',
              supportFlowConcatenation: 0,
              type: 'sendchannelSms',
              variables: [
                {
                  code: 'msgChannel',
                  description: '',
                  dynamicEndpointConfig:
                    '{"isMultiSelect":false,"effectVarialeCodeList":[{"code":"receiver","valueSource":3}]}',
                  extension: '',
                  id: 533,
                  isRequired: true,
                  name: '通知渠道',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '',
                  valueSource: 2,
                },
                {
                  code: 'receiver',
                  description: '',
                  dynamicEndpointConfig:
                    '{"apiId":"storeStaff","responseCovert":"id","isMultiSelect":true,"componentStyle":"dropDownSelection","dependenciesVariales":["msgChannel"]}',
                  extension: '',
                  id: 532,
                  isRequired: true,
                  name: '接收人',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '',
                  valueSource: 3,
                },
              ],
            },
            seriesActions: [],
            conditions: [
              {
                expressions: [
                  {
                    openField: '20',
                    operator: {
                      compareType: 1,
                      description: '小于',
                      id: 1,
                      name: '小于',
                      symbol: '<',
                    },
                    variable: {
                      code: 'stockNum',
                      description: '请输入库存数量',
                      dynamicEndpointConfig: '',
                      extension: '',
                      id: 8,
                      isRequired: true,
                      name: '商品库存',
                      needPermissionVerification: false,
                      type: 1,
                      validateRule: '^[0-9]+[0-9]*$',
                      valueSource: 1,
                    },
                  },
                ],
                rule: 1,
              },
            ],
          },
        ],
      },
    },
    // 订单信息变更时：给店铺管理员发送提醒
    [SkillTemplateIdMap.OrderPickupChangeReminder]: {
      flowRule: {
        chainRules: [
          {
            action: {
              actionFieldValueMaps4Display: defaultAdminListActionFieldValueMaps4Display,
              actionFieldValueMaps4Execute: defaultAdminListActionFieldValueMaps4Execute,
              code: 'SendChannelSMS',
              description: '根据渠道变量分发',
              id: 36,
              name: '发送消息提醒',
              supportFlowConcatenation: 0,
              type: 'sendchannelSms',
              variables: [
                {
                  code: 'msgChannel',
                  description: '',
                  dynamicEndpointConfig:
                    '{"isMultiSelect":false,"effectVarialeCodeList":[{"code":"receiver","valueSource":3}]}',
                  extension: '',
                  id: 533,
                  isRequired: true,
                  name: '通知渠道',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '',
                  valueSource: 2,
                },
                {
                  code: 'receiver',
                  description: '',
                  dynamicEndpointConfig:
                    '{"apiId":"storeStaff","responseCovert":"id","isMultiSelect":true,"componentStyle":"dropDownSelection","dependenciesVariales":["msgChannel"]}',
                  extension: '',
                  id: 532,
                  isRequired: true,
                  name: '接收人',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '',
                  valueSource: 3,
                },
              ],
            },
            seriesActions: [],
            conditions: [
              {
                expressions: [
                  {
                    operator: {
                      compareType: 1,
                      description: '包含',
                      id: 7,
                      name: '包含',
                      symbol: 'in',
                    },
                    restrictedFields: [
                      {
                        description: '已发货',
                        id: 124,
                        name: '已发货',
                        value: '1',
                        text: '已发货',
                        key: 124,
                      },
                    ],
                    variable: {
                      code: 'deliveryStatus',
                      description: '请配置发货状态',
                      dynamicEndpointConfig: '',
                      extension: '',
                      id: 356,
                      isRequired: true,
                      name: '发货状态',
                      needPermissionVerification: false,
                      type: 2,
                      validateRule: '',
                      valueSource: 2,
                    },
                  },
                ],
                rule: 1,
              },
              {
                expressions: [
                  {
                    operator: {
                      compareType: 1,
                      description: '包含',
                      id: 9,
                      name: '包含',
                      symbol: 'anyMatch',
                    },
                    restrictedFields: [
                      {
                        description: '收货人手机号',
                        id: 130,
                        name: '收货人手机号',
                        value: '2',
                        text: '收货人手机号',
                        key: 130,
                      },
                      {
                        description: '收货地址',
                        id: 131,
                        name: '收货地址',
                        value: '1',
                        text: '收货地址',
                        key: 131,
                      },
                    ],
                    variable: {
                      code: 'modifyContentTypes',
                      description: '',
                      dynamicEndpointConfig: '',
                      extension: '',
                      id: 358,
                      isRequired: true,
                      name: '修改内容',
                      needPermissionVerification: false,
                      type: 3,
                      validateRule: '',
                      valueSource: 2,
                    },
                  },
                ],
                rule: 1,
              },
            ],
          },
        ],
      },
    },
    [SkillTemplateIdMap.PromiseOrderTimeoutReminder]: {
      flowRule: {
        chainRules: [
          {
            action: {
              actionFieldValueMaps4Display: '{"orderShippedTimeOut":{"name":"48"}}',
              actionFieldValueMaps4Execute: '{"orderShippedTimeOut":"48"}',
              code: 'CountOrderForXdChannel',
              description: '',
              id: 33,
              name: '统计小店未发货订单',
              supportFlowConcatenation: 1,
              type: 'actionCountOrderWithConditions',
              variables: [
                {
                  code: 'orderShippedTimeOut',
                  description:
                    '当订单处于"待发货"状态的时长大于等于设置的时长时,自动统计超时未发货订单的数量,不包含预售订单',
                  dynamicEndpointConfig: '',
                  extension: '{"variablePattern":{"styleTemplate":{"suffixDesc":"小时"}}}',
                  id: 416,
                  isRequired: true,
                  name: '订单未发货时长',
                  needPermissionVerification: false,
                  type: 0,
                  validateRule: '^(?:[1-9]|[1-9][0-9]|[1-9][0-9][0-9])$',
                  valueSource: 1,
                },
              ],
            },
            seriesActions: [
              {
                actionFieldValueMaps4Display: defaultAdminListActionFieldValueMaps4Display,
                actionFieldValueMaps4Execute: defaultAdminListActionFieldValueMaps4Execute,
                code: 'SendChannelSMS',
                description: '根据渠道变量分发',
                id: 36,
                name: '发送消息提醒',
                supportFlowConcatenation: 0,
                type: 'sendchannelSms',
                variables: [
                  {
                    code: 'msgChannel',
                    description: '',
                    dynamicEndpointConfig: '{"isMultiSelect":false}',
                    extension: '',
                    id: 533,
                    isRequired: true,
                    name: '通知渠道',
                    needPermissionVerification: false,
                    type: 1,
                    validateRule: '',
                    valueSource: 2,
                  },
                  {
                    code: 'receiver',
                    description: '',
                    dynamicEndpointConfig:
                      '{"apiId":"storeStaff","responseCovert":"id","isMultiSelect":true,"componentStyle":"dropDownSelection","dependenciesVariales":["msgChannel"]}',
                    extension: '',
                    id: 532,
                    isRequired: true,
                    name: '接收人',
                    needPermissionVerification: false,
                    type: 1,
                    validateRule: '',
                    valueSource: 3,
                  },
                ],
              },
            ],
            conditions: [],
          },
        ],
        triggerDefine: {
          attributes: 'scheduled',
          code: 'scheduled',
          corpus: '定时任务,定时每小时/每天/每周/每月/每小时',
          description: '',
          id: 16,
          name: '定时任务',
          subject: 'scheduled',
          subjectDetail: `{"repeatRule":{"cycleRule":"week","startTime":"${(() => {
            const now = new Date();
            const nextMonday = setDay(addWeeks(startOfWeek(now), 1), 1);
            const nextMondayAt10 = setMinutes(setHours(nextMonday, 10), 0);
            return format(nextMondayAt10, 'YYYY-MM-DD HH:mm');
          })()}","endType":0,"endTime":"","weekDays":[2]}}`,
          supportDelay: false,
        },
      },
    },
    // 物流异常提醒
    [SkillTemplateIdMap.PromiseLogisticsAnomalyReminder]: {
      flowRule: {
        chainRules: [
          {
            action: {
              actionFieldValueMaps4Display: defaultAdminListActionFieldValueMaps4Display,
              actionFieldValueMaps4Execute: defaultAdminListActionFieldValueMaps4Execute,
              code: 'SendChannelSMS',
              description: '根据渠道变量分发',
              id: 36,
              name: '发送消息提醒',
              supportFlowConcatenation: 0,
              type: 'sendchannelSms',
              variables: [
                {
                  code: 'msgChannel',
                  description: '',
                  dynamicEndpointConfig:
                    '{"isMultiSelect":false,"effectVarialeCodeList":[{"code":"receiver","valueSource":3},{"code":"receiver","valueSource":3},{"code":"receiver","valueSource":3},{"code":"receiver","valueSource":3},{"code":"receiver","valueSource":3},{"code":"receiver","valueSource":3}]}',
                  extension: '',
                  id: 533,
                  isRequired: true,
                  name: '通知渠道',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '',
                  valueSource: 2,
                },
                {
                  code: 'receiver',
                  description: '',
                  dynamicEndpointConfig:
                    '{"apiId":"storeStaff","responseCovert":"id","isMultiSelect":true,"componentStyle":"dropDownSelection","dependenciesVariales":["msgChannel"]}',
                  extension: '',
                  id: 532,
                  isRequired: true,
                  name: '接收人',
                  needPermissionVerification: false,
                  type: 1,
                  validateRule: '',
                  valueSource: 3,
                },
              ],
            },
            seriesActions: [],
            conditions: [
              {
                expressions: [
                  {
                    operator: {
                      compareType: 1,
                      description: '包含',
                      id: 7,
                      name: '包含',
                      symbol: 'in',
                    },
                    restrictedFields: [
                      {
                        description: '揽收超时',
                        id: 140,
                        name: '揽收超时',
                        value: '1',
                        text: '揽收超时',
                        key: 140,
                      },
                      {
                        description: '转运超时',
                        id: 141,
                        name: '转运超时',
                        value: '2',
                        text: '转运超时',
                        key: 141,
                      },
                      {
                        description: '签收超时',
                        id: 142,
                        name: '签收超时',
                        value: '3',
                        text: '签收超时',
                        key: 142,
                      },
                    ],
                    variable: {
                      code: 'abnormalLogisticsType',
                      description: '物流异常类型',
                      dynamicEndpointConfig: '',
                      extension: '',
                      id: 378,
                      isRequired: true,
                      name: '物流异常类型',
                      needPermissionVerification: false,
                      type: 2,
                      validateRule: '',
                      valueSource: 2,
                    },
                  },
                ],
                rule: 1,
              },
            ],
          },
        ],
      },
    },
    // 送礼专区
    [SkillTemplateIdMap.MarketingGiftZone]: {
      flowRule: {
        chainRules: [
          {
            action: {
              id: 64,
              actionFieldValueMaps4Execute: '{"giftZoneName":"微信送礼专区","isAutoHosted":true}',
              actionFieldValueMaps4Display: '{"giftZoneName":"微信送礼专区","isAutoHosted":true}',
            },
            conditions: [],
          },
        ],
        isManagedFlow: 0,
        channelEntityId: 27623700,
      },
    },
    [SkillTemplateIdMap.MarketingHotGoodsTop]: {
      flowRule: {},
    },
  };

  skills.forEach(skill => {
    if (chainRulesConfig[skill.skillTemplateId]) {
      const flowConfig = skill.config.flowRule || skill.config.flowTemplate;
      const defaultFlowRule = chainRulesConfig[skill.skillTemplateId];
      skill.config = {
        ...skill.config,
        flowRule: {
          ...flowConfig,
          ...defaultFlowRule.flowRule,
        },
      };

      skill.initialized = true;
      skill.enable = true;
    }
  });

  return [...skills];
}

export default generateDefaultSkillConfig;
