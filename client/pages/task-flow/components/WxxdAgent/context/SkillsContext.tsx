import { ISkill } from 'pages/task-flow/constants';
import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import generateDefaultSkillConfig from '../utils/defaultSkillConfig';

interface SkillsContextType {
  skills: ISkill[];
  setSkills: (skills: ISkill[]) => void;
}

const SkillsContext = createContext<SkillsContextType | undefined>(undefined);

export const SkillsProvider: React.FC<{ children: ReactNode; initialSkills: ISkill[] }> = ({
  children,
  initialSkills,
}) => {
  const [skills, setSkills] = useState<ISkill[]>(initialSkills);

  useEffect(() => {
    generateDefaultSkillConfig(skills).then(newSkills => {
      setSkills(newSkills);
    });
  }, []);

  return <SkillsContext.Provider value={{ skills, setSkills }}>{children}</SkillsContext.Provider>;
};

export const useSkills = () => {
  const context = useContext(SkillsContext);
  if (context === undefined) {
    throw new Error('useSkills must be used within a SkillsProvider');
  }
  return context;
};
