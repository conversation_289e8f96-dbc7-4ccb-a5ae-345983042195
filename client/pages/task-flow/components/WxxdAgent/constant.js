export const PAGE_STATUS = {
  GUIDE: 'guide',
  INIT: 'init',
  DASHBOARD: 'dashboard',
};

export const SenderRole = {
  /** 用户 */
  USER: 0,
  /** 助手/AI */
  ASSISTANT: 1,
  /** 系统 */
  SYSTEM: 2,
};

/** 智能体消息块类型 */
export const AgentMessageBlockTypeEnum = {
  /** 文本消息 */
  TEXT: 0,
  /** 图片消息 */
  IMAGE: 1,
  /** 按钮组消息 */
  BTN_GROUP: 2,
  /** 思维链消息 */
  THOUGHT_CHAIN: 3,
  /** 任务执行消息 */
  TASK_EXECUTION: 4,
};

export const AgentTaskExecutionStatusEnum = {
  /** 等待执行 */
  PENDING: 0,
  /** 正在执行 */
  RUNNING: 1,
  /** 执行完成 */
  COMPLETED: 2,
  /** 执行失败 */
  FAILED: 3,
};

export const Stage = {
  Intro: 'intro',
  Progress: 'progress',
  List: 'list',
};

/**
 * 消息状态枚举
 * 用于标识消息的发送状态
 */
export const MessageStatus = {
  /** 正在发送 */
  SENDING: 0,
  /** 发送完成 */
  DONE: 1,
  /** 发送错误 */
  ERROR: 2,
};

/**
 * 初始化进度
 */
export const INIT_PROCESS = {
  /** 自动学习 */
  AUTO_LEARNING: 1,
  /** 一键开店 */
  ONE_CLICK_STORE: 2,
  /** 预览方案 */
  PREVIEW_PLAN: 3,
  /** 执行方案 */
  EXECUTE_PLAN: 4,
};

export const Avatar = 'https://img01.yzcdn.cn/public_files/2025/03/19/3a4fd8a740fae369.svg';

export const Sender = { role: SenderRole.ASSISTANT, avatar: Avatar };

// 处理证件类型选项
export const CARD_TYPE_OPTIONS = [
  { key: 'IDENTIFICATION_TYPE_MAINLAND_IDCARD', text: '中国大陆居民-身份证' },
  {
    key: 'IDENTIFICATION_TYPE_OVERSEA_PASSPORT',
    text: '其他国家或地区居民-护照',
  },
  {
    key: 'IDENTIFICATION_TYPE_HONGKONG',
    text: '中国香港居民--来往内地通行证',
  },
  { key: 'IDENTIFICATION_TYPE_MACAO', text: '中国澳门居民--来往内地通行证' },
  { key: 'IDENTIFICATION_TYPE_TAIWAN', text: '中国台湾居民—-来往大陆通行证' },
  { key: 'IDENTIFICATION_TYPE_FOREIGN_RESIDENT', text: '外国人居留证' },
  { key: 'IDENTIFICATION_TYPE_HONGKONG_MACAO_RESIDENT', text: '港澳居民证' },
  { key: 'IDENTIFICATION_TYPE_TAIWAN_RESIDENT', text: '台湾居民证' },
];

// 结算账户类型选项
export const SETTLE_ACCOUNT_TYPE_OPTIONS = [
  { key: 74, text: '对公转账' },
  { key: 75, text: '对私转账' },
];

/** 身份类型(枚举类型待微信确认)  法人、经办人 */
export const SUPER_ADMIN_TYPE_OPTIONS = [
  { key: '65', text: '经营者/法人' },
  { key: '66', text: '经办人' },
];

export const BUSINESS_SUBJECT_TYPE_OPTIONS = [
  { key: 2, text: '企业' },
  { key: 4, text: '个体工商户' },
];

/** 开店类型  OpenStoreType_ENTERPRISE = 1; // 企业  OpenStoreType_INDIVIDUAL = 2; // 个人 */
export const OPEN_STORE_TYPE_OPTIONS = [
  { key: '1', text: '企业' },
  { key: '2', text: '个人' },
];

/** 店铺名称格式  1：{视频号}  2：{视频号}的小店 */
export const STORE_NAME_TYPE_OPTIONS = [
  { key: 1, text: '{视频号}' },
  { key: 2, text: '{视频号}的小店' },
];

export const SHOP_AUTH_STATUS = {
  /** 未开店 */
  NOT_OPEN: 0,
  /** 开店中 */
  OPENING: 1,
  /** 开店成功 */
  SUCCESS: 2,
  /** 开店失败 */
  FAILED: 3,
};

/**
 * 代开店状态
 */
export const SHOP_AGENT_CREATE_STATUS = {
  /** 未开店 */
  NOT_APPLY: 0,
  /** 开店中 */
  STARTE: 1,
  /** 开店已完成 */
  FINISHED: 2,
  /** 开店已终止 */
  CANCEL: 3,
};

/**
 * 代开店资料授权状态
 * https://qima.feishu.cn/wiki/DTrcwjf0Oi4rQnkNHXqcF0oan5b#share-RH0XdGfIloJgJAxWIHbcgNXGnSh
 */
export const CERT_AUDIT_STATUS = {
  /** 待关联店铺 */
  PENDING_RELATE_SHOP: 10,
  /** 数据初始完成 */
  INIT_COMPLETE: 20,
  INIT_COMPLETE_2: 21,
  /** 资料已提交，待审核 */
  PENDING_AUDIT: 30,
  /** 资料审核未通过 */
  REJECTED: 31,
  /** 账户待认证 */
  ACCOUNT_PENDING: 40,
  /** 账户审核失败 */
  ACCOUNT_FAILED: 41,
  /** 协议待签署 */
  AGREEMENT_PENDING: 50,
  /** 协议签署失败 */
  AGREEMENT_FAILED: 51,
  /** 协议签署成功 */
  AGREEMENT_SUCCESS: 52,
  /** 店铺信息提审 */
  SHOP_INFO_AUDIT: 60,
  /** 审核失败，信息待修改 */
  FAILED_INFO_MODIFY: 61,
  /** 审核通过，开店完成 */
  SUCCESS: 100,
};

export const OperatorType = {
  /** 小于 */
  LessThan: 1,
  /** 小于等于 */
  LessThanOrEqual: 2,
  /** 等于 */
  Equals: 3,
  /** 大于 */
  GreaterThan: 4,
  /** 大于等于 */
  GreaterThanOrEqual: 5,
  /** 包含 */
  In: 7,
};

export const OperatorText = {
  [OperatorType.Equals]: '=',
  [OperatorType.GreaterThan]: '>',
  [OperatorType.GreaterThanOrEqual]: '>=',
  [OperatorType.LessThan]: '<',
  [OperatorType.LessThanOrEqual]: '<=',
  [OperatorType.In]: '包含',
};

export const SkillTemplateIdMap = {
  /** 商品缺货自动补库存 */
  GoodsAutoStock: 20,
  /** 商品缺货自动提醒 */
  GoodsSoldoutReminder: 21,

  /** 订单收货信息修改提醒 */
  OrderPickupChangeReminder: 24,

  /** 订单发货超时提醒 */
  PromiseOrderTimeoutReminder: 25,
  /** 物流异常提醒 */
  PromiseLogisticsAnomalyReminder: 26,
  /** 未发货自动同意退款 */
  PromiseNoKuaidiAutoAgreeRefundReminder: 22,
  /** 已发货自动同意退货 */
  PromiseAutoAgreeReturnReminder: 23,

  /** 热卖商品自动置顶 */
  MarketingHotGoodsTop: 30,
  /** 送礼专区自动创建 */
  MarketingGiftZone: 50,
  /** 指定标签客户下单提醒 */
  MarketingTagCustomerOrderReminder: 39,

  // /** 微信小店托管周报 */
  WeeklyReport: 51,
};

export const SHOP_INIT_PROGRESS = {
  /** 未初始化/自动学习 */
  INIT: 1, // AUTO_LEARNING
  /** 一键开店 */
  ONE_CLICK_STORE: 2,
  /** 预览方案 */
  PREVIEW_PLAN: 3,
  /** 执行方案 */
  EXECUTE_PLAN: 4,
  /** 跳过 */
  SKIP: 10,
  /** 初始化完成 */
  SUCCESS: 100,
};
