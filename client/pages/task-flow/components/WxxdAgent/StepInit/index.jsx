import { useEffect, useState } from 'react';
import './index.scss';
import StepBar from '../common/StepBar';
import { INIT_PROCESS } from '../constant';
import { saveInitProgress } from '../api';
import { SkillsProvider } from '../context/SkillsContext';
import AutoLearning from './components/AutoLearningV2';
import OneClickStore from './components/OneClickStore';
import PlanPreview from './components/PlanPreview';
import ApplyPlan from './components/ApplyPlan';
import { querySkills } from 'pages/task-flow/api/agent';

const WxxdInit = props => {
  const { nextAgentStatus, initProgress, agentId, skills: initialSkills } = props;
  const [currentStep, setCurrentStep] = useState(initProgress);

  const nextStep = () => {
    const step = currentStep + 1;
    setCurrentStep(step);
    saveInitProgress({ value: String(step) });
  };

  return (
    <SkillsProvider initialSkills={initialSkills}>
      <div className="wxxd-init-container">
        <div className="wxxd-init-container__agent">
          <StepBar current={currentStep} />

          <div className={`wxxd-init-container__content ${`step-${currentStep}`}`}>
            {/* 自动学习 */}
            {currentStep === INIT_PROCESS.AUTO_LEARNING && (
              <AutoLearning autoHide={true} nextStep={nextStep} />
            )}

            {/* 一键开店 */}
            {currentStep === INIT_PROCESS.ONE_CLICK_STORE && <OneClickStore nextStep={nextStep} />}

            {/* 预览方案 */}
            {currentStep === INIT_PROCESS.PREVIEW_PLAN && (
              <PlanPreview agentId={agentId} nextStep={nextStep} />
            )}

            {/* 执行方案 */}
            {currentStep === INIT_PROCESS.EXECUTE_PLAN && (
              <ApplyPlan
                agentId={agentId}
                nextStep={() => {
                  nextAgentStatus();
                }}
              />
            )}
          </div>
        </div>
      </div>
    </SkillsProvider>
  );
};

export default WxxdInit;
