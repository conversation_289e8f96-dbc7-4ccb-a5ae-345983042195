import cx from 'classnames';
import React, { useCallback, useRef, useState } from 'react';
import {
  Dropdown,
  DropdownClickTrigger,
  DropdownContent,
  DropdownPosition,
  Icon,
  Menu,
} from 'zent';

import './index.scss';
import EditDrawerForInit from '../edit-drawer';
import { SkillTemplateIdMap } from 'pages/task-flow/components/WxxdAgent/constant';
import { useSkills } from 'pages/task-flow/components/WxxdAgent/context/SkillsContext';

const { MenuItem } = Menu;

const DocumentLayout = ({ title, subtitle, onEdit, sections, agentId }) => {
  // 引用内容区域的DOM元素
  const contentRefs = useRef({});
  const { skills, setSkills } = useSkills();

  // 处理目录项点击事件
  const handleMenuItemClick = id => {
    const element = contentRefs.current[id];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // 渲染左侧目录
  const renderMenu = () => {
    return (
      <div className="document-menu">
        <div className="document-menu-content">
          {sections.map((section, index) => (
            <div key={section.id} className="menu-section">
              <div className="menu-section-title" onClick={() => handleMenuItemClick(section.id)}>
                {section.title}
              </div>

              {section.subsections?.length > 0 && (
                <div className="menu-subsections">
                  {section.subsections.map((subsection, index) => (
                    <div
                      key={subsection.id}
                      className="menu-subsection-title"
                      onClick={() => handleMenuItemClick(subsection.id)}
                    >
                      {index + 1}. {subsection.title}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const handleConfirm = (skillTemplateId, values) => {
    onEdit(skillTemplateId, values);
  };

  const renderEditItem = skillTemplateId => {
    skillTemplateId = Number(skillTemplateId);
    const skill = skills.find(item => item.skillTemplateId === skillTemplateId);
    switch (skillTemplateId) {
      case SkillTemplateIdMap.MarketingHotGoodsTop:
        return null;
      default:
        return (
          <EditDrawerForInit
            skill={skill}
            agentId={agentId}
            onChange={data => {
              const index = skills.findIndex(item => item.skillTemplateId === skillTemplateId);
              skills[index].config.flowRule = data.skill.config?.flowRule;
              setSkills([...skills]);
            }}
          />
        );
    }
  };

  const filterEnable = ({ skillTemplateId }) => {
    if (!skillTemplateId) {
      return true;
    }
    const skill = skills.find(item => item.skillTemplateId === Number(skillTemplateId));
    return skill?.enable;
  };

  const filterDisable = ({ skillTemplateId }) => {
    if (!skillTemplateId) {
      return false;
    }
    const skill = skills.find(item => item.skillTemplateId === Number(skillTemplateId));
    return !skill?.enable;
  };

  const formatLabel = useCallback(
    point => {
      return point.formatLabel
        ? point.formatLabel(skills.find(item => item.skillTemplateId === point.skillTemplateId))
        : point.label;
    },
    [skills],
  );

  const formatDesc = useCallback(
    point => {
      return point.formatDesc
        ? point.formatDesc(skills.find(item => item.skillTemplateId === point.skillTemplateId))
        : point.desc;
    },
    [skills],
  );

  // 渲染右侧内容
  const renderContent = () => {
    return (
      <div className="document-content">
        {sections.map(section => {
          let useableBulletPoints = [];
          let showEmptyTip = section.bulletPoints?.filter(filterEnable).length === 0;
          if (section.useSubBulletPoints) {
            const flattenBulletPoints = [];
            section.bulletPoints.forEach(item => {
              if (item.subBulletPoints) {
                item.subBulletPoints.forEach(subItem => {
                  flattenBulletPoints.push(subItem);
                });
              }
            });
            useableBulletPoints = flattenBulletPoints.filter(filterDisable);
            showEmptyTip = flattenBulletPoints.filter(filterEnable).length === 0;
          } else {
            useableBulletPoints = section.bulletPoints?.filter(filterDisable) ?? [];
          }

          return (
            <div
              key={section.id}
              className="content-section"
              ref={el => (contentRefs.current[section.id] = el)}
            >
              <div
                className={cx('content-section-content', {
                  'content-section-editable': section.editable,
                })}
                style={section?.style || {}}
              >
                {/* 标题 */}
                <h2 className="content-section-title">
                  <p>{section.title}</p>
                  {section.editable && (
                    <p className="editable-icon">{renderEditItem(section.skillTemplateId)}</p>
                  )}
                  {section.addable && useableBulletPoints.length > 0 && (
                    <Dropdown position={DropdownPosition.AutoBottomLeft}>
                      <DropdownClickTrigger>
                        <p className="editable-icon">
                          <Icon type="plus" className="editable-icon-icon" />
                          添加
                        </p>
                      </DropdownClickTrigger>
                      <DropdownContent>
                        <Menu
                          className="wxxd-agent-plan-preview__menu"
                          onClick={(_, skillTemplateId) => {
                            handleConfirm(skillTemplateId, {
                              enable: true,
                            });
                          }}
                        >
                          {useableBulletPoints.map(({ skillTemplateId, label, menu }) => {
                            return <MenuItem key={skillTemplateId}>{menu || label}</MenuItem>;
                          })}
                        </Menu>
                      </DropdownContent>
                    </Dropdown>
                  )}
                </h2>

                {/* 描述 */}
                {section.content && (
                  <ul className="section-content">
                    {section.content.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                )}

                {/* 技能列表 */}
                {section.bulletPoints && (
                  <ul className="section-content">
                    {section.bulletPoints.filter(filterEnable).map((point, index) => {
                      if (section.useSubBulletPoints) {
                        if (point.subBulletPoints.filter(filterEnable).length > 0) {
                          return (
                            <li key={`li-${index}-${point.id}`}>
                              <strong key={`p-${index}-${point.id}`}>{point.title}</strong>
                              <ol key={`ol-${index}-${point.id}`} className="section-content">
                                {point.subBulletPoints.filter(filterEnable).map((item, index) => (
                                  <li key={`li-${index}-${item.skillTemplateId}`}>
                                    <div className="info-item">
                                      <div>
                                        {item.label && <span>{item.label}</span>}
                                        {item.desc ? <>：{item.desc}</> : ''}
                                      </div>
                                      {/* 编辑技能 */}
                                      {item.editable && (
                                        <div className="editable-item">
                                          {/* 编辑 */}
                                          <div className="editable-item-icon">
                                            {renderEditItem(item.skillTemplateId)}
                                          </div>
                                          {/* 删除 */}
                                          <div
                                            className="editable-item-icon"
                                            onClick={() =>
                                              handleConfirm(item.skillTemplateId, {
                                                enable: false,
                                              })
                                            }
                                          >
                                            <Icon type="remove-o" className="editable-icon-icon" />
                                            删除
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </li>
                                ))}
                              </ol>
                            </li>
                          );
                        }
                        return null;
                      }
                      return (
                        <li
                          className={cx({
                            'info-item-editable': point.editable,
                          })}
                          key={point.skillTemplateId}
                        >
                          <div>
                            <div className="info-item">
                              <div>
                                {point.label && <strong>{formatLabel(point)}</strong>}
                                {point.desc ? <>：{formatDesc(point)}</> : ''}
                              </div>
                              {point.editable && (
                                <div className="editable-item">
                                  {/* 编辑 */}
                                  <div className="editable-item-icon">
                                    {renderEditItem(point.skillTemplateId)}
                                  </div>
                                  {/* 删除 */}
                                  <div
                                    className="editable-item-icon"
                                    onClick={() =>
                                      handleConfirm(point.skillTemplateId, {
                                        enable: false,
                                      })
                                    }
                                  >
                                    <Icon type="remove-o" className="editable-icon-icon" />
                                    删除
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </li>
                      );
                    })}

                    {section.emptyTip && showEmptyTip && (
                      <p className="section-content-empty">{section.emptyTip}</p>
                    )}
                  </ul>
                )}
                {section.subsections?.map((subsection, index) => (
                  <div
                    key={subsection.id}
                    className="content-subsection"
                    ref={el => (contentRefs.current[subsection.id] = el)}
                  >
                    <div className="content-subsection-item">
                      <h3 className="content-subsection-title">
                        {index + 1}. {subsection.title}
                      </h3>
                      {subsection.editable && (
                        <div className="editable-item">
                          <p className="editable-item-icon">
                            <Icon type="edit-o" className="editable-icon-icon" />
                            修改
                          </p>
                          {/* <p
                                className="editable-item-icon"
                                onClick={() => props.handleDelete(point.key)}
                              >
                                <Icon
                                  type="remove-o"
                                  className="editable-icon-icon"
                                />
                                删除
                              </p> */}
                        </div>
                      )}
                    </div>
                    {/* <h3 className="content-subsection-title">
                      {index + 1}. {subsection.title}
                    </h3> */}

                    {subsection.content && <div className="content-text">{subsection.content}</div>}
                    {subsection.bulletPoints.length > 0 && (
                      <ul className="section-content">
                        {subsection.bulletPoints.map((point, index) => {
                          return (
                            <li key={index}>
                              <p className="info-item">{point.desc}</p>
                            </li>
                          );
                        })}
                      </ul>
                    )}
                    {subsection.bulletPointsExtra && (
                      <div className="point-extra">{subsection.bulletPointsExtra}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="document-layout">
      <div className="document-container">
        <div className="document-header">
          <h1 className="document-title">{title}</h1>
          <div className="document-subtitle">{subtitle}</div>
        </div>
        <div className="document-body">
          {renderMenu()}
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default DocumentLayout;
