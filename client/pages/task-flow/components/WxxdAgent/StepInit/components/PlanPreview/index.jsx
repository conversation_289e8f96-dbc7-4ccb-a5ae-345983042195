/* eslint-disable react/prop-types */
import { useState, useEffect, useCallback } from 'react';
import { BlockLoading, Button } from 'zent';
import { getFormattedDate } from '../../../utils';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import Container from '../ui/container';
import DocumentLayout from './components/document-layout';
import Footer from '../ui/footer';

import './index.scss';
import { AgentMessageBlockTypeEnum, SkillTemplateIdMap } from '../../../constant';
import { AgentBlock as AgentMessageBlock } from '@youzan/agent-components';
import { useSkills } from '../../../context/SkillsContext';
import { getPreviewPlanText } from '../AutoLearningV2/learningText';
import { formatSkillDesc } from '../../../utils/formatSkill';

const learningConfigs = {
  type: AgentMessageBlockTypeEnum.THOUGHT_CHAIN,
  isStreamTyping: true,
  thoughtChain: {
    isAutoCollapse: true,
    title: '正在深度思考...',
    currentStep: 0,
    steps: [
      {
        topic: '正在分析小店运行目标',
        content: getPreviewPlanText(),
      },
    ],
  },
};
const previewConfigs = {
  type: AgentMessageBlockTypeEnum.TEXT,
  markdown: '我为你生成了可以自由编辑的托管方案，请确认方案：',
  isStreamTyping: true,
};

const timeoutConfig = {
  previewBlock: 18000,
  document: 18000,
  footer: 19000,
};

const PlanPreview = ({ nextStep, agentId }) => {
  const [state, setState] = useState({
    shopName: '',
    showPreviewBlock: false,
    showDocument: false,
    showFooter: false,
    loading: false,
  });
  const { skills, setSkills } = useSkills();

  const timers = [];

  useEffect(() => {
    timers.push(
      setTimeout(() => {
        setState(prev => ({ ...prev, showPreviewBlock: true }));
        learningConfigs.thoughtChain.title = '已深度思考';
        learningConfigs.thoughtChain.currentStep = 1;
        learningConfigs.thoughtChain.steps[0].topic = '已分析小店运营目标';
      }, timeoutConfig.previewBlock),
      setTimeout(() => {
        setState(prev => ({ ...prev, showDocument: true }));
      }, timeoutConfig.document),
    );

    setState(prev => ({ ...prev, shopName: _global.shopInfo.shopName }));

    return () => {
      timers.forEach(clearTimeout);
    };
  }, []);

  const handleEdit = useCallback(
    (skillTemplateId, value) => {
      const updatedSkills = skills.map(item => {
        if (item.skillTemplateId === Number(skillTemplateId)) {
          return { ...item, enable: value.enable };
        }
        return item;
      });
      setSkills(updatedSkills);
    },
    [skills, setSkills],
  );

  const getSections = () => {
    return [
      {
        id: 'section-1',
        title: '一、运营目标',
        content: ['提升微信小店的运营效率，降低运营投入成本', '助力小店销量提升'],
      },
      {
        id: 'section-2',
        key: '',
        title: '二、商品托管',
        addable: true,
        emptyTip: '暂未配置商品托管方案',
        bulletPoints: [
          {
            menu: '缺货自动补库存',
            label: '库存缺货时',
            desc: <span>自动补库存</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.GoodsAutoStock,
            formatLabel(skill) {
              const result = formatSkillDesc(skill);
              return result.condition;
            },
            formatDesc(skill) {
              const result = formatSkillDesc(skill);
              return result.action;
            },
          },
          {
            menu: '缺货自动提醒',
            label: '库存为 0 时',
            desc: <span>给店铺管理员发送提醒</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.GoodsSoldoutReminder,
            formatLabel(skill) {
              const result = formatSkillDesc(skill);
              return result.condition;
            },
            formatDesc(skill) {
              const result = formatSkillDesc(skill);
              return result.action;
            },
          },
        ],
      },
      {
        id: 'section-3',
        key: '',
        title: '三、订单托管',
        emptyTip: '暂未配置订单托管方案',
        addable: true,
        bulletPoints: [
          {
            label: '订单信息变更时',
            desc: <span>给店铺管理员发送提醒</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.OrderPickupChangeReminder,
          },
        ],
      },
      {
        id: 'section-4',
        key: '',
        title: '四、履约托管',
        emptyTip: '暂未配置履约托管方案',
        addable: true,
        bulletPoints: [
          {
            label: '订单发货超时提醒',
            desc: <span>订单超时未发货，通知店铺管理员</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.PromiseOrderTimeoutReminder,
          },
          {
            label: '物流异常时',
            desc: <span>通知店铺管理员</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.PromiseLogisticsAnomalyReminder,
          },
        ],
      },
      {
        id: 'section-5',
        key: '',
        title: '五、营销托管',
        emptyTip: '暂未配置营销托管方案',
        addable: true,
        useSubBulletPoints: true,
        bulletPoints: [
          {
            title: '热销商品自动置顶',
            subBulletPoints: [
              {
                menu: '热销商品自动置顶',
                label: '将微信小店内的热销商品自动调整到店铺首页顶部',
                editable: true,
                key: '',
                skillTemplateId: SkillTemplateIdMap.MarketingHotGoodsTop,
              },
            ],
          },
          {
            title: '自动搭建送礼专区',
            subBulletPoints: [
              {
                menu: '自动搭建送礼专区',
                label: '在微信小店顶部自动搭建送礼专区',
                editable: true,
                key: '',
                skillTemplateId: SkillTemplateIdMap.MarketingGiftZone,
              },
            ],
          },
        ],
      },
    ];
  };

  const handleNextClick = useCallback(() => {
    nextStep();
  }, [skills, nextStep]);

  const { showPreviewBlock, showDocument, showFooter, loading, shopName } = state;

  const title = `${shopName ? `${shopName} | ` : ''}微信小店托管方案`;
  const subtitle = `运营专员：微信小店托管智能体  |  汇报时间：${getFormattedDate()}`;

  return (
    <div className="plan-preview">
      <BlockLoading loading={loading}>
        <Container>
          <AgentBlockWrapper>
            <AgentMessageBlock block={learningConfigs} />
            {showPreviewBlock && <div className="plan-preview__divider"></div>}
            {showPreviewBlock && <AgentMessageBlock block={previewConfigs} />}
            {showDocument && (
              <DocumentLayout
                title={title}
                subtitle={subtitle}
                sections={getSections()}
                onEdit={handleEdit}
                agentId={agentId}
              />
            )}
          </AgentBlockWrapper>
          {showDocument && (
            <Footer>
              <Button style={{ margin: '10px 0 0 50px' }} onClick={handleNextClick} type="primary">
                确认托管方案，一键执行
              </Button>
            </Footer>
          )}
        </Container>
      </BlockLoading>
    </div>
  );
};

export default PlanPreview;
