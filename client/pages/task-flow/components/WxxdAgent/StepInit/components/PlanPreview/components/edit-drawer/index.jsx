import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Icon } from 'zent';
import './index.scss';
import EditSkillDrawer from 'pages/task-flow/components/EditSkillDrawer';
import { flowAtom } from 'pages/work-flow/atoms';
import { useAtom } from 'jotai';

const EditDrawerForInit = ({ skill, agentId, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [flowData, setFlowData] = useAtom(flowAtom);

  const handleSubmit = async () => {
    setLoading(true);
    setVisible(false);
    setLoading(false);
  };

  const {
    config: { flowTemplate, flowRule },
    initialized,
  } = skill ?? {};

  useEffect(() => {
    if (visible) {
      setFlowData(flowTemplate);
    }
  }, [visible, flowTemplate]);

  return (
    <>
      <span className="edit-action" onClick={() => setVisible(true)}>
        <Icon className="edit-icon" type="edit-o" />
        编辑
      </span>
      <EditSkillDrawer
        // refreshSkills={refreshSkills}
        visible={visible}
        onClose={() => setVisible(false)}
        skill={skill}
        agentId={agentId}
        onlyEdit={true}
        onChange={onChange}
      />
    </>
  );
};

export default EditDrawerForInit;
