import React from 'react';
import FormSectionWrapper from './FormSectionWrapper';
import { SUPER_ADMIN_TYPE_OPTIONS } from '../../../../../../constant';

export default function SuperAdmin({
  detail,
  updateXdDetail,
  isEditing = true,
  drawer = false,
  filter,
}) {
  // const currentComponents4LegalEntity = [
  //   'superAdminType',
  //   'superAdminName',
  //   'superAdminCardNo',
  //   'superAdminPhone',
  //   'superAdminEmail',
  //   'superAdminAddress',
  // ];

  const currentComponents4Agent = [
    'superAdminType',
    'superAdminCardType',
    '__superAdminCard',
    '__superAdminDate',
    'superAdminName',
    'superAdminCardNo',
    'superAdminPhone',
    'superAdminEmail',
    'superAdminAddress',
  ];

  // 根据超级管理员类型选择对应的组件列表
  const getComponents = detail => {
    return currentComponents4Agent;
    // return detail.superAdmin?.type === SUPER_ADMIN_TYPE_OPTIONS[0].key
    //   ? currentComponents4LegalEntity
    //   : currentComponents4Agent;
  };

  let components = getComponents(detail);

  if (filter) {
    components = filter(components);
  }

  return (
    <FormSectionWrapper
      title="超级管理员信息"
      tip="请使用开店时扫码授权人的身份信息"
      components={components}
      detail={detail}
      updateXdDetail={updateXdDetail}
      isEditing={isEditing}
      drawer={drawer}
    />
  );
}
