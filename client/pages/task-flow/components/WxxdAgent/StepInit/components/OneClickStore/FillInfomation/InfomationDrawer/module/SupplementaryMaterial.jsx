import React from 'react';
import FormSectionWrapper from './FormSectionWrapper';

export default function SupplementaryMaterial({
  detail,
  updateXdDetail,
  isEditing = true,
  drawer = false,
  filter,
}) {
  let components = []
    .concat(detail.specialQualification.needSpecialQua ? ['specialQuaCerts'] : [])
    .concat(['supplementaryCerts', 'supplementaryDesc']);

  if (filter) {
    components = filter(components);
  }

  return (
    <FormSectionWrapper
      title="补充资质材料"
      components={components}
      detail={detail}
      updateXdDetail={updateXdDetail}
      isEditing={isEditing}
      drawer={drawer}
    />
  );
}
