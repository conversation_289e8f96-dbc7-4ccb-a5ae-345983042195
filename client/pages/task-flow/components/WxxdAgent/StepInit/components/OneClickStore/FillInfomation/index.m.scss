.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid rgba(242, 246, 255, 0.67);
  border-radius: 8px;
  // max-height: 540px;
}

.title {
  font-size: 16px;
  line-height: 1.4;
  color: #333333;
  margin-bottom: 20px;
}

.infoBox {
  box-sizing: border-box;
  background: #ffffff;
  padding: 20px;
  width: 670px;
  height: 102px;
}

.divider {
  width: 100%;
  height: 1px;
  background: #eeeeee;
  margin: 20px 0;
}

.missInfoBox {
  box-sizing: border-box;
  background: #ffffff;
  padding: 20px;
  width: 670px;
  // max-height: 300px;
  // overflow-y: auto;
}

.infoHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.infoTitle {
  font-size: 16px;
  color: #333333;
  display: flex;
  align-items: center;
}

.infoIcon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  color: #999999;
}

.infoLink {
  font-size: 16px;
  color: #155bd4;
  cursor: pointer;
}

.infoDesc {
  width: 450px;
  margin-left: 22px;
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
}
