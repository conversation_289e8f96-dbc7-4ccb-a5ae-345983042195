import React, { Component } from 'react';
import { InlineLoading } from 'zent';
import styles from './index.m.scss';
import { AgentBlock } from '@youzan/agent-components';
import { AgentMessageBlockTypeEnum } from 'pages/task-flow/components/WxxdAgent/constant';

export default function CheckBindStatus() {
  return (
    <div className={styles.container}>
      <InlineLoading loading icon="circle" textPosition="right" />
      <AgentBlock
        block={{
          type: AgentMessageBlockTypeEnum.TEXT,
          isStreamTyping: true,
          markdown: '正在检测你的微信小店状态，请稍等...',
        }}
      />
    </div>
  );
}
