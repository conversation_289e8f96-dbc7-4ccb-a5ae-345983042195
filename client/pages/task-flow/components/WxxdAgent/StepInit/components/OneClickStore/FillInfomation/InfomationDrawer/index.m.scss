.drawerFooter {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  gap: 8px;
}

.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.editButton {
  position: fixed;
  right: 20px;
  top: 65px;
  margin-left: 16px;
  cursor: pointer;
}

.editIcon {
  width: 20px;
  height: 20px;
}

.dateSeparator {
  padding: 0 2px;
}

.DatePermanently {
  border-color: #e0e0e0;
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
  background-color: #fff;
  height: 32px;
  width: 150px;
  padding: 0 10px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
}
.calendarIcon {
  font-size: 18px;
  color: #ccc;
}

.DatePermanentlySwitch {
  margin-left: 8px;
}

.tipsContainer {
  padding: 16px;
}
