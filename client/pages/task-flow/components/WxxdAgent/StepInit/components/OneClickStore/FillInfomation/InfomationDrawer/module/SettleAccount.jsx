import React from 'react';
import FormSectionWrapper from './FormSectionWrapper';

export default function SettleAccount({
  detail,
  updateXdDetail,
  isEditing = true,
  drawer = false,
  filter,
}) {
  let settleAccountComponents = [
    'settleAccountType',
    'settleAccountName',
    'settleAccountNo',
    'settleAccountBankName',
    '__settleAccountRegion',
  ];

  if (filter) {
    settleAccountComponents = filter(settleAccountComponents);
  }

  return (
    <FormSectionWrapper
      title="结算账户信息"
      components={settleAccountComponents}
      detail={detail}
      updateXdDetail={updateXdDetail}
      isEditing={isEditing}
      drawer={drawer}
    />
  );
}
