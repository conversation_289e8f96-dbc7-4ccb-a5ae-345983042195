import { useState, useEffect } from 'react';
import Bind<PERSON><PERSON><PERSON> from './BindYouzan';
import FillInfomation from './FillInfomation';
import { Button, Icon, InlineLoading, Notify } from 'zent';
import { queryShopBindAccount, editAndSubmit } from '../../../api';
import { AgentMessageBlockTypeEnum } from '../../../constant';

import styles from './index.m.scss';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import { AgentBlock } from '@youzan/agent-components';
import { wait } from '../../../utils';
import { FormFieldComponents } from './FillInfomation/InfomationDrawer/module/configs/components';
import { get, isEmpty } from 'lodash';

const statusMap = {
  // 检查绑定状态
  CHECK: 0,
  // 已绑定有赞
  BINDED_YOUZAN: 1,
  // 未绑定有赞
  UNBIND_YOUZAN: 2,
  // 收集资料
  COLLECT_INFORMATION: 3,
};

const OneClickStore = ({ nextStep }) => {
  const [status, setStatus] = useState(statusMap.CHECK);
  const [isLoadDetail, setIsLoadDetail] = useState(false);
  const [detail, setDetail] = useState({});
  const [collectProcess, setCollectProcess] = useState(0);
  const [missingInfos, setMissingInfos] = useState([]);

  useEffect(() => {
    Promise.all([queryShopBindAccount(), wait(1500)]).then(([shopBindAccount, _]) => {
      if (shopBindAccount.length > 0) {
        handleBindShop();
      } else {
        setStatus(statusMap.UNBIND_YOUZAN);
      }
    });
  }, []);

  const onAgentCreateShop = async detailData => {
    setStatus(statusMap.COLLECT_INFORMATION);
    setTimeout(() => {
      if (detailData) {
        setDetail(detailData);
        setIsLoadDetail(true);
      }
    }, 2000);
  };

  const handleBindShop = () => {
    setStatus(statusMap.BINDED_YOUZAN);
    wait(1000).then(() => {
      nextStep();
    });
  };

  useEffect(() => {
    if (!isEmpty(detail) && missingInfos.length === 0) {
      let missingInfos = [];
      Object.keys(FormFieldComponents).forEach(key => {
        const config = FormFieldComponents[key];
        if (config.required) {
          let hasMissing = false;
          if (config.keys) {
            hasMissing = config.keys.some(subKey => {
              return !get(detail, `${config.keyPath}.${subKey}`);
            });
          } else {
            const value = get(detail, config.keyPath);
            hasMissing = !value;
          }

          if (config.enableKeyPath && !get(detail, config.enableKeyPath)) {
            hasMissing = false;
          }

          if (hasMissing) {
            missingInfos.push(key);
          }
        }
      });
      setMissingInfos(missingInfos);
    }
  }, [detail]);

  // 验证是否必填项都填了
  const validateMissingInfo = data => {
    return missingInfos.some(info => {
      const config = FormFieldComponents[info];
      let hasMissing = false;

      if (config.keys) {
        hasMissing = config.keys.some(subKey => {
          return !get(data, `${config.keyPath}.${subKey}`);
        });
      } else {
        hasMissing = !get(data, config.keyPath);
      }

      return hasMissing;
    });
  };

  return (
    <div className={styles.container}>
      <AgentBlockWrapper>
        {/* 检查状态 */}
        {status === statusMap.CHECK && (
          <div className={styles.checkContainer}>
            <InlineLoading loading icon="circle" textPosition="right" />
            <AgentBlock
              block={{
                type: AgentMessageBlockTypeEnum.TEXT,
                isStreamTyping: true,
                markdown: '正在检测你的微信小店状态，请稍等...',
              }}
            />
          </div>
        )}

        {/* 已绑定有赞，直接下一步 */}
        {status === statusMap.BINDED_YOUZAN && (
          <div className={styles.checkContainer}>
            <Icon type="check" className={styles.checkSuccessIcon} />
            <AgentBlock
              block={{
                type: AgentMessageBlockTypeEnum.TEXT,
                isStreamTyping: true,
                markdown: '检测到你已完成微信小店授权，自动进入下一步',
              }}
            />
          </div>
        )}

        {/* 未绑定有赞 */}
        {status === statusMap.UNBIND_YOUZAN && (
          <BindYouzan onAgentCreateShop={onAgentCreateShop} onBindShop={handleBindShop} />
        )}

        {/* 收集资料 */}
        {status === statusMap.COLLECT_INFORMATION && (
          <>
            {isLoadDetail ? (
              <FillInfomation
                detail={detail}
                onChange={value => {
                  setDetail(value);
                }}
                missingInfos={missingInfos}
              />
            ) : (
              <AgentBlock
                block={{
                  type: AgentMessageBlockTypeEnum.TEXT,
                  isStreamTyping: true,
                  markdown: '你已授权有赞代开店，正在收集开店资料，请耐心等待...',
                }}
              />
            )}
          </>
        )}
      </AgentBlockWrapper>

      {isLoadDetail && (
        <div className={styles.submitBtnBox}>
          <Button
            style={{
              width: 288,
              height: 40,
            }}
            className={styles.submitBtn}
            type="primary"
            onClick={() => {
              const missing = validateMissingInfo(detail);
              if (missing) {
                Notify.error(`请将以上内容填写完整后再提交`);
                return;
              }

              editAndSubmit(detail)
                .then(res => {
                  nextStep();
                })
                .catch(err => {
                  Notify.error('提交失败，请稍后重试');
                });
            }}
          >
            确认并提交开店资料
          </Button>
        </div>
      )}
    </div>
  );
};

export default OneClickStore;
