import { FieldType } from 'pages/task-flow/components/WxxdAgent/common/formly/form-field/fieldType';
import {
  BUSINESS_SUBJECT_TYPE_OPTIONS,
  CARD_TYPE_OPTIONS,
  OPEN_STORE_TYPE_OPTIONS,
  SETTLE_ACCOUNT_TYPE_OPTIONS,
  STORE_NAME_TYPE_OPTIONS,
  SUPER_ADMIN_TYPE_OPTIONS,
} from '../../../../../../../constant';

const SuperAdminFields = {
  // SuperAdmin 组件字段
  superAdminType: {
    keyPath: 'superAdmin.type',
    type: FieldType.RADIO,
    required: true,
    label: '超级管理员类型',
    options: SUPER_ADMIN_TYPE_OPTIONS,
  },
  superAdminName: {
    keyPath: 'superAdmin.personCardName',
    type: FieldType.TEXT,
    required: true,
    label: '姓名',
  },
  superAdminCardNo: {
    // condition: formData => formData.superAdmin?.type === SUPER_ADMIN_TYPE_OPTIONS[1].key,
    keyPath: 'superAdmin.personCardNo',
    type: FieldType.TEXT,
    required: true,
    label: '证件号码',
  },
  superAdminCardType: {
    // condition: formData => formData.superAdmin?.type === SUPER_ADMIN_TYPE_OPTIONS[1].key,
    keyPath: 'superAdmin.personCardType',
    type: FieldType.SELECT,
    required: true,
    label: '证件类型',
    options: CARD_TYPE_OPTIONS,
  },
  __superAdminCard: {
    // condition: formData => formData.superAdmin?.type === SUPER_ADMIN_TYPE_OPTIONS[1].key,
    keyPath: 'superAdmin',
    keys: ['personCardPortrait', 'personCardNation'],
    type: FieldType.CARD_IMAGE_UPLOADER,
    required: true,
    label: '证件照片',
  },
  __superAdminDate: {
    // condition: formData => formData.superAdmin?.type === SUPER_ADMIN_TYPE_OPTIONS[1].key,
    keyPath: 'superAdmin',
    keys: ['personCardStartDate', 'personCardEndDate'],
    type: FieldType.DATE_RANGE_PICKER,
    required: true,
    label: '证件有效期',
  },
  superAdminPhone: {
    keyPath: 'superAdmin.phoneNumber',
    type: FieldType.TEXT,
    required: true,
    label: '联系方式',
  },
  superAdminEmail: {
    keyPath: 'superAdmin.email',
    type: FieldType.TEXT,
    required: true,
    label: '邮箱',
  },
  superAdminAddress: {
    keyPath: 'superAdmin.personCardAddress',
    type: FieldType.TEXT,
    required: true,
    label: '证件居住地址',
  },
};
const SettleAccountFields = {
  // SettleAccount 组件字段
  settleAccountType: {
    keyPath: 'settleAccount.accountType',
    type: FieldType.SELECT,
    required: true,
    label: '账户类型',
    options: SETTLE_ACCOUNT_TYPE_OPTIONS,
  },
  settleAccountName: {
    keyPath: 'settleAccount.accountName',
    type: FieldType.TEXT,
    required: true,
    label: '开户名称',
  },
  settleAccountNo: {
    keyPath: 'settleAccount.bankAccountNo',
    type: FieldType.TEXT,
    required: true,
    label: '银行卡号',
  },
  settleAccountBankName: {
    keyPath: 'settleAccount',
    keys: ['bankName', 'bankCode'],
    type: FieldType.BANK_SELECT,
    required: true,
    label: '开户银行名称',
  },
  __settleAccountRegion: {
    keyPath: 'settleAccount',
    keys: ['provinceCode', 'cityCode'],
    type: FieldType.REGION_SELECT,
    required: true,
    label: '开户地区',
  },
};
const StoreBasicFields = {
  // StoreBasic 组件字段
  storeBasicType: {
    keyPath: 'storeBasic.openStoreType',
    type: FieldType.SELECT,
    required: true,
    label: '开店类型',
    options: OPEN_STORE_TYPE_OPTIONS,
  },
  storeBasicCustomerName: {
    keyPath: 'storeBasic.customerName',
    type: FieldType.TEXT,
    required: true,
    label: '店铺自定义名称',
  },
  // storeNameType: {
  //   keyPath: 'storeBasic.storeNameType',
  //   type: FieldType.SELECT,
  //   required: true,
  //   label: '店铺名称格式',
  //   options: STORE_NAME_TYPE_OPTIONS,
  // },
  storeBasicAvatar: {
    keyPath: 'storeBasic.headImage',
    type: FieldType.IMAGE_UPLOADER,
    required: true,
    label: '店铺头像',
    tips: '请上传店铺头像，尺寸400x400像素，大小不超过5MB',
  },
  storeIntroduction: {
    keyPath: 'storeBasic.introduction',
    type: FieldType.TEXT,
    required: false,
    label: '店铺简介',
  },
  // 品牌信息字段
  // storeBasicBrandMerchant: {
  //   keyPath: 'storeBasic.brandMerchant',
  //   type: FieldType.SWITCH,
  //   required: false,
  //   label: '是否品牌商家',
  // },
  brandInfoName: {
    enableKeyPath: 'storeBasic.brandMerchant',
    keyPath: 'storeBasic.brandInfo.brandName',
    type: FieldType.BRAND_SELECT,
    required: true,
    label: '品牌名称',
  },
  brandInfoSelfOperated: {
    enableKeyPath: 'storeBasic.brandMerchant',
    keyPath: 'storeBasic.brandInfo.selfOperated',
    type: FieldType.SWITCH,
    required: false,
    label: '是否自营',
  },
  brandInfoRegisterNo: {
    enableKeyPath: 'storeBasic.brandMerchant',
    keyPath: 'storeBasic.brandInfo.registerNo',
    type: FieldType.TEXT,
    required: true,
    label: '商标注册号',
  },
  brandInfoRegisterCertifications: {
    enableKeyPath: 'storeBasic.brandMerchant',
    keyPath: 'storeBasic.brandInfo.registerCertifications',
    type: FieldType.MULTI_IMAGE_UPLOADER,
    required: true,
    label: '商标注册证',
    tips: '图片800px*800px以上，大小不超过10MB',
  },
  brandInfoRenewCertifications: {
    enableKeyPath: 'storeBasic.brandMerchant',
    keyPath: 'storeBasic.brandInfo.renewCertifications',
    type: FieldType.MULTI_IMAGE_UPLOADER,
    required: true,
    label: '变更/存续证明',
    tips: '图片800px*800px以上，大小不超过10MB',
  },
  // 店铺补充信息
  storeBasicSupplementaryUseAuthorizationImage: {
    keyPath: 'storeBasic.supplementary.useAuthorizationImage',
    type: FieldType.IMAGE_UPLOADER,
    required: false,
    label: '授权图片(选填)',
  },
  storeBasicSupplementaryAssistImages: {
    keyPath: 'storeBasic.supplementary.assistImages',
    type: FieldType.MULTI_IMAGE_UPLOADER,
    required: false,
    label: '补充资料(选填)',
  },
  storeBasicSupplementaryDesc: {
    keyPath: 'storeBasic.supplementary.desc',
    type: FieldType.TEXT,
    required: false,
    label: '补充说明(选填)',
  },
};
const BusinessSubjectFields = {
  // 营业执照
  businessLicenseUrl: {
    keyPath: 'businessSubject.businessLicenseUrl',
    type: FieldType.IMAGE_UPLOADER,
    required: true,
    label: '营业执照',
    tips: '请上传清晰的营业执照照片，大小不超过5MB',
  },
  // 主体名称
  businessMerchantName: {
    keyPath: 'businessSubject.businessMerchantName',
    type: FieldType.TEXT,
    required: true,
    label: '商户名称',
  },
  // 证件号/社会信用代码
  businessSocialCreditCode: {
    keyPath: 'businessSubject.businessSocialCreditCode',
    type: FieldType.TEXT,
    required: true,
    label: '社会信用代码',
  },
  // 主体类型
  businessSubjectType: {
    keyPath: 'businessSubject.businessSubjectType',
    type: FieldType.RADIO,
    required: true,
    label: '主体类型',
    options: BUSINESS_SUBJECT_TYPE_OPTIONS,
  },
  businessRegisterAddress: {
    keyPath: 'businessSubject.businessRegisterAddress',
    type: FieldType.TEXT,
    required: true,
    label: '注册地址',
  },

  // 营业执照有效期
  __businessDate: {
    keyPath: 'businessSubject',
    keys: ['businessStartDate', 'businessEndDate'],
    type: FieldType.DATE_RANGE_PICKER,
    required: true,
    label: '营业执照有效期',
  },

  personCardType: {
    keyPath: 'businessSubject.personCardType',
    type: FieldType.SELECT,
    required: true,
    label: '法人证件类型',
    options: CARD_TYPE_OPTIONS,
  },

  // 证件照片
  __personCard: {
    keyPath: 'businessSubject',
    keys: ['personCardPortrait', 'personCardNation'],
    type: FieldType.CARD_IMAGE_UPLOADER,
    required: true,
    label: '法人证件照片',
  },

  personCardName: {
    keyPath: 'businessSubject.personCardName',
    otherKeyPaths: ['businessSubject.businessPersonName'],
    type: FieldType.TEXT,
    required: true,
    label: '法人证件姓名',
  },
  personCardNo: {
    keyPath: 'businessSubject.personCardNo',
    type: FieldType.TEXT,
    required: true,
    label: '法人证件号码',
  },
  personCardAddress: {
    keyPath: 'businessSubject.personCardAddress',
    type: FieldType.TEXT,
    required: true,
    label: '法人证件居住地址',
  },

  __personCardDate: {
    keyPath: 'businessSubject',
    keys: ['personCardStartDate', 'personCardEndDate'],
    type: FieldType.DATE_RANGE_PICKER,
    required: true,
    label: '法人证件有效期',
  },
};

const SpecialQualificationFields = {
  // SpecialQualification 组件字段
  // specialQuaNeed: {
  //   keyPath: 'specialQualification.needSpecialQua',
  //   type: FieldType.SWITCH,
  //   required: false,
  //   label: '是否需要特殊资质',
  // },
  specialQuaCerts: {
    enableKeyPath: 'specialQualification.needSpecialQua',
    keyPath: 'specialQualification.specialQuaCerts',
    type: FieldType.MULTI_IMAGE_UPLOADER,
    required: true,
    label: '特殊资质材料',
    tips: '请上传特殊资质材料图片，每张大小不超过10MB',
  },
  // SupplementaryMaterial 组件字段
  supplementaryCerts: {
    keyPath: 'supplementaryMaterial.certs',
    type: FieldType.MULTI_IMAGE_UPLOADER,
    required: false,
    label: '补充资质材料',
  },
  supplementaryDesc: {
    keyPath: 'supplementaryMaterial.desc',
    type: FieldType.TEXT,
    required: false,
    label: '补充资质描述',
  },
};
export const FormFieldComponents = {
  ...BusinessSubjectFields,
  ...SuperAdminFields,
  ...SettleAccountFields,
  ...StoreBasicFields,
  ...SpecialQualificationFields,
};
