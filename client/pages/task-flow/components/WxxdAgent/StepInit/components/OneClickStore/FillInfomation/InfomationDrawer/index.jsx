import { useEffect, useState } from 'react';
import { I<PERSON>, Drawer, But<PERSON> } from 'zent';
import styles from './index.m.scss';
import BusinessSubject from './module/BusinessSubject';
import SuperAdmin from './module/SuperAdmin';
import SettleAccount from './module/SettleAccount';
import StoreBasic from './module/StoreBasic';
import SupplementaryMaterial from './module/SupplementaryMaterial';
import WxxdTips from 'pages/task-flow/components/WxxdAgent/common/WxxdTips';
import { cloneDeep } from 'lodash';

export default function InfomationDrawer({
  visible,
  loading,
  onClose,
  onChange,
  detail = null,
  editMode = false,
}) {
  const [formData, setFormData] = useState(detail);
  const [isEditing, setIsEditing] = useState(editMode);

  useEffect(() => {
    setFormData(cloneDeep(detail));
  }, [detail, visible]);

  const handleSubmit = () => {
    onChange(formData);
    onClose();
  };

  const toggleEditMode = () => {
    setIsEditing(!isEditing);
    if (isEditing) {
      // 点击取消按钮时，重置为初始数据
      setFormData(cloneDeep(detail));
    }
  };

  const handleClose = () => {
    // 关闭弹窗时重置数据状态
    setFormData(cloneDeep(detail));
    setIsEditing(editMode);
    onClose();
  };

  return (
    <Drawer
      title="开店资料"
      loading={loading}
      visible={visible}
      onClose={handleClose}
      width={800}
      maskClosable
      footer={
        <>
          {isEditing && (
            <div className={styles.drawerFooter}>
              <Button onClick={toggleEditMode}>取消</Button>
              {isEditing && (
                <Button type="primary" onClick={handleSubmit}>
                  保存
                </Button>
              )}
            </div>
          )}
        </>
      }
    >
      {formData && (
        <div className={styles.formContainer}>
          {/* 编辑按钮 */}
          {!isEditing && (
            <div onClick={toggleEditMode} className={styles.editButton}>
              <Icon type="edit-o" className={styles.editIcon} />
              修改
            </div>
          )}

          {detail.failReason && (
            <div className={styles.tipsContainer}>
              <WxxdTips tip={{ message: detail.failReason }} />
            </div>
          )}

          {[BusinessSubject, SuperAdmin, SettleAccount, StoreBasic, SupplementaryMaterial].map(
            Component => (
              <Component
                key={Component.name}
                detail={formData}
                drawer={true}
                updateXdDetail={setFormData}
                isEditing={isEditing}
              />
            ),
          )}
        </div>
      )}
    </Drawer>
  );
}
