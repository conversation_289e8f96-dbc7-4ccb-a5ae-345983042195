import React from 'react';
import FormSectionWrapper from './FormSectionWrapper';

export default function BusinessSubject({
  detail,
  updateXdDetail,
  isEditing = false,
  drawer = false,
  filter,
}) {
  let currentComponents = [
    'businessLicenseUrl',
    'businessSubjectType',
    'businessMerchantName',
    'businessSocialCreditCode',
    'businessRegisterAddress',
    '__businessDate',
    'personCardType',
    '__personCard',
    'personCardName',
    'personCardNo',
    'personCardAddress',
    '__personCardDate',
  ];

  if (filter) {
    currentComponents = filter(currentComponents);
  }

  return (
    <FormSectionWrapper
      title="主体信息"
      components={currentComponents}
      detail={detail}
      updateXdDetail={updateXdDetail}
      isEditing={isEditing}
      drawer={drawer}
    />
  );
}
