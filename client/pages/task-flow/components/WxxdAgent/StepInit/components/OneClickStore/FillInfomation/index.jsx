import { useState } from 'react';
import { Icon } from 'zent';
import styles from './index.m.scss';
import InfomationDrawer from './InfomationDrawer';
import BusinessSubject from './InfomationDrawer/module/BusinessSubject';
import SuperAdmin from './InfomationDrawer/module/SuperAdmin';
import SettleAccount from './InfomationDrawer/module/SettleAccount';
import StoreBasic from './InfomationDrawer/module/StoreBasic';
import SupplementaryMaterial from './InfomationDrawer/module/SupplementaryMaterial';

function InfoBox({ icon, title, link, desc, onClick, style }) {
  return (
    <div className={styles.infoBox} style={style}>
      <div className={styles.infoHeader}>
        <div className={styles.infoTitle}>
          {icon && <Icon type={icon} className={styles.infoIcon} />}
          {title}
        </div>
        <div className={styles.infoLink} onClick={onClick}>
          {link}
        </div>
      </div>
      {desc && <div className={styles.infoDesc}>{desc}</div>}
    </div>
  );
}

export default function FillInfomation({ detail = {}, onChange, missingInfos }) {
  const [visible, setVisible] = useState(false);

  return (
    <div className={styles.container}>
      <div className={styles.title}>
        你已授权有赞代开店，开店资料收集如下，你可点击查看已收集到的资料是否正确：
      </div>

      <InfoBox
        icon="text-guide-o"
        title="开店资料"
        link="查看"
        desc="包括：主体类型、营业执照、组织结构代码、法人姓名、法人身份证号、结算账户、商户名称、注册地址、注册日期"
        onClick={() => {
          setVisible(true);
        }}
      />

      {missingInfos.length > 0 && (
        <>
          <div className={styles.divider}></div>

          <div className={styles.title}>为了保证开店流程顺利，请需要补充以下资料：</div>

          <div className={styles.missInfoBox}>
            {[BusinessSubject, SuperAdmin, SettleAccount, StoreBasic, SupplementaryMaterial].map(
              Component => (
                <Component
                  key={Component.name}
                  detail={detail}
                  drawer={true}
                  updateXdDetail={onChange}
                  isEditing={true}
                  filter={components => {
                    return components.filter(component => {
                      return missingInfos.includes(component);
                    });
                  }}
                />
              ),
            )}
          </div>
        </>
      )}

      {visible && (
        <InfomationDrawer
          visible={visible}
          onChange={value => onChange(value)}
          onClose={() => setVisible(false)}
          detail={detail}
        />
      )}
    </div>
  );
}
