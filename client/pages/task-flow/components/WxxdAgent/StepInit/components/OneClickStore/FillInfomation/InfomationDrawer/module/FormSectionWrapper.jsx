import React, { useEffect, useState } from 'react';
import { FormFieldComponents } from './configs/components';
import FormSection from 'pages/task-flow/components/WxxdAgent/common/formly/form-section';
import { get } from 'lodash';

/**
 * 表单区块统一包装组件，用于展示各类信息表单
 * @param {Object} props - 组件属性
 * @param {string} props.title - 表单区块标题
 * @param {Array} props.components - 要显示的表单字段键名数组
 * @param {Object} props.detail - 表单数据对象
 * @param {Function} props.updateXdDetail - 更新表单数据的回调函数
 * @param {boolean} props.isEditing - 是否处于编辑模式
 * @param {boolean} props.drawer - 是否在抽屉中显示
 * @param {Function} props.shouldRender - 可选的条件渲染函数，返回布尔值决定是否渲染
 */
export default function FormSectionWrapper({
  title,
  tip = '',
  components,
  detail,
  updateXdDetail,
  drawer = false,
  shouldRender = () => true,
  isEditing = true,
  disabledMap = {},
}) {
  // 如果提供了条件渲染函数且返回false，则不渲染任何内容
  if (!shouldRender(detail)) {
    return null;
  }

  return (
    <div>
      {components.length > 0 && (
        <FormSection
          title={title}
          tip={tip}
          fields={components.map(key => {
            const config = FormFieldComponents[key];
            return {
              ...config,
              field: key,
            };
          })}
          formData={detail}
          drawer={drawer}
          onChange={value => updateXdDetail(value)}
          isEditing={isEditing}
          disabledMap={disabledMap}
        />
      )}
    </div>
  );
}
