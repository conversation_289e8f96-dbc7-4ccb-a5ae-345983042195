.options-container {
  display: flex;
  flex-direction: column;
}

.store-option {
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 22px 20px;
  width: 884px;
  height: 86px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.store-option:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.option-content {
  display: flex;
  align-items: center;
}

.option-icon {
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.option-text-container {
  flex: 1;
}

.option-title {
  font-size: 16px;
  color: #333333;
}

.option-action {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.description-text {
  color: #155bd4;
  font-size: 16px;
  margin-right: 8px;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

.action-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.action-container button {
  min-width: 160px;
}

.auth-store-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-container {
  width: 235.38px;
  height: 375px;
  display: flex;
  font-size: 12px;
  font-weight: 500;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to bottom, #fdf3ef 0%, #ffffff 20%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &__zanicon {
    width: 40px;
    height: 40px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    background: #eb0b19;
    border-radius: 50%;

    .zenticon {
      font-size: 25px;
      color: #fff;
    }
  }

  &__name {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;

    &_title {
      font-size: 16px;
      color: #333333;
    }

    &_desc {
      font-size: 14px;
      color: #969799;
    }
  }
}

.qrcode-tips {
  margin-bottom: 24px;
  text-align: center;

  .tips-title {
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    text-align: center;
  }
}

.dialog-footer {
  margin-top: 32px;
  width: 100%;

  .confirm-btn {
    width: 100%;
    height: 40px;
  }
}
