import { useState, useCallback, useEffect, useRef } from 'react';
import classNames from 'classnames';
import { BlockLoading, Button, Dialog, Icon, Notify } from 'zent';
import { QRCodeSVG } from 'qrcode.react';

import './index.scss';
import { AgentBlock } from '@youzan/agent-components';
import {
  getAuthLink,
  getDetail,
  getWechatOpenShopStatus,
  queryShopBindAccount,
} from 'pages/task-flow/components/WxxdAgent/api';
import {
  AgentMessageBlockTypeEnum,
  SHOP_AGENT_CREATE_STATUS,
} from 'pages/task-flow/components/WxxdAgent/constant';

import queryString from 'query-string';
import { useLocation } from 'react-router';
import fullfillImage from '@youzan/utils/fullfillImage';

// 存储CDN图片URL的映射
const imageUrls = {
  notOpenIcon: 'https://img01.yzcdn.cn/public_files/2025/03/19/01625a3f84be8955.png',
  openedIcon: 'https://img01.yzcdn.cn/public_files/2025/03/19/fba4e36ba56200e6.png',
  rightArrow: 'https://img01.yzcdn.cn/public_files/2025/03/19/31ce05b81a780f09.svg',
};

// 选项组件
function Option({ value, title, description, iconType, onClick, isLoading }) {
  return (
    <BlockLoading loading={isLoading}>
      <div className={classNames('store-option')} onClick={onClick}>
        <div className="option-content">
          <div className="option-icon">
            <img
              src={fullfillImage(
                iconType === 'opened' ? imageUrls.openedIcon : imageUrls.notOpenIcon,
              )}
              alt="选项图标"
              className="option-image"
            />
          </div>
          <div className="option-text-container">
            <span className="option-title">{title}</span>
          </div>
          <div className="option-action">
            <span className="description-text">{description}</span>
            <img src={imageUrls.rightArrow} alt="箭头" className="arrow-icon" />
          </div>
        </div>
      </div>
    </BlockLoading>
  );
}

function ExistingStoreOption({ onBindShop }) {
  const [checkLoading, setCheckLoading] = useState(false);
  const [visible, setVisible] = useState(false);

  const handleExistClick = useCallback(async () => {
    setVisible(true);
    window.open('https://www.youzan.com/v4/wxvideo/video-shop?from=wxshop-agent', '_blank');
  }, [onBindShop]);

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const handleCheck = useCallback(async () => {
    setCheckLoading(true);

    try {
      const shopBindAccount = await queryShopBindAccount();
      if (shopBindAccount.length > 0) {
        onBindShop();
      } else {
        Notify.error('未检测到绑定成功的店铺');
      }
    } catch (e) {
      console.error(e);
      Notify.error('未检测到绑定成功的店铺');
    } finally {
      setCheckLoading(false);
    }
  }, []);

  return (
    <>
      <Option
        value="exist"
        title="我已经开通了微信小店"
        description="绑定有赞，开启托管"
        iconType="opened"
        onClick={handleExistClick}
      />

      <Dialog
        className="auth-store-dialog"
        visible={visible}
        onClose={handleClose}
        footer={
          <>
            <Button type="primary" onClick={handleCheck} loading={checkLoading}>
              我已绑定
            </Button>
          </>
        }
        title="绑定微信小店"
      >
        <p>是否已将微信小店授权绑定有赞？</p>
      </Dialog>
    </>
  );
}

function NewStoreOption({ onAgentCreateShop }) {
  const [visible, setVisible] = useState(false);
  const [checkLoading, setCheckLoading] = useState(false);

  const [typingDone, setTypingDone] = useState(false);
  const [authLink, setAuthLink] = useState('');
  const [existLoading, setExistLoading] = useState(false);
  const [newLoading, setNewLoading] = useState(false);

  const query = queryString.parse(useLocation().search);

  const handleNewClick = useCallback(async () => {
    setNewLoading(true);
    try {
      const res = await getAuthLink();
      setAuthLink(res.authLink);
      setVisible(true);
    } finally {
      setNewLoading(false);
    }
  }, [onAgentCreateShop]);

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const handleCheck = useCallback(async () => {
    setCheckLoading(true);
    try {
      if (query.detail == '1') {
        const openShopInfo = await getWechatOpenShopStatus();
        const detailData = await getDetail({
          appId: openShopInfo.appId,
          authStatus: openShopInfo.authStatus,
        });
        setVisible(false);
        await onAgentCreateShop(detailData);
        return;
      }

      const openShopInfo = await getWechatOpenShopStatus();
      if (openShopInfo?.authStatus === SHOP_AGENT_CREATE_STATUS.STARTE) {
        const detailData = await getDetail({
          appId: openShopInfo.appId,
          authStatus: openShopInfo.authStatus,
        });
        if (detailData) {
          setVisible(false);
          await onAgentCreateShop(detailData);
        } else {
          Notify.error('信息获取失败，请稍后再试！');
        }
      } else {
        Notify.error('未检测到已授权的微信小店，请扫码授权');
      }
    } catch (e) {
      console.error(e);
      Notify.error('未检测到已授权的微信小店，请扫码授权');
    } finally {
      setCheckLoading(false);
    }
  }, []);

  return (
    <>
      <Option
        value="new"
        title="我还未开通微信小店"
        description="授权有赞，一键开店"
        iconType="not-opened"
        onClick={handleNewClick}
        isLoading={newLoading}
      />

      {/* 授权开店弹窗 */}
      <Dialog
        className="auth-store-dialog"
        visible={visible}
        onClose={handleClose}
        footer={
          <>
            <Button type="primary" onClick={handleCheck} loading={checkLoading}>
              我已授权
            </Button>
          </>
        }
        title="扫码授权开店"
      >
        <div className="auth-store-content">
          <div className="qrcode-tips">
            <p className="tips-title">扫码授权有赞开店</p>
          </div>
          <div className="qrcode-container">
            <div className="qrcode-container__zanicon">
              <Icon type="youzan" />
            </div>
            <div className="qrcode-container__name">
              <p className="qrcode-container__name_title">有赞开店</p>
              <p className="qrcode-container__name_desc">向你申请代开店授权</p>
            </div>
            {authLink && <QRCodeSVG width={150} value={authLink} size={200} />}
          </div>
        </div>
      </Dialog>
    </>
  );
}

function BindYouzan({ onAgentCreateShop, onBindShop }) {
  const [typingDone, setTypingDone] = useState(false);
  const timeoutRef = useRef(null);

  useEffect(() => {
    timeoutRef.current = setTimeout(() => {
      setTypingDone(true);
    }, 1000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const block = {
    type: AgentMessageBlockTypeEnum.TEXT,
    isStreamTyping: true,
    markdown: '检测到你的微信小店未绑定有赞，请选择对应的场景完成绑定操作：',
  };

  return (
    <>
      <AgentBlock block={block} />
      {typingDone && (
        <div className="options-container">
          <ExistingStoreOption onBindShop={onBindShop} />
          <NewStoreOption onAgentCreateShop={onAgentCreateShop} />
        </div>
      )}
    </>
  );
}

export default BindYouzan;
