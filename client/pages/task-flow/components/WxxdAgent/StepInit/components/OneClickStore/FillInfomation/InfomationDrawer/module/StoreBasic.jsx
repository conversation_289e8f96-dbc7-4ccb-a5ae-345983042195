import React, { useEffect, useState } from 'react';
import FormSectionWrapper from './FormSectionWrapper';

export default function StoreBasic({
  detail,
  updateXdDetail,
  isEditing = true,
  drawer = false,
  filter,
}) {
  // 基本信息字段
  let basicComponents = [
    'storeBasicType',
    // 'storeNameType',
    'storeBasicCustomerName',
    'storeIntroduction',
    'storeBasicAvatar',
    // 'storeBasicBrandMerchant',
  ]
    .concat(
      detail.storeBasic?.brandMerchant
        ? [
            'brandInfoName',
            'brandInfoSelfOperated',
            'brandInfoRegisterNo',
            'brandInfoRegisterCertifications',
            'brandInfoRenewCertifications',
          ]
        : [],
    )
    .concat([
      'storeBasicSupplementaryUseAuthorizationImage',
      'storeBasicSupplementaryAssistImages',
      'storeBasicSupplementaryDesc',
    ]);

  if (filter) {
    basicComponents = filter(basicComponents);
  }

  return (
    <>
      <FormSectionWrapper
        title="店铺基本信息"
        components={basicComponents}
        detail={detail}
        updateXdDetail={updateXdDetail}
        isEditing={isEditing}
        drawer={drawer}
        disabledMap={
          {
            // storeBasicBrandMerchant: !isEnableBrand,
          }
        }
      />
    </>
  );
}
