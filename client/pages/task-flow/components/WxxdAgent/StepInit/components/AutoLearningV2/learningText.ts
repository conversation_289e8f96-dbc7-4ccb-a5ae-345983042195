const autoStudyTexts = [
  '我是您的有赞智能助手，我的使命是帮助您把微信小店经营得风生水起。为此，我需要先熟悉您店铺的方方面面，从所处的行业、主营类目，到商品的销售数据、店铺的日常运营情况，以及客户的需求和反馈。我会结合这些信息，为您量身定制运营方案。同时，我拥有海量同行业案例可供参考，并且会持续学习其他优秀店铺的运营经验，将这些宝贵的经验融入到您的店铺运营中，帮助您提升店铺的竞争力，让您的生意蒸蒸日上。',
  '作为有赞智能体，我的核心任务是助力您的微信小店蓬勃发展。为此，我需要全面了解您店铺的运营细节，包括所处的行业、商品类目、销售情况、店铺运营状况以及客户群体特征等。通过对这些信息的深入分析，我将为您打造专属的运营策略。我不仅积累了丰富的同行业案例经验，还会不断学习其他优秀店铺的成功模式，将这些经验运用到您的店铺中，帮助您优化运营流程，提升客户满意度，让您的店铺在市场中脱颖而出。',
  '作为有赞智能体，我的核心任务是助力您的微信小店蓬勃发展。为此，我需要全面了解您店铺的运营细节，包括所处的行业、商品类目、销售情况、店铺运营状况以及客户群体特征等。通过对这些信息的深入分析，我将为您打造专属的运营策略。我不仅积累了丰富的同行业案例经验，还会不断学习其他优秀店铺的成功模式，将这些经验运用到您的店铺中，帮助您优化运营流程，提升客户满意度，让您的店铺在市场中脱颖而出。',
  '我是有赞智能体，专注于帮您运营好微信小店。为了实现这一目标，我需要先对您的店铺进行全面了解，包括店铺所在的行业、主营类目、商品销售数据、店铺运营情况以及客户信息等。这些信息将帮助我为您制定精准的运营策略。同时，我拥有丰富的同行业案例积累，并会持续学习其他优秀店铺的成功经验，将这些经验融入到您的店铺运营中，助力您的店铺在激烈的市场竞争中脱颖而出，实现业绩的稳步增长。',
  '作为您的有赞智能助手，我的核心职责是帮助您将微信小店经营得有声有色。为此，我需要先深入了解您店铺的运营情况，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。通过对这些信息的分析，我将为您量身定制运营策略。同时，我积累了丰富的同行业案例经验，并会不断学习其他优秀店铺的成功经验，将这些经验运用到您的店铺运营中，帮助您优化运营流程，提升客户满意度，让您的店铺在市场中占据一席之地。',
  '我是有赞智能体，致力于帮您运营好微信小店。为了更好地完成这一任务，我需要先了解您店铺的运营情况，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。这些信息将帮助我为您制定精准的运营策略。同时，我拥有丰富的同行业案例积累，并会不断学习其他优秀店铺的成功经验，将这些经验融入到您的店铺运营中，帮助您提升店铺的竞争力，让您的生意蒸蒸日上。',
  '作为有赞智能助手，我的核心使命是帮助您将微信小店经营得风生水起。为此，我需要先熟悉您店铺的运营细节，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。通过对这些信息的分析，我将为您量身定制运营策略。同时，我积累了丰富的同行业案例经验，并会持续学习其他优秀店铺的成功模式，将这些经验融入到您的店铺运营中，帮助您优化运营流程，提升客户满意度，让您的店铺在市场中脱颖而出。',
  '我是有赞智能体，我的核心职责是帮助您将微信小店经营得有声有色。为此，我需要先深入了解您店铺的运营情况，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。这些信息将帮助我为您制定精准的运营策略。同时，我拥有丰富的同行业案例积累，并会不断学习其他优秀店铺的成功经验，将这些经验融入到您的店铺运营中，帮助您提升店铺的竞争力，让您的生意蒸蒸日上。',
  '作为您的有赞智能助手，我的核心使命是帮助您将微信小店经营得风生水起。为此，我需要先熟悉您店铺的运营细节，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。通过对这些信息的分析，我将为您量身定制运营策略。同时，我积累了丰富的同行业案例经验，并会持续学习其他优秀店铺的成功模式，将这些经验融入到您的店铺运营中，帮助您优化运营流程，提升客户满意度，让您的店铺在市场中脱颖而出。',
  '我是有赞智能体，我的核心职责是帮助您将微信小店经营得有声有色。为此，我需要先深入了解您店铺的运营情况，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。这些信息将帮助我为您制定精准的运营策略。同时，我拥有丰富的同行业案例积累，并会不断学习其他优秀店铺的成功经验，将这些经验融入到您的店铺运营中，帮助您提升店铺的竞争力，让您的生意蒸蒸日上。',
  '作为有赞智能助手，我的核心使命是帮助您将微信小店经营得风生水起。为此，我需要先熟悉您店铺的运营细节，包括店铺所在的行业、主营类目、商品销售数据、店铺运营状况以及客户群体特征等。通过对这些信息的分析，我将为您量身定制运营策略。同时，我积累了丰富的同行业案例经验，并会持续学习其他优秀店铺的成功模式，将这些经验融入到您的店铺运营中，帮助您优化运营流程，提升客户满意度，让您的店铺在市场中脱颖而出。',
];

const PreviewPlanTexts = [
  [
    '在当今激烈的电商竞争中，许多用户渴望找到一个可靠的平台来托管自己的微信小店。有赞作为知名的微信服务商，凭借其卓越的实力和专业服务，为众多商家提供了全方位的店铺托管解决方案。目前，有赞已经成功托管了超过300个微信店铺，积累了丰富的运营经验，能够为商家提供从店铺搭建到日常管理的全方位支持，让商家轻松开启电商之旅。',
    '对于那些希望专注于产品开发和推广，却无暇顾及店铺日常运营的用户来说，托管微信小店无疑是一个明智的选择。有赞作为微信生态中的资深服务商，凭借其强大的技术团队和丰富的行业经验，已经为超过300个微信店铺提供了专业的托管服务。无论是店铺装修、商品上架，还是订单处理和客户服务，有赞都能提供一站式解决方案，让商家省心省力。',
    '用户在运营微信小店时，常常面临诸多挑战，如复杂的店铺管理、营销推广以及数据分析等。有赞作为微信生态中的佼佼者，凭借其资深的运营团队和先进的技术支持，为商家提供了全方位的托管服务。目前，有赞已经成功托管了超过300个微信店铺，积累了丰富的实战经验，能够帮助商家轻松应对各种运营难题，提升店铺的竞争力。',
    '在电商领域，店铺托管服务的质量直接关系到商家的经营效果。有赞作为微信生态中的专业服务商，凭借其多年的行业积累和强大的技术实力，为商家提供了高质量的托管服务。目前，有赞已经成功托管了超过300个微信店铺，积累了丰富的运营经验。从店铺搭建到日常管理，从营销推广到数据分析，有赞都能为商家提供一站式解决方案，让商家专注于核心业务。',
    '对于许多用户来说，微信小店的运营需要投入大量的时间和精力。有赞作为专业的微信服务商，凭借其资深的运营团队和先进的技术支持，为商家提供了全方位的托管服务。目前，有赞已经成功托管了超过300个微信店铺，积累了丰富的实战经验。无论是店铺装修、商品上架，还是订单处理和客户服务，有赞都能为商家提供贴心的支持，让商家轻松开启电商之旅。',
  ],
  // [
  //   '[店铺名称]信息分析[店铺名称]是一家专注于女装的微商城店铺，主要面向20-35岁女性，产品涵盖上衣、裙子、牛仔裤等。店铺以创始人IP为核心优势，老板是时尚女性，在社交媒体拥有大量粉丝，为品牌带来高流量。凭借精准的客户定位和丰富的产品线，店铺计划今年实现1000万营收目标。',
  //   '[店铺名称]专注于女装，目标客户为20-35岁女性，产品包括上衣、裙子、牛仔裤等。店铺创始人作为时尚IP，在社交媒体拥有大量粉丝，为品牌带来高关注度。凭借精准定位和丰富的产品线，店铺计划今年实现1000万营收。',
  //   '[店铺名称]是一家女装微商城，目标客户为20-35岁女性，产品涵盖上衣、裙子、牛仔裤等。店铺以时尚创始人IP为核心优势，老板在社交媒体拥有大量粉丝，为品牌带来高流量。精准的客户定位和丰富的产品线助力今年1000万营收目标。',
  //   '[店铺名称]是一家女装微商城，目标客户为20-35岁女性，产品包括上衣、裙子、牛仔裤等。店铺以时尚创始人IP为核心优势，老板在社交媒体拥有大量粉丝，为品牌带来高流量。凭借精准定位和丰富的产品线，店铺计划今年实现1000万营收。',
  //   '[店铺名称]是一家女装微商城，面向20-35岁女性，主营上衣、裙子、牛仔裤等。店铺以时尚创始人IP为核心，老板在社交媒体拥有众多粉丝，为品牌带来高流量。精准定位和丰富产品助力今年1000万营收目标。',
  // ],
  // [
  //   '在服饰领域，有赞系统为众多商家提供助力。凭借订单处理、商品管理、履约交付等环节的智能化操作，为商家高效运营小店保驾护航。',
  //   '有赞系统在服饰行业大放异彩，已助力超 100 家店铺。其通过智能托管订单、商品和履约流程，让商家运营效率大幅提升。',
  //   '有赞系统为服饰行业商家提供全方位支持。从订单处理到商品管理，再到履约交付，智能托管与自动执行助力商家高效运营。',
  //   '有赞系统专注于服饰行业，为商家提供智能服务。在订单、商品、履约等场景中，实现自动执行，助力商家提升运营效率。',
  //   '有赞系统为服饰商家提供智能托管服务。在订单处理、商品管理、履约交付等环节，实现自动化操作，助力商家高效运营。',
  // ],
  [
    '在日常运营中，维持销售的紧张状态是提升店铺竞争力的关键策略之一。根据历史案例经验，当库存数量低于5个时，建议自动补充5个库存。这种策略不仅能营造出商品的稀缺感，激发消费者的购买欲望，还能有效避免因缺货而错失的销售机会。同时，开启库存不足、订单信息变更、售后异常订单以及物流异常订单的提醒功能，能够让您第一时间掌握店铺运营的动态，及时处理可能出现的问题，确保店铺运营的顺畅与高效。此外，结合微信小店的营销策略，将热销商品自动置顶在店铺首页，能够最大化地利用首页流量，吸引更多潜在客户的关注，从而提升商品的曝光率和销售量。同时，自动搭建送礼专区，为客户提供便捷的购物选择，不仅能满足客户的送礼需求，还能进一步促进销量的增长，为店铺带来更多的收益。',
    '为了在激烈的市场竞争中脱颖而出，店铺的精细化运营至关重要。根据过往的历史案例，我们发现保持一定的销售紧张状态能够有效提升店铺的销售业绩。因此，建议您在日常运营中，当库存数量不足5个时，自动补充5个库存。这一策略不仅能够营造出商品供不应求的氛围，吸引消费者尽快下单，还能确保店铺在关键时刻不会因缺货而错失销售机会。同时，开启库存不足、订单信息变更、售后异常订单以及物流异常订单的提醒功能，让您能够及时了解店铺的运营状况，快速响应各种问题，提升店铺的运营效率和服务质量。此外，结合微信小店的营销策略，将热销商品自动置顶在店铺首页，能够充分利用首页的流量优势，吸引更多客户的关注，从而提高商品的曝光率和销售量。同时，在首页自动搭建送礼专区，为客户提供便捷的购物选择，不仅能满足客户的送礼需求，还能进一步促进销量的增长，为店铺带来更多的收益。',
    '在电商运营中，如何把握销售节奏、提升运营效率是每个商家都需要面对的问题。根据历史案例经验，保持一定的销售紧张状态是提升店铺业绩的有效手段。因此，建议您在日常运营中，当库存数量不足5个时，自动补充5个库存。这一策略不仅能够营造出商品稀缺的氛围，激发消费者的购买欲望，还能确保店铺在关键时刻不会因缺货而错失销售机会。同时，开启库存不足、订单信息变更、售后异常订单以及物流异常订单的提醒功能，让您能够及时掌握店铺的运营动态，快速处理可能出现的问题，提升店铺的运营效率和服务质量。此外，结合微信小店的营销策略，将热销商品自动置顶在店铺首页，能够最大化地利用首页流量，吸引更多潜在客户的关注，从而提升商品的曝光率和销售量。同时，在首页自动搭建送礼专区，为客户提供便捷的购物选择，不仅能满足客户的送礼需求，还能进一步促进销量的增长，为店铺带来更多的收益。',
    '在电商运营中，细节往往决定了成败。根据历史案例经验，保持一定的销售紧张状态是提升店铺业绩的有效手段。因此，建议您在日常运营中，当库存数量不足5个时，自动补充5个库存。这一策略不仅能够营造出商品稀缺的氛围，激发消费者的购买欲望，还能确保店铺在关键时刻不会因缺货而错失销售机会。同时，开启库存不足、订单信息变更、售后异常订单以及物流异常订单的提醒功能，让您能够及时掌握店铺的运营动态，快速处理可能出现的问题，提升店铺的运营效率和服务质量。此外，结合微信小店的营销策略，将热销商品自动置顶在店铺首页，能够最大化地利用首页流量，吸引更多潜在客户的关注，从而提升商品的曝光率和销售量。同时，在首页自动搭建送礼专区，为客户提供便捷的购物选择，不仅能满足客户的送礼需求，还能进一步促进销量的增长，为店铺带来更多的收益。',
    '在电商运营中，如何把握销售节奏、提升运营效率是每个商家都需要面对的问题。根据历史案例经验，保持一定的销售紧张状态是提升店铺业绩的有效手段。因此，建议您在日常运营中，当库存数量不足5个时，自动补充5个库存。这一策略不仅能够营造出商品稀缺的氛围，激发消费者的购买欲望，还能确保店铺在关键时刻不会因缺货而错失销售机会。同时，开启库存不足、订单信息变更、售后异常订单以及物流异常订单的提醒功能，让您能够及时掌握店铺的运营动态，快速处理可能出现的问题，提升店铺的运营效率和服务质量。此外，结合微信小店的营销策略，将热销商品自动置顶在店铺首页，能够最大化地利用首页流量，吸引更多潜在客户的关注，从而提升商品的曝光率和销售量。同时，在首页自动搭建送礼专区，为客户提供便捷的购物选择，不仅能满足客户的送礼需求，还能进一步促进销量的增长，为店铺带来更多的收益。',
  ],
];

export const getAutoStudyText = () => {
  return autoStudyTexts[Math.floor(Math.random() * autoStudyTexts.length)];
};

export const getPreviewPlanText = () => {
  const [industry, shopPlan] = PreviewPlanTexts;
  return `${industry[Math.floor(Math.random() * industry.length)]} ${
    shopPlan[Math.floor(Math.random() * shopPlan.length)]
  }`;
};
