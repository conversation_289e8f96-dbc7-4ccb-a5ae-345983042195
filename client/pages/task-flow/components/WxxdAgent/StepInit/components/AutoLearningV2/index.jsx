import React from 'react';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import Container from '../ui/container';
import './index.scss';
import { getAutoStudyText } from './learningText';
import { AgentMessageBlockTypeEnum } from '../../../constant';
import { AgentBlock as AgentMessageBlock } from '@youzan/agent-components';
import '@youzan/agent-components/dist/index.css';
import Typewriter from '../../../common/type-writer';
import { BlockLoading, Icon } from 'zent';

const CHECK_ICON =
  'https://img01.yzcdn.cn/upload_files/2025/03/20/FnNBo-H55ILM2VXM5e9jCWw0DqzE.png';

const CLOSE_THINKING_CONFIGS = [
  {
    done: true,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '正在学习店铺相关知识，请稍等...',
    isStreamTyping: true,
  },
  {
    title: '店铺信息',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习店铺信息',
    isStreamTyping: true,
  },
  {
    title: '商品信息',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习商品信息',
    isStreamTyping: true,
  },
  {
    title: '订单信息',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习订单信息',
    isStreamTyping: true,
  },
  {
    title: '售后体系',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习售后体系',
    isStreamTyping: true,
  },
];

const COLLECT_INFO_CONFIGS = [
  {
    title: '行业相关知识',
    done: true,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始搜集和学习同行业相关知识，请稍等...',
    isStreamTyping: true,
  },
  {
    title: '微信小店开店规则',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习微信小店开店规则',
    isStreamTyping: true,
  },
  {
    title: '微信小店运营规范',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习微信小店运营规范',
    isStreamTyping: true,
  },
  {
    title: '微信小店常见问题',
    done: false,
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '已学习微信小店常见问题',
    isStreamTyping: true,
  },
];

const TIMEOUT_CONFIG = {
  closeThinking: 10000,
  closeThinkingItem: 1500,
  collectInfo: 25000,
  collectInfoItem: 1500,
  footer: 36000,
};

const autoStudyText = getAutoStudyText();
class AutoLearning extends React.Component {
  state = {
    shopInfo: {},
    showCloseThinking: false,
    showCollectInfo: false,
    showFooter: false,
    currentCloseThinkingConfigs: [],
    currentCollectInfoConfigs: [],
    closeThinkingIndex: 0,
    collectInfoIndex: 0,
  };

  timers = {
    closeThinking: null,
    collectInfo: null,
    footer: null,
    closeThinkingInterval: null,
    collectInfoInterval: null,
  };

  componentDidMount() {
    this._isMounted = true;
    this.setupTimers();
  }

  componentWillUnmount() {
    this._isMounted = false;
    Object.values(this.timers).forEach(timer => {
      if (timer) clearTimeout(timer);
      if (timer) clearInterval(timer);
    });
  }

  setupTimers = () => {
    this.timers.closeThinking = setTimeout(() => {
      this.setState({ showCloseThinking: true }, this.startCloseThinkingInterval);
    }, TIMEOUT_CONFIG.closeThinking);

    this.timers.collectInfo = setTimeout(() => {
      CLOSE_THINKING_CONFIGS[0].markdown = '已完成本店铺知识的学习';

      this.setState(
        {
          showCollectInfo: true,
          currentCloseThinkingConfigs: CLOSE_THINKING_CONFIGS,
          currentCollectInfoConfigs: [],
          // collectInfoIndex: 1
        },
        this.startCollectInfoInterval,
      );
    }, TIMEOUT_CONFIG.collectInfo);

    this.timers.footer = setTimeout(() => {
      this.setState({ showFooter: true });
    }, TIMEOUT_CONFIG.footer);
  };

  startProgressiveInterval = (configs, stateKey, configsKey, intervalTime) => {
    return setInterval(() => {
      this.setState(prevState => {
        const currentIndex = prevState[stateKey];
        const currentConfig = prevState[configsKey][currentIndex - 1];
        if (prevState[stateKey] < configs.length) {
          if (currentConfig && currentConfig.done === false) {
            currentConfig.done = true;
            return {
              [configsKey]: [...prevState[configsKey]],
            };
          } else {
            return {
              [configsKey]: [...prevState[configsKey], configs[currentIndex]],
              [stateKey]: prevState[stateKey] + 1,
            };
          }
        } else {
          if (currentConfig && currentConfig.done === false) {
            currentConfig.done = true;
            return {
              [configsKey]: [...prevState[configsKey]],
            };
          } else {
            clearInterval(this.timers[`${stateKey}Interval`]);
            return null;
          }
        }
      });
    }, intervalTime);
  };

  startCloseThinkingInterval = () => {
    this.timers.closeThinkingInterval = this.startProgressiveInterval(
      CLOSE_THINKING_CONFIGS,
      'closeThinkingIndex',
      'currentCloseThinkingConfigs',
      TIMEOUT_CONFIG.closeThinkingItem,
    );
  };

  startCollectInfoInterval = () => {
    this.timers.collectInfoInterval = this.startProgressiveInterval(
      COLLECT_INFO_CONFIGS,
      'collectInfoIndex',
      'currentCollectInfoConfigs',
      TIMEOUT_CONFIG.collectInfoItem,
    );
  };

  render() {
    const {
      showCloseThinking,
      showCollectInfo,
      showFooter,
      currentCloseThinkingConfigs,
      currentCollectInfoConfigs,
      shopInfo,
    } = this.state;

    const thoughtChainConfig = {
      type: AgentMessageBlockTypeEnum.THOUGHT_CHAIN,
      isStreamTyping: true,
      thoughtChain: {
        isAutoCollapse: true,
        title: showCloseThinking ? '已完成思考' : '正在深度思考...',
        currentStep: showCloseThinking ? 1 : 0,
        steps: [
          {
            topic: showCloseThinking ? '已完成分析店铺数据' : '正在分析你的店铺数据',
            content: autoStudyText,
          },
        ],
      },
    };

    if (showFooter) {
      currentCollectInfoConfigs[0].markdown = '已完成行业相关知识的学习';
      setTimeout(() => {
        this.props.nextStep();
      }, 2000);
    }

    return (
      <div className="learning">
        <Container>
          <AgentBlockWrapper>
            <AgentMessageBlock block={thoughtChainConfig} />
            {showCloseThinking && <div className="learning__divider"></div>}
            {showCloseThinking &&
              currentCloseThinkingConfigs.map((config, index) => (
                <div key={`close-${index}`} className="learning__item">
                  {index > 0 &&
                    (config.warn ? (
                      <Icon className="icon icon-warn" type="warning" />
                    ) : config.done ? (
                      <Icon className="icon" type="check" />
                    ) : (
                      <BlockLoading
                        className="icon-loading"
                        loading
                        icon="circle"
                        iconSize={20}
                        height={20}
                      />
                    ))}

                  {config.done ? (
                    <AgentMessageBlock block={config} />
                  ) : (
                    <Typewriter text={`正在读取和学习${config.title}`} />
                  )}
                </div>
              ))}
          </AgentBlockWrapper>

          {showCollectInfo && currentCollectInfoConfigs.length > 0 && (
            <AgentBlockWrapper showIcon={false}>
              {currentCollectInfoConfigs.map((config, index) => (
                <div key={`collect-${index}`} className="learning__item">
                  {index > 0 &&
                    (config.warn ? (
                      <Icon className="icon icon-warn" type="warning" />
                    ) : config.done ? (
                      <Icon className="icon" type="check" />
                    ) : (
                      <BlockLoading
                        className="icon-loading"
                        loading
                        icon="circle"
                        iconSize={20}
                        height={20}
                      />
                    ))}

                  {config.done ? (
                    <AgentMessageBlock block={config} />
                  ) : (
                    <Typewriter text={`正在读取和学习${config.title}`} />
                  )}
                </div>
              ))}
            </AgentBlockWrapper>
          )}
        </Container>
      </div>
    );
  }
}

export default AutoLearning;
