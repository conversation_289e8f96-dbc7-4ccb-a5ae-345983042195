import React from 'react';
import './index.scss';



class Divider extends React.Component {

  static defaultProps = {
    splitColor: '#999999',
    splitWidth: '1px',
    gap: '8px',
    className: ''
  };

  render() {
    const { splitColor, splitWidth, gap, className, children } = this.props;
    
    const splitStyle = {
      backgroundColor: splitColor,
      width: splitWidth,
      margin: `0 ${gap}`
    };

    return (
      <div 
        className={`divider-container ${className}`}
        style={{ display: 'flex', alignItems: 'center' }}
      >
        {React.Children.map(children, (child, index) => {
          if (index === React.Children.count(children) - 1) {
            return child;
          }

          return (
            <React.Fragment key={child.key || index}>
              {child}
              <span className="divider-split" style={splitStyle} />
            </React.Fragment>
          );
        })}
      </div>
    );
  }
}

export default Divider;
