.wrapper {
  width: 924px;
  background: linear-gradient(92.39deg, #f3f7fd 0.43%, #f3f3fd 98.47%);
  border: 1px solid;
  border-image-source: linear-gradient(91.06deg, rgba(242, 246, 255, 0.67) 1.95%, #efefff 98.72%);
  border-radius: 4px;
  position: relative;
  // min-height: 100px;
  padding: 20px 20px 0;
  box-sizing: border-box;
  margin-bottom: 20px;
  padding-bottom: 20px;
  // border: 1px solid #E0E0E0;
  &__icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 32px;
    // height: 32px;
    transform: translate(5%, -50%);
  }

  [class*='thought-chain-message-block'] {
    padding: 0 !important;
  }
}
