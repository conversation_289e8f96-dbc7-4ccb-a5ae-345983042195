import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from 'zent';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import Container from '../ui/container';
import Footer from '../ui/footer';
import './index.scss';
import {
  AgentMessageBlockTypeEnum,
  AgentTaskExecutionStatusEnum,
  PAGE_STATUS,
  SHOP_INIT_PROGRESS,
  Stage,
} from '../../../constant';
import { AgentBlock as AgentMessageBlock } from '@youzan/agent-components';
import { saveSkill } from 'pages/task-flow/api/agent';
import { useSkills } from '../../../context/SkillsContext';
import { wait } from '../../../utils';
import WeeklyReportEdit from 'pages/task-flow/components/WxxdAgent/components/CustomEdit/WeeklyReportEdit';
import { SKILL_TEMPLATE_ID } from 'pages/task-flow/components/EditSkillDrawer/SkillCustomEdit';
import { get } from 'lodash';

const START_LAUNCH_BLOCK = {
  type: AgentMessageBlockTypeEnum.TEXT,
  markdown: `开始执行方案：`,
  isStreamTyping: true,
};

const launchBlockTemplate = {
  type: AgentMessageBlockTypeEnum.TASK_EXECUTION,
  taskExecution: {
    steps: [
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置商品托管',
        executionContent: '正在配置商品托管',
        completedContent: '已配置商品托管',
        failedContent: '配置商品托管失败',
      },
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置订单托管',
        executionContent: '正在配置订单托管',
        completedContent: '已配置订单托管',
        failedContent: '配置订单托管失败',
      },
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置履约托管',
        executionContent: '正在配置履约托管',
        completedContent: '已配置履约托管',
        failedContent: '配置履约托管失败',
      },
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置营销托管',
        executionContent: '正在配置营销托管',
        completedContent: '已配置营销托管',
        failedContent: '配置营销托管失败',
      },
    ],
    currentStep: -1,
  },
};

const ApplyPlan = ({ agentId, nextStep }) => {
  const [showLaunchBlock, setShowLaunchBlock] = useState(false);
  const [startLaunchBlock, setStartLaunchBlock] = useState(START_LAUNCH_BLOCK);
  const [steps, setSteps] = useState(launchBlockTemplate.taskExecution.steps);
  const [currentStep, setCurrentStep] = useState(-1);
  const [showFooter, setShowFooter] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [weeklyReportConfig, setWeeklyReportConfig] = useState({});

  const { skills } = useSkills();

  const timeoutsRef = useRef([]);

  const handleInitSkill = async () => {
    skills.forEach(async skill => {
      if (skill.skillTemplateId === SKILL_TEMPLATE_ID.WEEKLY_REPORT) {
        setWeeklyReportConfig(skill.config.flowRule || skill.config.flowTemplate);
      } else {
        await saveSkillWithTimer(skill);
      }
    });
  };

  const saveSkillWithTimer = async skill => {
    if (skill.config.flowRule) {
      const params = {
        skillId: skill?.id,
        agentId,
        enable: skill.enable,
        config: JSON.stringify(skill.config),
      };
      await saveSkill(params);
    }
  };

  const updateStepStatus = (stepIndex, newStatus) => {
    setSteps(prevSteps => {
      const newSteps = [...prevSteps];
      newSteps[stepIndex] = { ...newSteps[stepIndex], status: newStatus };
      return newSteps;
    });
    setCurrentStep(stepIndex);
  };

  const completeStep = currentStep => {
    setSteps(prevSteps => {
      const newSteps = [...prevSteps];
      const nextStepIndex = currentStep + 1;

      newSteps[currentStep] = {
        ...newSteps[currentStep],
        status: AgentTaskExecutionStatusEnum.COMPLETED,
      };

      if (nextStepIndex < newSteps.length) {
        newSteps[nextStepIndex] = {
          ...newSteps[nextStepIndex],
          status: AgentTaskExecutionStatusEnum.RUNNING,
        };
      }
      return newSteps;
    });
    setCurrentStep(currentStep + 1);
  };

  const setupTimers = async () => {
    const animationWaitTime = 2000;
    await wait(animationWaitTime);
    setShowLaunchBlock(true);

    await wait(animationWaitTime);
    updateStepStatus(0, AgentTaskExecutionStatusEnum.RUNNING);

    await wait(animationWaitTime);
    completeStep(0);

    await wait(animationWaitTime);
    completeStep(1);

    await wait(animationWaitTime);
    completeStep(2);

    await wait(animationWaitTime);
    completeStep(3);

    await wait(animationWaitTime);
    setShowFooter(true);
    setStartLaunchBlock(prev => ({
      ...prev,
      markdown: '方案执行完成，微信小店托管已成功开启。',
    }));
  };

  useEffect(() => {
    handleInitSkill();
    setupTimers();

    return () => {
      timeoutsRef.current.forEach(clearTimeout);
    };
  }, []);

  const getLaunchBlock = useCallback(
    () => ({
      ...launchBlockTemplate,
      taskExecution: {
        ...launchBlockTemplate.taskExecution,
        steps,
        currentStep,
      },
    }),
    [steps, currentStep],
  );

  const dynamicLaunchBlock = getLaunchBlock();

  const saveWeeklyReport = async () => {
    setSaveLoading(true);
    const weeklyReportSkill = skills.find(
      skill => skill.skillTemplateId === SKILL_TEMPLATE_ID.WEEKLY_REPORT,
    );

    try {
      await saveSkill({
        skillId: weeklyReportSkill?.id,
        agentId,
        enable: true,
        config: JSON.stringify({ flowRule: weeklyReportConfig }),
      });
      await saveInitProgress({ value: SHOP_INIT_PROGRESS.SUCCESS });
    } catch (error) {}

    await nextStep();
    setSaveLoading(false);
  };

  return (
    <div className="launch">
      <Container>
        <AgentBlockWrapper>
          <AgentMessageBlock block={startLaunchBlock} />
          {showLaunchBlock && (
            <div className="launch__steps">
              <AgentMessageBlock block={dynamicLaunchBlock} />
            </div>
          )}
        </AgentBlockWrapper>

        {showFooter && (
          <>
            <AgentBlockWrapper showIcon={false}>
              <WeeklyReportEdit
                skill={skills.find(
                  skill => skill.skillTemplateId === SKILL_TEMPLATE_ID.WEEKLY_REPORT,
                )}
                uiStyle="card"
                customFlowRuleData={weeklyReportConfig}
                setCustomFlowRuleData={config => {
                  setWeeklyReportConfig(config);
                }}
                useDefaultStaff={true}
              />
            </AgentBlockWrapper>
            <Footer>
              <Button
                loading={saveLoading}
                style={{ margin: '10px 0 0 50px' }}
                onClick={saveWeeklyReport}
                type="primary"
              >
                完成
              </Button>
            </Footer>
          </>
        )}
      </Container>
    </div>
  );
};

export default ApplyPlan;
