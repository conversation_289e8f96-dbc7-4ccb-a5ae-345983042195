import { useEffect, useState } from 'react';
import { BlockLoading, Notify } from 'zent';
import WxxdAIGuide from './StepGuide';
import {
  CERT_AUDIT_STATUS,
  INIT_PROCESS,
  PAGE_STATUS,
  SHOP_AGENT_CREATE_STATUS,
  SHOP_INIT_PROGRESS,
} from './constant';
import WxxdInit from './StepInit';
import { querySkills } from 'pages/task-flow/api/agent';
import {
  getInitProgress,
  getIsEnableNewWXxdAgent,
  getOpenShopStatus,
  queryShopBindAccount,
  saveInitProgress,
} from './api';
import WxxdTips from './common/WxxdTips';
import AuthDialog from './components/AuthDialog';
import WxxdShopInfoEdit from './components/ShopInfoEdit';

import styles from './index.m.scss';

// 定义 tip 类型
interface TipAction {
  label: string;
  onClick: (index?: number) => void;
}

interface Tip {
  message: string;
  actions: TipAction[];
}

const WxxdAgent = ({ agentId, dashboard }) => {
  const [loading, setLoading] = useState(false);
  // 微信小店开店状态
  const [openStatus, setOpenStatus] = useState(null);
  // 协议待签署
  const [agreeDialogVisible, setAgreeDialogVisible] = useState(false);
  // 待完善资料
  const [fillInfoVisible, setFillInfoVisible] = useState(false);
  // 是否是老店
  const [isOldShop, setIsOldShop] = useState(false);
  const [isEnableNewWXxdAgent, setIsEnableNewWXxdAgent] = useState(false);

  const [pageStatus, setPageStatus] = useState(PAGE_STATUS.GUIDE);
  const [tips, setTips] = useState<Tip[]>([]);
  const [initProgress, setInitProgress] = useState(-1);
  const [firstInit, setFirstInit] = useState(false);
  const [skills, setSkills] = useState<any[]>([]);

  const checkWechatOpenStatus = () => {
    Promise.all([
      queryShopBindAccount(),
      getOpenShopStatus(),
      getInitProgress({ agentId: Number(agentId) }),
    ]).then(([shopList, openStatusList, initProgress]) => {
      const initProgressValue = Number(initProgress.value);

      let currentTips: Tip[] = [];
      const openStatus = openStatusList.find(
        item => item.authStatus === SHOP_AGENT_CREATE_STATUS.STARTE,
      );

      if (openStatus) {
        setOpenStatus(openStatus);

        const isAgreementPending =
          CERT_AUDIT_STATUS.AGREEMENT_PENDING === openStatus.certAuditStatus;
        const isAccountPending =
          openStatus.certAuditStatus >= CERT_AUDIT_STATUS.ACCOUNT_PENDING &&
          openStatus.certAuditStatus < CERT_AUDIT_STATUS.AGREEMENT_SUCCESS;
        const isFailedInfoModify =
          CERT_AUDIT_STATUS.FAILED_INFO_MODIFY === openStatus.certAuditStatus ||
          CERT_AUDIT_STATUS.REJECTED === openStatus.certAuditStatus;

        if (isAgreementPending || isAccountPending) {
          currentTips.push({
            message: '微信小店开店申请已通过，请完成开店认证',
            actions: [
              {
                label: isAgreementPending ? '去签署' : '去认证',
                onClick: () => setAgreeDialogVisible(true),
              },
            ],
          });
        } else if (isFailedInfoModify) {
          currentTips.push({
            message: '微信小店开店申请未通过',
            actions: [{ label: '查看详情', onClick: () => setFillInfoVisible(true) }],
          });
        }
      } else if (shopList.length === 0) {
        // 没有代开店记录 且 没有绑定微信小店
        currentTips.push({
          message: '微信小店还未授权有赞，托管待生效',
          actions: [
            {
              label: '去授权',
              onClick: () => {
                window.open(
                  'https://www.youzan.com/v4/wxvideo/video-shop?from=wxshop-agent',
                  '_blank',
                );
              },
            },
            {
              label: '刷新',
              onClick: index => {
                queryShopBindAccount()
                  .then(res => {
                    if (res.length > 0) {
                      setTips(prev => {
                        const newTips = [...prev];
                        if (typeof index === 'number') {
                          newTips.splice(index, 1);
                        }
                        return newTips;
                      });
                    } else {
                      Notify.error('未检测到绑定成功的店铺');
                    }
                  })
                  .catch(() => {
                    Notify.error('刷新失败，请稍后再试');
                  });
              },
            },
          ],
        });
      }

      // 未初始化完成，展示提示
      // if (initProgressValue < SHOP_INIT_PROGRESS.SUCCESS) {
      //   currentTips.unshift({
      //     message: '你的开店流程还未完成，是否继续？',
      //     actions: [
      //       {
      //         label: '继续开店',
      //         onClick: () => setPageStatus(PAGE_STATUS.GUIDE),
      //       },
      //       {
      //         label: '忽略',
      //         onClick: index => {
      //           saveInitProgress({ value: SHOP_INIT_PROGRESS.SUCCESS });
      //           setTips(prev => {
      //             const newTips = [...prev];
      //             if (typeof index === 'number') {
      //               newTips.splice(index, 1);
      //             }
      //             return newTips;
      //           });
      //         },
      //       },
      //     ],
      //   });
      // }

      setTips(currentTips);
    });
  };

  useEffect(() => {
    let mounted = true;
    setLoading(true);

    Promise.all([
      getIsEnableNewWXxdAgent(),
      querySkills({ agentId: Number(agentId) }),
      getInitProgress({ agentId: Number(agentId) }),
    ]).then(([isEnableNewWXxdAgent, skills, initProgress]) => {
      if (mounted) {
        const initProgressValue = Number(initProgress.value);

        if (isEnableNewWXxdAgent) {
          // 有启用的技能，代表是老商家
          let hasEnabledSkill = skills.some(item => item.enable);
          if (
            // 初始化阶段处于[2-4]，当新商家处理
            initProgressValue >= SHOP_INIT_PROGRESS.ONE_CLICK_STORE &&
            initProgressValue <= SHOP_INIT_PROGRESS.EXECUTE_PLAN
          ) {
            setIsOldShop(false);
          } else {
            setIsOldShop(hasEnabledSkill);
          }

          setFirstInit(initProgressValue == SHOP_INIT_PROGRESS.INIT);

          // 跳过初始化 或者 初始化完成
          if (initProgressValue >= SHOP_INIT_PROGRESS.SKIP) {
            setPageStatus(PAGE_STATUS.DASHBOARD);
          }
        } else {
          // 白名单未开启，直接进入首页
          setPageStatus(PAGE_STATUS.DASHBOARD);
        }

        setIsEnableNewWXxdAgent(isEnableNewWXxdAgent);
        setInitProgress(initProgressValue);
        setSkills(skills);
        setLoading(false);
      }
    });
    return () => {
      mounted = false;
    };
  }, []); // 移除 agentId 依赖,只在组件挂载时执行一次

  // 只在 PAGE_STATUS.DASHBOARD 时计算 tips
  useEffect(() => {
    if (pageStatus === PAGE_STATUS.DASHBOARD && isEnableNewWXxdAgent) {
      checkWechatOpenStatus();
    }
  }, [pageStatus, isEnableNewWXxdAgent]);

  if (loading) return <BlockLoading />;

  switch (pageStatus) {
    case PAGE_STATUS.GUIDE:
      return (
        <WxxdAIGuide
          isOldShop={isOldShop}
          firstInit={firstInit}
          nextAgentStatus={setPageStatus}
          gotoDashboard={() => {
            saveInitProgress({ value: SHOP_INIT_PROGRESS.SUCCESS });
            setPageStatus(PAGE_STATUS.DASHBOARD);
          }}
        />
      );
    case PAGE_STATUS.INIT:
      return (
        <WxxdInit
          nextAgentStatus={() => {
            setPageStatus(PAGE_STATUS.DASHBOARD);
          }}
          initProgress={initProgress}
          skills={skills}
          agentId={agentId}
        />
      );
    case PAGE_STATUS.DASHBOARD:
      return dashboard({
        tips: (
          <div>
            {tips.map((item, index) => (
              <div className={styles.wxxdTips} key={`wxxd-tips-${index}`}>
                <WxxdTips index={index} tip={item} />
              </div>
            ))}

            <AuthDialog visible={agreeDialogVisible} onClose={() => setAgreeDialogVisible(false)} />

            <WxxdShopInfoEdit
              visible={fillInfoVisible}
              openStatus={openStatus}
              onClose={() => setFillInfoVisible(false)}
            />
          </div>
        ),
      });
    default:
      return <></>;
  }
};

export default WxxdAgent;
