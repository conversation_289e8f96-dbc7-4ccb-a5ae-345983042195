import request from 'common/request';
import ajax from 'zan-pc-ajax';

const API_PREFIX = '/v4/third-plugin/wechat-shop-management';

/**
 * 查询已授权微信小店
 * @param {*} query
 * @returns
 */
export const queryShopBindAccount = () =>
  request.get('/v4/wxvideo/api/video-shop/queryShopBindAccount');

/**
 * 查询开店资料
 * @param {} query
 * @returns
 */
export const getDetail = (params: any) => request.get(`${API_PREFIX}/getDetail`, params);

export const editAndSubmit = (params: any) => request.post(`${API_PREFIX}/editAndSubmit`, params);

export const getAuthLink = (params: any) => request.get(`${API_PREFIX}/getAuthLink`, params);

export const getOpenShopStatus = () => request.get(`${API_PREFIX}/getOpenShopStatus`);

export const getWechatOpenShopStatus = (params?: any) =>
  request.get(`${API_PREFIX}/getWechatOpenShopStatus`, params);

export const getInitProgress = (params: any) =>
  request.get(`${API_PREFIX}/getInitProgress`, params);

export const saveInitProgress = (params: any) =>
  request.post(`${API_PREFIX}/saveInitProgress`, params);

export const querySettleBankList = () => request.get(`${API_PREFIX}/querySettleBankList`);

export const brandDictionarySearch = (params: any) =>
  request.get(`${API_PREFIX}/brandDictionarySearch`, params);

export const getDingtalkConfig = () =>
  request.get('/v4/message/api/messagepush/channel/config/dingtalk');

export const getWecomConfig = () => request.get('/v4/message/api/messagepush/channel/config/wecom');

export const getWecomStaffList = (params: any) =>
  request.get(`${API_PREFIX}/getWecomStaffList`, params);

export const getUploadToken = (params: any) => request.get(`${API_PREFIX}/getUploadToken`, params);

export const getRegionModelById = (params: any) =>
  request.get(`${API_PREFIX}/getRegionModelById`, params);

export const getXdWeekBusinessReport = (params: any) =>
  request.get(`${API_PREFIX}/getXdWeekBusinessReport`, params);

/** 查询20个高级管理员 */
export const findFirst20Admin = () => {
  return ajax<any>('/v4/shop/setting/staff/findStaff.json', {
    method: 'GET',
    data: {
      page: 1,
      pageSize: 20,
      roleId: 1,
      status: 'ON',
    },
  });
};

/**
 * 获取是否开启新版微信小店
 * 原本是留给品牌开关的，现在弃用了，因为pc-v4已经发布，就不改命名了
 */
export const getIsEnableNewWXxdAgent = () => request.get(`${API_PREFIX}/getIsEnableBrand`);
