import React from 'react';
import { Icon } from 'zent';
import styles from './ReportItem.m.scss';
import type { IReportItem } from '..';

interface CompareData {
  value: number;
  text: string;
  isIncrease: boolean;
}

interface ReportItemProps {
  title: string;
  value: IReportItem;
  unit?: string;
  compareData?: CompareData[];
  loading?: boolean;
}

const ReportItem: React.FC<ReportItemProps> = ({
  title,
  value,
  unit = '',
  compareData = [],
  loading = false,
}) => {
  if (loading) {
    return (
      <div className={styles.reportItem}>
        <div className={styles.skeleton}></div>
      </div>
    );
  }

  return (
    <div className={styles.reportItem}>
      <div className={styles.title}>{title}</div>
      {value.visitNum !== undefined ? (
        <div className={styles.value}>
          {value.visitNum}
          {unit && <span className={styles.unit}>{unit}</span>}
        </div>
      ) : (
        <div className={styles.value}>
          {value.visitPayRate}
        </div>
      )}
      <div className={styles.compareList}>
        {compareData.map((item, index) => (
          <div key={index} className={styles.compareItem}>
            <span className={styles.compareText}>{item.text}</span>
            <span
              className={`${styles.compareValue} ${
                item.value === 0
                  ? styles.unchanged
                  : item.isIncrease
                    ? styles.increase
                    : styles.decrease
              }`}
            >
              {item.value !== 0 && (
                <Icon
                  type={item.isIncrease ? 'caret-up' : 'caret-down'}
                  className={styles.icon}
                />
              )}
              {item.value}%
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReportItem;
