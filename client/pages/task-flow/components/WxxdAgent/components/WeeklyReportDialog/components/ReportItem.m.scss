.reportItem {
  width: 130px;
  display: flex;
  flex-direction: column;
  padding: 12px;
  padding-left: 0;
  background-color: #fff;
  border-radius: 4px;
}

.title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.value {
  font-size: 28px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: flex;
  align-items: baseline;
}

.unit {
  font-size: 14px;
  color: #999;
  margin-left: 4px;
  margin-bottom: 4px;
}

.compareList {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.compareItem {
  display: flex;
  align-items: center;
  gap: 10px;
}

.compareText {
  font-size: 13px;
  color: #999;
}

.compareValue {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.icon {
  font-size: 12px;
  margin-right: 4px;
}

.increase {
  color: #f44336;
}

.decrease {
  color: #4bae4f;
}

.unchanged {
  color: #999;
}

.skeleton {
  width: 100%;
  height: 90px;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
