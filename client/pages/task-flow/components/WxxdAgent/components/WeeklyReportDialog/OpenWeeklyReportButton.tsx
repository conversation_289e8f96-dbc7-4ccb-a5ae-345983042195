import React, { useEffect, useState } from 'react';
import WeeklyReportDialog from '.';
import { getXdWeekBusinessReport } from '../../api';

const OpenWeeklyReportButton: React.FC<{ weekStr: string; executeTime: string }> = ({
  weekStr,
  executeTime,
}) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<any[]>([]);

  // 模拟加载数据
  const fetchData = () => {
    setLoading(true);
    getXdWeekBusinessReport({
      weekStr,
    })
      .then(res => {
        setReportData([
          {
            title: '本周自动执行操作',
            unit: '次',
            visitNum: res.operatorCnt,
            visitPayRate: 11,
            visitRateCompareWeek: res.operatorChangePercent,
          },
          {
            title: '物流异常提醒',
            visitNum: res.abnormalLogisticsCnt,
            unit: '次',
            visitPayRate: 11,
            visitRateCompareWeek: res.abnormalLogisticsChangePercent,
          },
          {
            title: '订单收货地址修改',
            unit: '次',
            visitNum: res.modifyOrderAddressCnt,
            visitPayRate: 11,
            visitRateCompareWeek: res.modifyOrderAddressChangePercent,
          },
          {
            title: '订单发货超时提醒',
            unit: '次',
            visitNum: res.shippingTimeoutCnt,
            visitPayRate: 11,
            visitRateCompareWeek: res.shippingTimeoutChangePercent,
          },
          {
            title: '为你节约工作量',
            visitNum: res.saveWorkTime,
            unit: '人日',
            visitPayRate: 11,
            visitRateCompareWeek: res.saveWorkTimeChangePercent,
          },
        ]);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    // 组件挂载时可以预加载数据
    fetchData();
  }, []);

  const handleOpenReport = () => {
    // 打开弹窗前可以刷新数据
    fetchData();
    setVisible(true);
  };

  return (
    <div>
      <a onClick={handleOpenReport}>查看周报</a>

      <WeeklyReportDialog
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        loading={loading}
        data={reportData}
        time={executeTime}
      />
    </div>
  );
};

export default OpenWeeklyReportButton;
