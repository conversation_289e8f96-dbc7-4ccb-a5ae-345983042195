import React from 'react';
import { Dialog } from 'zent';
import styles from './index.m.scss';
import ReportItem from './components/ReportItem';
import { formatDisplayTime } from 'fns/time';

export interface IReportItem {
  title: string;
  visitNum?: string;
  unit?: string;
  visitPayRate?: number;
  visitRateCompareWeek?: number;
}

interface WeeklyReportDialogProps {
  visible: boolean;
  onClose: () => void;
  loading?: boolean;
  time?: string;
  data?: Array<IReportItem>;
}

const WeeklyReportDialog: React.FC<WeeklyReportDialogProps> = ({
  visible,
  onClose,
  loading = false,
  data = [],
  time = '',
}) => {
  return (
    <Dialog
      visible={visible}
      onClose={onClose}
      className={styles.reportDialog}
      title={
        <div className={styles.reportTitleContainer}>
          <div className={styles.reportTitle}>店铺经营分析报告</div>
          <div className={styles.reportTime}>统计时间：{formatDisplayTime(time)}</div>
        </div>
      }
      footer={null}
    >
      <div className={styles.reportContainer}>
        <div className={styles.reportType}>
          <span className={styles.reportTypeTitle}>数据概况</span>
        </div>
        <div className={styles.reportContent}>
          {data.map(item => {
            const { visitRateCompareWeek = 0 } = item;
            return (
              <ReportItem
                title={item.title}
                value={item}
                unit={item.unit}
                loading={loading}
                compareData={[
                  {
                    text: '较上周',
                    value: Math.abs(visitRateCompareWeek),
                    isIncrease: visitRateCompareWeek >= 0,
                  },
                ]}
              />
            );
          })}
        </div>
      </div>
    </Dialog>
  );
};

export default WeeklyReportDialog;
