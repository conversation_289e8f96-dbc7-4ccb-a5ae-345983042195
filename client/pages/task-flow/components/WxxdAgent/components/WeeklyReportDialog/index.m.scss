.weeklyReport {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;

  & > div {
    min-width: 240px;
    flex: 1;
  }
}

.reportTitleContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}
.reportTitle {
  font-size: 20px;
  font-weight: 500;
}
.reportTime {
  font-size: 12px;
  color: #999;
}

.reportContainer {
  display: flex;
  flex-direction: row;
  gap: 36px;
}

.reportType {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 80px;
  cursor: pointer;
}

.reportTypeTitle {
  width: 80px;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #f1f6ff;
  font-weight: 500;
  color: #155bd4;
}

.reportContent {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 32px 48px;
  width: 550px;
}
