.formContainer {
  .formItem {
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
  }

  .formLabel {
    font-size: 14px;
    width: 140px;
    text-align: right;
    margin-right: 8px;
    height: 32px;
    line-height: 32px;
  }

  .formContent {
    width: 400px;
    height: 32px;
    line-height: 32px;
  }

  .checkboxItem {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 16px;
  }

  .required {
    color: #f44;
    margin-right: 4px;
  }
}

.weeklyReportCustomEdit {
  .formLabel {
    width: 100px;
  }
}

// 商品选择结果展示
.itemSelectField {
  height: 32px;
  flex-grow: 1;
  cursor: pointer;
  background-color: #fff;
  text-align: center;
  padding: 0 8px;
  border-radius: 4px;
  color: #155bd4;
  box-sizing: border-box;
  display: flex;

  .title {
    // 一行超出显示省略号
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 160px;
  }
  .count {
    white-space: pre-wrap;
  }
}
.value {
  color: #155bd4;
}

.autoHostedTip {
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0px;
  margin-top: -16px;
  margin-left: 148px;
  color: #999999;
}

// 商品选择抽屉
.goodsSelector {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 16px 4px;
  box-sizing: border-box;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

.drawerFooter {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 12px;
}

.goodsList {
  flex: 1;
  overflow: hidden;
}

.goodsPagination {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.goodsSearchBar {
  padding: 12px 0;
  width: 200px;
}

.goods-container {
  .goods-card {
    display: flex;

    img {
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
    .info {
      margin-left: 12px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-weight: 400;
      line-height: 20px;

      .name {
        color: #155bd4;
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .price {
        color: #dd712f;
      }
    }
  }
}

.channelActions {
  display: flex;
  margin-left: 8px;

  a {
    color: #38f;
    margin-right: 8px;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      text-decoration: underline;
    }
  }

  .disabledLink {
    color: #999;
    cursor: not-allowed;

    &:hover {
      text-decoration: none;
    }
  }
}
