.notice-config {
  border-radius: 4px;

  &__title {
    font-size: 14px;
    color: #333;
    margin-bottom: 16px;
  }

  &__cards {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  &__card {
    // flex: 1;
    min-width: 400px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f2f3f5;
      background-color: #fff;
    }

    &-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    &-content {
      padding: 16px;
    }

    &-desc {
      font-size: 14px;
      color: #666;
      margin-bottom: 16px;
      line-height: 1.5;
    }
  }

  &__action-link {
    color: #3860f4;
    margin-left: 12px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      color: #2347db;
    }

    &--disabled {
      color: #999;
      cursor: not-allowed;

      &:hover {
        color: #999;
      }
    }
  }

  &__select-wrapper {
    margin-top: 8px;
  }

  // zent组件覆盖样式
  :global {
    .zent-switch {
      &-checked {
        background-color: #3860f4;
      }
    }
  }
}

.wecom-staff-selector-scroller {
  height: 200px;

  .loading-text, .no-data {
    text-align: center;
    padding: 10px 0;
    color: #999;
    font-size: 14px;
  }
}
.wecom-staff-selector__select-mobile {
  color: #999;
  font-size: 14px;
  margin-left: 8px;
}

.weekly_channel_checkbox {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 16px;

  .zent-checkbox-wrap {
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  .zent-checkbox-label {
    display: flex;
    align-items: center;
    flex-direction: row;
  }

  &_disable_text {
    font-size: 12px;
    color: #999;
    margin-right: 8px;
  }
}
