import { useState, useEffect } from 'react';
import { IVariable, EGroupCode } from 'pages/work-flow/constant';
import { Checkbox, Switch, Notify } from 'zent';
import SelectItem from '../../../../SubDrawer/SelectItem';
import { isRetailChainStore } from '@youzan/utils-shop';
import { findFirst20Admin, getDingtalkConfig, getWecomConfig } from '../../../api';
import WecomStaffSelector from './WecomStaffSelector';

import './index.scss';
import styles from '../styles.m.scss';
import { isEmpty } from 'lodash';

// 类型定义
interface WecomStaffItem {
  staffId: string;
  staffName: string;
  mobileAccount?: string;
  mobileNumber?: string;
}

interface StaffListResponse {
  content: WecomStaffItem[];
  total: number;
}

const MESSAGE_CHANNEL = {
  APP: 341,
  SMS: 204,
  WX: 205,
  DING: 206,
};

const receiverVariable: IVariable = {
  // @ts-ignore
  code: 'receiver',
  dynamicEndpointConfig:
    '{"apiId":"storeStaff","responseCovert":"id","isMultiSelect":true,"componentStyle":"dropDownSelection"}',
  id: 25,
  name: '消息接收人',
  type: 1,
  valueSource: 3,
};

const channelMap = {
  [MESSAGE_CHANNEL.APP]: 'AppMsgChannel',
  [MESSAGE_CHANNEL.WX]: 'WecomHelperMsgChannel',
  [MESSAGE_CHANNEL.DING]: 'DingTalkMsgChannel',
};

// 渠道配置
const CHANNEL_CONFIG = {
  [MESSAGE_CHANNEL.APP]: {
    code: 'AppMsgChannel',
    name: 'App通知',
    title: 'App消息',
    description: '通过有赞微商城、有赞门店App的消息频道发送消息',
    id: 1,
    groupCode: EGroupCode.SYSTEM_COMMON,
  },
  [MESSAGE_CHANNEL.WX]: {
    code: 'WecomHelperMsgChannel',
    name: '企业微信',
    title: '企业微信',
    description: '通过企业微信发送消息',
    id: 2,
    groupCode: EGroupCode.WECOM_HELPER,
  },
  [MESSAGE_CHANNEL.DING]: {
    code: 'DingTalkMsgChannel',
    name: '钉钉',
    title: '钉钉',
    description: '通过钉钉发送消息',
    id: 3,
    groupCode: EGroupCode.DING_TALK,
  },
};

interface IConfig {
  msgChannel: number[];
  receiversByChannel: Record<string, any[]>;
}

// 定义组件属性类型
interface WeeklyReportCustomEditProps {
  skill: any;
  customFlowRuleData: any;
  setCustomFlowRuleData: (data: any) => void;
  uiStyle?: 'default' | 'card'; // 默认使用原有的UI，card使用NoticeConfig风格的UI
  useDefaultStaff?: boolean;
}

// 渠道选择器组件属性
interface ChannelSelectorProps {
  channelKey: number;
  disabled: boolean;
  selectedData: string;
  onReceiverChange: (channel: string, value: any) => void;
}

// 抽象的渠道选择器组件
const ChannelSelector = ({
  channelKey,
  disabled,
  selectedData,
  onReceiverChange,
}: ChannelSelectorProps) => {
  const channel = CHANNEL_CONFIG[channelKey];

  return channelKey === MESSAGE_CHANNEL.WX ? (
    <WecomStaffSelector
      disabled={disabled}
      value={selectedData}
      onChange={value => {
        onReceiverChange(channelMap[channelKey], {
          actionFieldValueMaps4Display: JSON.stringify({
            WecomHelperMsgChannel: value,
          }),
        });
      }}
    />
  ) : (
    <SelectItem
      disabled={disabled}
      setEditingAction={value => {
        onReceiverChange(channelMap[channelKey], value);
      }}
      variable={{
        ...receiverVariable,
        // @ts-ignore
        code: channelMap[channelKey],
      }}
      extParam={{ msgChannel: [channelKey] }}
      subDrawerData={{
        code: channelMap[channelKey],
        description: '',
        id: channel.id,
        name: channel.name,
        type: '',
        variables: [],
        groupCode: channel.groupCode,
        canUse: true,
        supportFlowConcatenation: false,
      }}
      selectedData={selectedData}
      editingAction={{
        name: channel.name,
        variables: [],
      }}
    />
  );
};

const WeeklyReportEdit = ({
  customFlowRuleData,
  setCustomFlowRuleData,
  uiStyle = 'default',
  useDefaultStaff = false,
}: WeeklyReportCustomEditProps) => {
  const { chainRules } = customFlowRuleData ?? {};
  const chainRule = chainRules?.[0] ?? {};
  const { seriesActions = [] } = chainRule ?? {};
  const seriesAction = seriesActions?.[0] ?? {};
  const actionFieldValueMaps4ExecuteJSON = JSON.parse(
    seriesAction?.actionFieldValueMaps4Execute || '{}',
  );
  const actionFieldValueMaps4DisplayJSON = JSON.parse(
    seriesAction?.actionFieldValueMaps4Display || '{}',
  );
  const defaultConfig = {
    ...actionFieldValueMaps4ExecuteJSON,
    ...actionFieldValueMaps4DisplayJSON,
  };

  const [config, setConfig] = useState<IConfig>({ msgChannel: [], receiversByChannel: {} });

  useEffect(() => {
    if (isEmpty(config.receiversByChannel) && !isEmpty(defaultConfig)) {
      setConfig({
        msgChannel: defaultConfig?.msgChannel || [],
        receiversByChannel: defaultConfig?.receiversByChannel || {
          DingTalkMsgChannel: [],
          AppMsgChannel: [],
          WecomHelperMsgChannel: [],
        },
      });
    }
  }, [defaultConfig]);

  // 企微相关状态
  const [wecomStatus, setWecomStatus] = useState({
    loading: false,
    available: false,
  });

  // 钉钉相关状态
  const [dingtalkStatus, setDingtalkStatus] = useState({
    loading: false,
    available: true, // 默认可用，与原WeeklyReportCustomEdit保持一致
  });

  // 初始化时查询企微渠道状态
  useEffect(() => {
    // 无论是哪种 UI 样式都查询通道状态
    Promise.all([
      queryChannelStatus(MESSAGE_CHANNEL.DING),
      queryChannelStatus(MESSAGE_CHANNEL.WX),
    ]).then(([isDingAvailable, isWecomAvailable]) => {
      if (useDefaultStaff) {
        findFirst20Admin().then(res => {
          const defaultReceivers = res?.items?.map(staff => {
            return {
              id: staff.adminId,
              name: staff.name,
              linkPhone: staff.linkPhone,
            };
          });

          setConfig({
            ...config,
            msgChannel: [MESSAGE_CHANNEL.APP].concat(isDingAvailable ? [MESSAGE_CHANNEL.DING] : []),
            receiversByChannel: {
              ...config.receiversByChannel,
              AppMsgChannel: defaultReceivers,
              DingTalkMsgChannel: defaultReceivers,
              WecomHelperMsgChannel: defaultReceivers,
            },
          });
        });
      }
    });
  }, []);

  // 查询消息通道状态
  const queryChannelStatus = async (channelKey: number) => {
    const isWX = channelKey === MESSAGE_CHANNEL.WX;
    const isDing = channelKey === MESSAGE_CHANNEL.DING;

    const setChannelStatus = isWX ? setWecomStatus : setDingtalkStatus;
    setChannelStatus(prev => ({ ...prev, loading: true }));

    try {
      const res = isWX ? await getWecomConfig() : isDing ? await getDingtalkConfig() : {};
      // 始终可用，与NoticeConfig保持一致
      const isAvailable = res.status == 1;
      setChannelStatus({
        loading: false,
        available: isAvailable,
      });
      return isAvailable;
    } catch (error) {
      console.error('查询状态失败', error);
      setChannelStatus({
        loading: false,
        available: false,
      });
    }
    return false;
  };

  // 刷新渠道状态
  const refreshChannelStatus = async (channelKey: number) => {
    await queryChannelStatus(channelKey);
    Notify.info('状态已刷新');
  };

  // 更新自定义编辑数据
  const updateCustomEditData = (newConfig: IConfig) => {
    setConfig(newConfig);

    setCustomFlowRuleData({
      ...customFlowRuleData,
      chainRules: [
        {
          ...chainRule,
          // action: {
          //   actionFieldValueMaps4Execute: '', //任务执行参数传值的位置
          //   id: 65, //固定值
          // },
          seriesActions: [
            {
              ...seriesAction,
              actionFieldValueMaps4Execute: JSON.stringify({
                msgChannel: newConfig.msgChannel,
                receiversByChannel: {
                  DingTalkMsgChannel: newConfig.receiversByChannel?.DingTalkMsgChannel?.map(
                    item => item.id,
                  ),
                  AppMsgChannel: newConfig.receiversByChannel?.AppMsgChannel?.map(item => item.id),
                  WecomHelperMsgChannel: newConfig.receiversByChannel?.WecomHelperMsgChannel?.map(
                    item => item.id,
                  ),
                },
              }),
              actionFieldValueMaps4Display: JSON.stringify({
                msgChannel: newConfig.msgChannel,
                receiversByChannel: {
                  DingTalkMsgChannel: newConfig.receiversByChannel?.DingTalkMsgChannel?.map(
                    item => {
                      return {
                        id: item.id,
                        name: item.name,
                      };
                    },
                  ),
                  AppMsgChannel: newConfig.receiversByChannel?.AppMsgChannel?.map(item => {
                    return {
                      id: item.id,
                      name: item.name,
                    };
                  }),
                  WecomHelperMsgChannel: newConfig.receiversByChannel?.WecomHelperMsgChannel?.map(
                    item => {
                      return {
                        id: item.id,
                        name: item.name,
                      };
                    },
                  ),
                },
              }),
              // id: 66, //固定值，发消息的操作
            },
          ],
        },
      ],
    });
  };

  const handleMessageChannelsChange = (value: number[]) => {
    updateCustomEditData({
      ...config,
      msgChannel: value,
    });
  };

  const handleMessageReceiverChange = (channel: string, value: any) => {
    const display = JSON.parse(value?.actionFieldValueMaps4Display || '{}');

    const newReceiversByChannelDisplay = {
      ...config.receiversByChannel,
      ...display,
    };

    updateCustomEditData({
      ...config,
      receiversByChannel: newReceiversByChannelDisplay,
    });
  };

  // 处理卡片UI样式的通道开关变更
  const handleSwitchChange = (channelKey: number, checked: boolean) => {
    const newMsgChannel = [...config.msgChannel];

    if (checked && !newMsgChannel.includes(channelKey)) {
      newMsgChannel.push(channelKey);
    } else if (!checked && newMsgChannel.includes(channelKey)) {
      const index = newMsgChannel.indexOf(channelKey);
      newMsgChannel.splice(index, 1);
    }

    updateCustomEditData({
      ...config,
      msgChannel: newMsgChannel,
    });
  };

  // 构建SelectItem需要的receiversByChannel数据
  const getReceiversByChannel = () => {
    return JSON.stringify(config.receiversByChannel);
  };

  // 默认UI样式（原WeeklyReportCustomEdit风格）
  const renderDefaultUI = () => {
    return (
      <div className={`${styles.formContainer} ${styles.weeklyReportCustomEdit}`}>
        <div className={styles.formItem}>
          <div className={styles.formLabel}>
            <span className={styles.required}>*</span>
            <span>推送渠道：</span>
          </div>
          <div className={styles.formContent}>
            <Checkbox.Group value={config.msgChannel} onChange={handleMessageChannelsChange}>
              <div className={styles.checkboxItem}>
                <Checkbox value={MESSAGE_CHANNEL.APP}>
                  {CHANNEL_CONFIG[MESSAGE_CHANNEL.APP].title}
                </Checkbox>
                <ChannelSelector
                  channelKey={MESSAGE_CHANNEL.APP}
                  disabled={!config.msgChannel.includes(MESSAGE_CHANNEL.APP)}
                  selectedData={getReceiversByChannel()}
                  onReceiverChange={handleMessageReceiverChange}
                />
              </div>

              <div className="weekly_channel_checkbox">
                <Checkbox value={MESSAGE_CHANNEL.WX} disabled={!wecomStatus.available}>
                  {CHANNEL_CONFIG[MESSAGE_CHANNEL.WX].title}
                  {!wecomStatus.available && (
                    <div className={styles.channelActions}>
                      <span className="weekly_channel_checkbox_disable_text">暂未开通</span>
                      <a
                        href="https://www.youzan.com/v4/message/messagepush#/channel"
                        target="_blank"
                      >
                        去设置
                      </a>
                      <a
                        className={wecomStatus.loading ? styles.disabledLink : ''}
                        onClick={() =>
                          !wecomStatus.loading && refreshChannelStatus(MESSAGE_CHANNEL.WX)
                        }
                      >
                        {wecomStatus.loading ? '刷新中...' : '刷新'}
                      </a>
                    </div>
                  )}
                </Checkbox>

                {
                  <ChannelSelector
                    channelKey={MESSAGE_CHANNEL.WX}
                    disabled={
                      !wecomStatus.available || !config.msgChannel.includes(MESSAGE_CHANNEL.WX)
                    }
                    selectedData={getReceiversByChannel()}
                    onReceiverChange={handleMessageReceiverChange}
                  />
                }
              </div>

              {!isRetailChainStore && (
                <div className={styles.checkboxItem}>
                  <Checkbox value={MESSAGE_CHANNEL.DING} disabled={!dingtalkStatus.available}>
                    {CHANNEL_CONFIG[MESSAGE_CHANNEL.DING].title}
                    {!dingtalkStatus.available && (
                      <div className={styles.channelActions}>
                        <a
                          href="https://www.youzan.com/v4/message/messagepush#/channel"
                          target="_blank"
                        >
                          去设置
                        </a>
                        <a
                          className={dingtalkStatus.loading ? styles.disabledLink : ''}
                          onClick={() =>
                            !dingtalkStatus.loading && refreshChannelStatus(MESSAGE_CHANNEL.DING)
                          }
                        >
                          {dingtalkStatus.loading ? '刷新中...' : '刷新'}
                        </a>
                      </div>
                    )}
                  </Checkbox>

                  {
                    <ChannelSelector
                      channelKey={MESSAGE_CHANNEL.DING}
                      disabled={
                        !dingtalkStatus.available ||
                        !config.msgChannel.includes(MESSAGE_CHANNEL.DING)
                      }
                      selectedData={getReceiversByChannel()}
                      onReceiverChange={handleMessageReceiverChange}
                    />
                  }
                </div>
              )}
            </Checkbox.Group>
          </div>
        </div>
      </div>
    );
  };

  // 渲染卡片样式的渠道卡片
  const renderChannelCard = (channelKey: number) => {
    const channel = CHANNEL_CONFIG[channelKey];
    const isWX = channelKey === MESSAGE_CHANNEL.WX;
    const isDing = channelKey === MESSAGE_CHANNEL.DING;

    const isAvailable = isWX ? wecomStatus.available : isDing ? dingtalkStatus.available : true;
    const isLoading = isWX ? wecomStatus.loading : isDing ? dingtalkStatus.loading : false;

    return (
      <div className="notice-config__card">
        <div className="notice-config__card-header">
          <div className="notice-config__card-title">{channel.title}</div>
          <Switch
            checked={config.msgChannel.includes(channelKey)}
            disabled={isLoading || !isAvailable}
            onChange={checked => handleSwitchChange(channelKey, checked)}
          />
        </div>
        <div className="notice-config__card-content">
          <div className="notice-config__card-desc">
            {channel.description}
            {!isAvailable && (
              <>
                <a
                  className="notice-config__action-link"
                  href="https://www.youzan.com/v4/message/messagepush#/channel"
                  target="_blank"
                >
                  去设置
                </a>
                <a
                  className={`notice-config__action-link ${
                    isLoading ? 'notice-config__action-link--disabled' : ''
                  }`}
                  onClick={() => !isLoading && refreshChannelStatus(channelKey)}
                >
                  {isLoading ? '刷新中...' : '刷新'}
                </a>
              </>
            )}
          </div>
          {isAvailable && config.msgChannel.includes(channelKey) && (
            <div className="notice-config__select-wrapper">
              <ChannelSelector
                channelKey={channelKey}
                disabled={!config.msgChannel.includes(channelKey)}
                selectedData={getReceiversByChannel()}
                onReceiverChange={handleMessageReceiverChange}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  // 卡片UI样式（NoticeConfig风格）
  const renderCardUI = () => {
    return (
      <div className="notice-config">
        <div className="notice-config__title">
          你可在下方选择消息的推送渠道，我将会每周为你推送店铺托管报告:
        </div>

        <div className="notice-config__cards">
          {/* App消息卡片 */}
          {renderChannelCard(MESSAGE_CHANNEL.APP)}

          {/* 企业微信卡片 */}
          {renderChannelCard(MESSAGE_CHANNEL.WX)}

          {/* 钉钉卡片 */}
          {!isRetailChainStore && renderChannelCard(MESSAGE_CHANNEL.DING)}
        </div>
      </div>
    );
  };

  return uiStyle === 'card' ? renderCardUI() : renderDefaultUI();
};

export default WeeklyReportEdit;
