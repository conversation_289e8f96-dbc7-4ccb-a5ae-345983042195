import { useState, useEffect, useCallback } from 'react';
import { Select, InfiniteScroller, InlineLoading } from 'zent';
import { getWecomStaffList } from '../../../api';
import { debounce } from 'lodash';

/**
 * 通知配置组件
 * 用于设置微信小店通知方式和接收人
 */
const WecomStaffSelector = ({ value, disabled, onChange }) => {
  // 渠道状态加载中
  const [loading, setLoading] = useState(false);

  // 企微员工列表数据
  const [wecomStaffList, setWecomStaffList] = useState<any[]>([]);
  const [wecomStaffPage, setWecomStaffPage] = useState({
    pageNum: 1,
    pageSize: 20,
    total: 0,
  });
  const [wecomSearchName, setWecomSearchName] = useState('');
  const [keyword, setKeyword] = useState(wecomSearchName);
  const [selectedIds, setSelectedIds] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);

  // 防抖处理的搜索函数
  const debouncedSearch = useCallback(
    debounce(value => {
      setWecomSearchName(value);
      setWecomStaffPage(prev => ({
        ...prev,
        pageNum: 1,
      }));
      // 重置列表和更多状态
      setWecomStaffList([]);
      setHasMore(true);
    }, 300),
    [setWecomSearchName, setWecomStaffPage, setWecomStaffList, setHasMore],
  );

  // 组件卸载时取消pending的防抖调用
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  useEffect(() => {
    if (value) {
      const selectedIds = JSON.parse(value);
      setSelectedIds(
        selectedIds.WecomHelperMsgChannel?.map(staff => ({
          key: staff.id,
          text: staff.name,
        })),
      );
    }
  }, [value]);

  // 获取企微员工列表
  const fetchWecomStaffList = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getWecomStaffList({
        pageNum: 1, // 始终从第一页开始获取
        pageSize: wecomStaffPage.pageSize,
        searchName: wecomSearchName,
      });

      if (res?.content) {
        // 计算是否有更多数据
        const total = res.total || 0;
        const newContent = res.content.map(staff => ({
          key: staff.staffId,
          text: staff.staffName,
          mobile: staff.mobileAccount || staff.mobileNumber,
        }));

        // 初始化列表数据
        setWecomStaffList(newContent);

        // 更新分页信息
        setWecomStaffPage({
          pageNum: 1,
          pageSize: wecomStaffPage.pageSize,
          total,
        });

        // 判断是否还有更多数据可加载
        const hasReachedEnd = wecomStaffPage.pageSize >= total;
        const hasResults = newContent.length > 0;
        setHasMore(!hasReachedEnd && hasResults);
      } else {
        // 如果没有内容，则设置没有更多数据
        setWecomStaffList([]);
        setHasMore(false);
      }
    } catch (error) {
      console.error('获取企微员工列表失败', error);
      setWecomStaffList([]);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [wecomSearchName, wecomStaffPage.pageSize]);

  // 查询企微员工列表
  useEffect(() => {
    let isMounted = true;

    const doFetch = async () => {
      if (isMounted) {
        await fetchWecomStaffList();
      }
    };

    doFetch();

    return () => {
      isMounted = false;
    };
  }, [fetchWecomStaffList]);

  // 处理企微员工选择变更
  const handleWecomStaffChange = wecomSelectedIds => {
    onChange && onChange(wecomSelectedIds.map(staff => ({ id: staff.key, name: staff.text })));
  };

  // 处理企微搜索
  const handleWecomSearch = value => {
    setKeyword(value); // 更新关键词状态，立即更新UI

    // 如果清空搜索词，立即搜索所有数据
    if (!value) {
      // 如果当前已经是空搜索，不需要重置
      if (wecomSearchName === '') return;

      setWecomSearchName('');
      setWecomStaffPage(prev => ({
        ...prev,
        pageNum: 1,
      }));
      setWecomStaffList([]);
      setHasMore(true);
    } else {
      // 使用防抖处理有搜索词的情况
      debouncedSearch(value);
    }
  };

  // 加载更多数据
  const loadMore = closeLoading => {
    if (!hasMore || loading) {
      closeLoading && closeLoading();
      return Promise.resolve();
    }

    const nextPage = wecomStaffPage.pageNum + 1;

    // 返回API调用的Promise
    return getWecomStaffList({
      pageNum: nextPage,
      pageSize: wecomStaffPage.pageSize,
      searchName: wecomSearchName,
    })
      .then(res => {
        if (res?.content) {
          const total = res.total || 0;
          const newContent = res.content.map(staff => ({
            key: staff.staffId,
            text: staff.staffName,
            mobile: staff.mobileAccount || staff.mobileNumber,
          }));

          // 更新列表数据
          setWecomStaffList(prev => [...prev, ...newContent]);

          // 更新分页信息
          setWecomStaffPage(prev => ({
            ...prev,
            pageNum: nextPage,
            total,
          }));

          // 判断是否还有更多数据
          const hasReachedEnd = nextPage * wecomStaffPage.pageSize >= total;
          const hasResults = newContent.length > 0;
          setHasMore(!hasReachedEnd && hasResults);
        } else {
          setHasMore(false);
        }

        // 调用closeLoading函数表示加载完成
        closeLoading && closeLoading();
      })
      .catch(error => {
        console.error('获取企微员工列表失败', error);
        setHasMore(false);
        closeLoading && closeLoading();
      });
  };

  // 渲染企微员工列表选项内容
  const renderWxOptionContent = item => {
    const { mobile, text } = item;
    return (
      <p>
        <span>{text}</span>
        {mobile && (
          <span className="wecom-staff-selector__select-mobile">
            {mobile.countryCode}-{mobile.mobileNumber}
          </span>
        )}
      </p>
    );
  };

  function renderOptionList(optionList, renderOption) {
    return (
      <InfiniteScroller
        hasMore={hasMore}
        className="wecom-staff-selector-scroller"
        skipLoadOnMount
        loadMore={loadMore}
        loader={
          <div className="loading-text">
            <InlineLoading
              iconSize={18}
              loading
              icon="circle"
              iconText="加载中…"
              textPosition="right"
              colorPreset="grey"
            />
          </div>
        }
        // threshold={50}
      >
        {optionList.map((item, index) => renderOption(item, index))}
      </InfiniteScroller>
    );
  }

  return (
    <Select
      disabled={disabled}
      className="wecom-staff-selector"
      loading={loading}
      options={wecomStaffList}
      value={selectedIds}
      placeholder="选择企业微信员工"
      multiple
      keyword={keyword}
      onChange={handleWecomStaffChange}
      onKeywordChange={handleWecomSearch}
      renderOptionContent={renderWxOptionContent}
      renderOptionList={renderOptionList}
    />
  );
};

export default WecomStaffSelector;
