import { useState, useEffect } from 'react';
import {
  Input,
  Radio,
  RadioGroup,
  Drawer,
  Button,
  Pagination,
  Input as ZentInput,
  Grid,
  Notify,
  BlockLoading,
} from 'zent';
import { formatMoney } from 'fns/chart-format';
import ajax from 'zan-pc-ajax';
import SelectItem from '../../../../SubDrawer/SelectItem';
import { get, isEmpty } from 'lodash';

import styles from '../styles.m.scss';

interface IConfig {
  giftZoneName: string;
  isAutoHosted: boolean;
  outItems: IGoodItem[];
  xdShop: any[];
}

interface IGoodItem {
  outItemId: number;
  title: string;
  headImg: string;
  price: number;
  stockNum: number;
}

interface IGoodsSelectProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: ({ ids, goods }: { ids: number[]; goods: IGoodItem[] }) => void;
  initialSelectedIds: number[];
  channelEntityId: number;
}

const goodsColumns = [
  {
    title: '商品名称',
    width: '300',
    name: 'name',
    bodyRender: _ => {
      const { title, headImg, price } = _;
      return (
        <div className={styles.goodsContainer}>
          <div className={styles.goodsCard}>
            <img src={headImg} width={48} height={48} />
            <div className={styles.info}>
              <div className={styles.name}>{title}</div>
              <div className={styles.price}>￥{formatMoney(price)}</div>
            </div>
          </div>
        </div>
      );
    },
  },
  {
    title: '库存',
    name: 'stockNum',
  },
];

const GoodsSelect = ({
  visible,
  onClose,
  onConfirm,
  initialSelectedIds,
  channelEntityId,
}: IGoodsSelectProps) => {
  const [goodsList, setGoodsList] = useState<IGoodItem[]>([]);
  const [selectedGoodsIds, setSelectedGoodsIds] = useState<number[]>(initialSelectedIds || []);
  const [selectedGoods, setSelectedGoods] = useState<IGoodItem[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchGoodsList(page, pageSize, searchKeyword);
    }
  }, [visible, page, pageSize, searchKeyword]);

  useEffect(() => {
    setSelectedGoodsIds(initialSelectedIds || []);
  }, [initialSelectedIds]);

  // 模拟获取商品列表
  const fetchGoodsList = (currentPage: number, size: number, keyword: string) => {
    setLoading(true);

    ajax({
      url: '/v4/wxvideo/api/goods-manager/queryGoodsList',
      method: 'GET',
      data: {
        page: currentPage,
        pageSize: size,
        status: JSON.stringify([1]),
        title: keyword,
        // 指定微信小店
        mpIds: JSON.stringify([channelEntityId]),
      },
    })
      .then(res => {
        setGoodsList(res.items);
        setTotal(res.paginator.totalCount);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handlePageChange = (data: { current: number; pageSize: number }) => {
    setPage(data.current);
  };

  const handleSearch = () => {
    setPage(1);
    fetchGoodsList(1, pageSize, searchKeyword);
  };

  const handleConfirmSelect = () => {
    if (selectedGoodsIds.length < 3) {
      Notify.error('请选择至少3个商品');
      return;
    }

    onConfirm({
      ids: selectedGoodsIds,
      goods: selectedGoods.map(item => ({
        outItemId: item.outItemId,
        title: item.title,
        headImg: item.headImg,
        price: item.price,
        stockNum: item.stockNum,
      })),
    });
  };

  return (
    <Drawer
      title="选择商品"
      visible={visible}
      onClose={onClose}
      maskClosable
      placement="right"
      width={680}
      footer={
        <div className={styles.drawerFooter}>
          <Button type="primary" onClick={handleConfirmSelect}>
            确定({selectedGoodsIds.length})
          </Button>
          <Button onClick={onClose}>取消</Button>
        </div>
      }
    >
      <div className={styles.goodsSelector}>
        <div className={styles.goodsSearchBar}>
          <ZentInput
            placeholder="搜索商品名称"
            value={searchKeyword}
            onChange={e => setSearchKeyword(e.target.value)}
            icon="search"
            onIconClick={handleSearch}
          />
        </div>

        <div className={styles.goodsList}>
          {loading ? (
            <div className={styles.loading}>加载中...</div>
          ) : (
            <Grid
              columns={goodsColumns}
              datasets={goodsList}
              scroll={{ y: window.innerHeight - 250 }}
              rowClassName={(data, index) => `${data.outItemId}-${index}`}
              rowKey="outItemId"
              selection={{
                selectedRowKeys: selectedGoodsIds,
                isSingleSelection: false,
                onSelect: (selectedRowKeys, selectedRows, currentRow) => {
                  if (selectedRowKeys.length > 5) {
                    Notify.error('你最多选择5个');
                    setSelectedGoodsIds([...selectedGoodsIds]);
                    setSelectedGoods([...selectedGoods]);
                  } else {
                    setSelectedGoodsIds(selectedRowKeys);
                    setSelectedGoods(selectedRows);
                  }
                },
              }}
              size="large"
            />
          )}
        </div>

        <div className={styles.goodsPagination}>
          <Pagination
            current={page}
            totalItem={total}
            pageSize={pageSize}
            onChange={handlePageChange}
          />
        </div>
      </div>
    </Drawer>
  );
};

const GiftZoneCustomEdit = ({ customFlowRuleData, setCustomFlowRuleData }) => {
  const { chainRules } = customFlowRuleData ?? {};
  const { action = null } = chainRules?.[0] ?? {};
  const actionFieldValueMaps4ExecuteJSON = JSON.parse(action?.actionFieldValueMaps4Execute || '{}');
  const actionFieldValueMaps4DisplayJSON = JSON.parse(action?.actionFieldValueMaps4Display || '{}');
  const defaultConfig = {
    ...actionFieldValueMaps4ExecuteJSON,
    ...actionFieldValueMaps4DisplayJSON,
  };
  const [config, setConfig] = useState<IConfig | any>({});

  useEffect(() => {
    if (isEmpty(config) && !isEmpty(defaultConfig)) {
      setConfig({
        giftZoneName: defaultConfig.giftZoneName || '微信送礼专区',
        // 默认自动选择
        isAutoHosted: defaultConfig.isAutoHosted === false ? false : true,
        outItems: defaultConfig.outItems || [],
        // 微信小店
        xdShop: defaultConfig.xdShop,
      });
    }
  }, [defaultConfig]);

  const [visible, setVisible] = useState(false);

  const channelEntityId = get(config, 'xdShop[0].id', '');

  const updateCustomEditData = (newConfig: IConfig) => {
    setConfig(newConfig);

    setCustomFlowRuleData({
      ...customFlowRuleData,
      channelEntityId,
      chainRules: [
        {
          action: {
            id: 64, // 固定值
            actionFieldValueMaps4Execute: JSON.stringify({
              channelEntityId,
              giftZoneName: newConfig.giftZoneName,
              isAutoHosted: newConfig.isAutoHosted,
              outItemIds: newConfig.isAutoHosted
                ? []
                : newConfig.outItems.map(item => item.outItemId),
            }),
            actionFieldValueMaps4Display: JSON.stringify({
              channelEntityId,
              giftZoneName: newConfig.giftZoneName,
              isAutoHosted: newConfig.isAutoHosted,
              outItems: newConfig.outItems,
              xdShop: newConfig.xdShop,
            }),
          },
        },
      ],
    });
  };

  const handleShopSelect = (xdShop: any) => {
    updateCustomEditData({
      ...config,
      xdShop,
    });
  };

  const handleNameChange = (e: any) => {
    updateCustomEditData({
      ...config,
      giftZoneName: e.target.value,
    });
  };

  const handleGoodsChooseTypeChange = (e: any) => {
    updateCustomEditData({
      ...config,
      isAutoHosted: e.target.value,
    });
  };

  const handleOpenGoodsSelector = () => {
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleConfirmSelect = ({ ids, goods }: { ids: number[]; goods: IGoodItem[] }) => {
    updateCustomEditData({
      ...config,
      outItems: goods,
    });
    setVisible(false);
  };

  if (isEmpty(config)) {
    return <BlockLoading />;
  }

  return (
    <div className={styles.formContainer}>
      <div className={styles.formItem}>
        <div className={styles.formLabel}>
          <span className={styles.required}>*</span>
          <span>送礼专区商品选择：</span>
        </div>
        <div className={styles.formContent}>
          <RadioGroup onChange={handleGoodsChooseTypeChange} value={config.isAutoHosted}>
            <Radio value={true}>自动选择</Radio>
            <Radio value={false}>人工选择</Radio>
          </RadioGroup>
        </div>
      </div>

      {config.isAutoHosted && (
        <div className={styles.formItem}>
          <div className={styles.autoHostedTip}>系统会自动选择热销商品进入礼物专区</div>
        </div>
      )}

      {!config.isAutoHosted && (
        <>
          <div className={styles.formItem}>
            <div className={styles.formLabel}>
              <span className={styles.required}>*</span>
              <span>选择店铺：</span>
            </div>
            <div className={styles.formContent}>
              <SelectItem
                setEditingAction={value => {
                  const xdDisplay = JSON.parse(value?.actionFieldValueMaps4Display || '{}');
                  handleShopSelect(xdDisplay.xdShop);
                }}
                variable={{
                  // @ts-ignore
                  code: 'xdShop',
                  dynamicEndpointConfig:
                    '{"apiId":"wxxdShopSelector","responseCovert":"id","extParam":{},"componentStyle":"popUpSelection"}',
                  extension: '',
                  id: 426,
                  name: '微信小店',
                  type: 2,
                  valueSource: 3,
                }}
                selectedData={JSON.stringify(config)}
                editingAction={{
                  name: '微信小店',
                  variables: [],
                }}
              />
            </div>
          </div>
          <div className={styles.formItem}>
            <div className={styles.formLabel}>
              <span className={styles.required}>*</span>
              <span>选择商品：</span>
            </div>
            <div className={styles.formContent} onClick={handleOpenGoodsSelector}>
              {config.outItems.length > 0 ? (
                <div className={`${styles.itemSelectField} ${styles.value} `}>
                  <div className={styles.title}>{config.outItems[0].title}</div>
                  {config.outItems.length > 1 && (
                    <div className={styles.count}> 等{config.outItems.length}个</div>
                  )}
                </div>
              ) : (
                <div className={styles.itemSelectField}>
                  <div className={styles.title}>选择商品</div>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      <div className={styles.formItem}>
        <div className={styles.formLabel}>
          <span className={styles.required}>*</span>
          <span>送礼专区名称：</span>
        </div>
        <div className={styles.formContent}>
          <Input placeholder="请输入名称" value={config.giftZoneName} onChange={handleNameChange} />
        </div>
      </div>

      <GoodsSelect
        visible={visible}
        onClose={handleClose}
        onConfirm={handleConfirmSelect}
        // 因为商品可能一开始在小店中，后续被移出，所以这里每次都要重新选
        initialSelectedIds={[]}
        channelEntityId={channelEntityId}
      />
    </div>
  );
};

export default GiftZoneCustomEdit;
