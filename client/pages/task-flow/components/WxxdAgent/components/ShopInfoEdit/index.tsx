import { useEffect, useState } from 'react';
import InfomationDrawer from '../../StepInit/components/OneClickStore/FillInfomation/InfomationDrawer';
import { editAndSubmit, getDetail } from '../../api';
import { Notify } from 'zent';

function WxxdShopInfoEdit({ openStatus, visible, onClose }) {
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState(null);
  useEffect(() => {
    if (!detail && openStatus) {
      setLoading(true);
      getDetail({
        appId: openStatus.appId,
        authStatus: openStatus.authStatus,
      })
        .then(res => {
          setDetail(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [openStatus]);

  return (
    <InfomationDrawer
      loading={loading && !detail}
      visible={visible}
      onChange={value => {
        editAndSubmit(value)
          .then(res => {
            onClose();
          })
          .catch(() => {
            Notify.error('提交失败');
          });
      }}
      onClose={onClose}
      editMode={true}
      detail={detail}
    />
  );
}

export default WxxdShopInfoEdit;
