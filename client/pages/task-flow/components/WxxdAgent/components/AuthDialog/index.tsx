import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Dialog, Button, Icon, BlockLoading, Notify } from 'zent';
import { QRCodeSVG } from 'qrcode.react';
import { getWechatOpenShopStatus } from 'pages/task-flow/components/WxxdAgent/api';
import { CERT_AUDIT_STATUS } from 'pages/task-flow/components/WxxdAgent/constant';
import './index.scss';

interface WxAuthDialogProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// 状态机变化 ACCOUNT_PENDING(40) -> PENDING_AUDIT(30) -> ACCOUNT_FAILED(41)或AGREEMENT_PENDING(50) -> AGREEMENT_FAILED(51)或AGREEMENT_SUCCESS(52)
const AuthDialog: React.FC<WxAuthDialogProps> = ({ visible, onClose, onSuccess }) => {
  const [authLink, setAuthLink] = useState<string>('');
  const [certAuditStatus, setCertAuditStatus] = useState<number>(-1);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取认证状态
  const fetchAuthStatus = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getWechatOpenShopStatus();
      if (res) {
        const newStatus = res.certAuditStatus;

        setCertAuditStatus(newStatus);

        // 二维码
        if (res.wechatJumpUrl) {
          setAuthLink(res.wechatJumpUrl);
        } else {
          // 清除已有的二维码链接，避免显示过期的二维码
          setAuthLink('');
        }

        // 如果状态为签约成功或更高，调用成功回调
        if (newStatus >= CERT_AUDIT_STATUS.AGREEMENT_SUCCESS) {
          onSuccess && onSuccess();
          Notify.success('签约成功,即将刷新页面');
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }
      }
    } catch (error) {
      console.error('获取认证状态失败:', error);
    } finally {
      setLoading(false);
    }
  }, [onSuccess, certAuditStatus]);

  useEffect(() => {
    if (visible) {
      fetchAuthStatus();
    }
  }, [visible]);

  // 获取当前认证步骤
  const currentStep = useMemo(() => {
    if (certAuditStatus === -1) {
      return 0; // 初始状态
    }

    // ACCOUNT_PENDING(40) 或 任何审核中的状态 (PENDING_AUDIT(30))
    if (
      certAuditStatus === CERT_AUDIT_STATUS.ACCOUNT_PENDING ||
      certAuditStatus === CERT_AUDIT_STATUS.PENDING_AUDIT
    ) {
      return 1; // 第一步：法人认证
    }

    // ACCOUNT_FAILED(41) 表示账户审核失败，但仍在第一步
    if (certAuditStatus === CERT_AUDIT_STATUS.ACCOUNT_FAILED) {
      return 1; // 第一步：法人认证（失败状态）
    }

    // AGREEMENT_PENDING(50) 表示协议待签署，进入第二步
    if (certAuditStatus === CERT_AUDIT_STATUS.AGREEMENT_PENDING) {
      return 2; // 第二步：签约
    }

    // AGREEMENT_FAILED(51) 表示协议签署失败，仍在第二步
    if (certAuditStatus === CERT_AUDIT_STATUS.AGREEMENT_FAILED) {
      return 2; // 第二步：签约（失败状态）
    }

    // 大于等于 AGREEMENT_SUCCESS(52) 的状态表示流程已完成
    if (certAuditStatus >= CERT_AUDIT_STATUS.AGREEMENT_SUCCESS) {
      return 3; // 完成
    }

    // 其他状态默认显示为第一步
    return 1;
  }, [certAuditStatus]);

  // 刷新按钮点击事件
  const handleRefresh = () => {
    fetchAuthStatus();
  };

  return (
    <Dialog
      visible={visible}
      onClose={onClose}
      className="wx-auth-dialog"
      title="开店认证"
      footer={null}
    >
      <div className="wx-auth-content">
        <div className="wx-auth-steps">
          <h3 className="wx-auth-steps-title">
            {currentStep > 2
              ? `已完成开店认证`
              : ` 还需${Math.min(3 - currentStep, 2)}步，即可完成开店`}
          </h3>

          {/* 第一步：法人认证 */}
          <div className="wx-auth-step-item">
            {currentStep > 1 ? (
              <Icon className="wx-auth-step-icon" type="check-circle-o" />
            ) : (
              <div className={`wx-auth-step-num ${currentStep === 1 ? 'pedding' : ''}`}>1</div>
            )}

            <div className="wx-auth-step-info">
              <div className="wx-auth-step-title">请法人使用微信扫码，完成商户法人认证</div>
              <BlockLoading loading={loading}>
                <div className="wx-auth-step-desc">
                  {!loading && currentStep === 1 && (
                    <>
                      {certAuditStatus === CERT_AUDIT_STATUS.ACCOUNT_FAILED && (
                        <div className="wx-auth-failed">
                          <Icon type="close-circle-o" /> 法人认证失败，请稍后重试
                        </div>
                      )}

                      {certAuditStatus === CERT_AUDIT_STATUS.PENDING_AUDIT && (
                        <div className="wx-auth-pending">
                          <Icon type="clock-o" /> 法人认证审核中，请耐心等待
                        </div>
                      )}

                      {(certAuditStatus === CERT_AUDIT_STATUS.ACCOUNT_PENDING ||
                        (certAuditStatus !== CERT_AUDIT_STATUS.PENDING_AUDIT &&
                          certAuditStatus !== CERT_AUDIT_STATUS.ACCOUNT_FAILED)) &&
                        authLink && (
                          <div className="wx-auth-qrcode">
                            <QRCodeSVG value={authLink} size={160} />
                          </div>
                        )}
                    </>
                  )}

                  {!loading && currentStep > 1 && (
                    <div className="wx-auth-success">
                      <Icon type="check-circle-o" /> 法人认证成功
                    </div>
                  )}
                </div>
              </BlockLoading>
            </div>
          </div>

          {/* 第二步：签约 */}
          <div className="wx-auth-step-item">
            {currentStep > 2 ? (
              <Icon className="wx-auth-step-icon" type="check-circle-o" />
            ) : (
              <div className={`wx-auth-step-num ${currentStep === 2 ? 'pedding' : ''}`}>2</div>
            )}

            <div className="wx-auth-step-info">
              <div className="wx-auth-step-title">
                请使用开店时扫码授权人的微信进行扫码，完成签约
              </div>

              <BlockLoading loading={loading}>
                <div className="wx-auth-step-desc">
                  {!loading && currentStep === 1 && (
                    <div className="wx-auth-step-desc">
                      请先完成法人认证，认证后可刷新获取签约二维码
                      <Button
                        type="primary"
                        className="wx-auth-refresh-btn"
                        loading={loading}
                        onClick={handleRefresh}
                      >
                        刷新
                      </Button>
                    </div>
                  )}
                  {!loading && currentStep === 2 && (
                    <>
                      {certAuditStatus === CERT_AUDIT_STATUS.AGREEMENT_FAILED && (
                        <div className="wx-auth-failed">
                          <Icon type="close-circle-o" /> 签约失败，请稍后重试
                        </div>
                      )}

                      {certAuditStatus === CERT_AUDIT_STATUS.AGREEMENT_PENDING && authLink ? (
                        <div className="wx-auth-qrcode">
                          <QRCodeSVG value={authLink} size={160} />
                          <Button
                            type="primary"
                            className="wx-auth-refresh-btn"
                            loading={loading}
                            onClick={handleRefresh}
                          >
                            刷新
                          </Button>
                        </div>
                      ) : certAuditStatus === CERT_AUDIT_STATUS.AGREEMENT_PENDING && !authLink ? (
                        <div className="wx-auth-step-desc">二维码获取失败，请联系客服</div>
                      ) : null}
                    </>
                  )}
                  {!loading && currentStep > 2 && (
                    <div className="wx-auth-success">
                      <Icon type="check-circle-o" /> 签约成功
                    </div>
                  )}
                </div>
              </BlockLoading>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default AuthDialog;
