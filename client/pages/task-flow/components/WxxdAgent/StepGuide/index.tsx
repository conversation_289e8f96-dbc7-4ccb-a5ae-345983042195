import { useMemo } from 'react';
import { Button } from 'zent';
import { PAGE_STATUS } from '../constant';

import './index.scss';

const WxxdAIGuide = ({ firstInit, nextAgentStatus, gotoDashboard, isOldShop }) => {
  const features = useMemo(
    () => [
      {
        title: '全天在线值守',
        description: '7×24小时不间断工作，无需人工干预',
        iconUrl: 'https://img01.yzcdn.cn/upload_files/2025/04/08/Fv77NYxZnsYyjLpzDh4wiC1i3JNJ.png',
      },
      {
        title: '一键代开店',
        description: '动动手指，就能帮你开通微信小店',
        iconUrl: 'https://img01.yzcdn.cn/upload_files/2025/04/08/Fjumie1RnpIZLabjmjl9CPu6B8cX.png',
      },
      {
        title: '提升运营效率',
        description:
          '在有赞就能管理小店订单（含送礼订单），还能自动帮你处理库存，提醒你处理异常订单',
        iconUrl: 'https://img01.yzcdn.cn/upload_files/2025/04/08/FnznUgIFBciulDRTKrrdF6Uo_U7k.png',
      },
      {
        title: '提高营销转化',
        description:
          '帮你自动置顶热销商品，创建送礼专区\n自动回复客户咨询，推荐商品，把握商机（即将上线）',
        iconUrl: 'https://img01.yzcdn.cn/upload_files/2025/04/08/Fulr4Ve0ti62SJT5L3YCMEHl3SV_.png',
      },
    ],
    [],
  );

  const handleConfirm = () => {
    nextAgentStatus(PAGE_STATUS.INIT);
  };

  return (
    <div className="guide-container">
      <div className="guide-container-main">
        <div className="header">
          <h1 className="title">微信小店托管</h1>
          <p className="subtitle">我是你的微信小店运营助手</p>
        </div>

        <img
          src="https://img.yzcdn.cn/public_files/2025/03/14/65d9dd8501744a91.png"
          alt="机器人"
          className="robotImage"
        />

        <div className="contentWrapper">
          <div className="featuresContainer">
            {features.map((feature, index) => (
              <div key={index} className="featureItem">
                <div className="featureHeader">
                  <img src={feature.iconUrl} alt={feature.title} className="featureIcon" />
                  <h3 className="featureTitle">{feature.title}</h3>
                </div>
                <p className="featureDescription">{feature.description}</p>
              </div>
            ))}
          </div>

          <div className="buttonContainer">
            {!isOldShop && firstInit ? (
              <Button type="primary" onClick={handleConfirm}>
                开启我的微信小店托管
              </Button>
            ) : (
              <>
                <Button type="primary" onClick={handleConfirm}>
                  {isOldShop ? '我要更新，使用新功能' : '继续初始化流程'}
                </Button>
                <Button onClick={gotoDashboard}>
                  {isOldShop ? '我不更新，使用原功能' : '直接进入智能体页面'}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WxxdAIGuide;
