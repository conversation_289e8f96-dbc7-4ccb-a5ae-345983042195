.guide-container {
  width: 100%;
  height: 100%;
  padding: 24px 0;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
}

.guide-container-main {
  position: relative;
  width: 964px;
  height: 680px;
  margin: 0 auto;
  border-radius: 16px;
  overflow: hidden;
  background: url('https://img01.yzcdn.cn/upload_files/2025/04/08/FtT5qb9sT5-CWk8phfNSPZ3YVnBf.png!origin.webp')
      no-repeat 0 0 / 100% 100%,
    linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
}

.header {
  position: absolute;
  top: 103px;
  left: 68px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 354px;
  z-index: 2;
}

.title {
  font-size: 28px;
  font-weight: 500;
  color: #333333;
  line-height: 1.29;
  margin: 0;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.subtitle {
  font-size: 16px;
  font-weight: 400;
  color: #666666;
  line-height: 1.75;
  margin: 0;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.robotImage {
  position: absolute;
  top: 64px;
  right: 98px;
  width: 200px;
  height: auto;
  z-index: 2;
}

.contentWrapper {
  position: relative;
  padding: 285px 68px;
}

.featuresContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px 80px;
  margin-bottom: 48px;
  max-width: 828px;
  position: relative;
  z-index: 2;
}

.featureItem {
  display: flex;
  flex-direction: column;
  max-width: 374px;
}

.featureHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.featureIcon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.featureTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 2;
  margin: 0;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.featureDescription {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 1.71;
  margin: 0;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  white-space: pre-line;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 119px;
  position: relative;
  z-index: 2;

  .zent-btn {
    width: 288px;
    height: 40px;

    + .zent-btn {
      background-color: #e0e0e0;
      margin-left: 60px;
      border: none;
    }
  }
}

@media (max-width: 768px) {
  .header {
    top: 30px;
    left: 20px;
  }

  .robotImage {
    top: 30px;
    right: 20px;
    width: 150px;
  }

  .contentWrapper {
    padding: 160px 20px 50px;
  }

  .featuresContainer {
    grid-template-columns: 1fr;
    gap: 40px 20px;
  }

  .featureItem {
    max-width: 100%;
  }

  .button {
    width: 100%;
    max-width: 288px;
  }
}
