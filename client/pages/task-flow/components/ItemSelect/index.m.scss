.select-container {
  padding: 16px 20px 0 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overscroll-behavior: contain;
  height: 100%;
  .goods-container {
    .goods-card {
      display: flex;

      img {
        width: 48px;
        height: 48px;
        object-fit: cover;
      }
      .info {
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-weight: 400;
        line-height: 20px;

        .name {
          color: #155bd4;
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .price {
          color: #dd712f;
        }
      }

      .info-only-name {
        // 居中 超出省略号
        margin-left: 12px;
        display: flex;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #155bd4;

        .goods-group-name {
          color: #333;
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .sku {
      margin-top: 12px;
      padding-left: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;

      :global {
        .zent-checkbox-label {
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          max-width: 150px;
          white-space: nowrap;
        }
      }
    }
  }

  .list {
    flex: 1;
    overflow: auto;
  }

  :global {
    .zent-form-direction-row {
      gap: 8px;
      .zent-form-control {
        flex: unset;
        margin-bottom: 12px;
      }
    }

    // 由于用grid实现无限滚动，loading的位置还要居中，故写死
    .zent-loading--block .zent-loading-mask > .zent-loading-icon-and-text {
      position: fixed;
      top: 50%;
      right: 270px;
      left: unset;
    }

    .zent-grid-td {
      vertical-align: top;
      word-break: break-all;
    }
  }

  .include-image {
    :global {
      .zent-grid-selection-checkbox {
        top: 42px;
      }
    }
  }

  .include-sub {
    :global {
      .zent-grid-selection-checkbox {
        top: 27px;
      }
    }
  }

  .footer {
    width: 100%;
    height: 64px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #eee;
  }
}

.header-title {
  padding-left: 12px;
  gap: 4px;
  display: flex;
  align-items: center;
}

.item-select-field {
  height: 38px;
  // width: 310px;
  flex-grow: 1;
  cursor: pointer;
  background-color: #fff;
  padding: 8px;
  line-height: 22px;
  border-radius: 4px;
  color: #ccc;
  box-sizing: border-box;
  border: 1px solid #e0e0e0;
  display: flex;
  .title {
    // 一行超出显示省略号
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 160px;
  }
  .count {
    white-space: pre-wrap;
  }
}

.value {
  color: #333;
}
.disabled {
  background: rgb(247, 247, 247);
}
.spuNo {
  color: rgb(100, 101, 102);
}
