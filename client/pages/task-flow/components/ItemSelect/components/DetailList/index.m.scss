.select-container {
  padding: 16px 20px 0 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overscroll-behavior: contain;
  height: 100%;

  .goods-container {
    .goods-card {
      display: flex;

      img {
        width: 48px;
        height: 48px;
        object-fit: cover;
      }
      .info {
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-weight: 400;
        line-height: 20px;

        .name {
          color: #155bd4;
          max-width: 600px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .price {
          color: #dd712f;
        }
      }
    }

    .sku {
      margin-top: 12px;
      padding-left: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      // 超过两行显示省略号
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
    }
  }

  .list {
    flex: 1;
    overflow: auto;
  }

  :global {
    .zent-form-direction-row {
      gap: 8px;
      .zent-form-control {
        flex: unset;
        margin-bottom: 12px;
      }
    }

    // 由于用grid实现无限滚动，loading的位置还要居中，故写死
    .zent-loading--block .zent-loading-mask > .zent-loading-icon-and-text {
      position: fixed;
      top: 50%;
      right: 270px;
      left: unset;
    }
  }

  .footer {
    width: 100%;
    height: 64px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #eee;
  }
}

.header-title {
  padding-left: 12px;
  gap: 4px;
  display: flex;
  align-items: center;
}

.spuNo {
  color: rgb(100, 101, 102);
}
