import { EValueSource, IAction, IVariable, TemplateStyle } from 'pages/work-flow/constant';
import TextAreaItem from '../TextAreaItem';
import InputItem from '../InputItem';
import SelectItem from '../SelectItem';
import RadioItem from '../RadioItem';
import { ItemSelectField } from '../../../components/ItemSelect';
import { Notify } from 'zent';
import { cloneDeep } from 'lodash';
import { parseJSON } from 'pages/work-flow/utils/flow';
import TagSelect from '../../tagSelect';

// 根据不同的valueSource渲染不同的输入框
export const renderValueInput = (
  variable: IVariable,
  selectedData,
  data,
  disabled = false,
  editingAction,
  setEditingAction,
) => {
  const extension = JSON.parse(variable?.extension || '{}');
  const skillField = extension?.skillField || '';
  const styleTemplate = extension?.variablePattern?.styleTemplate?.template || '';
  const { prefixDesc, suffixDesc } = extension?.variablePattern?.styleTemplate || {};
  const { actionFieldValueMaps4Display, actionFieldValueMaps4Execute } = selectedData || {};

  const { code = '' } = variable;

  const onFieldValueChange = (value: any) => {
    const { skuMap, selected, selectedMainIds, selectedSubIds } = value;

    console.log('skuMap', skuMap);

    // 更新 actionFieldValueMaps4Execute
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;
    actionFieldValueMaps4Execute[code] = skuMap;

    // 更新 actionFieldValueMaps4Display
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    actionFieldValueMaps4Display[code] = selected;

    // 更新 editingAction
    setEditingAction?.({
      ...(editingAction as IAction), // 假设 prev 不为 null
      actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute || '{}'),
      actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display || '{}'),
    });

    // 更新自定义的状态（如果需要）
    // setCustomSelectMainValue(selectedMainIds);
    // setCustomSelectSubValue(selectedSubIds);
  };

  // return <div className="value">{renderIconByType(data)}</div>;
  if (variable.valueSource === 1) {
    // 开放变量，input输入 action中找不到使用场景
    return styleTemplate === TemplateStyle.TextArea ? (
      <TextAreaItem
        editingAction={editingAction}
        setEditingAction={setEditingAction}
        variable={variable}
        subDrawerData={data}
        maxWordsLength={extension?.variablePattern?.styleTemplate?.maxWordsLength || 70}
        selectedData={{
          actionFieldValueMaps4Display,
          content: selectedData?.content,
          contentVariables: selectedData?.contentVariables,
        }}
      />
    ) : (
      <InputItem
        editingAction={editingAction}
        setEditingAction={setEditingAction}
        addonBefore={prefixDesc}
        addonAfter={suffixDesc}
        variable={variable}
        subDrawerData={data}
        disabled={disabled}
        selectedData={actionFieldValueMaps4Display}
      />
    );
  }
  if (variable.valueSource === 2) {
    // 固定选项 也用选择器，但是
    return (
      <SelectItem
        editingAction={editingAction}
        setEditingAction={setEditingAction}
        variable={variable}
        subDrawerData={data}
        selectedData={actionFieldValueMaps4Display}
      />
    );
  }
  if (variable.valueSource === 3) {
    // 有改动，不使用公用选择器，用Select
    // 20240830改动，valueSource为3 根据dynamicEndpointConfig中的componentStyle字段 'popUpSelection' 'dropDownSelection'决定使用哪种选择器
    const dynamicEndpointConfig = JSON.parse(variable.dynamicEndpointConfig || '{}');
    const { componentStyle, extParam = {}, dependenciesVariales = [] } = dynamicEndpointConfig;
    // 字段联动关系时存在禁用状态
    let disabled = false;
    // 根据后端返回dependenciesVariales来获取对应的前置依赖字段值，追加到extParam中
    if (dependenciesVariales.length) {
      const actionFieldValueMaps4ExecuteObj = parseJSON(
        editingAction?.actionFieldValueMaps4Execute,
      );
      dependenciesVariales.forEach(code => {
        const val = actionFieldValueMaps4ExecuteObj[code] || null;
        extParam[code] = val;
        // 依赖字段为空时禁用当前选择器
        if (!val || (Array.isArray(val) && !val.length)) {
          disabled = true;
        }
      });
    }

    console.log('disabled ========>', disabled);

    if (componentStyle === 'popUpSelection') {
      const { apiId, responseCovert, maxSelectedCount = Infinity } = dynamicEndpointConfig;

      const parsedData = JSON.parse(editingAction?.actionFieldValueMaps4Execute || '{}');

      const customSelectMainValue = Object.keys(parsedData[code] || {}).map(Number);

      const customSelectSubValue = Object.values(parsedData[code] || {})
        .flat()
        .filter((value, index, self) => self.indexOf(value) === index);

      return (
        <ItemSelectField
          type={apiId}
          outputKey={'skuMap'}
          isRetailGoods={true}
          subType={responseCovert}
          extParam={extParam}
          value={{ selectedMainIds: customSelectMainValue, selectedSubIds: customSelectSubValue }}
          onChange={onFieldValueChange}
          disabled={disabled}
          maxSelectedCount={maxSelectedCount}
          onError={() => {
            // 如果是采购任务的指定商品，不显示错误提示
            if (apiId === 'retailPurchaseGoods') {
              return;
            }
            Notify.error('请先选择操作符');
          }}
        />
      );
    }
    return (
      <SelectItem
        editingAction={editingAction}
        setEditingAction={setEditingAction}
        extParam={JSON.stringify(extParam || {})}
        variable={variable}
        subDrawerData={data}
        selectedData={actionFieldValueMaps4Display}
        selectedDataValue={actionFieldValueMaps4Execute}
        disabled={disabled}
      />
    );
  }
  if (variable.valueSource === EValueSource.RADIO) {
    // 新增的valueSource 单选组
    // 需要根据group中的id数组，判断这个单选组有哪些id的选项
    const { group } = JSON.parse(variable.dynamicEndpointConfig || '{}');
    const radioGroupVariables = data.variables.filter(item => group.includes(item.id));
    return (
      <RadioItem
        variable={variable}
        subDrawerData={data}
        selectedData={selectedData}
        radioGroupVariables={radioGroupVariables}
      />
    );
  }
};
