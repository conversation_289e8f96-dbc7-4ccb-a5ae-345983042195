.sub-drawer {
  padding: 16px 24px;

  .name {
    display: flex;
    padding: 4px 0;
    align-items: center;
    gap: 4px;
    color: #333;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;

    /* 142.857% */
    .icon {
      width: 20px;
      height: 20px;
    }
  }

  .variable-form-item {
    display: flex;
    // align-items: center;
    align-items: flex-start;
    margin-top: 16px;

    .label {
      flex-shrink: 0;
      min-width: 80px;
      position: relative;
      // line-height: 32px;
    }

    .variable-value-wrapper {
      flex-grow: 1;
      // margin-top: 16px;

      .zent-radio-wrap {
        width: 100%;
        margin-bottom: 8px !important;
      }
    }

    .variable-description {
      position: relative;
      // left: 100%;
      padding-top: 8px;
      color: #999;
      font-size: 12px;
      line-height: 20px;
      // white-space: nowrap;
    }

    .value {
      width: 248px;
      height: 32px;
      margin-left: 8px;
      // display: flex;
      // align-items: center;
    }
  }
}

.option-scroll-wrapper {
  max-height: 200px;
  // .option-item {
  //   padding: 6px 12px;
  //   height: 32px;
  //   display: flex;
  //   align-items: center;
  //   box-sizing: border-box;

  //   color: #333;
  //   font-feature-settings: 'clig' off, 'liga' off;

  //   /* 08_正文2（14px H20） */
  //   font-family: 'PingFang SC';
  //   font-size: 14px;
  //   font-style: normal;
  //   font-weight: 400;
  //   line-height: 20px; /* 142.857% */

  //   white-space: nowrap;
  //   overflow: hidden;
  //   text-overflow: ellipsis;

  //   .zent-select-v2-option {
  //     padding: 0;
  //   }

  //   span {
  //     color: #999;

  //     margin-left: 8px;
  //     white-space: nowrap;
  //     overflow: hidden;
  //     text-overflow: ellipsis;
  //   }

  //   &:hover {
  //     background-color: #f7f7f7;
  //     cursor: pointer;
  //   }
  // }
}

.option-item {
  // line-height: 32px;
  // padding: 6px 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  .name {
    font-size: 14px;
    color: #333;
    // line-height: 20px;
  }

  .phone {
    color: #999;
    font-size: 14px;
    // line-height: 20px;
    margin-left: 8px;
  }
}

.zent-select-v2-option-disabled {
  .option-item {
    .name {
      color: #ccc;
    }
  }
}

.zent-radio-wrap {
  margin-bottom: 8px !important;
}

.radio-label {
  display: inline-flex;
  align-items: center;

  flex-grow: 1;

  .radio-label-name {
    white-space: nowrap;
    margin-right: 8px;
  }

  .zent-input-wrapper {
    width: 80px !important;
  }
}