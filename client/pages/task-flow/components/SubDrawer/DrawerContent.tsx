import { ENodeType, IVariable } from 'pages/work-flow/constant';
import { FC } from 'react';
import { renderIconByType } from 'pages/work-flow/fns';
import { renderValueInput } from './fns';

interface DrawerContentProps {
  data: any;
  filteredVariableList?: IVariable[];
  selectedData: any;
  editingAction: any;
  setEditingAction: (action: any) => void;
}

const DrawerContent: FC<DrawerContentProps> = ({
  data,
  filteredVariableList,
  selectedData,
  editingAction,
  setEditingAction,
}) => {
  const renderIcon = () => {
    const icon = renderIconByType({ type: ENodeType.Action, groupCode: data?.groupCode });
    if (!icon) return null;
    return <div className="icon">{icon}</div>;
  };
  return (
    <div className="sub-drawer">
      <div className="name">
        {renderIcon()}
        {data?.name}
      </div>
      {filteredVariableList?.map(variable => (
        <div key={variable.id} className="variable-form-item">
          <div className="label matchMaxWidthDiv">{variable.name}：</div>
          <div className="variable-value-wrapper">
            {renderValueInput(variable, selectedData, data, false, editingAction, setEditingAction)}
            {!!variable.description && <div style={{ marginBottom: 16 }}></div>}
          </div>
        </div>
      ))}
    </div>
  );
};

export default DrawerContent;
