import { searchData } from 'api/workflow';
import { IAction, IActionItem, IVariable } from 'pages/work-flow/constant';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { InfiniteScroller, InlineLoading, Pop, Popover, Select } from 'zent';
import './style.scss';
import { editingActionAtom, flowAtom } from 'pages/task-flow/atoms';
import { useAtom } from 'jotai';
import {
  getListFieldsByTriggerAndVariable,
  checkSendMsgActionChannelPurview,
} from 'pages/work-flow/api';
import { FormChangeEvent } from 'pages/work-flow/utils/event';
import { parseJSON } from 'pages/work-flow/utils/flow';

const firstOpen = true;

const SelectItem = ({
  variable,
  subDrawerData,
  selectedData,
  selectedDataValue,
  extParam,
  editingAction,
  setEditingAction,
  disabled,
}: {
  variable: IVariable;
  subDrawerData: IActionItem;
  selectedData?: string;
  selectedDataValue?: string;
  extParam?: any;
  editingAction: IAction;
  setEditingAction: (action: IAction) => void;
  disabled?: boolean;
}) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [keyword, onKeywordChange] = useState('');

  // const optionsRef = useRef([]);

  const [flowData, setFlowData] = useAtom(flowAtom);

  // 配置对象
  const dynamicEndpointConfigObj = useMemo(() => {
    const { dynamicEndpointConfig } = variable;
    return parseJSON(dynamicEndpointConfig);
  }, [variable]);

  // 字段的值，单选返回第1个内容
  const fieldValue = dynamicEndpointConfigObj.isMultiSelect ? value : value?.[0];

  useEffect(() => {
    if (!variable?.code) {
      return;
    }
    // 当前select框变更值事件注册，为了解决select框改变，需要清空其他select框值的交互。
    // 目前只有select组件有此场景，暂时只注册select组件。
    const offEvent = FormChangeEvent.listen(variable?.code, value => {
      setValue(value || []);
    });
    return () => {
      offEvent();
    };
  }, [variable]);

  useEffect(() => {
    const { code } = variable;
    if (variable && selectedData && code) {
      const defaultValue = JSON.parse(selectedData || '{}')[code];

      if (defaultValue) {
        setValue(
          defaultValue.map(item => {
            return {
              ...item,
              text: item.name,
              key: item.id || item.staffId,
            };
          }),
        );
      }
    }
  }, [selectedData]);

  const fetchFieldOptions = async ({ page, variableId }) => {
    setLoading(true);
    try {
      const res = await getListFieldsByTriggerAndVariable({
        triggerId: flowData.triggerId,
        variableId,
        page,
        pageSize: 99,
      });
      const checkMap = await checkSendMsgActionChannelPurview().catch(() => {
        return {};
      });
      const { items } = res;

      setLoading(false);

      // 创建选项数组
      const mappedItems = items.map(item => {
        const checkInfo = checkMap[item.value] || {};
        return {
          ...item,
          disabled: item.isOptional === false || checkInfo.canUse === false,
          key: item.id,
          text: item.name,
          disabledReason: (
            <p>
              {checkInfo.unusableReason || ''}
              <a href={checkInfo.obtainPermissionWay} target="_blank">
                {checkInfo.obtainPermissionNotice}
              </a>
            </p>
          ),
        };
      });

      // 更新选项
      if (page === 1) {
        setOptions(mappedItems);
        return;
      }
      setOptions(prevOptions => [...prevOptions, ...mappedItems]);
    } catch (error) {
      console.error('Error fetching options:', error);
      setLoading(false);
    }
  };

  const fetchData = pageNum => {
    // setLoading(true);
    const { dynamicEndpointConfig, valueSource } = variable;
    if (valueSource === 2) {
      return fetchFieldOptions({ page: pageNum, variableId: variable.id });
    }
    const { apiId, responseCovert } = JSON.parse(dynamicEndpointConfig);

    return searchData({
      apiId,
      page: pageNum,
      pageSize: 10,
      keyWord: keyword,
      extParam,
    }).then(res => {
      const parsedOptions = res.items.map(item => {
        return {
          ...item,
          disabled: item.isOptional === false,
          key: item.id || item.staffId,
          text: item.name,
        };
      });
      if (pageNum === 1) {
        setOptions(parsedOptions);
        // optionsRef.current = parsedOptions;
      } else {
        setOptions(prev => prev.concat(parsedOptions));
        // optionsRef.current = optionsRef.current.concat(parsedOptions);
      }
      // setLoading(false);
    });
  };

  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      fetchData(1).finally(() => setLoading(false));
    }, 2000);

    return () => clearTimeout(timer);
  }, [keyword]);

  // 处理浮层打开
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen && firstOpen) {
      setLoading(true);
      fetchData(1).then(() => {
        setLoading(false);
      });
    }
    if (!isOpen) {
      setOptions([]);
      setPage(1);
      setLoading(false);
    }
  };
  /*
  function handleOpenChange(isOpen) {
    onOpenChange(isOpen);

    // 首次打开浮层加载数据
    if (isOpen && firstOpen) {
      firstOpen = false;

      setLoading(true);
      setTimeout(() => {
        setOptions(loadOptions());
        setLoading(false);
      }, 2000);
    }
  } */

  // 滚动加载更多
  const loadMore = closeLoading => {
    // setLoading(true);
    // 加载下一页数据
    return fetchData(page + 1)
      .then(() => {
        closeLoading();
        setPage(page + 1);
      })
      .catch(() => closeLoading());
  };
  /* const loadMore = closeLoading => {
    setTimeout(() => {
      setOptions(options.concat(loadOptions()));
      closeLoading && closeLoading();
    }, 20000);
  }; */

  const renderOptionList = (
    optionList: any[],
    renderOption: (option: any, index: number) => JSX.Element,
  ): React.JSX.Element => {
    return (
      <InfiniteScroller
        hasMore
        skipLoadOnMount
        loadMore={loadMore}
        className="option-scroll-wrapper"
        loader={
          <div className="loading-text">
            <InlineLoading
              iconSize={18}
              loading
              icon="circle"
              iconText="加载中…"
              textPosition="right"
              colorPreset="grey"
            />
          </div>
        }
      >
        {optionList.map((item, index) => {
          if (item.disabled && item.disabledReason) {
            return (
              <Pop trigger="hover" content={item.disabledReason}>
                {renderOption(item, index)}
              </Pop>
            );
          }
          return renderOption(item, index);
        })}
        {/* {optionList.map((item, index) => {
          return (
            <div className="option-item" key={index}>
              {renderOption(item, index)}
              <span>{item.linkPhone}</span>
            </div>
          );
        })} */}
      </InfiniteScroller>
    );
  };

  const onMultiValueChange = (values: any[]) => {
    let isMultiSelect = true;
    try {
      isMultiSelect = !!dynamicEndpointConfigObj.isMultiSelect;
    } catch (e) {
      console.error('Failed to parse JSON:', e);
    }
    if (!isMultiSelect) {
      values = values ? [values] : [];
    }

    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    const { dynamicEndpointConfig, code, valueSource } = variable;
    if (valueSource === 2) {
      actionFieldValueMaps4Execute[code!] = values.map(item => item.id);
      actionFieldValueMaps4Display[code!] = values;
    } else {
      const { responseCovert } = JSON.parse(dynamicEndpointConfig);

      actionFieldValueMaps4Execute[code!] = values.map(item => item[responseCovert]);
      actionFieldValueMaps4Display[code!] = values;
    }

    const { effectVarialeCodeList = [] } = dynamicEndpointConfigObj;
    // 清空effectVarialeCodeList的value
    // 为了解决当前select框带来的其他select框值的影响
    effectVarialeCodeList.forEach(item => {
      if ([2, 3].includes(item.valueSource)) {
        actionFieldValueMaps4Execute[item.code] = [];
        actionFieldValueMaps4Display[item.code] = [];
      } else {
        actionFieldValueMaps4Execute[item.code] = '';
        actionFieldValueMaps4Display[item.code] = '';
      }
      // 由于上述修改state并不会更新select框的value，因此需要主动触发对应select的value改变事件清主动清空
      FormChangeEvent.trigger(item.code, []);
    });

    // 给Select组件value赋值
    setValue(values);

    // 选中之后清空搜索关键字
    onKeywordChange('');

    // @ts-ignore
    setEditingAction({
      ...subDrawerData,
      actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
      actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
    });
  };

  const onValueChange = (value: any) => {
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;

    const { dynamicEndpointConfig, code, valueSource } = variable;
    if (valueSource === 2) {
      actionFieldValueMaps4Execute[code!] = value.id;
      actionFieldValueMaps4Display[code!] = value;
    } else {
      const { responseCovert } = JSON.parse(dynamicEndpointConfig);

      actionFieldValueMaps4Execute[code!] = value[responseCovert];
      actionFieldValueMaps4Display[code!] = value;
    }

    // 给Select组件value赋值
    setValue([value]);

    // 选中之后清空搜索关键字
    onKeywordChange('');

    // @ts-ignore
    setEditingAction({
      ...subDrawerData,
      actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
      actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
    });
  };

  const renderOptionContent = item => {
    const { name, linkPhone } = item || {};
    return (
      <div className="option-item">
        <span className="name">{name}</span>
        {linkPhone && <span className="phone">{linkPhone}</span>}
      </div>
    );
  };
  return (
    <div>
      <Select
        className="custom-select"
        clearable
        disabled={disabled}
        options={options}
        placeholder="请选择"
        multiple={!!dynamicEndpointConfigObj.isMultiSelect}
        filter={false}
        loading={loading}
        keyword={keyword}
        onKeywordChange={onKeywordChange}
        value={fieldValue}
        onChange={onMultiValueChange}
        open={open}
        onOpenChange={isOpen => handleOpenChange(isOpen)}
        // @ts-ignore
        renderOptionList={renderOptionList}
        renderOptionContent={renderOptionContent}
      />
      <div className="variable-description">{variable.description}</div>
    </div>
  );
};

export default SelectItem;
