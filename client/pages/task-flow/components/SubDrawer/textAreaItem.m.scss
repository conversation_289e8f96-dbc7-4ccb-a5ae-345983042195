.text-area-item {
  flex-grow: 1;

  .text-area {
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    padding: 6px 8px;
  }

  .text-area-input {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 20px;
    color: #333;
    // background-color: #f5f5f5;
    // padding: 10px;
    box-sizing: border-box;

    min-height: 60px;
    overflow-y: auto;

    span {
      color: #155bd4;
    }
  }

  .text-area-operation {
    width: 100%;
    display: flex;
    padding-top: 6px;
    justify-content: space-between;
    align-items: center;
    border-top: 1px dotted #eee;
    margin-top: 8px;

    .text-area-operation-left {
      display: flex;
      align-items: center;

      .variable-item {
        padding: 8px 16px;
        margin-top: 4px;

        &:first-of-type {
          margin-top: 0;
        }
        &:hover {
          background-color: #f7f7f7;
          cursor: pointer;
        }
      }

      .insert-varible {
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
        text-align: left;
        display: flex;
        align-items: center;

        &:hover {
          color: #155bd4;
          rect {
            fill: #155bd4;
          }
          circle {
            stroke: #155bd4;
          }
        }
        span {
          margin-left: 4px;
          cursor: pointer;
        }
      }
    }
    .text-area-operation-right {
      .text-length-limit {
        font-family: Avenir;
        font-size: 10px;
        font-weight: 400;
        line-height: 10px;
        text-align: right;
        color: #ccc;

        .invalid-count {
          color: #d41f15;
        }
      }
    }
  }
  .invalid-text {
    margin-top: 12px;

    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;

    color: #d41f15;
  }
}
