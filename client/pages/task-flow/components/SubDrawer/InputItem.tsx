import { useAtom } from 'jotai';
import { editingActionAtom } from 'pages/task-flow/atoms';
import React, { useEffect, useState } from 'react';
import { Input } from 'zent';

const InputItem = ({
  variable,
  subDrawerData,
  selectedData,
  disabled,
  addonBefore = '',
  addonAfter = '',
  editingAction,
  setEditingAction,
}) => {
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    if (variable && selectedData) {
      const { code } = variable;
      const defaultValue = JSON.parse(selectedData || '{}')[code]?.name;
      setInputValue(defaultValue);
    }
  }, [selectedData]);

  const onInputChange = e => {
    const pattern = variable?.validateRule;
    const { value } = e.target;
    const trimmedValue = value.trim(); // 去除前导和尾随空格
    if (pattern && (trimmedValue || trimmedValue === '0')) {
      const regex = new RegExp(pattern);
      if (regex.test(trimmedValue)) {
        setInputValue(trimmedValue);
      } else {
        // 这一步看起来很傻逼  但是是为了限制输入框直接被输入，和inputValue对不上的问题
        setInputValue(inputValue || '');
      }
    } else {
      setInputValue(trimmedValue);
    }
  };

  const onInputBlur = e => {
    const { value } = e.target;
    const actionFieldValueMaps4Display = JSON.parse(
      editingAction?.actionFieldValueMaps4Display || '{}',
    ) as any;
    const actionFieldValueMaps4Execute = JSON.parse(
      editingAction?.actionFieldValueMaps4Execute || '{}',
    ) as any;

    const { dynamicEndpointConfig, code } = variable;

    actionFieldValueMaps4Execute[code!] = value;
    actionFieldValueMaps4Display[code!] = { name: value };

    // @ts-ignore
    setEditingAction({
      ...subDrawerData,
      actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
      actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
    });
  };

  return (
    <>
      <Input
        value={inputValue}
        onChange={onInputChange}
        onBlur={onInputBlur}
        disabled={disabled}
        addonBefore={addonBefore}
        addonAfter={addonAfter}
        min={2}
        max={10}
        width={'100%'}
        type={'text'}
      />
      <div className="variable-description">{variable.description}</div>
    </>
  );
};

export default InputItem;
