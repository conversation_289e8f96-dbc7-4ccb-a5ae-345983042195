@mixin selected {
  background-color: #f1f6ff;
  color: #155bd4;
  font-weight: 500;
}

.tab-select-container {
  display: flex;
  align-items: center;

  margin-right: 36px;
  .item {
    // width: 80px;
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: left;
    margin-bottom: 4px;
    box-sizing: border-box;

    // 一行超出省略号
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 4px;
    cursor: pointer;

    padding: 0 8px;

    &:hover {
      @include selected;
    }
  }

  .active {
    @include selected;
  }
}
