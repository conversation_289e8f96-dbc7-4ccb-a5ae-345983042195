import React, { CSSProperties } from 'react';
import useSelect, { CommonSelectProps } from '../../../../hooks/useSelect';
import styles from './index.m.scss';
import classNames from 'classnames';
interface TabSelectProps extends CommonSelectProps {
  onChange?: (value: string | number) => void;
  direction?: CSSProperties['flexDirection'];
  className?: string;
}

const TabSelect: React.FC<TabSelectProps> = ({
  options,
  value,
  defaultValue,
  onChange,
  direction = 'column',
  ...rest
}) => {
  const { currentValue, setCurrentValue } = useSelect({ defaultValue, options, value });

  const handleChange = value => {
    setCurrentValue(value.value);
    onChange && onChange(value);
  };

  return (
    <div className={styles.tabSelectContainer} style={{ flexDirection: direction }} {...rest}>
      {options.map(option => (
        <span
          key={option.value}
          className={classNames({
            [styles.item]: true,
            [styles.active]: option.value === currentValue,
          })}
          onClick={() => handleChange({ ...option })}
        >
          {option.label}
        </span>
      ))}
    </div>
  );
};

export default TabSelect;
