import React from 'react';
import { Form, FormInputField, FormSelectField, FormStrategy, Input, Select } from 'zent';
import { ChainGoodsTypeList, chainShopType, GoodsType, GoodsTypeList } from '../../constant';
import { FormSearchSelectField } from '../../../../../../components/SearchSelect';
import { FormSearchCascader } from '../../../../../../components/SearchCascader';
import { searchData } from '../../../../../../api/workflow';
import { debounce, isEmpty } from 'lodash';
import { ItemSelectType } from '../../../../../work-flow/constant';
import { useAtom } from 'jotai';
import { flowAtom } from 'pages/work-flow/atoms';

import { isRetailHqStore, isRetailShop, isRetailBranchStore } from '@youzan/utils-shop';

const SearchForm = ({ onChange, type = ItemSelectType.goods }) => {
  const [flowData, setFlowData] = useAtom(flowAtom);

  const form = Form.useForm(FormStrategy.View);

  const emitSubmit = debounce(() => {
    form.submit();
  }, 500);

  const onSubmit = form => {
    const formData = form.getValue();
    const formatFormData = Object.keys(formData).reduce((acc, key) => {
      const value = formData[key];
      if (Array.isArray(value)) {
        acc[key] = value[value.length - 1];
      } else if (typeof value === 'object' && !isEmpty(value)) {
        acc[key] = value.key;
      } else {
        acc[key] = value;
      }
      return acc;
    }, {});
    onChange && onChange(formatFormData);
  };

  return (
    <div>
      <Form direction="row" form={form} onSubmit={onSubmit}>
        {type === ItemSelectType.goods && (
          <>
            <FormSelectField
              name="goodsType"
              onChange={emitSubmit}
              initialValue={GoodsTypeList.find(item => item.key === GoodsType.ALL)}
              props={{ options: isRetailShop ? ChainGoodsTypeList : GoodsTypeList, width: 150 }}
              withoutLabel
            ></FormSelectField>
            <FormSearchCascader
              name="goodsGroupIds"
              onChange={emitSubmit}
              defaultValue={['']}
              props={{
                fetchList: searchData,
                defaultParams: { apiId: 'goodsGroup', bizCode: flowData.triggerDefine?.code },
                placeholder: '选择分组',
                fixOptions: { id: '', name: '全部分组' },
                width: 150,
              }}
              withoutLabel
            ></FormSearchCascader>
            <FormInputField
              name="keyWord"
              onChange={emitSubmit}
              props={{
                onIconClick: () => {},
                icon: 'search',
                placeholder: '搜索商品名称、条码',
              }}
            ></FormInputField>
          </>
        )}
        {(type === ItemSelectType.chainStoreSellGoods ||
          type === ItemSelectType.chainStoreGoods ||
          type === ItemSelectType.retailPurchaseGoods) && (
          <>
            {type !== ItemSelectType.retailPurchaseGoods && (
              <FormSelectField
                name="goodsType"
                onChange={emitSubmit}
                initialValue={GoodsTypeList.find(item => item.key === GoodsType.ALL)}
                props={{
                  options: isRetailShop ? ChainGoodsTypeList : GoodsTypeList,
                  width: 150,
                }}
                withoutLabel
              ></FormSelectField>
            )}

            {!isRetailBranchStore && (
              <FormSearchCascader
                name="categoryId"
                onChange={emitSubmit}
                defaultValue={['']}
                multiple
                props={{
                  fetchList: searchData,
                  defaultParams: {
                    apiId: 'retailGoodsCategory',
                    bizCode: flowData.triggerDefine?.code,
                  },
                  placeholder: '选择分类',
                  fixOptions: { id: '', name: '全部分类' },
                  width: 100,
                }}
                withoutLabel
              ></FormSearchCascader>
            )}
            <FormInputField
              name="keyWord"
              onChange={emitSubmit}
              props={{
                onIconClick: () => {},
                icon: 'search',
                placeholder: '搜索商品名称、条码',
              }}
            ></FormInputField>
          </>
        )}

        {type === ItemSelectType.chainStoreSubShop && (
          <>
            {/* <FormSelectField
              name="storeType"
              onChange={emitSubmit}
              initialValue={GoodsTypeList.find(item => item.key === GoodsType.ALL)}
              props={{ options: isRetailHqStore ? ChainGoodsTypeList : GoodsTypeList, width: 150 }}
              withoutLabel
            ></FormSelectField>
            <FormSelectField
              name="chainDepartment"
              onChange={emitSubmit}
              initialValue={GoodsTypeList.find(item => item.key === GoodsType.ALL)}
              props={{ options: isRetailHqStore ? ChainGoodsTypeList : GoodsTypeList, width: 150 }}
              withoutLabel
            ></FormSelectField> */}
            {/* <FormSearchCascader
              name="storeType"
              onChange={emitSubmit}
              defaultValue={['']}
              props={{
                fetchList: searchData,
                defaultParams: { apiId: 'storeType', bizCode: flowData.triggerDefine?.code },
                placeholder: '店铺类型',
                fixOptions: { id: '', name: '全部类型' },
                width: 100,
              }}
              withoutLabel
            ></FormSearchCascader> */}
            <FormSearchCascader
              name="storeType"
              onChange={emitSubmit}
              defaultValue={['']}
              multiple
              props={{
                fetchList: searchData,
                defaultParams: {
                  apiId: 'retailShopChannel',
                  bizCode: flowData.triggerDefine?.code,
                },
                placeholder: '选择分类',
                fixOptions: { id: '', name: '全部分类' },
                width: 180,
              }}
              withoutLabel
            ></FormSearchCascader>
            <FormSearchCascader
              name="depId"
              onChange={emitSubmit}
              defaultValue={['']}
              props={{
                fetchList: searchData,
                defaultParams: { apiId: 'chainDepartment', bizCode: flowData.triggerDefine?.code },
                placeholder: '店铺部门',
                fixOptions: { id: '', name: '全部部门' },
                width: 180,
              }}
              withoutLabel
            ></FormSearchCascader>
            <FormInputField
              name="keyWord"
              onChange={emitSubmit}
              props={{
                onIconClick: () => {},
                icon: 'search',
                placeholder: '搜索店铺名称',
              }}
            ></FormInputField>
          </>
        )}
        {type === ItemSelectType.coupon && (
          <FormInputField
            name="keyWord"
            onChange={emitSubmit}
            props={{
              icon: 'search',
              placeholder: '搜索优惠券名称',
            }}
          ></FormInputField>
        )}
        {type === ItemSelectType.couponCode && (
          <FormInputField
            name="keyWord"
            onChange={emitSubmit}
            props={{
              icon: 'search',
              placeholder: '搜索优惠码名称',
            }}
          ></FormInputField>
        )}
        {type === ItemSelectType.present && (
          <FormInputField
            name="keyWord"
            onChange={emitSubmit}
            props={{
              icon: 'search',
              placeholder: '搜索赠品名称',
            }}
          ></FormInputField>
        )}
      </Form>
    </div>
  );
};

export default SearchForm;
