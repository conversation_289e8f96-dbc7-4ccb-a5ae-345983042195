import React, { useEffect, useState } from 'react';
import SearchForm from '../SearchForm';
import { Button, Checkbox, Drawer, Grid, Icon, InfiniteScroller, Pop } from 'zent';
import { querySelectedData, searchData } from '../../../../../../api/workflow';
import formatMoney from '@youzan/utils/money/format';
import styles from './index.m.scss';
import { useInfinityList } from '../../../../../../hooks/useInfinityList';
import { ItemSelectType, TYPE_DESC_MAP } from '../../../../../work-flow/constant';
import { isRetailBranchStore, isRetailHqStore } from '@youzan/utils-shop';

interface DetailListProps {
  type: ItemSelectType;
  listIds: any[];
  listSubIds?: any[];
  visible?: boolean;
  extParam?: any;
  onEdit: () => void;
  onClose: () => void;
}

const DetailList: React.FC<DetailListProps> = ({
  type,
  listIds,
  listSubIds,
  onEdit,
  onClose,
  visible,
  extParam,
}) => {
  const [detailList, setDetailList] = useState<any[]>([]);
  const [innerVisible, setInnerVisible] = useState(visible);

  const closeDrawer = () => {
    setInnerVisible(false);
    onClose && onClose();
  };

  const goodsColumns = [
    {
      title: '商品名称',
      width: '200',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <div className={styles.name}>{name}</div>
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            <div className={styles.sku}>
              {skuInfos?.length > 1 &&
                skuInfos
                  .filter(item => {
                    return listSubIds?.includes(item.id);
                  })
                  .map(sku => <div>{sku.name}</div>)}
            </div>
          </div>
        );
      },
    },
    {
      title: '商品分组',
      name: 'groupNames',
      // 数组格式，展示为逗号分隔
      bodyRender: _ => {
        const { groupNames } = _;
        return groupNames?.join(',') || '';
      },
    },
    {
      title: '库存',
      name: 'stockNum',
    },
  ];
  const couponColumns = [
    {
      title: '优惠券名称',
      name: 'name',
    },
    {
      title: '类型',
      name: 'cardTypeDesc',
    },
    {
      title: '优惠内容',
      name: 'formativeContext',
    },
  ];

  const couponCodeColumns = [
    {
      title: '优惠码名称',
      name: 'name',
    },
    {
      title: '类型',
      name: 'typeDesc',
    },
    {
      title: '优惠内容',
      name: 'formativeContext',
    },
    {
      title: '剩余库存',
      name: 'stockNum',
    },
  ];

  const presentColumns = [
    {
      title: '赠品名称',
      name: 'name',
      bodyRender: _ => {
        const { name, picture } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.infoOnlyName}>
                <div className={styles.name}>{name}</div>
                {/* <div className={styles.price}>￥{formatMoney(price)}</div> */}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: '剩余库存',
      name: 'stockNum',
    },
  ];
  const goodsGroupColumns = [
    {
      title: '分组名称',
      name: 'name',
    },
    {
      title: '当前商品总数',
      name: 'goodsCount',
    },
  ];

  const storeColumns = [
    {
      title: '店铺名称',
      name: 'name',
    },
    {
      title: '店铺类型',
      name: 'storeTypeDesc',
    },
  ];

  const storeGoodsColumns = [
    {
      title: '商品名称',
      width: '200',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <div className={styles.name}>{name}</div>
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            <div className={styles.sku}>
              {skuInfos?.length > 1 &&
                skuInfos
                  .filter(item => {
                    return listSubIds?.includes(item.id);
                  })
                  .map(sku => <div>{sku.name}</div>)}
            </div>
          </div>
        );
      },
    },
    /* {
      title: '商品分类',
      name: 'groupNames',
      // 数组格式，展示为逗号分隔
      bodyRender: _ => {
        const { groupNames } = _;
        return groupNames?.join(',') || '';
      },
    }, */
    !isRetailHqStore &&
      !isRetailBranchStore && {
        title: '库存',
        name: 'stockNum',
      },
  ];

  const storeSellGoodsColumns = [
    {
      title: '商品名称',
      width: '200',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <Pop trigger="hover" content={name} position="bottom-center">
                  <div className={styles.name}>{name}</div>
                </Pop>
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            <div className={styles.sku}>
              {skuInfos?.length > 1 &&
                skuInfos
                  .filter(item => {
                    return listSubIds?.includes(item.id);
                  })
                  .map(sku => <div>{sku.name}</div>)}
            </div>
          </div>
        );
      },
    },
    /* {
      title: '商品分组',
      name: 'groupNames',
      // 数组格式，展示为逗号分隔
      bodyRender: _ => {
        const { groupNames } = _;
        return groupNames?.join(',') || '';
      },
    }, */
    !isRetailHqStore &&
      !isRetailBranchStore && {
        title: '库存',
        name: 'stockNum',
      },
  ];

  const retailPurchaseGoodsColumns = [
    {
      title: '商品名称',
      width: '200',
      name: 'name',
      bodyRender: _ => {
        const { name, skuInfos, picture, price, spuNo } = _;
        return (
          <div className={styles.goodsContainer}>
            <div className={styles.goodsCard}>
              <img src={picture} width={48} height={48} />
              <div className={styles.info}>
                <div className={styles.name}>{name}</div>
                {spuNo && <div className={styles.spuNo}>{spuNo}</div>}
                <div className={styles.price}>￥{formatMoney(price)}</div>
              </div>
            </div>
            <div className={styles.sku}>
              {skuInfos?.length > 1 &&
                skuInfos
                  .filter(item => {
                    return listSubIds?.includes(item.id);
                  })
                  .map(sku =>
                    sku.skuNo ? (
                      <div>
                        <Pop
                          trigger="hover"
                          position={'right-center'}
                          content={
                            <Grid
                              datasets={[sku]}
                              columns={[
                                {
                                  title: '规格条码/编码',
                                  name: 'skuNo',
                                },
                              ]}
                            />
                          }
                        >
                          <span>
                            {sku.name}
                            <Icon type="info-circle-o" style={{ marginLeft: 2 }} />
                          </span>
                        </Pop>
                      </div>
                    ) : (
                      sku.name
                    ),
                  )}
            </div>
          </div>
        );
      },
    },
    {
      title: '库存',
      name: 'stockNum',
    },
    {
      title: '状态',
      name: 'lifeCycleName',
    },
  ];

  const columnMap = {
    [ItemSelectType.goods]: goodsColumns,
    [ItemSelectType.coupon]: couponColumns,
    [ItemSelectType.couponCode]: couponCodeColumns,
    [ItemSelectType.present]: presentColumns,
    [ItemSelectType.goodsGroup]: goodsGroupColumns,
    [ItemSelectType.chainStoreSubShop]: storeColumns,
    [ItemSelectType.chainStoreGoods]: storeGoodsColumns,
    [ItemSelectType.chainStoreSellGoods]: storeSellGoodsColumns,
    [ItemSelectType.retailPurchaseGoods]: retailPurchaseGoodsColumns,
  };

  const currentColumns = columnMap[type];

  useEffect(() => {
    setInnerVisible(visible);
  }, [visible]);

  useEffect(() => {
    // 通过listids获取详情列表
    querySelectedData({
      searchDataType: type,
      ids: listIds.join(','),
      extParam: JSON.stringify(extParam),
    }).then(res => {
      setDetailList(res);
    });
  }, []);

  return (
    <Drawer
      title={
        <>
          {/* 增加一个返回按钮+标题 */}
          <div className={styles.headerTitle}>
            <Icon
              type="left"
              style={{ fontSize: 30, cursor: 'pointer', fontWeight: 500 }}
              onClick={closeDrawer}
            />
            <span>{`${TYPE_DESC_MAP[type] || ''}范围`}</span>
          </div>
        </>
      }
      placement="right"
      width={682}
      visible={innerVisible}
      onClose={() => {
        setInnerVisible(false);
        onClose && onClose();
      }}
      maskClosable
      closeBtn={<Icon type="close" style={{ fontWeight: 600, fontSize: 22 }} />}
    >
      <div className={styles.selectContainer}>
        <Grid
          columns={currentColumns}
          rowKey={'id'}
          datasets={detailList}
          className={styles.list}
        ></Grid>
        <div className={styles.footer}>
          <Button type="primary" onClick={onEdit}>
            编辑
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default DetailList;
