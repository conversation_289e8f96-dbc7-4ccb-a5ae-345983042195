.tag-select-field {
  cursor: pointer;
  height: 38px;
  // width: 310px;
  flex-grow: 1;
  cursor: pointer;
  background-color: #fff;
  padding: 8px;
  line-height: 22px;
  border-radius: 4px;
  color: #ccc;
  box-sizing: border-box;
  border: 1px solid #e0e0e0;
  display: flex;

  .title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;

    color: #333;
  }

  .count {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;

    color: #333;
  }
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  height: 64px;
  align-items: center;
  padding: 0 16px;

  .footer-text {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;

    color: #999;
  }

  .footer-link {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;

    color: #155BD4;
    cursor: pointer;
  }
}

.select-container {
  padding: 0 24px 16px;

  .tag-group {
    margin-top: 20px;

    .tag-group-name {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;

      color: #333;
    }

    .tag-item-wrapper {
      margin-top: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        width: 152px;
        height: 30px;
        border-radius: 4px;
        background-color: #f8f8f8;
        color: #0D0C21;
        display: flex;
        align-items: center;
        justify-content: center;

        cursor: pointer;

        &:hover {
          background-color: rgba(21, 91, 212, 0.1);
          color: #155BD4;
        }
      }

      .selected {
        background-color: rgba(21, 91, 212, 0.1);
        color: #155BD4;
      }
    }
  }
}