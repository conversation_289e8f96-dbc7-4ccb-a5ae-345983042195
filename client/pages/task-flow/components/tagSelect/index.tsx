import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Icon } from 'zent';
import { querySelectedDataByTag, searchData } from '../../../../api/workflow';
import styles from './index.m.scss';
import { useInfinityList } from '../../../../hooks/useInfinityList';


const TagSelect: React.FC<any> = ({
  closeDrawer,
  tagVisible,
  onConfirm,
  selectedTags,
  setSelectedTags,
  setShowSelectedTags
}) => {
  const { loading, hasMore, list, loadMore, refresh } = useInfinityList({
    fetchList: searchData,
    defaultParams: {
      apiId: 'userTag',
    },
    defaultPageSize: 200,
  });

  const handleTagClick = (tagId: string, group: any) => {
    const isMultiSelect = group.isMultiSelect;
    setSelectedTags((prev: any) => {
      if (prev.includes(tagId)) {
        return prev.filter((id: string) => id !== tagId);
      }

      if (isMultiSelect) {
        return [...prev, tagId];
      } else {
        const groupTagIds = group?.items.map(item => item.id) || [];
        const otherTags = prev.filter(id => !groupTagIds.includes(id));

        return [...otherTags, tagId];
      }
    });
  };

  return <Drawer
    title={
      <>
        {/* 增加一个返回按钮+标题 */}
        <div className={styles.headerTitle}>
          <Icon
            type="left"
            style={{ fontSize: 20, cursor: 'pointer', fontWeight: 500 }}
            onClick={() => closeDrawer()}
          />
          <span>选择标签</span>
        </div>
      </>
    }
    placement="right"
    width={692}
    visible={tagVisible}
    onClose={() => {
      closeDrawer();
    }}
    maskClosable
    closeBtn={<Icon type="close" style={{ fontWeight: 600, fontSize: 22 }} />}
    footer={<div className={styles.footer}>
      <span className={styles.footerText}>已选{selectedTags.length}个标签</span>
      <span className={styles.footerLink} onClick={() => setShowSelectedTags(true)}>查看已选标签</span>
      <Button onClick={() => closeDrawer()}>取消</Button>
      <Button
        type="primary"
        onClick={() => {
          onConfirm(selectedTags);
          closeDrawer();
        }}
      >
        确定
      </Button>
    </div>}
  >
    <div className={styles.selectContainer}>
      {list.map((item: any) => {
        return <div className={styles.tagGroup} key={item.id}>
          <div className={styles.tagGroupName}>
            {item.groupName}
            <span className={styles.selectType}>
              ({item.isMultiSelect ? '多选' : '单选'})
            </span>
          </div>
          <div className={styles.tagItemWrapper}>
            {item.items.map((tag: any) => {
              return <div
                onClick={() => handleTagClick(tag.id, item)}
                key={tag.id}
                className={`${styles.tagItem} ${selectedTags.includes(tag.id) ? styles.selected : ''}`}
              >
                {tag.itemName}
              </div>;
            })}
          </div>
        </div>;
      })}



    </div>
  </Drawer>
};

export default TagSelect;

const TagSelectedDrawer = ({
  visible,
  onClose,
  selectedTags
}) => {
  const [selectedTagList, setSelectedTagList] = useState<any[]>([]);

  useEffect(() => {
    // 通过listids获取详情列表
    querySelectedDataByTag({
      searchDataType: 'userTag',
      ids: selectedTags.join(','),
    }).then(res => {
      console.log(res);
      setSelectedTagList(res);
    });
  }, [selectedTags]);
  return <Drawer
    title={<div className={styles.headerTitle}><Icon
      type="left"
      style={{ fontSize: 20, cursor: 'pointer', fontWeight: 500 }}
      onClick={() => onClose()}
    />已选{selectedTags.length}个标签</div>}
    placement="right"
    width={692}
    visible={visible}
    onClose={onClose}
  >
    <div className={styles.selectContainer}>
      {selectedTagList.map((item: any) => {
        return <div className={styles.tagGroup} key={item.id}>
          <div className={styles.tagGroupName}>
            {item.groupName}
          </div>
          <div className={styles.tagItemWrapper}>
            {item.items.map((tag: any) => {
              return <div
                key={tag.id}
                className={`${styles.tagItem} ${selectedTags.includes(tag.id) ? styles.selected : ''}`}
              >
                {tag.itemName}
              </div>;
            })}
          </div>
        </div>;
      })}
    </div>
  </Drawer>
}

const TagSelectField = ({
  onConfirm,
  selectedTags: selectedTagsProp
}) => {
  const [showTagSelect, setShowTagSelect] = useState(false);
  const [fieldTitle, setFieldTitle] = useState('');
  const [showSelectedTags, setShowSelectedTags] = useState(false);
  const [selectedTags, setSelectedTags] = useState<number[]>(selectedTagsProp || []);

  const closeDrawer = () => {
    setShowTagSelect(false);
  };

  return (
    <>
      <div
        onClick={() => setShowTagSelect(true)}
        className={`${styles.tagSelectField} ${fieldTitle ? styles.value : ''}`}
      >
        <div className={styles.title} style={{ color: selectedTags.length ? '#333' : '#999' }}>{selectedTags.length ? '已选标签' : '请选择'}</div>
        {selectedTags.length > 0 && <div className={styles.count}> {selectedTags.length}个</div>}
      </div>
      <TagSelect closeDrawer={closeDrawer} setShowSelectedTags={setShowSelectedTags} onConfirm={onConfirm} selectedTags={selectedTags} setSelectedTags={setSelectedTags} tagVisible={showTagSelect} />
      <TagSelectedDrawer
        visible={showSelectedTags}
        onClose={() => setShowSelectedTags(false)}
        selectedTags={selectedTags}
      />
    </>
  );
};

export { TagSelectField };
