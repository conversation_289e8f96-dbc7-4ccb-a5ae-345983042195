export enum GoodsType {
  /**
   * 全部商品
   */
  ALL = '',
  /**
   * 实物商品
   */
  NORMAL = 'NORMAL',
  /**
   * 虚拟商品
   */
  VIRTUAL = 'VIRTUAL',
  /**
   * 电子卡券商品
   */
  ECARD = 'ECARD',
  /**
   * 付费等级
   */
  MEMBER_GRADE = 'MEMBER_GRADE',
  /**
   * 付费会员卡
   */
  MEMBER_CARD = 'MEMBER_CARD',
  /**
   * 酒店商品
   */
  HOTEL = 'HOTEL',
  /**
   * 周期购商品
   */
  PERIOD_BUY = 'PERIOD_BUY',
  /**
   * 分销商品
   */
  FEN_XIAO = 'FEN_XIAO',
  /**
   * 海淘商品
   */
  HAI_TAO = 'HAI_TAO',
  /**
   * 课程商品
   */
  PAID_COUPONS = 'PAID_COUPONS',
  /**
   * 预售商品
   */
  PRESALE = 'PRESALE',
  /**
   * 烘焙茶饮
   */
  TEA_BAKING = 'TEA_BAKING',
}

export const GoodsTypeList = [
  {
    key: '',
    text: '全部商品',
  },
  {
    key: GoodsType.NORMAL,
    text: '实物商品',
  },
  {
    key: GoodsType.VIRTUAL,
    text: '虚拟商品',
  },
  {
    key: GoodsType.ECARD,
    text: '电子卡券商品',
  },
  {
    key: GoodsType.MEMBER_GRADE,
    text: '付费等级',
  },
  {
    key: GoodsType.MEMBER_CARD,
    text: '付费会员卡',
  },
  {
    key: GoodsType.HOTEL,
    text: '酒店商品',
  },
  {
    key: GoodsType.PERIOD_BUY,
    text: '周期购商品',
  },
  {
    key: GoodsType.FEN_XIAO,
    text: '分销商品',
  },
  {
    key: GoodsType.HAI_TAO,
    text: '海淘商品',
  },
  {
    key: GoodsType.PAID_COUPONS,
    text: '付费优惠券商品',
  },
  {
    key: GoodsType.PRESALE,
    text: '预售商品',
  },
  {
    key: GoodsType.TEA_BAKING,
    text: '烘焙茶饮',
  },
];

// 连锁商品类型
export const ChainGoodsTypeList = [
  {
    key: '',
    text: '全部商品',
  },
  {
    key: GoodsType.NORMAL,
    text: '实物商品',
  },
  {
    key: GoodsType.VIRTUAL,
    text: '虚拟商品',
  },
  {
    key: GoodsType.ECARD,
    text: '电子卡券',
  },
  {
    key: GoodsType.TEA_BAKING,
    text: '烘焙茶饮',
  },
  {
    key: GoodsType.PAID_COUPONS,
    text: '付费优惠券商品',
  },
];

export const chainShopType = [
  // （门店：OFFLINE_STORE,网店：ONLINE_STORE）
  {
    key: '',
    text: '全部类型',
  },
  {
    key: 'OFFLINE_STORE',
    text: '门店',
  },
  {
    key: 'ONLINE_STORE',
    text: '网店',
  },
];
