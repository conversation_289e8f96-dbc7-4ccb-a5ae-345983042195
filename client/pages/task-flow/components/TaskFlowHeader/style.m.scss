.task-flow-header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .task-flow-header-left {
        display: flex;
        align-items: center;
        align-items: center;

        .task-flow-header-left-logo {
            width: 32px;
            height: 32px;
        }

        .task-flow-header-left-title {
            font-size: 24px;
            font-weight: 500;
            line-height: 40px;

            margin-left: 12px;
        }
    }

    .task-flow-header-right {
        display: flex;
        align-items: center;
        gap: 12px;

        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        line-height: 16.8px;

        .task-flow-header-right-status {
            display: flex;
            align-items: center;

            .status-dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #45A110;
            }

            .status-text {
                margin-left: 8px;
                color: #45A110;
            }
        }

        .task-flow-header-right-status-unrun {
            .status-dot {
                background-color: #ccc;
            }

            .status-text {
                color: #ccc;
            }
        }

        .task-flow-header-right-status-running {
            .status-dot {
                background-color: #45A110;
            }

            .status-text {
                color: #45A110;
            }

        }

        .split-line {
            background-color: #E0E0E0;
            width: 1px;
            height: 8px;
        }

        .task-flow-header-right-time {

            .time-text {

                color: rgba(51, 51, 51, 0.3);
            }
        }
    }
}