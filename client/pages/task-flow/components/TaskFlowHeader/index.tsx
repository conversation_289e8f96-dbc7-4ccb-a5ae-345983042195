import styles from './style.m.scss';
import React, { useState } from 'react';

export default function TaskFlowHeader({ agentInfo }: { agentInfo: any }) {

    const { name, updateTime, status, icon } = agentInfo;

    return <div className={styles.taskFlowHeader}>
        <div className={styles.taskFlowHeaderLeft}>
            <img
                className={styles.taskFlowHeaderLeftLogo}
                src={icon}
                alt="logo"
            />
            <div className={styles.taskFlowHeaderLeftTitle}>{name}</div>
        </div>

        <div className={styles.taskFlowHeaderRight}>
            <div className={styles.taskFlowHeaderRightStatus + ' ' + (status === 0 ? styles.taskFlowHeaderRightStatusUnrun : styles.taskFlowHeaderRightStatusRunning)}>
                <span className={styles.statusDot}></span>
                <span className={styles.statusText}>{status === 0 ? '未运行' : '运行中'}</span>
            </div>

            <div className={styles.splitLine}></div>

            <div className={styles.taskFlowHeaderRightTime}>
                <span className={styles.timeText}>最近更新时间 {updateTime}</span>
            </div>
        </div>
    </div>
}