import React from 'react';
import styles from './styles.m.scss';

const Description: React.FC<{ agentInfo: any; tips?: React.ReactNode }> = ({ agentInfo, tips }) => {
  const { description } = agentInfo;
  return (
    <div className={styles.desc}>
      {tips && <div className={styles.tips}>{tips}</div>}

      <div className={styles.descTitle}>描述</div>
      <div className={styles.descContent}>{description}</div>
    </div>
  );
};

export default Description;
