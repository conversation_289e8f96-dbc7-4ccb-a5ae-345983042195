@use '../../common.scss';

.skills {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .skills-title {
    @extend .commonCardTitle;
  }

  .skills-list {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .skill-item {
      display: flex;
      flex-direction: column;
      gap: 12px;
      border-radius: 8px;
      padding: 20px;
      background-color: #f6f8fb;

      .skill-item-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .skill-item-top-left {
          display: flex;
          align-items: center;

          .skill-item-title {
            font-size: 16px;
            font-weight: 500;
          }

          .skill-item-status {
            margin-left: 16px;
            display: flex;
            align-items: center;
            color: #45a110;

            .status-dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background-color: #45a110;
              display: inline-block;
              margin-right: 8px;
            }
          }

          .paused {
            color: #999;

            .status-dot {
              background-color: #999;
            }
          }

          .running {
            color: #45a110;

            .status-dot {
              background-color: #45a110;
            }
          }
        }

        .skill-item-action {
          display: flex;
          gap: 12px;
          visibility: hidden;

          img {
            width: 20px;
            height: 20px;
            cursor: pointer;
          }
        }
      }

      .skill-item-desc {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: rgba(51, 51, 51, 0.3);
      }

      &:hover {
        .skill-item-action {
          visibility: visible;
        }
      }
    }

    .skill-group {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 24px;
      background-color: #f7f7f7;
      border-radius: 8px;

      .group-title {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 4px;
          width: 4px;
          height: 16px;
          background: #3594f0;
          border-radius: 2px;
        }
      }

      .group-divider {
        height: 1px;
        background-color: #f0f0f0;
      }

      .group-content {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .skill-item {
          padding: 0;
          gap: 4px;
          background-color: transparent;
        }
      }
    }
  }
}
