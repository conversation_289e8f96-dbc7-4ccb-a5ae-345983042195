import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { ISkill } from '../../constants/index';
import styles from './styles.m.scss';
import { Button, closeDialog, openDialog, Notify } from 'zent';
import EditSkillDrawer from '../EditSkillDrawer';
import { useAtom } from 'jotai';
import { flowAtom, editingActionAtom, editingConditionAtom } from 'pages/work-flow/atoms';
import { querySkills, saveSkill } from 'pages/task-flow/api/agent';
import { formatConditionWhenGet } from 'pages/work-flow/utils/flow';
import SkillsGroupList from './skillGroupList';
import { isWechatMiniStoreAgent } from '../CommonLayout/tools';

interface SkillsListProps {
  agentInfo?: any;
  agentId: number;
}

interface SkillItemProps {
  skill: ISkill;
  key: number;
  drawerVisible: boolean;
  setDrawerVisible: Dispatch<SetStateAction<boolean>>;
  setEditSkill: Dispatch<SetStateAction<ISkill | null>>;
  agentId: number;
  refreshSkills?: () => void;
}

const DIALOG_ID = 'toggleTaskStatus';

const SkillItem: React.FC<SkillItemProps> = ({
  skill,
  key,
  drawerVisible,
  setDrawerVisible,
  setEditSkill,
  agentId,
  refreshSkills,
}) => {
  const [flowData, setFlowData] = useAtom(flowAtom);
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [editingCondition, setEditingCondition] = useAtom(editingConditionAtom);

  const handleTaskStatusChange = (enable: boolean) => {
    const { initialized, config } = skill;
    const { flowTemplate, flowRule } = config;
    const chainRules = initialized ? flowRule?.chainRules : flowTemplate?.chainRules;
    saveSkill({
      skillId: skill.id,
      agentId,
      config: JSON.stringify({
        flowRule: {
          chainRules,
        },
      }),
      enable,
    })
      .then(() => {
        closeDialog(DIALOG_ID);
        Notify.success(enable ? '启用成功' : '暂停成功');
        refreshSkills?.();
      })
      .catch(error => {
        Notify.error(error);
      });
  };

  const handleToggleTaskStatus = ({
    enable,
    initialized,
  }: {
    enable: boolean;
    initialized: boolean;
  }) => {
    console.log(enable, initialized);

    if (!initialized && skill.configurable) {
      handleEditTask(skill.id, true);
      return;
    }

    openDialog({
      dialogId: DIALOG_ID,
      title: '提示',
      children: `确定要${!enable ? '启用' : '暂停'}该技能吗？`,
      footer: (
        <>
          <Button onClick={() => closeDialog(DIALOG_ID)}>取消</Button>
          {!enable ? (
            <Button type="primary" onClick={() => handleTaskStatusChange(true)}>
              启用
            </Button>
          ) : (
            <Button type="primary" onClick={() => handleTaskStatusChange(false)}>
              暂停
            </Button>
          )}
        </>
      ),
    });
  };

  const handleEditTask = (id: number, enable: boolean = false) => {
    const {
      config: { flowTemplate, flowRule },
      initialized,
    } = skill ?? {};
    console.log(id);
    setDrawerVisible(true);
    setEditSkill(skill);
    // @ts-ignore
    setFlowData(flowTemplate);

    // 用于在没有初始化时，启用技能会打开编辑弹窗，需要修改 enable 为 true
    if (enable) {
      setEditSkill({
        ...skill,
        enable: true,
      });
    }

    /* if (!initialized) {
            if (flowTemplate.chainRules?.length) {
                setEditingAction(flowTemplate.chainRules[0].action);
                setEditingCondition(flowTemplate.chainRules[0].conditions);
            }
        } else {
            setEditingAction(flowRule![0].action);
            setEditingCondition(flowRule![0].conditions);
        } */
  };

  return (
    <div key={key} className={styles.skillItem}>
      <div className={styles.skillItemTop}>
        <div className={styles.skillItemTopLeft}>
          <div className={styles.skillItemTitle}>{skill.name}</div>
          <div
            className={
              styles.skillItemStatus + ' ' + (skill.enable ? styles.running : styles.paused)
            }
          >
            <div className={styles.statusDot}></div>
            {skill.enable ? '运行中' : '已暂停'}
          </div>
        </div>
        <div className={styles.skillItemAction}>
          {skill.configurable && (
            <div onClick={() => handleEditTask(skill.id)}>
              <img src="https://img01.yzcdn.cn/upload_files/2024/11/16/FhliqxGDI3YNAPnEsjw7N_YqfjB9.png" />
            </div>
          )}
          <div
            onClick={() =>
              handleToggleTaskStatus({ enable: skill.enable, initialized: skill.initialized })
            }
          >
            {skill.enable ? (
              <img src="https://img01.yzcdn.cn/upload_files/2024/11/16/Fl39i_v0-ejcQXHE7uqyRPWVLJUL.png" />
            ) : (
              <img src="https://img01.yzcdn.cn/upload_files/2024/11/22/Fojue24lH30Peq9TFXapzVEza4NH.png" />
            )}
          </div>
        </div>
      </div>
      <div className={styles.skillItemDesc}>{skill.description}</div>
    </div>
  );
};

const SkillsList: React.FC<SkillsListProps> = ({ agentInfo, agentId }) => {
  const [editSkillDrawerVisible, setEditSkillDrawerVisible] = useState(false);
  const [editSkill, setEditSkill] = useState<ISkill | null>(null);
  const [skills, setSkills] = useState<ISkill[]>([]);

  const [useGroupSkillList, setUseGroupSkillList] = useState(false);

  useEffect(() => {
    fetchSkills();
  }, [agentId]);

  const fetchSkills = () => {
    querySkills({ agentId: Number(agentId) }).then(res => {
      const skillsData: ISkill[] = res.map(item => {
        return {
          ...item,
          config: {
            ...item.config,
            flowRule: item.config.flowRule
              ? {
                  ...item.config.flowRule,
                  chainRules: formatConditionWhenGet(item.config.flowRule.chainRules),
                }
              : undefined,
            flowTemplate: item.config.flowTemplate
              ? {
                  ...item.config.flowTemplate,
                  chainRules: formatConditionWhenGet(item.config.flowTemplate.chainRules),
                }
              : undefined,
          },
        };
      });

      setSkills(skillsData);
      const isWechatXdAgent = isWechatMiniStoreAgent(agentInfo);
      setUseGroupSkillList(isWechatXdAgent);
    });
  };

  return (
    <div className={styles.skills}>
      <div className={styles.skillsTitle}>技能</div>
      <div className={styles.skillsList}>
        {useGroupSkillList ? (
          <SkillsGroupList
            agentCode={agentInfo?.code}
            agentId={agentId}
            refreshSkills={fetchSkills}
            skills={skills}
            setDrawerVisible={setEditSkillDrawerVisible}
            setEditSkill={setEditSkill}
          />
        ) : (
          skills.map((skill, index) => (
            <SkillItem
              key={index}
              refreshSkills={fetchSkills}
              skill={skill}
              agentId={agentId}
              drawerVisible={editSkillDrawerVisible}
              setDrawerVisible={setEditSkillDrawerVisible}
              setEditSkill={setEditSkill}
            />
          ))
        )}
      </div>

      <EditSkillDrawer
        refreshSkills={fetchSkills}
        visible={editSkillDrawerVisible}
        onClose={() => setEditSkillDrawerVisible(false)}
        skill={editSkill}
        agentId={agentId}
      />
    </div>
  );
};

export default SkillsList;
