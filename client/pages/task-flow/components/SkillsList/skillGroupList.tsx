import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { ISkill } from '../../constants/index';
import styles from './styles.m.scss';
import { Button, closeDialog, openDialog, Notify } from 'zent';
import { useAtom } from 'jotai';
import { flowAtom, editingAction<PERSON>tom, editingCondition<PERSON>tom } from 'pages/work-flow/atoms';
import { saveSkill } from 'pages/task-flow/api/agent';
import { skillGroupMap } from './skillGroupConfig';
import { formatSkillDesc, needFormatSkillDesc } from '../WxxdAgent/utils/formatSkill';

interface SkillsListProps {
  agentId: number;
  agentCode: string;
  refreshSkills?: () => void;
  skills: ISkill[];
  setDrawerVisible: Dispatch<SetStateAction<boolean>>;
  setEditSkill: Dispatch<SetStateAction<ISkill | null>>;
}

interface SkillItemProps {
  skill: ISkill;
  setDrawerVisible: Dispatch<SetStateAction<boolean>>;
  setEditSkill: Dispatch<SetStateAction<ISkill | null>>;
  agentId: number;
  refreshSkills?: () => void;
}

const DIALOG_ID = 'toggleTaskStatus';

const SkillGroupItem: React.FC<SkillItemProps> = ({
  skill,
  setDrawerVisible,
  setEditSkill,
  agentId,
  refreshSkills,
}) => {
  const [flowData, setFlowData] = useAtom(flowAtom);

  const handleTaskStatusChange = (enable: boolean) => {
    const { initialized, config } = skill;
    const { flowTemplate, flowRule } = config;
    const chainRules = initialized ? flowRule?.chainRules : flowTemplate?.chainRules;
    saveSkill({
      skillId: skill.id,
      agentId,
      config: JSON.stringify({
        flowRule: {
          chainRules,
        },
      }),
      enable,
    })
      .then(() => {
        closeDialog(DIALOG_ID);
        Notify.success(enable ? '启用成功' : '暂停成功');
        refreshSkills?.();
      })
      .catch(error => {
        Notify.error(error);
      });
  };

  const handleToggleTaskStatus = ({
    enable,
    initialized,
  }: {
    enable: boolean;
    initialized: boolean;
  }) => {
    if (!initialized && skill.configurable) {
      handleEditTask(skill.id, true);
      return;
    }

    openDialog({
      dialogId: DIALOG_ID,
      title: '提示',
      children: `确定要${!enable ? '启用' : '暂停'}该技能吗？`,
      footer: (
        <>
          <Button onClick={() => closeDialog(DIALOG_ID)}>取消</Button>
          {!enable ? (
            <Button type="primary" onClick={() => handleTaskStatusChange(true)}>
              启用
            </Button>
          ) : (
            <Button type="primary" onClick={() => handleTaskStatusChange(false)}>
              暂停
            </Button>
          )}
        </>
      ),
    });
  };

  const handleEditTask = (id: number, enable: boolean = false) => {
    const {
      config: { flowTemplate, flowRule },
      initialized,
    } = skill ?? {};
    setEditSkill(skill);
    setDrawerVisible(true);
    // @ts-ignore
    setFlowData(flowTemplate);

    // 用于在没有初始化时，启用技能会打开编辑弹窗，需要修改 enable 为 true
    if (enable) {
      setEditSkill({
        ...skill,
        enable: true,
      });
    }
  };

  const renderSkillDesc = (skill: ISkill) => {
    if (needFormatSkillDesc(skill)) {
      const result = formatSkillDesc(skill);
      return (
        <div className={styles.skillItemDesc}>
          当<span>{result.condition}</span>
          {'时，'}
          <span>{result.action}</span>
        </div>
      );
    }
    return <div className={styles.skillItemDesc}>{skill.description}</div>;
  };

  return (
    <div className={styles.skillItem}>
      <div className={styles.skillItemTop}>
        <div className={styles.skillItemTopLeft}>
          <div className={styles.skillItemTitle}>{skill.name}</div>
          <div
            className={
              styles.skillItemStatus + ' ' + (skill.enable ? styles.running : styles.paused)
            }
          >
            <div className={styles.statusDot}></div>
            {skill.enable ? '运行中' : '已暂停'}
          </div>
        </div>
        <div className={styles.skillItemAction}>
          {skill.configurable && (
            <div onClick={() => handleEditTask(skill.id)}>
              <img src="https://img01.yzcdn.cn/upload_files/2024/11/16/FhliqxGDI3YNAPnEsjw7N_YqfjB9.png" />
            </div>
          )}
          <div
            onClick={() =>
              handleToggleTaskStatus({ enable: skill.enable, initialized: skill.initialized })
            }
          >
            {skill.enable ? (
              <img src="https://img01.yzcdn.cn/upload_files/2024/11/16/Fl39i_v0-ejcQXHE7uqyRPWVLJUL.png" />
            ) : (
              <img src="https://img01.yzcdn.cn/upload_files/2024/11/22/Fojue24lH30Peq9TFXapzVEza4NH.png" />
            )}
          </div>
        </div>
      </div>
      {renderSkillDesc(skill)}
    </div>
  );
};

const SkillsGroupList: React.FC<SkillsListProps> = ({
  agentCode,
  agentId,
  skills,
  refreshSkills,
  setDrawerVisible,
  setEditSkill,
}) => {
  const [groupedSkills, setGroupedSkills] = useState<{ title?: string; skills: ISkill[] }[]>([]);

  useEffect(() => {
    handleGroupSkills();
  }, [skills, agentCode]);

  const handleGroupSkills = () => {
    // 微信小店特定处理
    const grouped: { title?: string; skills: ISkill[] }[] = [];

    const skillGroup = skillGroupMap[agentCode] || [];
    // 创建分组数组
    skillGroup.forEach(group => {
      const groupSkills = skills
        .filter(skill => group.skills.includes(skill.skillTemplateId))
        .sort((a, b) => {
          // 根据 group 中的顺序排序
          return group.skills.indexOf(a.skillTemplateId) - group.skills.indexOf(b.skillTemplateId);
        });

      if (groupSkills.length > 0) {
        grouped.push({
          title: group.title,
          skills: groupSkills,
        });
      }
    });

    // 处理未分组的技能
    const ungroupedSkills = skills.filter(
      skill =>
        !skillGroup
          .map(group => group.skills)
          .flat()
          .includes(skill.skillTemplateId),
    );

    if (ungroupedSkills.length > 0) {
      grouped.push({
        title: '其他技能',
        skills: ungroupedSkills,
      });
    }

    setGroupedSkills(grouped);
  };

  return (
    <div className={styles.skillsList}>
      {groupedSkills.map((group, groupIndex) => (
        <div key={`group-${groupIndex}`} className={styles.skillGroup}>
          {group.title && <div className={styles.groupTitle}>{group.title}</div>}
          <div className={styles.groupDivider}></div>
          <div className={styles.groupContent}>
            {group.skills.map((skill, index) => (
              <SkillGroupItem
                key={`group-item-${skill.skillTemplateId}`}
                refreshSkills={refreshSkills}
                skill={skill}
                agentId={agentId}
                setDrawerVisible={setDrawerVisible}
                setEditSkill={setEditSkill}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SkillsGroupList;
