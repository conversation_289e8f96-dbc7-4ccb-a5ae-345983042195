import React from 'react';
import { IDataOverview } from '../../constants';
import styles from './styles.m.scss';

interface DataOverviewProps {
    data: IDataOverview[];
}

const DataOverview: React.FC<DataOverviewProps> = ({ data }) => {
    return (
        <div className={styles.dataOverview}>
            {data.map((item, index) => (
                <div key={index} className={styles.dataOverviewItem}>
                    <div className={styles.dataOverviewItemTitle}>{item.title}</div>
                    <div className={styles.dataOverviewItemValue}>{item.content}</div>
                </div>
            ))}
        </div>
    );
};

export default DataOverview;