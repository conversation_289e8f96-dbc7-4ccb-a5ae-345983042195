.formulaReport {
  .zent-grid-th[data-zv='9.12.13'] {
    background-color: transparent;
  }

  .zent-grid-tbody {
    .zent-grid-tr:nth-child(odd) {
      .zent-grid-td:not(:first-child) {
        background-color: #f8f8f8;
      }
    }
  }

  .zent-grid-tr[data-zv='9.12.13']:hover,
  .zent-grid-tr__mouseover[data-zv='9.12.13'] {
    background-color: transparent;
  }
  /* .zent-grid-th:not(.symbol):hover {
    //   background-color: #f8f8f8;
    background-color: #e6efff !important;
    cursor: pointer;
  } */

}
