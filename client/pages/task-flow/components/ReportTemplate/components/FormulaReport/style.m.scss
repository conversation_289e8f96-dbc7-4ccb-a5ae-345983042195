.tab-wrapper {
  margin-top: 40px;
  margin-bottom: 24px;
  .tabs {
    width: 100%;
    display: flex;
    align-items: center;
    .tab-panel {
      color: #999;
      font-feature-settings: 'clig' off, 'liga' off;
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 150% */
      letter-spacing: 0.4px;

      position: relative;
      margin-right: 32px;
      padding-bottom: 4px;
      cursor: pointer;

      .bottom-border {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: #333;

        transition: all 0.1s ease-in;
      }
      &:hover {
        color: #333;
        .bottom-border {
          width: 12px;
        }
      }
    }
    .not-allowed {
      cursor: default;
      &:hover {
        color: #999;
        .bottom-border {
          width: 0;
        }
      }
    }
    .active {
      color: #333;
      .bottom-border {
        width: 100%;
      }
      &:hover {
        color: #333;
        .bottom-border {
          width: 100%;
        }
      }
    }
  }
}

.abnormal {
  color: #d42f15;
  svg {
    path {
      fill: #d42f15;
    }
  }
}
