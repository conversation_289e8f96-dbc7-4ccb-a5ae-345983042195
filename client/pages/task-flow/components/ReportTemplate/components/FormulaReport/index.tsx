import React, { useCallback, useEffect, useState } from 'react';
import style from './style.m.scss';
import { Grid, Icon } from 'zent';
import './zent.scss';
import { uniq } from 'lodash';

import indicator from '../../localTemplateData/indicator.json';
import { formatDisplayTime } from '../../../../../../fns/time';
import IndicatorAnalysis from '../IndicatorAnalysis';
import EmptyData from '../EmptyData';

interface Props {
  // 在这里定义组件的props
  onExplainIndex: (index: string) => void;
  data: any;
  loading?: boolean;
}

const Tabs = ({ tabs, onChange, loading }) => {
  return (
    <div className={style.tabs}>
      {tabs.map(item => {
        return (
          <div
            key={item.key}
            className={`${style.tabPanel} ${item.active ? style.active : loading ? style.notAllowed : ''}`}
            onClick={() => onChange(item.key)}
          >
            {item.title}
            <div className={style.bottomBorder}></div>
          </div>
        );
      })}
    </div>
  );
};

const FormulaReport: React.FC<Props> = ({ onExplainIndex, data: formulaData, loading = false }) => {
  // const [indexTabs, setIndexTabs] = useState<any[]>([]);
  const [activeId, setActiveId] = useState('');
  const [columns, setColumns] = useState<any[]>([]);
  const [datasets, setDatasets] = useState<any[]>([]);
  const [tabs, setTabs] = useState<any[]>([]);

  const onTabChange = tab => {
    if (loading) return;
    const newTabs = tabs;
    newTabs.forEach(item => {
      item.active = item.key === tab;
    });
    onExplainIndex(tab);
  };

  const convertDataToColumns = formulaData => {
    const columns: any[] = [
      {
        title: '',
        name: 'colName',
        className: 'colName',
        width: 130,
        textAlign: 'right',
        // fixed: 'left',
      },
    ];
    const indexAnalysisList: any[] = [];

    const data = formulaData.indexAnalysisWay.indexFormula.displayFormula;
    const { waveResultData } = formulaData;
    data.forEach((item, index) => {
      columns.push({
        title: item.displayName,
        name: item.indexName,
        isIndex: item.index,
        className: item.index ? '' : 'symbol',
        width: item.index ? 130 : 30,
        textAlign: 'center',
      });
      if (item.index && item.indexName !== formulaData.indexAnalysisWay.indexFormula.indexName) {
        indexAnalysisList.push({
          title: item.displayName,
          key: item.indexName,
        });
      }
    });

    waveResultData
      ?.filter(item => item.abnormal)
      .forEach(item => {
        Object.keys(item.currentData).forEach(key => {
          columns.find(column => column.name === key).bodyRender = data => {
            return <span className={style.abnormal}>{data?.[key]}</span>;
          };
        });
      });
    // setIndexTabs(indexAnalysisList);
    return { columns, indexAnalysisList };
  };

  const convertFormulaDataToDatasets = formulaData => {
    const datasets: any[] = [];
    const {
      waveResultData,
      currentTimeRange,
      momTimeRange,
      analysisIndexData,
      indexAnalysisWay,
      indicatorDisplayTypeMap,
    } = formulaData;

    // 当天数据
    const currentData = {
      colName: uniq(
        [currentTimeRange.startDay, currentTimeRange.endDay].map(item =>
          formatDisplayTime(item, currentTimeRange.dateTypeCode),
        ),
      ).join('-'),
      [indexAnalysisWay.indexFormula.indexName]: analysisIndexData.currentValue
        ? analysisIndexData.currentValue.toFixed(2)
        : '-',
    };
    // 对比数据
    const momData = {
      /* colName: uniq(
        [momTimeRange.startDay, momTimeRange.endDay].map(item => formatDisplayTime(item, momTimeRange.dateTypeCode)),
      ).join('-'), */
      colName: '环比',
      [indexAnalysisWay.indexFormula.indexName]: analysisIndexData.momRate
        ? (analysisIndexData.momRate * 100).toFixed(2) + '%'
        : '-',
    };
    // 贡献度
    const contributionRateData = {
      colName: '贡献度',
      [indexAnalysisWay.indexFormula.indexName]: '-',
    };

    waveResultData?.forEach(item => {
      Object.keys(item.currentData).forEach(key => {
        // 根据不同情况返回当前值
        const current = () => {
          if (!item.currentData[key].currentValue) return '-';
          if (indicatorDisplayTypeMap && indicatorDisplayTypeMap[key] === 'MONEY') {
            return (item.currentData[key].currentValue / 100).toFixed(2);
          }
          if (indicatorDisplayTypeMap && indicatorDisplayTypeMap[key] === 'PERCENT') {
            return (item.currentData[key].currentValue * 100).toFixed(2) + '%';
          }
          return item.currentData[key].currentValue.toFixed(2);
        };
        // 根据不同情况返回对比值
        const mom = () => {
          if (!item.currentData[key].momValue) return '-';
          if (item.currentData[key]?.momRate > 0) {
            return (
              <div>
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M8.73488 7.3581C8.93374 7.62165 8.74573 7.99902 8.41558 7.99902L3.5893 7.99902C3.25773 7.99902 3.07012 7.61879 3.27185 7.35565L5.70422 4.18294C5.86511 3.97309 6.18171 3.97431 6.34097 4.18539L8.73488 7.3581Z"
                    fill="#333333"
                  />
                </svg>

                {(item.currentData[key].momRate * 100).toFixed(2) + '%'}
              </div>
            );
          }
          if (item.currentData[key]?.momRate < 0) {
            return (
              <div>
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M3.26502 4.64092C3.06616 4.37738 3.25417 4 3.58432 4H8.4106C8.74217 4 8.92978 4.38023 8.72805 4.64337L6.29568 7.81608C6.1348 8.02593 5.8182 8.02472 5.65893 7.81364L3.26502 4.64092Z"
                    fill="#333333"
                  />
                </svg>

                {(item.currentData[key].momRate * 100).toFixed(2) + '%'}
              </div>
            );
          }
        };

        currentData[key] = current();
        momData[key] = mom();
        contributionRateData[key] = item.contributionRate ? (item.contributionRate * 100).toFixed(2) + '%' : '-';
      });
    });
    datasets.push(currentData);
    datasets.push(momData);
    datasets.push(contributionRateData);
    return datasets;
  };
  useEffect(() => {
    const { columns, indexAnalysisList } = convertDataToColumns(formulaData);
    const datasets = convertFormulaDataToDatasets(formulaData);

    setColumns(columns);
    setDatasets(datasets);
    indexAnalysisList[0].active = true;
    setTabs(indexAnalysisList);
    onTabChange(indexAnalysisList[0].key);
  }, []);

  const onClickTableHead = e => {
    const { target } = e;
    const selectedIndex = columns.find(item => item.title === target.innerText);
  };

  const renderIndicatorAnalysis = () => {
    const {
      indexFormula: { displayFormula = [] },
    } = formulaData?.indexAnalysisWay;
    const indicatorList: any[] = displayFormula
      .filter(item => item.index)
      .map(item => {
        return {
          name: item.displayName,
          key: item.indexName,
          indicator: indicator[item.indexName],
        };
      });
    return <IndicatorAnalysis indicatorList={indicatorList} />;
  };

  return (
    <div onClick={onClickTableHead} className="formulaReport">
      {datasets?.length ? (
        <>
          {renderIndicatorAnalysis()}
          <Grid
            className={'custom-grid'}
            columns={columns}
            datasets={datasets}
            scroll={{ x: 550 }}
            rowClassName={(data, index) => `row-${index}`}
          />
        </>
      ) : (
        <EmptyData />
      )}
      <div className={style.tabWrapper}>
        <Tabs onChange={onTabChange} tabs={tabs} loading={loading} />
      </div>
    </div>
  );
};

export default FormulaReport;
