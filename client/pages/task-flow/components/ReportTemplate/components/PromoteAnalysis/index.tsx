import React from 'react';
import { groupBy, isNil } from 'lodash';
import { Grid, IGridColumn, ClampLines } from 'zent';
import Text from '../Text';
import { formats } from '../../../../../../fns/number';
import style from './style.m.scss';

interface Props {
  data: any;
  moduleId?: number | string;
  reportData?: any;
}

const LEVEL_LIST = ['优质', '潜力', '鸡肋', '低质'];

const handler = {
  '0': list => {
    const mapData = groupBy(list, 'psLevel');

    return LEVEL_LIST.map(level => ({
      children: mapData[level] || [],
      psLevel: level,
    })).filter(({ children = [] }) => children.length > 0);
  },
  others: list => list,
};

const columnMap: Record<string, IGridColumn[]> = {
  '0': [
    {
      title: '效果分层',
      name: 'psLevel',
      width: 100,
    },
    {
      title: '推广数量',
      name: 'psCount',
      width: 100,
      bodyRender: ({ children = [] }) => {
        return children.length;
      },
    },
    {
      title: '推广信息案例',
      name: 'psDetail',
      width: 200,
      bodyRender: ({ children = [] }) => {
        const text = children.map(item => item.psName).join('、');
        return <ClampLines text={text}></ClampLines>;
      },
    },
  ],
  others: [
    {
      title: '推广名称',
      name: 'pageName',
      width: 200,
      bodyRender: ({ psName }) => {
        return <ClampLines text={psName}></ClampLines>;
      },
    },
    {
      title: '渠道规模',
      name: 'psUvScore',
      bodyRender: ({ psUvScore }) => psUvScore.toFixed(2),
    },
    {
      title: '拉新能力',
      name: 'psAddNewUvScore',
      bodyRender: ({ psAddNewUvScore }) => psAddNewUvScore.toFixed(2),
    },
    {
      title: '转化质量',
      name: 'psPayUvRateScore',
      bodyRender: ({ psPayUvRateScore }) => psPayUvRateScore.toFixed(2),
    },
    {
      title: '整体收益',
      name: 'psPayAmtScore',
      bodyRender: ({ psPayAmtScore }) => psPayAmtScore.toFixed(2),
    },
  ],
};

const indicatorColumn: IGridColumn[] = [
  {
    title: '推广名称',
    name: 'psName',
    width: 150,
    fixed: 'left',
    bodyRender: ({ psName }) => <ClampLines text={psName}></ClampLines>,
  },
  {
    title: '访客数',
    name: 'uv',
    width: 150,
  },
  {
    title: '新访客数',
    name: 'newUv',
    width: 150,
  },
  {
    title: '新客占比',
    name: 'newUvProportion',
    bodyRender: ({ newUvProportion }) => formats.percent(newUvProportion),
    width: 150,
  },
  {
    title: '新成交客户数',
    name: 'newCustomerPaidUv',
    width: 150,
  },
  {
    title: '新成交客户占比',
    name: 'newPayUvProportion',
    bodyRender: ({ newPayUvProportion }) => formats.percent(newPayUvProportion),
    width: 150,
  },
  {
    title: '新客成交转化',
    name: 'newPayUvRate',
    width: 150,
    bodyRender: ({ newPayUvRate }) => formats.percent(newPayUvRate),
  },
  {
    title: '转化支付人数',
    name: 'toPayUv',
    width: 150,
  },
  {
    title: '访问支付转化率',
    name: 'toPayRate',
    bodyRender: ({ toPayRate }) => formats.percent(toPayRate),
    width: 150,
  },
  {
    title: '转化支付金额（元）',
    name: 'toPayAmount',
    bodyRender: ({ toPayAmount }) => formats.money(toPayAmount) + '元',
    width: 150,
  },
  // {
  //   // todo
  //   title: '连带支付金额',
  //   name: 'toOrderAmount',
  //   bodyRender: ({ toOrderAmount }) => formats.money(toOrderAmount) + '元',
  // },
  // {
  //   title: '总支付金额',
  //   name: 'toPayAmount',
  //   bodyRender: ({ toPayAmount }) => formats.money(toPayAmount) + '元',
  // },
];

function PromoteAnalysis({ data, moduleId, reportData }: Props) {
  const { data: list = [] } = data[0].value || {};

  // @ts-ignore
  const fn = handler[moduleId] || handler.others;

  const result = fn?.(list);
  // @ts-ignore
  const columns = columnMap[moduleId] || columnMap.others;

  if (moduleId === 999) {
    const { explain = '' } = reportData || {};
    return <Text text={explain}></Text>;
  }

  if (!result || isNil(moduleId)) return <></>;

  return (
    <div>
      <div className={style.title}>推广分层数据</div>
      <Grid datasets={result} columns={columns}></Grid>
      {moduleId !== 0 && moduleId !== 999 ? (
        <>
          <div className={style.title}>数据表现</div>
          <Grid
            datasets={list}
            columns={indicatorColumn}
            scroll={{
              x: 1500,
            }}
          ></Grid>
        </>
      ) : null}
    </div>
  );
}

export default PromoteAnalysis;
