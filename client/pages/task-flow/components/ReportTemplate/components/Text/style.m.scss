.text-black {
  padding: 12px;
  border-radius: 4px;
  background: #ffffff;
  max-width: calc(100vw - 76px);

  .message-text-content {
    text-align: justify;
  }

  &.inversion {
    background: #cddbf4;
  }
}

html.dark {
  .message-reply {
    .whitespace-pre-wrap {
      font-size: 14px;
      white-space: pre-wrap;
      color: var(--n-text-color);
    }
  }

  .highlight pre,
  pre {
    background-color: #282c34;
  }
}

.tag-input-model__textarea {
  width: 100%;
  min-height: 56px;
  background: #ffffff;
  border-radius: 8px;
  border: none;
  outline: none;
  resize: none;

  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #333333;
  word-break: break-all;

  &.absolute {
    position: absolute;
    top: 0;
    height: 100%;
    left: 0;
  }
}

.message-hidden {
  opacity: 0;
  visibility: hidden;
}

.message-content {
  position: relative;
  height: 100%;
}

.system_hint .markdown-body p {
  color: #ccc !important;
  text-align: center;
  font-size: 12px !important;
  font-family: PingFang SC;
  line-height: 20px;
  letter-spacing: 0.4px;
}

.shortcut {
  color: #155bd4;
  cursor: pointer;
  border-bottom: 1px dashed #333;
}

.shortcut-pop {
  cursor: pointer;

}