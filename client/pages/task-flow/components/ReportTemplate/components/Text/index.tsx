import React, { useState, useEffect, useMemo } from 'react';
import style from './style.m.scss';
import AtomText from '../../../../../../atom-components/Text';
import get from 'lodash/get';
import { Pop } from 'zent';
import { toggleJarvisAssistants } from '../../../../../../fns/events';

interface ITextProps {
  inversion?: boolean;
  error?: boolean;
  text: string;
  loading?: boolean;
  asRawText?: boolean;
  isEdit?: boolean;
  sendText?: (text: string) => void;
  onDataChanged?: (data: any) => void;
}

const Text = ({ text = '', isEdit = false, onDataChanged }: ITextProps) => {
  const [templateText, setTemplateText] = useState(text);

  useEffect(() => {
    setTemplateText(text);
  }, [text]);

  const handleTemplateTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    onDataChanged && onDataChanged(event.target.value);
  };

  const customerComponents = useMemo(() => {
    if (isEdit) {
      return undefined;
    }
    return (defaultComponents: any) => {
      return {
        a(props) {
          // 其他a标签 default
          return defaultComponents.a(props);
        },
        span(props: any) {
          const shortcut = get(props, 'data-shortcut');
          if (shortcut) {
            const title = `如何设置${shortcut}`;
            return (
              <Pop
                trigger="hover"
                position="auto-top-center"
                content={
                  <span
                    className={style.shortcutPop}
                    onClick={() => {
                      toggleJarvisAssistants({
                        visible: true,
                        actions: [
                          {
                            type: 'set_input_content',
                            data: title,
                          },
                        ],
                      });
                    }}
                  >
                    {title}
                  </span>
                }
              >
                <span className={style.shortcut}>{shortcut}</span>
              </Pop>
            );
          }
          return <span>{props.children}</span>;
        },
      };
    };
  }, [isEdit]);

  return (
    <div className={style.messageContent}>
      <div className={` ${isEdit ? style.messageHidden : ''}`}>
        <AtomText
          className={`${style.markdownBody} ${style.messageTextContent}`}
          data={templateText}
          customerComponents={customerComponents}
        />
      </div>
      {isEdit && text && (
        <textarea
          className={`${style.tagInputModelTextarea} ${style.absolute}`}
          value={templateText}
          onChange={handleTemplateTextChange}
        />
      )}
    </div>
  );
};

export default Text;
