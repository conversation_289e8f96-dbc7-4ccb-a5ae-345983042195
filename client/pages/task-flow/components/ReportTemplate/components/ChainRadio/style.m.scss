.horizontal {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.chain-ratio {
  display: flex;
  margin-top: 4px;
  font-size: 12px;
  line-height: 20px;
  /* identical to box height, or 167% */

  color: #999999;
  gap: 4px;

  &-content {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .chain-value {
    font-weight: 500;
    min-width: 45px;
    font-family: Avenir;
  }

  .zenticon {
    margin: 0 3px;
  }

  .zenticon-arrow-up {
    color: #e70800;
  }

  .zenticon-arrow-down {
    color: #45a110;
  }

  .empty-value {
    margin-left: 6px;
  }
  .rise {
    color: #d42f15;
  }
  .fall {
    color: #45a110;
  }
  .chain-ratio-icon {
    width: 12px;
    height: 12px;
    position: relative;
    top: -1px;
  }
}
