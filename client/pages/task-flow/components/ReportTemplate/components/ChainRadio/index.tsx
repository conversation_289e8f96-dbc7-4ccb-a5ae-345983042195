import React from 'react';
import { ArrowDownIcon, ArrowUpIcon } from '../../../../../../svg';
import isNil from 'lodash/isNil';
import { formatRatioPercent } from '../../../../../../fns/report';
import styles from './style.m.scss';

interface ChainRatio {
  text: string;
  value: number;
}

interface Props {
  chainRatioList: ChainRatio[];
  display?: string;
}

const ChainRadio: React.FC<Props> = ({ chainRatioList }) => {
  const isValueNil = (value: any) => {
    return value === null || value === undefined;
  };

  return (
    <div className={styles.display}>
      {chainRatioList.map((chainRatio, index) => (
        <div className={styles['chain-ratio']} key={index}>
          {chainRatio?.text}
          <span
            className={`${styles['chain-ratio-content']} ${
              chainRatio?.value > 0 ? styles.rise : chainRatio?.value < 0 ? styles.fall : ''
            }`}
          >
            <span className={styles['chain-ratio-icon']}>
              {chainRatio?.value > 0 ? <ArrowUpIcon /> : chainRatio?.value < 0 ? <ArrowDownIcon /> : null}
            </span>
            <span>
              {isValueNil(chainRatio?.value) ? (
                '-'
              ) : (
                <span
                  className={`${styles['chain-value']} ${styles['avenir-number']}`}
                  dangerouslySetInnerHTML={{ __html: formatRatioPercent(chainRatio?.value, { withUnitPadding: true }) }}
                />
              )}
            </span>
          </span>
        </div>
      ))}
    </div>
  );
};

export default ChainRadio;
