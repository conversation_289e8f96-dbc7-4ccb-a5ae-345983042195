// style.m.scss

// 设置表格容器的样式，确保支持水平滚动
.table-container {
  //   width: 400px;
  //   overflow-x: auto;
  //   padding: 16px;
  //   background-color: #f9f9f9;
}

// 设置表格内的图片样式
.table-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
}

// 设置单元格的样式
.grid-cell {
  padding: 8px;
  text-align: center;
}

// 设置表头的样式
.grid-header {
  font-weight: bold;
  background-color: #e0e0e0;
  text-align: center;
}

.sku-name-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sku-no {
  color: #999;
  font-size: 11px;
}

.custom-time-tags {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.interval-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.trend-wrapper {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  // 超链接样式
  color: #007bff;
  text-decoration: none;

  svg {
    margin-right: 5px;
  }
}

.custom-time-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}
