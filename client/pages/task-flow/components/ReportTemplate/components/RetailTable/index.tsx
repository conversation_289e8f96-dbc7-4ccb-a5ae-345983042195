import React, { useEffect, useImperativeHandle, useMemo, useState, forwardRef } from 'react';
import {
  Button,
  Grid,
  ISelectItem,
  Notify,
  Pagination,
  Pop,
  Radio,
  RadioButton,
  Select,
  Tag,
  TimePicker,
  TimeRangePicker,
} from 'zent';
import style from './style.m.scss';
import { exportGoodsSalesReport, getRetailReportTableData } from '../../../../api/indexV2';
import { Line } from '@ant-design/plots';

interface HourSegment {
  hourSegment: string;
  goodsNetSalesAmtStr: string;
}

interface DataItem {
  photoUrl: string;
  skuName: string;
  skuNo: string;
  skuDesc: string;
  category1Name: string;
  brand1Name: string;
  salesKdtIdsStr: string;
  orderSourcesStr: string;
  hourSegmentList: HourSegment[];
}
interface TrendChartProps {
  data: DataItem;
}

const TrendChart: React.FC<TrendChartProps> = React.memo(({ data }) => {
  const chartData = useMemo(
    () =>
      data.hourSegmentList.map(segment => ({
        time: segment.hourSegment.split('-')[0],
        value: parseFloat(segment.goodsNetSalesAmtStr),
      })),
    [data.hourSegmentList],
  );

  const config = {
    data: chartData,
    xField: 'time',
    yField: 'value',
    smooth: true,
    height: 200,
    width: 400,
    xAxis: {
      label: {
        formatter: v => `${v}`,
      },
    },
    yAxis: {
      label: {
        formatter: v => `￥${v}`,
      },
    },
    tooltip: {
      formatter: datum => {
        return { name: '销售额', value: `￥${datum.value.toFixed(2)}` };
      },
    },
  };

  return (
    <div style={{ padding: '10px' }}>
      <h3>{data.skuName} 销售趋势</h3>
      <Line {...config} />
    </div>
  );
});

const RetailTable = forwardRef<any, any>(({ data: originData, currentTimeRange }, ref) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataSource, setDataSource] = useState<DataItem[]>([]);
  const [interval, setInterval] = useState<any>({
    key: 1,
    value: 1,
    text: '1小时',
  }); // 默认间隔时间为'1'小时，使用字符串
  const [pageSize, setPageSize] = useState(20); // 每页显示的条数
  const [tableLoading, setTableLoading] = useState(false);

  const [customTimeRange, setCustomTimeRange] = useState<[string, string]>(['', '']);
  const [customTimeRanges, setCustomTimeRanges] = useState<Array<{ start: string; end: string }>>([]);

  const [customStartTime, setCustomStartTime] = useState<any>('');
  const [customEndTime, setCustomEndTime] = useState<any>('');

  useImperativeHandle(ref, () => ({
    exportExcel: exportExcel,
  }));

  const result = useMemo(() => {
    const _result = {};
    (originData || []).forEach(({ key, ...res }) => {
      _result[key] = res;
    });

    return _result;
  }, [originData]);

  useEffect(() => {
    // @ts-ignore
    const { items, paginator } = result?.retailTable?.value.data.data;
    setDataSource(items);
    setCurrentPage(paginator.page || 1);
    setTotal(paginator.totalCount || 0);
    setPageSize(paginator.pageSize || 20);
  }, []);

  useEffect(() => {
    if (interval.value !== 'custom') {
      fetchTableData();
    }
  }, [interval]);

  useEffect(() => {
    if (interval.value === 'custom') {
      fetchTableData();
    }
  }, [customTimeRanges]);

  const isTimeInSelectedRanges = (time: number) => {
    return customTimeRanges.some(range => {
      const start = parseInt(range.start, 10);
      const end = parseInt(range.end, 10);
      return time > start && time < end;
    });
  };

  const handleIntervalChange = e => {
    setInterval(e);
  };

  const handleConfirmCustomTimeRange = () => {
    if (!customStartTime || !customEndTime) {
      Notify.error('请选择开始时间和结束时间');
      return;
    }

    if (customStartTime && customEndTime) {
      console.log(customStartTime, customEndTime);
      const newStart = parseInt(customStartTime.value, 10);
      const newEnd = parseInt(customEndTime.value, 10);

      if (isNaN(newStart) || isNaN(newEnd)) {
        Notify.error('时间格式不正确，请使用有效的时间值');
        return;
      }

      // 检查开始时间是否等于结束时间
      if (newStart === newEnd) {
        Notify.error('开始时间不能与结束时间相同');
        return;
      }

      if (newStart > newEnd) {
        Notify.error('结束时间必须大于开始时间');
        return;
      }

      // 检查是否有重叠的时间段
      const overlappingRange = customTimeRanges.find(range => {
        const rangeStart = parseInt(range.start, 10);
        const rangeEnd = parseInt(range.end, 10);

        // 处理跨越午夜的情况
        const isRangeOvernight = rangeStart > rangeEnd;
        const isNewRangeOvernight = newStart > newEnd;

        if (isRangeOvernight && isNewRangeOvernight) {
          return true; // 必定重叠
        } else if (isRangeOvernight) {
          return newStart <= rangeEnd || newEnd >= rangeStart;
        } else if (isNewRangeOvernight) {
          return rangeStart <= newEnd || rangeEnd >= newStart;
        } else {
          return (
            (newStart >= rangeStart && newStart < rangeEnd) ||
            (newEnd > rangeStart && newEnd <= rangeEnd) ||
            (newStart <= rangeStart && newEnd >= rangeEnd) ||
            (newStart >= rangeStart && newEnd <= rangeEnd)
          );
        }
      });

      if (overlappingRange) {
        Notify.error(`添加时间失败，与已有时间段 ${overlappingRange.start} - ${overlappingRange.end} 重叠`);
      } else {
        setCustomTimeRanges(prev => [
          ...prev,
          {
            start: customStartTime.value,
            end: customEndTime.value,
          },
        ]);
        setCustomStartTime('');
        setCustomEndTime('');
        Notify.success('时间段添加成功');
      }
    }
  };

  const removeCustomTimeRange = (index: number) => {
    setCustomTimeRanges(prev => prev.filter((_, i) => i !== index));
  };

  const columns = useMemo(() => {
    const baseColumns = [
      {
        title: '销售商品',
        name: 'photoUrl',
        width: 200,
        fixed: 'left',
        bodyRender: (data: DataItem) => (
          <div className={style.skuNameWrapper}>
            <img
              src={
                JSON.parse(data.photoUrl || '[]')[0]?.url ||
                'https://img01.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png'
              }
              alt={data.skuName}
              style={{ width: '50px', height: '50px' }}
            />
            <div>
              <div>{data.skuName}</div>
              <p className={style.skuNo}>{data.skuDesc}</p>
              <p className={style.skuNo}>{data.skuNo}</p>
            </div>
          </div>
        ),
      },
      {
        title: '商品条码',
        name: 'spuNo',
        width: 120,
      },
      {
        title: '商品分类',
        name: 'category1Name',
        width: 150,
      },
      {
        title: '商品品牌',
        name: 'brand1Name',
        width: 100,
      },
      {
        title: '销售渠道',
        name: 'orderSourcesStr',
        width: 120,
      },
      {
        title: '销售单元',
        name: 'salesKdtIdsStr',
        width: 200,
      },
    ];

    const timeColumns = (dataSource.length > 0 ? dataSource[0].hourSegmentList : []).map(segment => ({
      title: segment.hourSegment,
      name: `hourSegment_${segment.hourSegment}`,
      width: 130,
      bodyRender: (rowData: DataItem) => {
        const segmentData = rowData.hourSegmentList.find(s => s.hourSegment === segment.hourSegment);
        return segmentData ? segmentData.goodsNetSalesAmtStr : '0.00';
      },
    }));

    const trendColumn = {
      title: '趋势图',
      name: 'trend',
      width: 80,
      bodyRender: (data: DataItem) => (
        <div className={style['trend-wrapper']}>
          <Pop content={<TrendChart data={data} />} trigger="hover">
            曲线图
          </Pop>
        </div>
      ),
    };

    return [...baseColumns, ...timeColumns, trendColumn];
  }, [dataSource]);

  const getCommonParams = (page = currentPage) => {
    // @ts-ignore
    const { requestParams = {} } = result?.retailTable?.value;
    const { startDay, endDay } = currentTimeRange || {};
    let params: any = {
      ...requestParams,
      salesKdtIds: JSON.stringify(requestParams.salesKdtIds || []),
      orderSources: JSON.stringify(requestParams.orderSources || []),

      pageNo: page,
      pageSize: 20,
      endDate: endDay + '',
      startDate: startDay + '',
      retailSource: 'aigc-app',
      hourInterval: interval.value,
    };
    if (interval.value === 'custom') {
      params.hourInterval = '1';
      const timeRanges = customTimeRanges.map(range => ({ startTime: +range.start, endTime: +range.end }));
      params.customHourList = JSON.stringify(timeRanges);
    }
    return params;
  };

  const businessTimeType = useMemo(() => {
    // @ts-ignore
    const { requestParams = {} } = result?.retailTable?.value;
    return requestParams.businessTimeType || 0;
  }, [result]);

  const handlePageChange = page => {
    setCurrentPage(page.current);
    // 在这里可以添加获取数据的逻辑
    fetchTableData(page.current);
  };

  const fetchTableData = (pageNo = currentPage) => {
    setTableLoading(true);
    setDataSource([]);
    getRetailReportTableData(getCommonParams(pageNo)).then(res => {
      const { items, paginator } = res;

      setDataSource(items);
      setCurrentPage(paginator.page || 1);
      setTotal(paginator.totalCount || 0);
      setPageSize(paginator.pageSize || 20);

      setTableLoading(false);
    });
  };

  const exportExcel = () => {
    // 导出excel
    return exportGoodsSalesReport(getCommonParams());
  };

  const handleCustomStartTimeChange = value => {
    setCustomStartTime(value);
  };

  const handleCustomEndTimeChange = value => {
    setCustomEndTime(value);
  };

  // 生成小时选项
  const hourOptions = Array.from({ length: 25 }, (_, i) => ({
    key: i,
    value: i, // 使用数字作为 value
    text: `${i.toString().padStart(2, '0')}:00`,
  }));

  const timeOptions = [
    {
      key: 1,
      value: 1,
      text: '1小时',
    },
    {
      key: 3,
      value: 3,
      text: '3小时',
    },
    {
      key: 6,
      value: 6,
      text: '6小时',
    },
    {
      key: 'custom',
      value: 'custom',
      text: '自定义',
    },
  ];

  const renderBusinessTime = () => {
    console.log('result', result);
    // @ts-ignore
    const { businessStartTime, businessEndTime } = result?.retailTable?.value.data;
    if (!businessStartTime || !businessEndTime) {
      return '';
    }
    return `查询时间范围：${businessStartTime} ~ ${businessEndTime}`;
  };

  return (
    <div className={style['table-container']}>
      {/* 展示营业日营业时间范围 */}
      {businessTimeType === 1 && <div style={{ marginBottom: '8px' }}>{renderBusinessTime()}</div>}

      <div className={style['interval-selector']}>
        <label>间隔时间: </label>
        {/* @ts-ignore */}
        <Select
          width={120}
          value={interval}
          options={timeOptions}
          onChange={handleIntervalChange}
          className="w-32"
        ></Select>
        {interval.value === 'custom' && (
          <>
            <div className={style['custom-time-selector']}>
              <Select
                width={120}
                value={customStartTime}
                onChange={handleCustomStartTimeChange}
                // @ts-ignore
                options={hourOptions}
                placeholder="开始时间"
              />
              <Select
                width={120}
                value={customEndTime}
                onChange={handleCustomEndTimeChange}
                // @ts-ignore
                options={hourOptions}
                placeholder="结束时间"
              />
              <Button onClick={handleConfirmCustomTimeRange} type="primary">
                添加时间
              </Button>
            </div>
          </>
        )}
      </div>

      <div className={style['custom-time-tags']}>
        {customTimeRanges.map((range, index) => (
          <Tag key={index} theme="blue" closable onClose={() => removeCustomTimeRange(index)}>
            {range.start + ':00'}-{range.end + ':00'}
          </Tag>
        ))}
      </div>

      <Grid
        columns={columns}
        loading={tableLoading}
        datasets={dataSource}
        rowKey="skuNo"
        scroll={{ x: 590, y: window.innerHeight > 800 ? 400 : 280 }}
        // className={style['grid-cell']}
      />
      <Pagination
        current={currentPage}
        totalItem={total}
        showQuickJumper={false}
        onChange={handlePageChange}
        pageSize={pageSize}
      />
    </div>
  );
});

export default RetailTable;
