import React from 'react';
import LimitStringWithPop from '../../../../../../components/limit-string-pop';
import positionHandler from '../../../../../../fns/position';
import { useConfig } from '../../../../../../hooks/use-config';

const ClampText = ({ text, width = 180 }: any) => {
  const { width: pageWidth } = useConfig();

  return (
    <LimitStringWithPop
      text={text}
      width={width}
      popProps={{
        className: 'rc-indicator-shortcut-tooltip',
        position: positionHandler({ width: pageWidth }),
      }}
    />
  );
};

export default ClampText;
