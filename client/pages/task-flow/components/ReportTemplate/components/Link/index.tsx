import React from 'react';

import { checkOpenUrlIsBlank } from '../../../../../../fns/link';
import { logger } from '../../../../../../fns/logger';

const Link = props => {
  const { href, children, isBlank, onClick: propsOnClick } = props;
  // 如果强行指定isBlank
  const target = isBlank || checkOpenUrlIsBlank(href) ? '_blank' : '_self';

  const onClick = e => {
    propsOnClick && propsOnClick(e);
    if (target === '_self') {
      logger &&
        logger.log({
          et: 'click', // 事件类型
          ei: 'micro_app_navigate', // 事件标识
          en: '微前端跳转', // 事件名称
          params: {
            url: href,
            component: 'jarvis_assistant',
          }, // 事件参数
        });
    }
  };

  return (
    <a {...props} target={target} onClick={onClick}>
      {children}
    </a>
  );
};

export default Link;
