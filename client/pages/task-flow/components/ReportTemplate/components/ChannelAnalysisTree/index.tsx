import React, { useEffect, useRef, useState } from 'react';
import { DecompositionTreeGraph } from '@ant-design/graphs';
// import style from '../../../../../../components/Preview/style.m.scss';
import IndicatorAnalysis from '../IndicatorAnalysis';
import indicator from '../../localTemplateData/indicator.json';
import { formatMoney } from '../../../../../../fns/chart-format';

interface Props {
  // props 类型定义
  data?: any;
  onClickIndex?: ({
    indexAnalysis,
    path,
    timeParam,
    dateType,
  }: {
    indexAnalysis: string;
    path: string[];
    timeParam: { startDay: number; endDay: number };
    dateType: number;
  }) => void;
  showComplete?: boolean;
}

interface TreeNode {
  id: string;
  value: {
    title: string;
    items: { text: string; value?: string; icon?: string }[];
  };
  depth?: number;
  children?: TreeNode[];
}

const ChannelAnalysisTree: React.FC<Props> = ({ data: originData, onClickIndex, showComplete = false }) => {
  const [config, setConfig] = useState<any>(null);
  const [chartData, setChartData] = useState<any[]>([]);

  const treeGraphRef = useRef<HTMLDivElement>(null);
  const myGraph = useRef(null);

  /* function truncateTree(node, depth) {
    if (depth <= 0) {
      const newNode = cloneDeep(node);
      delete newNode.children;
      return newNode;
    }

    const children = node.children?.map(child => truncateTree(child, depth - 1));
    return {
      ...node,
      children,
    };
  } */
  const result = {};
  (originData || []).forEach(({ key, ...res }) => {
    result[key] = { ...res, key };
  });

  const formatDiffValue = value => {
    if (!value || value === 0) {
      return '0';
    }
    return `${formatMoney(value)}`;
  };

  function convertToTree(data, id) {
    // const formatMoney = data.currentGmv.currentValue / 100;
    const node = {
      id,
      value: {
        title: data.channelChName || '-',
        name: data.channelName,
        percent: data.waveResultData ? data.waveResultData.proportion : 0,
        abnormal: data.waveResultData ? data.waveResultData.abnormal : false,
        items: [
          {
            text: `变化贡献度：${
              data.waveResultData?.contributionRate
                ? `${(data.waveResultData.contributionRate * 100).toFixed(2)}%`
                : '-'
            }       变化量：${formatDiffValue(
              (data.currentGmv?.currentValue || 0) - (data.currentGmv?.momValue || 0),
            )}`,
          },
          {
            text: `增幅率：${data.currentGmv?.momRate ? `${(data.currentGmv?.momRate * 100).toFixed(2)}%` : '-'}`,
            /* value: `变化贡献度：${
              data.waveResultData?.contributionRate
                ? `${(data.waveResultData.contributionRate * 100).toFixed(2)}%`
                : '-'
            }`, */
          },
          {
            value: `${formatMoney(data.currentGmv.currentValue || 0)}                   ${
              id.split('-').length === 1
                ? '100%'
                : data.waveResultData.proportion
                ? `${(data.waveResultData.proportion * 100).toFixed(2)}%`
                : '-'
            }`,
          },
          /* {
            value: `${
              formatMoney >= 1000000
                ? `${(formatMoney / 1000000).toFixed(2)}百万`
                : formatMoney >= 10000
                ? `${(formatMoney / 10000).toFixed(2)}万`
                : `${formatMoney}元`
            }/${
              id.split('-').length === 1
                ? '100%'
                : data.waveResultData.proportion
                ? `${(data.waveResultData.proportion * 100).toFixed(2)}%`
                : '-'
            }`,
          }, */
          /*
           */
          /* {
            text: '占比',
            */
        ],
      },
      depth: id.split('-').length,
    };

    if (data.subChannelResults) {
      node['children'] = data.subChannelResults.map((subData, index) => convertToTree(subData, `${id}-${index}`));
    } else if (data.children) {
      node['children'] = data.children.map((childData, index) => convertToTree(childData, `${id}-${index}`));
    }

    return node;
  }

  /*  const getChildren = node => {
    const { id } = node;
    const targetNode = findNode(id);
    return targetNode?.children || [];
  }; */

  const findNode = (id, chartData) => {
    const dataPath = [];
    const search = node => {
      // @ts-ignore
      dataPath.push({ name: node.value.name, title: node.value.title });
      if (node.id === id) {
        return node;
      }
      if (node.children) {
        for (const child of node.children) {
          const result = search(child);
          if (result) {
            return result;
          }
          dataPath.pop();
        }
      }
      return undefined;
    };
    const result = search(chartData);
    return {
      node: result,
      dataPath,
    };
  };

  useEffect(() => {
    if (!treeGraphRef.current) return;
    // 获取id为tree-graph-wrapper的div的width
    setTimeout(() => {
      const pages = document.querySelectorAll(`.page`);
      const width = pages[pages.length - 1]?.clientWidth;
      // const { data: testData } = fluctuationSourceReport;
      if (!originData) return;
      // @ts-ignore
      const { data: _data } = result?.ChannelAnalysisTree?.value;
      if (!_data) return;
      const data = convertToTree(_data, '0');
      // @ts-ignore
      setChartData(data);

      setConfig({
        data,
        level: showComplete ? 10 : 3,
        width: showComplete ? 616 : width - 48,
        height: showComplete ? 650 : (width - 48) * 1.2,
        // 控制整体布局，主要是节点之间的距离 https://g6.antv.antgroup.com/manual/middle/layout/tree-graph-layout#%E9%85%8D%E7%BD%AE%E6%A0%91%E5%9B%BE%E5%B8%83%E5%B1%80
        layout: {
          // type: 'compactBox',
          // rankSep: 300,
          // nodeSep: 240,
        },
        markerCfg: cfg => {
          const { depth, id } = cfg;
          const needShow = findNode(id, data).node.children?.length > 0;
          return {
            show: needShow,
            collapsed: depth >= 2,
            style: {
              fill: '#5E697D',
              stroke: '#f7f7f7',
              padding: 4,
            },
          };
        },
        nodeCfg: {
          size: [186, 23],
          percent: {
            position: 'bottom',
            size: 4,
            style: arg => {
              return {
                radius: arg.value.percent < 0.01 ? 0 : [0, 0, 4, 4],
                padding: 10,
                fill: arg.value.abnormal ? '#F24343' : '#155bd4',
                style: {
                  padding: [10, 10, 10, 10],
                },
              };
            },
            // 没用，找不到让外边框修改样式的方法
            /* containerStyle: arg => {
              return {
                style: {
                  radius: [4, 4, 0, 0],
                },
                radius: [4, 4, 0, 0],
              };
            }, */
          },
          title: {
            containerStyle: arg => {
              return {
                fill: 'transparent',
                radius: [8, 8, 0, 0],
                innerHeight: 40,
                outerHeight: 40,
                height: 40,
                cursor: 'pointer',
              };
            },
            style: arg => {
              return {
                fill: '#333',
                fontSize: 12,
                cursor: 'pointer',
                x: 14,
                y: 10,
                fontWeight: 500,
                height: 40,
                lineHeight: 40,
                padding: [12, 0, 12, 0],
              };
            },
          },
          items: {
            style: (cfg, group, type) => {
              const styles = {
                icon: {
                  width: 10,
                  height: 10,
                },
                value: {
                  fill: cfg.value.abnormal ? '#333' : '#333',
                  fontSize: 12,
                  // x: 110,
                  // x: 120,
                  x: 14,

                  // y: 12,
                  fontFamily: 'Avenir',
                  paddingTop: 1000,
                  marginTop: 30,
                  cursor: 'pointer',
                },
                text: {
                  fill: cfg.value.abnormal ? '#333' : '#333',
                  fontSize: 8,
                  x: 14,
                  // y: 42,
                  fontFamily: 'Avenir',
                  paddingTop: 30,
                  marginTop: 30,
                  cursor: 'pointer',
                },
              };
              return styles[type];
            },
          },
          nodeStateStyles: {
            hover: {
              stroke: '#155bd4',
              lineWidth: 2,
            },
          },
          style: arg => {
            return {
              fill: '#fff',
              radius: 8,
              stroke: 'transparent',
              cursor: 'pointer',
            };
          },
        },
        edgeCfg: {
          endArrow: {
            show: false,
          },
          edgeStateStyles: {
            hover: {
              stroke: '#155bd4',
              lineWidth: 2,
            },
          },
          style: (item, graph) => {
            /**
             * graph.findById(item.target).getModel()
             * item.source: 获取 source 数据
             * item.target: 获取 target 数据
             */
            // console.log(graph.findById(item.source).getModel());
            return {
              stroke: '#5E697D',
              size: 2,
            };
          },
        },
        behaviors: [
          'drag-canvas',
          {
            type: 'zoom-canvas',
            sensitivity: 0.4,
            // minZoom: 0.5,
          },
        ],
        onReady: graph => {
          // graph.zoom(0.5, { x: 100, y: 100 });
          graph.on('node:click', evt => {
            evt.originalEvent.stopPropagation();
            // 这一段是为了获取节点的点击位置，如果点击位置太靠右，则识别为点击了右侧的icon，不触发点击事件
            const { x, y } = evt;
            const nodes = graph.getNodes();
            const clickedNode = nodes.find(node => {
              const bbox = node.getBBox();
              // 核心就在这个 - 10，这个10是右侧icon的宽度
              return x >= bbox.minX && x <= bbox.maxX - 10 && y >= bbox.minY && y <= bbox.maxY;
            });
            if (!clickedNode) return;

            const {
              item: {
                _cfg: { id },
              },
            } = evt;
            const {
              currentTimeRange: { startDay, endDay, dateTypeCode },
              // @ts-ignore
            } = result.ChannelAnalysisTree.value.data;
            const { node, dataPath } = findNode(id, data);
            onClickIndex &&
              onClickIndex({
                indexAnalysis: node.value.name,
                path: dataPath,
                timeParam: { startDay, endDay },
                dateType: dateTypeCode as number,
              });
          });
          myGraph.current = graph;
        },
      });
      // 这里取300 是为了获取到正确的外层width，外层width是通过flex-grow撑开的，有个动画过程，在300ms后可以取到相对正常的值，不然取到的太小
    }, 300);
  }, []);

  const renderIndicatorAnalysis = () => {
    const indicatorList: any[] = [
      {
        name: 'GMV',
        key: 'gmv',
        indicator: indicator['self_sale_paid_order_amt'],
      },
      {
        // 变化贡献度
        name: '变化贡献度',
        key: 'contributionRate',
        indicator: indicator['contributionRate'],
      },
      {
        // 变化量
        name: '变化量',
        key: 'diffValue',
        indicator: indicator['diffValue'],
      },
      {
        // 增幅率
        name: '增幅率',
        key: 'momRate',
        indicator: indicator['momRate'],
      },
    ];

    return <IndicatorAnalysis indicatorList={indicatorList} />;
  };

  return (
    // 组件 JSX
    <>
      {renderIndicatorAnalysis()}
      <div ref={treeGraphRef} id="treeGraph" style={{ background: '#f7f7f7' }}>
        {/* <div
        onClick={() => {
          // @ts-ignore
          // myGraph.zoom();
          // myGraph.zoomTo(1, { x: 100, y: 100 });
          myGraph.current.fitView(0);
        }}
      >
        点我还原
      </div> */}
        {config && <DecompositionTreeGraph style={{ background: '#f7f7f7' }} {...config} />}
      </div>
    </>
  );
};

export default ChannelAnalysisTree;
