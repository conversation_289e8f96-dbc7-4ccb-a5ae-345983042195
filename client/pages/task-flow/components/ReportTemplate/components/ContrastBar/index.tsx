import React from 'react';
import style from './style.m.scss';
import indicator from '../../localTemplateData/indicator.json';
import IndicatorAnalysis from '../IndicatorAnalysis';
import EmptyData from '../EmptyData';

const colorList = ['#6492F6', '#5DD3F5'];

const ContrastBar = ({ data }) => {
  /* const mockData = {
    currentTimeRange: {
      dateType: 'NATURAL_DAY',
      dateTypeCode: 1,
      endDay: 20230807,
      startDay: 20230807,
    },
    drillDimension: ['is_new_payment_customer'],

    indexAnalysisWay: {
      analysisIndex: 'UV',
      analysisType: 'ADD',
      queryAddDimensions: ['IS_NEW_PAYMENT_CUSTOMER'],
      queryIndexs: ['UV'],
    },
    momTimeRange: {
      dateType: 'NATURAL_DAY',
      dateTypeCode: 1,
      endDay: 20230806,
      startDay: 20230806,
    },

    waveResultData: [
      {
        abnormal: true,
        contribution: 24,
        contributionRate: 0.5217,
        currentData: {
          uv: {
            chName: '49',
            currentValue: 49,
            dimensionColumn: false,
            momRate: 0.96,
            momValue: 25,
          },
          is_new_payment_customer: {
            chName: '非客户',
            currentValue: -1,
            dimensionColumn: true,
          },
        },
        proportion: 0.6621,
      },
      {
        abnormal: true,
        contribution: 22,
        contributionRate: 0.4782,
        currentData: {
          uv: {
            chName: '24',
            currentValue: 24,
            dimensionColumn: false,
            momRate: 11,
            momValue: 2,
          },
          is_new_payment_customer: {
            chName: '老客',
            currentValue: 0,
            dimensionColumn: true,
          },
        },
        proportion: 0.3243,
      },
      {
        abnormal: false,
        contribution: 0,
        contributionRate: 0,
        currentData: {
          uv: {
            chName: '1',
            currentValue: 1,
            dimensionColumn: false,
            momRate: 0,
            momValue: 1,
          },
          is_new_payment_customer: {
            chName: '新客',
            currentValue: 1,
            dimensionColumn: true,
          },
        },
        proportion: 0.0135,
      },
    ],
    yoyTimeRange: {
      dateType: 'NATURAL_DAY',
      dateTypeCode: 1,
      endDay: 20230731,
      startDay: 20230731,
    },
  }; */

  const singleBar = (data, index) => {
    const {
      currentData: { uv, ...rest },
      contributionRate,
      proportion,
    } = data;
    return (
      <div className={style.singleBar}>
        {/* @ts-ignore */}
        <p className={style.title}>{Object.values(rest).map(item => item.chName)}</p>
        <div className={style.chart}>
          <div
            className={style.bar}
            style={{ width: proportion * 100 + '%', background: colorList[index % colorList.length] }}
          ></div>
          <p className={style.proportion} style={{ color: colorList[index % colorList.length] }}>
            {(proportion * 100).toFixed(2)}
            <span className={style.percentSymbol}>%</span>
          </p>
        </div>
        <p className={style.description}>
          变化贡献度{contributionRate ? (contributionRate * 100).toFixed(2) + '%' : '-'}%，客户数{uv.currentValue}
          ，变化量
          <span className={uv.currentValue - uv.momValue >= 0 ? style.up : style.down}>
            {uv.currentValue - uv.momValue || 0}
          </span>
          ， 增幅率
          <span className={uv.momRate >= 0 ? style.up : style.down}>
            {uv.momRate ? (uv.momRate * 100).toFixed(2) + '%' : 0}
          </span>
        </p>
      </div>
    );
  };
  const renderIndicatorAnalysis = () => {
    const { waveResultData = [] } = data;

    const indicatorList: any[] = [];
    waveResultData?.forEach(singleData => {
      const {
        currentData: { uv, ...rest },
      } = singleData;
      Object.keys(rest).forEach(key => {
        const result = {
          name: rest[key].chName,
          value: rest[key].currentValue,
          key: `${key}`, // add comma to separate the key-value pairs
          indicator: indicator[key],
        };
        if (result.name === '新客') {
          result.indicator = indicator['is_new_customer'];
        } else if (result.name === '老客') {
          result.indicator = indicator['is_old_customer'];
        } else if (result.name === '非客户') {
          result.indicator = indicator['is_not_customer'];
        }
        indicatorList.push(result);
      });
    });
    return <IndicatorAnalysis indicatorList={indicatorList} />;
  };

  return (
    <div>
      {renderIndicatorAnalysis()}
      {data.waveResultData && data.waveResultData.length ? (
        data.waveResultData?.map((item, index) => singleBar(item, index))
      ) : (
        // <div className={style.noData}>暂无数据</div>
        <EmptyData />
      )}
    </div>
  );
};

export default ContrastBar;
