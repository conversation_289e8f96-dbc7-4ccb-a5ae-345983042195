// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { G2, Line } from '@ant-design/plots';
import IndicatorAnalysis from '../../components/IndicatorAnalysis';
import indicator from '../../localTemplateData/indicator.json';
import { uniq } from 'lodash';
import { formatValues } from '../../../../../../fns/chart-format';


interface Props {
  // props 类型定义
  data: any;
}

const GmvAnalysisLine: React.FC<Props> = ({ data: chartData }) => {
  const [data, setData] = useState<any>([]);
  const [waveData, setWaveData] = useState<any>('');
  //   const [LineChartData, setLineChartData] = useState(data);
  /* const getLineChartData = item => {
    const { gmv, gmvTrend } = item;
    return {
      xName: 'day',
      yName: 'gmv',
      chartData: indicatorValueTrend.map(({ indicatorValue, currentDay }) => {
        return {
          ['day']: currentDay,
          [indicatorData.indicatorAlias]: formatValues[getCurrentFormat()](indicatorValue),
        };
      }),
    };
  }; */

  useEffect(() => {
    // const { data } = chartData;

    const result: any = {};
    (chartData || []).forEach(({ key, ...res }) => {
      result[key] = { ...res, key };
    });
    setData(formatData(result.GmvAnalysisLine?.value?.data));

    const { currentDataIsAbnormal } = result.GmvAnalysisLine?.value?.data || {};
    const { currentTimeRange } = result.GmvAnalysisLine?.value || {};
    if (currentDataIsAbnormal) {
      setWaveData(currentTimeRange.startDay + '');
    }
    // setWaveData('20230907');
  }, []);

  const formatData = data => {
    const { gmvTrend = [], fieldChNameMap = {} } = data || {};
    const result: any[] = [];
    gmvTrend.forEach(item => {
      const { HQ_KDT_ID, PAR, par, ...rest } = item;
      Object.keys(rest).forEach(key => {
        result.push({
          category: fieldChNameMap ? fieldChNameMap[key?.toLowerCase()] : key?.toLowerCase(),
          money: rest[key] / 100,
          date: PAR || par,
        });
      });
    });
    return result;
  };

  const config = {
    data,
    xField: 'date',
    yField: 'money',
    seriesField: 'category',
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: v => (v > 1 ? `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, s => `${s},`) : v),
      },
    },
    point: {
      shape: 'breath-point',
      color: 'red',
    },
    tooltip: {
      customItems: items => {
        return items.map(item => {
          return {
            ...item,
            value: item.value + '元',
          };
        });
      },
    },
  };

  G2.registerShape('point', 'breath-point', {
    draw(cfg, container) {
      const data = cfg.data;
      const point = {
        x: cfg.x,
        y: cfg.y,
      };
      const group = container.addGroup();

      if (data?.date === waveData) {
        const decorator1 = group.addShape('circle', {
          attrs: {
            x: point.x,
            y: point.y,
            r: 5,
            fill: cfg.color,
            opacity: 0.5,
          },
        });
        const decorator2 = group.addShape('circle', {
          attrs: {
            x: point.x,
            y: point.y,
            r: 5,
            fill: cfg.color,
            opacity: 0.5,
          },
        });
        const decorator3 = group.addShape('circle', {
          attrs: {
            x: point.x,
            y: point.y,
            r: 5,
            fill: cfg.color,
            opacity: 0.5,
          },
        });
        decorator1.animate(
          {
            r: 10,
            opacity: 0,
          },
          {
            duration: 1800,
            easing: 'easeLinear',
            repeat: true,
          },
        );
        decorator2.animate(
          {
            r: 10,
            opacity: 0,
          },
          {
            duration: 1800,
            easing: 'easeLinear',
            repeat: true,
            delay: 600,
          },
        );
        decorator3.animate(
          {
            r: 10,
            opacity: 0,
          },
          {
            duration: 1800,
            easing: 'easeLinear',
            repeat: true,
            delay: 1200,
          },
        );
        group.addShape('circle', {
          attrs: {
            x: point.x,
            y: point.y,
            r: 3,
            fill: cfg.color,
            opacity: 0.7,
          },
        });
        group.addShape('circle', {
          attrs: {
            x: point.x,
            y: point.y,
            r: 1.5,
            fill: cfg.color,
          },
        });
      }

      return group;
    },
  });

  // 波动判断 TODO:
  /* if (false) {
    config['annotations'] = [
      {
        type: 'regionFilter',
        start: ['20230804', 'min'],
        end: ['20230806', 'max'],
        color: 'red',
      },
    ];
  } */

  const renderIndicatorAnalysis = () => {
    const { gmv = {} } = chartData[0]?.value?.data ?? {};
    const indicatorList: any[] = [
      {
        name: 'GMV',
        value: gmv.currentValue || 0,
        formattedValue: formatValues(gmv.currentValue || 0, 'MONEY'),
        key: 'gmv',
        indicator: indicator['self_sale_paid_order_amt'],
      },
    ];

    let timeModel = { ...(chartData[0]?.value?.currentTimeRange || {}), ...(chartData[0]?.value?.timeModel || {}) };
    // 写一个方法把 '20230809'这样的格式转换成 '2023-08-09'
    const getStatisticsTimeRange = (date: string) => {
      // 写一个方法把 '20230809'这样的格式转换成 '2023-08-09'
      const year = date?.toString().slice(0, 4);
      const month = date?.toString().slice(4, 6);
      const day = date?.toString().slice(6, 8);
      return `${year}-${month}-${day}`;
    };
    timeModel.startDay = getStatisticsTimeRange(timeModel.startDay);
    timeModel.endDay = getStatisticsTimeRange(timeModel.endDay);

    return <IndicatorAnalysis indicatorList={indicatorList} timeModel={timeModel} />;
  };

  return (
    <div>
      {renderIndicatorAnalysis()}
      {/* @ts-ignore */}
      <Line {...config} />
    </div>
  );
};

export default GmvAnalysisLine;
