import React from 'react';
import style from './style.m.scss';

import { Pop } from 'zent';
import { uniq } from 'lodash';
import { format } from 'date-fns';
import { formatValues } from '../../../../../../fns/chart-format';
import { TopRight } from '../../../../../../fns/pop';
import { Info16pxIcon } from '../../../../../../svg';
import { IIdicator, TimeModel } from '../../../../constants/indicatorPanel';

interface IIdicatorAnalysis {
  indicatorList: IIdicator[];
  timeModel?: TimeModel;
}

const IndicatorAnalysis = ({ indicatorList, timeModel }: IIdicatorAnalysis) => {
  const getStatisticsTimeRange = () => {
    return uniq([timeModel?.startDay, timeModel?.endDay]);
  };

  return (
    <div className={style.indicatorAnalysis}>
      {indicatorList.map(item => {
        return (
          <div key={item.key} className={style.indicator}>
            <span>{item.name}</span>
            {item.formattedValue && <span>：{item.formattedValue}</span>}{' '}
            <Pop
              trigger="hover"
              position={TopRight}
              content={
                <div className={'jarvis-tips-wrapper'}>
                  <div className={'tips-title'}>{item?.indicator?.indicatorAlias}</div>
                  {<div className={'tips-content'}>{item?.indicator?.indicatorDefinition}</div>}
                </div>
              }
            >
              <div className="svg-wrapper">
                <Info16pxIcon />
              </div>
            </Pop>
          </div>
        );
      })}
      {timeModel && (
        <div className={style.timeRange}>
          统计时间：
          <span className={style.number}>
            {getStatisticsTimeRange()
              .map(time => {
                return time && format(new Date(time), 'YYYY-MM-DD');
              })
              .join(' 至 ')}
          </span>
        </div>
      )}
    </div>
  );
};

export default IndicatorAnalysis;
