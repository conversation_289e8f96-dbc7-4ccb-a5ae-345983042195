import React from 'react';
import { Cross16pxIcon } from '../../../../../../svg';
import styles from './style.m.scss';

interface Props {
  isEdit: boolean;
  onDelete: () => void;
}

const CloseBtn: React.FC<Props> = ({ isEdit, onDelete }) => {
  return (
    <div className={styles.crossIcon} style={{ display: isEdit ? 'block' : 'none' }} onClick={onDelete}>
      <div className={styles.crossIconContent}>
        <Cross16pxIcon />
      </div>
    </div>
  );
};

export default CloseBtn;
