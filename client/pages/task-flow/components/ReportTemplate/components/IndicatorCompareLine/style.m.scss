.indicator-compare {
    padding: 12px 0;
    position: relative;
    overflow: hidden;
  }
  
  .edit {
    background-color: #f8f8f8;
    padding: 12px;
  }
  
  .title {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    /* identical to box height, or 150% */
  
    color: #333333;
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  
  .progress {
    &-content {
      display: flex;
      align-items: top;
      justify-content: space-between;
      width: 100%;
      height: 6px;
      overflow: hidden;
      margin: 9px 0;
    }
    &-item {
      position: relative;
      width: 50%;
      min-width: 8px;
      display: block;
      height: 6px;
      width: 100%;
      border-radius: 100px;
      background: #5dd3f5;
    }
  }
  
  .compare-item {
    margin-top: 12px;
  
    + .compare-item {
      .indicator-value {
        color: #6492f7;
      }
      .progress-item {
        background: #6492f7;
      }
    }
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
  }
  
  .indicator-name {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */
  
    color: #333333;
  }
  .indicator-value {
    white-space: nowrap;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */
    color: #5dd3f5;
  }
  .flow-between {
    display: flex;
    justify-content: space-between;
    & > div {
      text-align: left;
      &:last-child {
        text-align: right;
      }
    }
  }
  
  .progress-content-value {
    margin-bottom: 12px;
  }
  .progress-value-item {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    /* identical to box height, or 143% */
  
    color: #2dbde7;
    &:last-child {
      color: #6492f7;
      text-align: right;
    }
  }
  
  .progress-panel {
    margin-bottom: -4px;
  }
  .svg-wrapper {
    transform: scale(0.9);
    display: flex;
  }