import React from 'react';
import style from './style.m.scss';
import { formats } from '../../../../../../fns/number';
import { formatOptions, formatRatioPercent } from '../../../../../../fns/chart-format';
import ChainRadio from '../ChainRadio';
import CloseBtn from '../CloseBtn';
import { Info16pxIcon } from '../../../../../../svg';
import { Pop } from 'zent';
import { TopRight } from '../../../../../../fns/pop';

interface Indicator {
  key: string;
  name: string;
  value: {
    currentValue: number;
    momRate?: number;
    yoyRate?: number;
  };
  displayFormat: string;
  indicatorAlias?: string;
  indicatorDefinition?: string;
}

interface Rule {
  base: {
    key: string;
    name: string;
  };
  target: {
    key: string;
    name: string;
  };
}

interface Props {
  name: string;
  data: Indicator[];
  rule: Rule;
  momTimeRangeInfo?: string;
  yoyTimeRangeInfo?: string;
  isEdit?: boolean;
  onDelete?: (data: Indicator[]) => void;
}

const IndicatorCompareLine: React.FC<Props> = ({
  name,
  data,
  rule,
  momTimeRangeInfo = '同比',
  yoyTimeRangeInfo = '环比',
  isEdit = false,
  onDelete = () => {},
}) => {
  const result: Record<string, Indicator> = {};
  (data || []).forEach(({ key, ...res }) => {
    result[key] = { ...res, key };
  });

  const baseValue = result[rule?.base?.key]?.value?.currentValue;
  const targetValue = result[rule?.target?.key]?.value?.currentValue;

  const baseRatio = (() => {
    const currentRatio = baseValue / targetValue;
    return result[rule?.base?.key]?.value?.currentValue ? formatRatioPercent(Math.min(currentRatio, 1)) : 0;
  })();

  const targetRatio = (() => {
    const currentRatio = targetValue / baseValue;
    return result[rule?.target?.key]?.value?.currentValue ? formatRatioPercent(Math.min(currentRatio, 1)) : 0;
  })();

  const ratioWidthList = [baseRatio, targetRatio];

  const base = { ...(result[rule?.base?.key] || {}), ...rule?.base };
  const target = { ...(result[rule?.target?.key] || {}), ...rule?.target };

  const getFormatValue = (val: number, type: string) => {
    const currentFormat = type.toLowerCase();
    return formats[currentFormat](val, {
      withUnitPadding: true,
      ...formatOptions[currentFormat],
    });
  };

  const getChainRatioList = (indicator: Indicator) => {
    return [
      {
        text: momTimeRangeInfo,
        value: indicator?.value?.momRate || 0,
      },
      {
        text: yoyTimeRangeInfo,
        value: indicator?.value?.yoyRate || 0,
      },
    ];
  };

  return (
    <div className={style.indicatorCompare + ` ${isEdit ? style.edit : ''}`}>
      <div className={style.title}>
        {name}
        {!isEdit && (
          <Pop
            trigger="hover"
            position={TopRight}
            content={
              <div className={'jarvis-tips-wrapper'}>
                <div className={'tips-title'}>{[base, target][0].indicatorAlias}</div>
                {<div className={'tips-content'}>{[base, target][0].indicatorDefinition}</div>}
              </div>
            }
          >
            <div className={style.svgWrapper}>
              <Info16pxIcon />
            </div>
          </Pop>
        )}
      </div>
      {[base, target].map((item, index) => (
        <div className={style.compareItem} key={item.key}>
          <div className={style.indicatorName}>{item.name}</div>
          <div className={style.flexBetween + ' ' + style.progressPanel}>
            <div className={style.progressContent}>
              <div className={style.progressItem} style={{ width: ratioWidthList[index] }}></div>
            </div>
            <div
              className={style.indicatorValue + ' ' + style.avenirNumber}
              dangerouslySetInnerHTML={{
                __html: getFormatValue(item.value?.currentValue || 0, item.displayFormat),
              }}
            ></div>
          </div>
          <ChainRadio chainRatioList={getChainRatioList(item)} display="horizontal" />
        </div>
      ))}
      <CloseBtn isEdit={isEdit} onDelete={() => onDelete(data)} />
    </div>
  );
};

export default IndicatorCompareLine;
