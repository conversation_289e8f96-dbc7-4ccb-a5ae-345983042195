.container {
    position: relative;
  
    margin: 12px 0 -8px;
    box-sizing: content-box;
  }
  
  .funnel-container-content {
    position: relative;
    height: 165px;
    width: 300px;
    margin: 0 auto;
    background-image: url('https://img01.yzcdn.cn/upload_files/2023/06/28/Fo_Ig4KslPdFOKs-bvCL0cvLkHk4.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }
  
  .edit {
    background-color: #f8f8f8;
    padding: 12px;
  }
  
  .funnel-panel {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .funnel-item {
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    .value {
      color: #fff;
      text-align: center;
      font-size: 14px;
      font-family: Avenir;
      font-weight: 900;
      line-height: 14px;
      margin-bottom: 4px;
    }
    .name {
      color: #fff;
      font-size: 10px;
      font-family: PingFang SC;
      line-height: 10px;
      opacity: 0.7;
    }
  }
  
  .funnel-fixed-item {
    position: absolute;
    text-align: center;
    white-space: nowrap;
    .value {
      color: #5e697d;
      text-align: center;
      font-size: 14px;
      font-family: Avenir;
      font-weight: 500;
      line-height: 14px;
      margin-bottom: 4px;
    }
    .name {
      color: #a6b0bc;
      font-size: 10px;
      font-family: PingFang SC;
      line-height: 10px;
    }
    &.base {
      left: 240px;
      top: 38px;
    }
    &.target {
      left: 226px;
      top: 98px;
    }
    &.total {
      right: 234px;
      top: 68px;
    }
  }