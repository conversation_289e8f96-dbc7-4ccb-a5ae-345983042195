import React, { useState, useEffect } from 'react';
import style from './style.m.scss';

import { formats } from '../../../../../../fns/number';
import CloseBtn from '../CloseBtn';
import { formatOptions, formatRatioPercent } from '../../../../../../fns/chart-format';

interface Indicator {
  key: string;
  indicatorAlias: string;
  value: {
    currentValue: number;
  };
  displayFormat: string;
}

interface Rule {
  top: {
    key: string;
  };
  center: {
    key: string;
  };
  bottom: {
    key: string;
  };
}

interface Name {
  topCenter: string;
  centerBottom: string;
  topBottom: string;
}

interface Props {
  data: Indicator[];
  rule: Rule;
  name: Name;
  isEdit: boolean;
  onDelete: () => void;
}

const Funnel = ({ data, rule, name, isEdit, onDelete }: Props) => {
  const result = {};
  (data || []).forEach(({ key, ...res }) => {
    result[key] = res;
  });

  const topData = result[rule?.top?.key];
  const centerData = result[rule?.center?.key];
  const bottomData = result[rule?.bottom?.key];

  const dataList = [topData, centerData, bottomData].filter(item => item);

  const topCenterRatio = () => {
    if (!topData?.value?.currentValue) return '-';
    return formatRatioPercent(centerData?.value?.currentValue / topData?.value?.currentValue, {
      ...formatOptions.percent,
    });
  };

  const topBottomRatio = () => {
    if (!topData?.value?.currentValue) return '-';
    return formatRatioPercent(bottomData?.value?.currentValue / topData?.value?.currentValue, {
      ...formatOptions.percent,
    });
  };

  const centerBottomRatio = () => {
    if (!centerData?.value?.currentValue) return '-';
    return formatRatioPercent(bottomData?.value?.currentValue / centerData?.value?.currentValue, {
      ...formatOptions.percent,
    });
  };

  const getFormatValue = (val: number, type: string) => {
    const currentFormat = type.toLowerCase();
    return formats[currentFormat](val, {
      ...formatOptions[currentFormat],
    });
  };

  return (
    <div className={`${style.container} ${dataList.length === 3 ? '' : 'hidden'} ${isEdit ? 'edit' : ''}`}>
      <div className={style['funnel-container-content']}>
        <div className={style['funnel-panel']}>
          {dataList.map(indicator => (
            <div key={indicator.key} className={style['funnel-item']}>
              <div>
                <p className={style['value']}>
                  {getFormatValue(indicator.value?.currentValue || 0, indicator.displayFormat)}
                </p>
                <p className={style['name']}>{indicator.indicatorAlias}</p>
              </div>
            </div>
          ))}
        </div>
        <div className={`${style['funnel-fixed-item']} ${style['base']}`}>
          <p className={style['value']}>{topCenterRatio()}</p>
          <p className={style['name']}>{name.topCenter}</p>
        </div>
        <div className={`${style['funnel-fixed-item']} ${style['target']}`}>
          <p className={style['value']}>{centerBottomRatio()}</p>
          <p className={style['name']}>{name.centerBottom}</p>
        </div>
        <div className={`${style['funnel-fixed-item']} ${style['total']}`}>
          <p className={style['value']}>{topBottomRatio()}</p>
          <p className={style['name']}>{name.topBottom}</p>
        </div>
      </div>
      <CloseBtn isEdit={isEdit} onDelete={onDelete} />
    </div>
  );
};

export default Funnel;
