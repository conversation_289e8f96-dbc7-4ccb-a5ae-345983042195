import React, { useMemo } from 'react';
import style from './style.m.scss';
import IndicatorItem from '../IndicatorItem';

interface IIndicatorProps {
  data: any[];
  momTimeRangeInfo?: string;
  yoyTimeRangeInfo?: string;
  disabledValue?: boolean;
  isEdit: boolean;
  onDataChanged?: (data: any) => void;
}

const Indicator: React.FC<IIndicatorProps> = ({
  data,
  momTimeRangeInfo,
  yoyTimeRangeInfo,
  disabledValue,
  isEdit,
  onDataChanged,
}) => {
  const indicatorMap = useMemo(() => {
    const result = {};
    (data || []).forEach(({ key, ...res }) => {
      result[key] = res;
    });
    return result;
  }, [data]);

  const chainRatioList = indicator => {
    return [
      {
        text: momTimeRangeInfo,
        value: indicator?.value?.momRate,
      },
      {
        text: yoyTimeRangeInfo,
        value: indicator?.value?.yoyRate,
      },
    ];
  };

  return (
    <div className={style.indicatorList}>
      {console.log('reportTemplate data is ', data)}
      {data
        .map(item => ({
          ...item,
          ratioIndicator: {
            ...item.ratio,
            ...indicatorMap[item?.ratio?.key],
          },
        }))
        .filter(item => {
          return !item.hidden && item.value;
        })
        .map(indicator => {
          return (
            <IndicatorItem
              data={indicator}
              key={indicator.key}
              chainRatioList={chainRatioList(indicator)}
              disabledValue={disabledValue}
              isEdit={isEdit}
              onDataChanged={onDataChanged}
            />
          );
        })}
    </div>
  );
};

export default Indicator;
