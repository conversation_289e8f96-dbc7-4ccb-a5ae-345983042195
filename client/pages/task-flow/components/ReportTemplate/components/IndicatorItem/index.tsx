import React, { useMemo } from 'react';
// import IndicatorTip from './IndicatorTip';
// import ChainRadio from './ChainRadio';
// import CloseBtn from './CloseBtn';
import style from './style.m.scss';
import { Info16pxIcon } from '../../../../../../svg';
import ChainRadio from '../ChainRadio';
import CloseBtn from '../CloseBtn';
import { Pop } from 'zent';
import { formats } from '../../../../../../fns/number';
import { TopRight } from '../../../../../../fns/pop';
import { formatOptions, formatRatioPercent } from '../../../../../../fns/chart-format';

interface IIndicatorItemProps {
  data: any;
  chainRatioList: any[];
  indicatorName?: string;
  disabledValue?: boolean;
  isEdit: boolean;
  onDataChanged?: (data: any) => void;
}

const HUNDRED_MILLION = 1000000000;

const IndicatorItem: React.FC<IIndicatorItemProps> = ({
  data,
  chainRatioList,
  indicatorName,
  disabledValue,
  isEdit,
  onDataChanged,
}) => {
  const getFormatValue = (val: any, type: any = 'NUMBER') => {
    const currentFormat = type.toLowerCase();
    return formats[currentFormat](val, {
      withUnitPadding: true,
      ...formatOptions[currentFormat],
    });
  };

  const indicator = useMemo(() => {
    return data;
  }, [data]);

  const ratioIndicator = useMemo(() => {
    return data?.ratioIndicator ? data?.ratioIndicator : null;
  }, [data]);

  const hasRatio = useMemo(() => {
    return data?.ratioIndicator?.indicatorAlias ? data?.ratioIndicator : null;
  }, [data]);

  const ratio = () => {
    return formatRatioPercent(indicator.value?.currentValue / data?.ratioIndicator?.value?.currentValue, {
      withUnitPadding: true,
    });
  };

  const onDelete = () => {
    onDataChanged &&
      onDataChanged({
        key: data.getter,
        value: {
          ...data.value,
          isDeleted: true,
        },
      });
  };

  return (
    <div
      key={indicator.key}
      className={`${style.indicator} ${
        indicator.value?.currentValue > HUNDRED_MILLION ? style['full-indicator-item'] : ''
      } ${isEdit ? style.edit : ''}`}
    >
      <div className={style.title}>
        {indicatorName || indicator.indicatorAlias}
        {hasRatio ? ratioIndicator.name : ''}
        {!isEdit && (
          <Pop
            trigger="hover"
            position={TopRight}
            content={
              <div className={'jarvis-tips-wrapper'}>
                <div className={'tips-title'}>{indicator.indicatorAlias}</div>
                {
                  <div
                    className={'tips-content'}
                    dangerouslySetInnerHTML={{ __html: indicator.indicatorDefinition }}
                  ></div>
                }
              </div>
            }
          >
            <div className={style.svgWrapper}>
              <Info16pxIcon />
            </div>
          </Pop>
        )}
      </div>
      {!disabledValue && (
        <div className={style['indicator-list-item-value']}>
          <div className={`${style.value} ${style['avenir-number']}`}>
            <span
              dangerouslySetInnerHTML={{
                __html: getFormatValue(indicator.value?.currentValue, indicator.displayFormat),
              }}
            ></span>
            {hasRatio && <span dangerouslySetInnerHTML={{ __html: `(${ratio()})` }}></span>}
          </div>
        </div>
      )}
      <ChainRadio chainRatioList={chainRatioList} />
      <CloseBtn isEdit={isEdit} onDelete={onDelete} />
    </div>
  );
};

export default IndicatorItem;
