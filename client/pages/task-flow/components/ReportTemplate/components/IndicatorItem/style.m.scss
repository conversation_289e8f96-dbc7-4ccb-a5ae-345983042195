.indicator {
  position: relative;
  // width: calc(50% - 4px);
  min-width: 125px;
  padding: 12px 0;

  &.edit {
    background-color: #f8f8f8;
    padding: 12px;
    padding-right: 25px;
  }

  &.full-indicator-item {
    width: 100%;
  }

  .title {
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */

    color: #333333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }

  .big-value {
    font-size: 32px;
    line-height: 42px;
    font-weight: bolder;
    display: inline-block;
  }

  .value {
    font-size: 20px;
    line-height: 26px;
    margin-bottom: 4px;
    /* identical to box height, or 130% */
    font-family: Avenir;

    color: #333333;
    font-weight: bolder;
    display: inline-block;
  }
}

.chain-ratio {
  margin-top: 4px;
  font-size: 12px;
  line-height: 20px;
  /* identical to box height, or 167% */

  color: #999999;

  .chain-value {
    color: #323233;
    font-weight: 500;
    font-family: Avenir;
  }

  .zenticon {
    margin: 0 3px;
  }

  .zenticon-arrow-up {
    color: #e70800;
  }

  .zenticon-arrow-down {
    color: #45a110;
  }

  .empty-value {
    margin-left: 6px;
  }
  .rise {
    color: #d42f15;
  }
  .fall {
    color: #45a110;
  }
}

.svg-wrapper {
  transform: scale(0.9);
  display: flex;
}
