import React, { useEffect, useState } from 'react';
import { Grid } from 'zent';
import { cloneDeep } from 'lodash';
import EmptyData from '../EmptyData';
import indicator from '../../localTemplateData/indicator.json';
import { formatMoney } from '../../../../../../fns/chart-format';
import IndicatorAnalysis from '../IndicatorAnalysis';

const defaultColumns = [
  {
    title: '变化贡献度',
    name: 'contributionRate',
  },
  {
    title: '变化量',
    name: 'diffValue',
  },
  {
    title: '增幅率',
    name: 'momRate',
  },
];
const IndexAnalysisTable = ({ data, analysisIndex }) => {
  const [columns, setColumns] = useState<any[]>(defaultColumns);
  const [dataset, setDataset] = useState<any[]>();

  useEffect(() => {
    formatColumns();
    formatDataset();
  }, [data]);

  const formatColumns = () => {
    const { waveResultData, drillDimension } = data;
    const newColumns = cloneDeep(defaultColumns);
    newColumns.unshift({
      title: indicator[analysisIndex.toLowerCase()]?.indicatorAlias,
      name: analysisIndex,
      // @ts-ignore
      width: 130,
    });
    drillDimension.forEach(item => {
      newColumns.unshift({
        title: item.displayName,
        name: item.indexName,
        // @ts-ignore
        width: 100,
      });
    });
    setColumns(newColumns);
  };

  const formatDataset = () => {
    // Create a new array and use the forEach method to loop through the original array
    const newDataset: any[] = [];
    const {
      waveResultData,
      drillDimension,
      indexAnalysisWay: { analysisIndex },
    } = data;
    const keys = drillDimension.map(item => item.indexName);
    waveResultData?.forEach(item => {
      const result = {};
      console.log('keysis ', keys, item);
      keys.forEach(key => {
        const { contributionRate } = item;
        const { chName } = item.currentData[key];
        const value = item.currentData[analysisIndex.toLowerCase()];
        result[key] = chName;
        result[analysisIndex] = getAnalysisIndexValue(value);
        result['diffValue'] = getDiffValue(value);
        result['contributionRate'] = getContributionRate(contributionRate);
        result['momRate'] = getMomRate(value);
      });
      newDataset.push(result);
    });
    setDataset(newDataset);
  };

  const getAnalysisIndexValue = value => {
    const displayFormat = indicator[analysisIndex.toLowerCase()]?.displayFormat;
    if (!value?.currentValue) return '-';
    if (displayFormat === 'MONEY') {
      return formatMoney(value?.currentValue);
    }
    if (displayFormat === 'PERCENT') {
      return (value?.currentValue * 100).toFixed(2) + '%';
    }
    return value?.currentValue || '-';
  };

  const getDiffValue = value => {
    return value?.currentValue && value?.momValue ? (value?.currentValue - value?.momValue).toFixed(2) : '-';
  };

  const getContributionRate = contributionRate => {
    return contributionRate ? (contributionRate * 100).toFixed(2) + '%' : '-';
  };

  const getMomRate = value => {
    return value?.momRate ? (value?.momRate * 100).toFixed(2) + '%' : '-';
  };

  const renderIndicatorAnalysis = () => {
    const indicatorList: any[] = [
      {
        // 变化贡献度
        name: '变化贡献度',
        key: 'contributionRate',
        indicator: indicator['contributionRate'],
      },
      {
        // 客户数
        name: indicator[analysisIndex.toLowerCase()]?.indicatorAlias,
        key: analysisIndex.toLowerCase(),
        indicator: indicator[analysisIndex.toLowerCase()],
      },
      {
        // 变化量
        name: '变化量',
        key: 'diffValue',
        indicator: indicator['diffValue'],
      },
      {
        // 增幅率
        name: '增幅率',
        key: 'momRate',
        indicator: indicator['momRate'],
      },
    ];
    return <IndicatorAnalysis indicatorList={indicatorList} />;
  };

  return (
    <>
      {dataset?.length ? (
        <>
          {renderIndicatorAnalysis()}
          <Grid columns={columns} scroll={{ x: 550 }} datasets={dataset} />
        </>
      ) : (
        <EmptyData />
      )}
    </>
  );
};

export default IndexAnalysisTable;
