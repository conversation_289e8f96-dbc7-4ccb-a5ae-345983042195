import React, { useEffect, useState } from 'react';
import style from './style.m.scss';
import { InlineLoading } from 'zent';
import { goodsAnalysis } from '../../../../api/analysis';
import GoodsAnalysisTable from '../GoodsAnalysisTable';

const chartMap = {
  UV: 'bar',
  SELF_SALE_PLACED_PAID_RATE: 'table',
  CUSTOMER_PRICE: 'table',
  SELF_SALE_UV_PLACED_RATE: 'table',
  SELF_SALE_PAID_ORDER_AMT: 'formula',
  SELF_SALE_UV_PAID_RATE: 'formula',
};

const GoodsAnalysis = ({ params }) => {
  const [chartList, setChartList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    fetchGoodsAnalysis({ params });
  }, []);

  const fetchGoodsAnalysis = ({ params }) => {
    setLoading(true);
    return goodsAnalysis(params)
      .then(res => {
        const newChartList = res;

        setLoading(false);
        setChartList([newChartList]);
      })
      .catch(err => {
        setLoading(false);
        // toast(err.message || err || '请求失败');
      });
  };

  return (
    <div className={style.customIndexAnalysis}>
      {chartList.map((item, index) => {
        return <GoodsAnalysisTable key={index} data={item} />;
      })}

      {loading && (
        <div className={style.loading}>
          <InlineLoading loading={loading} icon="circle" iconText="加载中" colorPreset="grey" />
        </div>
      )}
      {chartList.length > 0 && !loading && <p className={style.suggests}>{chartList[chartList.length - 1].suggests}</p>}
    </div>
  );
};

export default GoodsAnalysis;
