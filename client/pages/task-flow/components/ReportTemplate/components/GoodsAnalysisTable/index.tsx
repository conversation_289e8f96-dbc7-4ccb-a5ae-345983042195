import React, { useEffect, useState } from 'react';
import indicator from '../../localTemplateData/indicator.json';
import { Grid } from 'zent';
import { cloneDeep } from 'lodash';
import formatMoney from '@youzan/utils/money/format';
import style from './style.m.scss';
import EmptyData from '../EmptyData';
import IndicatorAnalysis from '../IndicatorAnalysis';

const defaultColumns = [
  {
    title: '商品',
    name: 'goods',
    width: 180,
    bodyRender: data => {
      const { goodsUrl, goodsTitle, imageUrl } = data;
      return (
        <div className={style.goodsTd}>
          <div className={style.goodsImg}>
            <img src={imageUrl?.chName} />
          </div>
          <a href={goodsUrl?.chName} target="_blank" rel="noreferrer" className={style.goodsTitle}>
            {goodsTitle?.chName}
          </a>
        </div>
      );
    },
  },
  {
    title: '变化贡献度',
    name: 'contributionRate',
    width: 108,
  },
  {
    title: '支付金额',
    name: 'selfSalePaidOrderAmt',
    width: 108,
  },
  {
    title: '变化量',
    name: 'diffValue',
    width: 108,
  },
  {
    title: '增幅率',
    name: 'momRate',
    width: 108,
  },
];

const renderIndicatorAnalysis = () => {
  const indicatorList: any[] = [
    {
      // 变化贡献度
      name: '变化贡献度',
      key: 'contributionRate',
      indicator: indicator['contributionRate'],
    },
    {
      // 变化量
      name: '变化量',
      key: 'diffValue',
      indicator: indicator['diffValue'],
    },
    {
      // 增幅率
      name: '增幅率',
      key: 'momRate',
      indicator: indicator['momRate'],
    },
  ];
  return <IndicatorAnalysis indicatorList={indicatorList} />;
};

const GoodsAnalysisTable = ({ data }) => {
  const [columns, setColumns] = useState<any[]>(defaultColumns);
  const [dataset, setDataset] = useState<any[]>();

  useEffect(() => {
    formatColumns();
    formatDataset();
  }, [data]);

  const formatColumns = () => {
    // const { waveResultData, drillDimension } = data;
    const newColumns = cloneDeep(defaultColumns);
    /* drillDimension.forEach(item => {
      newColumns.unshift({
        title: item.displayName,
        name: item.indexName,
        // @ts-ignore
        width: 100,
      });
    }); */
    setColumns(newColumns);
  };

  const formatDataset = () => {
    // Create a new array and use the forEach method to loop through the original array
    const newDataset: any[] = [];
    const {
      waveResultData,
      drillDimension,
      indexAnalysisWay: { analysisIndex },
    } = data;
    const keys = drillDimension.map(item => item.indexName);
    waveResultData?.forEach(item => {
      const { contributionRate, currentData } = item;
      const result = {
        goodsUrl: currentData.goods_url,
        goodsTitle: currentData.goods_title,
        imageUrl: currentData.image_url,
        selfSalePaidOrderAmt: formatMoney(currentData.self_sale_paid_order_amt.currentValue || 0) + '元',
      };
      keys.forEach(key => {
        const { chName } = item.currentData[key];
        const value = item.currentData[analysisIndex.toLowerCase()];
        result[key] = chName;
        result['diffValue'] = getDiffValue(value);
        result['contributionRate'] = getContributionRate(contributionRate);
        result['momRate'] = getMomRate(value);
      });
      newDataset.push(result);
    });
    console.log('newDataSet is ', newDataset);
    setDataset(newDataset);
  };

  const getDiffValue = value => {
    return formatMoney((value?.currentValue || 0) - (value?.momValue || 0)) + '元';
  };

  const getContributionRate = contributionRate => {
    return contributionRate || contributionRate === 0 ? (contributionRate * 100).toFixed(2) + '%' : '-';
  };

  const getMomRate = value => {
    return value?.momRate ? (value?.momRate * 100).toFixed(2) + '%' : '-';
  };

  return (
    <>
      {dataset?.length ? (
        <>
          {renderIndicatorAnalysis()}
          <Grid columns={columns} scroll={{ x: 550 }} datasets={dataset} />
        </>
      ) : (
        <EmptyData />
      )}
    </>
  );
};

export default GoodsAnalysisTable;
