.indicator-compare {
  padding: 12px 0;
  position: relative;
  overflow: hidden;
}

.edit {
  background-color: #f8f8f8;
  padding: 12px;
}
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  /* identical to box height, or 150% */

  color: #333333;
  margin-bottom: 12px;
}

.progress {
  &-content {
    display: flex;
    align-items: top;
    justify-content: space-between;
    width: 100%;
    height: 6px;
    border-radius: 100px;
    overflow: hidden;
    margin: 12px 0;
  }
  &-item {
    position: relative;
    width: 50%;
    min-width: 12px;
    display: block;
    height: 6px;
    width: 100%;
    background: #5dd3f5;
    &::after {
      content: '';
      position: absolute;
      right: -4px;
      top: -3px;
      height: 12px;
      width: 8px;
      background-color: #fff;
      transform: skew(-30deg);
    }
    &.empty {
      min-width: inherit;
    }
    &.fill {
      &::after {
        display: none;
      }
    }
    &:last-child {
      background: #6492f7;
      &::after {
        content: '';
        position: absolute;
        left: -2px;
        top: -5px;
        height: 12px;
        width: 8px;
        background-color: #fff;
        transform: skew(-30deg);
      }
    }
  }
}

.indicator-name {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  color: #333333;
  margin-bottom: 4px;
}
.indicator-value {
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  color: #333333;
}
.flow-between {
  display: flex;
  justify-content: space-between;
  & > div {
    text-align: left;
    &:last-child {
      text-align: right;
    }
  }
}

.progress-content-value {
  margin-bottom: 8px;
}
.progress-value-item {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */

  color: #2dbde7;
  &:last-child {
    color: #6492f7;
    text-align: right;
  }
}

.svg-wrapper {
  transform: scale(0.9);
  display: flex;
}
