import React, { useEffect } from 'react';
import styles from './style.m.scss';
import ChainRadio from '../ChainRadio';
import CloseBtn from '../CloseBtn';
import { Info16pxIcon } from '../../../../../../svg';
import { Pop } from 'zent';
import { formats } from '../../../../../../fns/number';
import { TopRight } from '../../../../../../fns/pop';
import { formatOptions, formatRatioPercent } from '../../../../../../fns/chart-format';
interface IndicatorCompareProps {
  data: Array<any>;
  rule: any;
  momTimeRangeInfo?: string;
  yoyTimeRangeInfo?: string;
  isEdit?: boolean;
  name?: string;
  fillInCompare?: boolean;
  onDelete: () => void;
}

const IndicatorCompare: React.FC<IndicatorCompareProps> = ({
  data,
  rule,
  momTimeRangeInfo = '同比',
  yoyTimeRangeInfo = '环比',
  isEdit = false,
  name,
  fillInCompare = false,
  onDelete,
}) => {
  const result = {};
  (data || []).forEach(({ key, ...res }) => {
    result[key] = res;
  });

  const total = { ...(result[rule?.total?.key] || {}), ...rule?.total };
  const base = { ...(result[rule?.base?.key] || {}), ...rule?.base };
  const target = { ...(result[rule?.target?.key] || {}), ...rule?.target };
  const totalValue = total?.value?.currentValue || base?.value?.currentValue + target?.value?.currentValue;

  const baseRatioValue = (base?.value?.currentValue || 0) / totalValue;
  const targetRatioValue = (target?.value?.currentValue || 0) / totalValue;
  const fill = baseRatioValue === 1 || targetRatioValue === 1;
  const empty = !baseRatioValue && !targetRatioValue;

  const getFormatValue = (val: number, type: string) => {
    const currentFormat = type.toLowerCase();
    return formats[currentFormat](val, {
      withUnitPadding: true,
      ...formatOptions[currentFormat],
    });
  };

  const getChainRatioList = (indicator: any) => {
    return [
      {
        text: momTimeRangeInfo,
        value: indicator.value.momRate,
      },
      {
        text: yoyTimeRangeInfo,
        value: indicator.value.yoyRate,
      },
    ];
  };

  const getImageData = ({ currentValue }: any = {}) => {
    if (!currentValue) return [];
    return JSON.parse(currentValue);
  };

  const formatMoney = (value: number) => {
    return formats.money(value);
  };

  const formatPercent = (value: number) => {
    return formats.percent(value);
  };

  return (
    <div className={`${styles['indicator-compare']} ${isEdit ? styles['edit'] : ''}`}>
      <div className={styles['title']}>
        {total.name || total.indicatorAlias || name}
        {[base, target].length && !isEdit && (
          <Pop
            trigger="hover"
            position={TopRight}
            content={
              <div className={'jarvis-tips-wrapper'}>
                <div className={'tips-title'}>{[base, target][0].indicatorAlias}</div>
                <div className={'tips-content'}>{[base, target][0].indicatorDefinition}</div>
              </div>
            }
          >
            <div className={styles.svgWrapper}>
              <Info16pxIcon />
            </div>
          </Pop>
        )}
      </div>
      <div className={styles['flow-between']}>
        {[base, target].map(item => (
          <div key={item.name}>
            <div className={styles['indicator-name']}>{item.name}</div>
            <div
              className={`${styles['indicator-value']} ${styles['avenir-number']}`}
              dangerouslySetInnerHTML={{
                __html: getFormatValue(item.value.currentValue || 0, item.displayFormat),
              }}
            ></div>
          </div>
        ))}
      </div>
      {!empty && (
        <div className={styles['progress-content']}>
          <div
            className={`${styles['progress-item']} ${fill ? styles['empty'] : ''}`}
            style={{ width: formatRatioPercent(baseRatioValue || 0) }}
          ></div>
          <div
            className={`${styles['progress-item']} ${fill ? styles['fill'] : ''}`}
            style={{ width: formatRatioPercent(targetRatioValue || 0) }}
          ></div>
        </div>
      )}
      <div className={`${styles['progress-content-value']} ${styles['flow-between']}`}>
        <div
          className={`${styles['progress-value-item']} ${styles['avenir-number']}`}
          dangerouslySetInnerHTML={{
            __html: formatRatioPercent(baseRatioValue, { withUnitPadding: true }),
          }}
        ></div>
        <div
          className={`${styles['progress-value-item']} ${styles['avenir-number']}`}
          dangerouslySetInnerHTML={{
            __html: formatRatioPercent(targetRatioValue, { withUnitPadding: true }),
          }}
        ></div>
      </div>
      <div className={`${styles['flow-between']} ${styles['progress-value-chain']}`}>
        {[base, target].map(item => (
          <ChainRadio key={item.name} chainRatioList={getChainRatioList(item)} />
        ))}
      </div>
      <CloseBtn isEdit={isEdit} onDelete={onDelete} />
    </div>
  );
};

export default IndicatorCompare;
