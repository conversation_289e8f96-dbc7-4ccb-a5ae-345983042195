import React, { useEffect, useState } from 'react';
import style from './style.m.scss';
import FormulaReport from '../FormulaReport';
import { userAnalysisIndex } from '../../../../api/analysis';
import ContrastBar from '../ContrastBar';
import IndexAnalysisTable from '../IndexAnalysisTable';
import { cloneDeep } from 'lodash';
import { InlineLoading } from 'zent';

const chartMap = {
  UV: 'bar',
  SELF_SALE_PLACED_PAID_RATE: 'table',
  CUSTOMER_PRICE: 'table',
  SELF_SALE_UV_PLACED_RATE: 'table',
  SELF_SALE_PAID_ORDER_AMT: 'formula',
  SELF_SALE_UV_PAID_RATE: 'formula',
};

const CustomIndexAnalysis = ({ params }) => {
  const [chartList, setChartList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [cacheMap, setCacheMap] = useState<any>({});

  useEffect(() => {
    fetchAnalysisIndex({ params });
  }, []);

  const onExplainIndex = ({ selectedIndex, index }) => {
    const customParams = {
      ...params,
      indexName: selectedIndex,
    };
    if (cacheMap[selectedIndex]) {
      const newChartList = cloneDeep(chartList);
      if (index !== -1) {
        newChartList.splice(index + 1);
        setChartList(newChartList);
      }
      newChartList.push(cacheMap[selectedIndex]);

      setLoading(false);
      setChartList(newChartList);
    } else {
      fetchAnalysisIndex({ params: customParams, index });
    }
  };

  const fetchAnalysisIndex = ({ params, index = -1 }) => {
    setLoading(true);
    const newChartList = cloneDeep(chartList);
    //   从index往后的不要
    if (index !== -1) {
      newChartList.splice(index + 1);
      setChartList(newChartList);
    }
    return userAnalysisIndex(params)
      .then(res => {
        newChartList.push(res);
        cacheMap[params.indexName] = res;
        setCacheMap(cacheMap);

        setLoading(false);
        setChartList(newChartList);
      })
      .catch(err => {
        setLoading(false);
        // toast(err.message || err || '请求失败');
      });
  };

  return (
    <div className={style.customIndexAnalysis}>
      {chartList.map((item, index) => {
        console.log('chartList is ', chartList);
        return (
          <div key={index}>
            {/* 公式拆解 */}
            {chartMap[item?.indexAnalysisWay?.analysisIndex] === 'formula' && (
              <FormulaReport
                onExplainIndex={selectedIndex => onExplainIndex({ selectedIndex, index })}
                data={item}
                loading={loading}
              />
            )}
            {/* 柱状图 */}
            {chartMap[item?.indexAnalysisWay?.analysisIndex] === 'bar' && <ContrastBar data={item} />}
            {/* 表格数据 */}
            {chartMap[item?.indexAnalysisWay?.analysisIndex] === 'table' && (
              <IndexAnalysisTable data={item} analysisIndex={item?.indexAnalysisWay?.analysisIndex} />
            )}
          </div>
        );
      })}

      {loading && (
        <div className={style.loading}>
          <InlineLoading loading={loading} icon="circle" iconText="加载中" colorPreset="grey" />
        </div>
      )}
      {chartList.length > 0 && !loading && <p className={style.suggests}>{chartList[chartList.length - 1].suggests}</p>}
    </div>
  );
};

export default CustomIndexAnalysis;
