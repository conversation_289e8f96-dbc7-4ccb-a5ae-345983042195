import React, { useState, useMemo, useEffect } from 'react';
import CloseBtn from '../CloseBtn';
import styles from './style.m.scss';
import { formatRatioPercent } from '../../../../../../fns/chart-format';
import { formatMoney } from '../../../../../../fns/chart-format';

interface Props {
  data: Array<any>;
  isEdit: boolean;
  onDelete?: (data: any) => void;
}

function GoodsList({ data, isEdit, onDelete }: Props) {
  const [goodsList, setGoodsList] = useState<Array<any>>([]);
  const [gmv, setGmv] = useState<any>({});

  const result = useMemo(() => {
    const _result = {};
    (data || []).forEach(({ key, ...res }) => {
      _result[key] = res;
    });
    return _result;
  }, [data]);

  useEffect(() => {
    // @ts-ignore
    setGoodsList(result?.goods_rank?.value);
    // @ts-ignore
    setGmv(result?.gmv?.value);
  }, [result]);

  const getImageData = ({ currentValue }: { currentValue: string } = { currentValue: '' }) => {
    if (!currentValue) return [];
    return JSON.parse(currentValue);
  };

  return (
    <>
      {goodsList && (
        <div className={`${styles.container} ${isEdit ? styles.edit : ''}`}>
          <div className={styles.title}>Top10热销榜</div>
          <div className={styles.desc}>
            Top10商品GMV占店铺总GMV的
            <span
              className={styles['avenir-number']}
              dangerouslySetInnerHTML={{
                __html: formatRatioPercent(gmv?.currentValue, {
                  withUnitPadding: true,
                }),
              }}
            ></span>
          </div>
          {Array.isArray(goodsList) &&
            goodsList.map((handledGoodData, index) => (
              <div className={styles['goods-card']} key={index}>
                <div className={styles['goods-card__wrapper']}>
                  <div className={styles['goods-card__left']}>
                    <div className={styles['goods-card__image']}>
                      <div className={`${styles['goods-card__image-prefix']} ${styles[`index-${index + 1}`]}`}>
                        {index + 1}
                      </div>
                      <img src={getImageData(handledGoodData?.picture)[0]?.url} alt="" />
                    </div>
                    <div className={styles['goods-card__content']}>
                      <div className={styles['goods-card__title']}>{handledGoodData?.title?.currentValue}</div>
                      <div className={styles['goods-card__desc']}>
                        <span className={styles['goods-card__dash-text']}>
                          售出
                          <span
                            className={styles['avenir-number']}
                            dangerouslySetInnerHTML={{
                              __html: handledGoodData?.goods_self_sale_paid_sku_cnt?.currentValue || 0,
                            }}
                          ></span>
                          件
                        </span>
                        <span className={styles['goods-card__dash']}></span>
                        <span className={styles['goods-card__dash-text']}>
                          交易额
                          <span
                            className={styles['avenir-number']}
                            dangerouslySetInnerHTML={{
                              __html: formatMoney(handledGoodData?.goods_self_sale_paid_order_amt?.currentValue, {
                                withUnitPadding: true,
                              }),
                            }}
                          ></span>
                        </span>
                      </div>
                      <div className={styles['goods-card__desc']}>
                        单品转化
                        <span
                          className={styles['avenir-number']}
                          dangerouslySetInnerHTML={{
                            __html: formatRatioPercent(handledGoodData?.goods_self_sale_uv_paid_rate?.currentValue, {
                              withUnitPadding: true,
                            }),
                          }}
                        ></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          <CloseBtn isEdit={isEdit} onDelete={() => onDelete && onDelete(data)} />
        </div>
      )}
    </>
  );
}

export default GoodsList;
