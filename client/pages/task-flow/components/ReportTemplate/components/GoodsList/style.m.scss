.avenir-number {
  padding-left: 2px;
  padding-right: 2px;
}
.container {
  position: relative;
  padding: 4px 0 12px;
  .goods-card {
    position: relative;
    // margin-top: 8px;

    &__wrapper {
      display: flex;
      align-items: center;
      padding: 8px 0;
      background: transparent;
      border-radius: 4px;
    }

    &__left {
      display: flex;
      align-items: center;
      flex-grow: 1;
    }

    &__image {
      width: 56px;
      height: 56px;
      border-radius: 4px;
      overflow: initial;
      background: #f8f8f8;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      &-prefix {
        box-sizing: border-box;
        padding-bottom: 3px;
        position: absolute;
        z-index: 5;
        display: flex;
        justify-content: center;
        align-items: center;
        left: -3px;
        top: -3px;
        width: 20px;
        height: 26px;
        background-position: left top;
        background-repeat: no-repeat;
        background-size: contain;
        background-image: url('https://img01.yzcdn.cn/upload_files/2023/06/16/Fsyvh4Wli8wUOxtWKGdki_a0T8qi.png');
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        /* identical to box height, or 157% */

        color: #9ea3ab;
        &.index-1 {
          color: #9f6a38;
          background-image: url('https://img01.yzcdn.cn/upload_files/2023/06/16/FhRILvxVwtiVXDXbBRl9wQciNezT.png');
        }

        &.index-2 {
          color: #666d7f;
          background-image: url('https://img01.yzcdn.cn/upload_files/2023/06/16/Fp1GlpKq-qUt_dba0ptraAwArlz1.png');
        }

        &.index-3 {
          color: #aa8686;
          background-image: url('https://img01.yzcdn.cn/upload_files/2023/06/16/FqypC4VoLSMRHVQbOCam7YmrK7hM.png');
        }
      }
    }

    &__content {
      margin-left: 12px;
      width: 0;
      flex: 1;

      min-height: 56px;
      display: flex;
      flex-direction: column;
      justify-content: top;
    }

    &__title {
      font-family: 'PingFang SC';
      font-style: normal;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      /* identical to box height, or 157% */

      letter-spacing: 0.4px;

      color: #333333;

      display: block;
      /* Keep the following lines for two lines of text */
      // overflow: hidden;
      max-height: 48px;
      overflow: hidden;
      display: -webkit-box;
      text-overflow: ellipsis;
      // -webkit-box-orient: vertical;

      // overflow: hidden;
      white-space: pre-wrap;
      word-break: break-all;
      width: initial;
      // text-overflow: ellipsis;
    }

    &__desc {
      display: flex;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      align-items: center;
      /* identical to box height, or 167% */

      letter-spacing: 0.4px;

      color: #999999;
      &-text {
        opacity: 0.7;
      }
    }

    &__dash {
      display: inline-block;
      width: 1px;
      height: 8px;
      background-color: #cccccc;
      margin: 0 8px;
    }
    &__price {
      font-family: 'Avenir';
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      color: #999999;
    }

    &__right {
      /* 我希望__right 有默认宽度60px */
      width: 60px;
      display: flex;
      align-items: center;
      margin-left: 12px;

      .navigate-btn {
        width: 60px;
        height: 32px;
        border-radius: 100px;
        border: 1px solid #e0e0e0;
        background-color: transparent;

        &-content {
          display: flex;
          align-items: center;
        }

        .van-icon {
          margin-left: 4px;
          font-size: 12px;
          color: #999999;
        }
      }
    }

    /* .bottom-border {
      width: calc(100vw - 60px - 12px - 12px);
      height: 1px;
      transform: scaleY(50%);
      background-color: #e0e0e0;
      position: absolute;
      bottom: 0;
      right: 0;
    } */
  }
}

.edit {
  background-color: #f8f8f8;
  padding: 12px;
}
.title {
  font-weight: 500;
  font-size: 17px;
  line-height: 26px;
  /* identical to box height, or 153% */

  letter-spacing: 0.4px;

  color: #333333;
}

.desc {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  /* identical to box height, or 167% */

  letter-spacing: 0.4px;

  color: #999999;

  opacity: 0.7;
}
.goods-card:first-of-type {
  margin-top: 8px;
}
