{"uv": {"indicatorAlias": "访客数", "indicatorDefinition": "统计时间内，店铺访问人数，一人多次访问被计为一人。", "displayFormat": "NUMBER"}, "self_sale_paid_order_amt": {"indicatorAlias": "支付金额", "indicatorDefinition": "统计时间内，成功支付的订单金额之和（不剔除退款订单）。单位：元。", "displayFormat": "MONEY"}, "self_sale_paid_order_cnt": {"indicatorAlias": "支付订单数", "indicatorDefinition": "统计时间内，成功支付的订单数，一个订单对应唯一一个订单号。（拼团在成团时计入支付订单；定金预售在尾款支付时计入支付订单；\n货到付款在发货时计入支付订单；不剔除退款订单。） ", "displayFormat": "NUMBER"}, "self_sale_uv_paid_rate": {"indicatorAlias": "访问支付转化率", "indicatorDefinition": "统计时间内，店铺支付人数/访客数。", "displayFormat": "PERCENT"}, "new_paid_order_uv_2year": {"indicatorAlias": "新成交客户数", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户支付人数：统计时间内，新客户成功支付的人数（不剔除退款订单），一人多次支付被计为一人。\n新客户支付人数占比 = 新客户支付人数 ÷ 店铺客户总支付人数", "displayFormat": "NUMBER", "ratio": {"key": "self_sale_paid_order_uv", "name": "(占比)"}}, "repurchase_uv_ratio": {"indicatorAlias": "复购率", "indicatorDefinition": "统计时间内，复购客户人数占总支付客户数的百分比。计算公式：复购率 = 复购客户数 ÷ 总支付客户数 × 100%", "displayFormat": "PERCENT"}, "ump_self_sale_paid_order_amt": {"indicatorAlias": "营销支付金额", "indicatorDefinition": "统计时间内，营销活动带来的支付金额总和。营销活动只包含：优惠券、多人拼团、秒杀、限时折扣以及满减送。单位：元。\n营销支付金额占比：统计时间内，营销支付金额/店铺支付金额。", "fillInCompare": true, "displayFormat": "MONEY", "ratio": {"key": "self_sale_paid_order_amt", "name": "(占比)"}}, "not_ump_self_sale_paid_order_amt": {"indicatorAlias": "非营销支付金额", "indicatorDefinition": "统计时间内，非营销活动带来的支付金额。单位：元。", "ratioDefinition": "非营销支付金额占比：统计时间内，非营销支付金额/店铺支付金额。", "fillInCompare": true, "displayFormat": "MONEY"}, "self_sale_placed_order_uv": {"indicatorAlias": "下单人数", "indicatorDefinition": "统计时间内，成功下单的人数（不剔除退款订单），一人多次下单被计为一人。", "displayFormat": "NUMBER"}, "self_sale_paid_order_uv": {"indicatorAlias": "支付人数", "indicatorDefinition": "统计时间内，成功支付的人数（不剔除退款订单，一人多次支付被计为一人）。", "displayFormat": "NUMBER"}, "self_sale_uv_placed_rate": {"indicatorAlias": "访问下单转化率", "indicatorDefinition": "统计时间内，店铺下单人数 / 访客数。", "displayFormat": "PERCENT"}, "self_sale_placed_paid_rate": {"indicatorAlias": "下单支付转化率", "indicatorDefinition": "统计时间内，店铺支付人数 / 下单人数。", "displayFormat": "PERCENT"}, "self_sale_paid_sku_cnt": {"indicatorAlias": "支付件数", "indicatorDefinition": "统计时间内，成功支付的商品件数之和（不剔除退款订单）。", "displayFormat": "NUMBER"}, "sold_goods_cnt": {"indicatorAlias": "动销商品数", "indicatorDefinition": "统计时间内，销量不为 0 的商品数量。", "displayFormat": "NUMBER"}, "customer_price": {"indicatorAlias": "客单价", "indicatorDefinition": "统计时间内，每个客户的平均支付的金额。计算公式：客单价 = 支付金额 ÷ 支付人数", "displayFormat": "MONEY"}, "unit_price": {"indicatorAlias": "笔单价", "indicatorDefinition": "统计时间内，每笔订单的平均支付金额。计算公式：笔单价 = 支付金额 ÷ 支付订单数", "displayFormat": "MONEY"}, "orders_per_capita": {"indicatorAlias": "人均单数", "indicatorDefinition": "统计时间内，每个客户的平均购买订单数。计算公式：人均单数 = 支付订单数 ÷ 支付人数", "displayFormat": "NUMBER"}, "old_paid_order_uv_2year": {"indicatorAlias": "老成交客户数", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户访客数：统计时间内，老客户成功支付的人数（不剔除退款订单），一人多次支付被计为一人。\n老客户支付人数占比 = 老客户支付人数 ÷ 店铺客户总支付人数", "displayFormat": "NUMBER"}, "visit_customer_uv": {"indicatorAlias": "访问客户总数", "indicatorDefinition": "", "displayFormat": "NUMBER"}, "visit_new_customer_uv": {"indicatorAlias": "新客户访客数", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户访客数：统计时间内，新客户访问人数，一人多次访问被计为一人。\n新客户访客数占比=新客户访客数 ÷ 店铺客户访客数", "displayFormat": "NUMBER"}, "visit_old_customer_uv": {"indicatorAlias": "老访问客户数", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户访客数：统计时间内，老客户访问人数，一人多次访问被计为一人。\n老客户访客数占比=老客户访客数 ÷ 店铺客户访客数", "displayFormat": "NUMBER"}, "ump_roi": {"indicatorAlias": "营销支付ROI", "indicatorDefinition": "统计时间内，营销总体投入产出比，营销支付金额 / 营销优惠金额。", "displayFormat": "NUMBER"}, "ump_discount_amt": {"indicatorAlias": "优惠金额", "indicatorDefinition": "统计时间内，通过营销插件活动实际产生的折扣金额之和。单位：元。", "displayFormat": "MONEY"}, "ump_new_uv_paid_rate": {"indicatorAlias": "营销新客户访问支付转化率", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户营销访问支付转化率：统计时间内，店铺新客户营销支付人数 / 新客户营销访客数。", "displayFormat": "PERCENT"}, "ump_old_uv_paid_rate": {"indicatorAlias": "营销老客户访问支付转化率", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户营销访问支付转化率：统统计时间内，店铺老客户营销支付人数 / 老客户营销访客数。", "displayFormat": "PERCENT"}, "self_sale_paid_refunded_rate": {"indicatorAlias": "成功退款率", "indicatorDefinition": "统计时间内，成功退款订单数/支付订单数。以成功退款时间点为准。", "displayFormat": "PERCENT"}, "ump_self_sale_paid_order_cnt": {"indicatorAlias": "营销支付订单数", "indicatorDefinition": "统计时间内，通过营销插件成功支付的订单数。", "ratioDefinition": "营销支付订单数占比：统计时间内，营销支付订单数/店铺支付订单数。", "displayFormat": "NUMBER", "unit": "笔"}, "not_ump_self_sale_paid_order_cnt": {"indicatorAlias": "非营销支付订单数", "indicatorDefinition": "统计时间内，非营销活动带来的支付订单数。", "ratioDefinition": "非营销支付订单数占比：统计时间内，非营销支付订单数/店铺支付订单数。", "displayFormat": "NUMBER", "unit": "笔"}, "new_customer_price": {"indicatorAlias": "新客户客单价", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户客单价：统计时间内，每个新客户的平均支付的金额。计算公式：新客户客单价 = 新客户支付金额 ÷ 新客户支付人数", "displayFormat": "MONEY"}, "ump_new_customer_price": {"indicatorAlias": "新客户客单价", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户营销客单价：统计时间内，每个新客户通过营销活动的平均支付的金额。计算公式：新客户营销客单价 = 新客户营销支付金额 ÷ 新客户营销支付人数", "displayFormat": "MONEY"}, "old_customer_price": {"indicatorAlias": "老客户客单价", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户客单价：统计时间内，每个老客户的平均支付的金额。计算公式：老客户客单价 = 老客户支付金额 ÷ 老客户支付人数", "displayFormat": "MONEY"}, "ump_old_customer_price": {"indicatorAlias": "老客户客单价", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户营销客单价：统计时间内，每个老客户通过营销活动的平均支付的金额。计算公式：老客户营销客单价 = 老客户营销支付金额 ÷ 老客户营销支付人数", "displayFormat": "MONEY"}, "ump_new_self_sale_paid_order_amt": {"indicatorAlias": "新客户营销支付金额", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户营销支付金额：统计时间内，新客户通过营销活动带来的支付金额总和。单位：元。", "ratioDefinition": "新客户营销支付金额占比 = 新客户营销支付金额 / 店铺总营销支付金额", "displayFormat": "MONEY"}, "ump_old_self_sale_paid_order_amt": {"indicatorAlias": "老客户营销支付金额", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户营销支付金额：统计时间内，老客户通过营销活动带来的支付金额总和。单位：元。", "ratioDefinition": "老客户营销支付金额占比 = 老客户营销支付金额 / 店铺总营销支付金额", "displayFormat": "MONEY"}, "ump_new_visit_uv": {"indicatorAlias": "新客户营销访客数", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户营销访客数：统计时间内，新客户通过网店营销插件页面的访问人数，一人多次访问被计为一人。", "ratioDefinition": "新客户营销访客数占比=新客户营销访客数 ÷ 店铺客户的营销访客数", "displayFormat": "NUMBER"}, "ump_old_visit_uv": {"indicatorAlias": "老客户营销访客数", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户营销访客数：统计时间内，老客户通过网店营销插件页面的访问人数，一人多次访问被计为一人。", "ratioDefinition": "老客户营销访客数占比=老客户营销访客数 ÷ 店铺客户的营销访客数", "displayFormat": "NUMBER"}, "new_paid_order_amt_2year": {"indicatorAlias": "新客户支付金额", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户支付金额：统计时间内，新客户成功支付金额。单位：元。\n新客户支付金额占比 = 新客户支付金额 / 店铺总支付金额。", "displayFormat": "MONEY"}, "old_paid_order_amt_2year": {"indicatorAlias": "老客户支付金额", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户支付金额：统计时间内，老客户成功支付金额。单位：元。\n老客户支付金额占比 = 老客户支付金额 / 店铺总支付金额。", "displayFormat": "MONEY"}, "new_self_sale_uv_paid_rate": {"indicatorAlias": "新客户访问支付转化率", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户访问支付转化率：统计时间内，店铺新客户支付人数 / 新客户访客数。", "displayFormat": "PERCENT"}, "old_self_sale_uv_paid_rate": {"indicatorAlias": "老客户访问支付转化率", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户访问支付转化率：统统计时间内，店铺老客户支付人数 / 老客户访客数。", "displayFormat": "PERCENT"}, "new_self_sale_uv_placed_rate": {"indicatorAlias": "新客户访问下单转化率", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户访问下单转化率：统计时间内，店铺新客户下单人数 / 新客户访问人数。", "displayFormat": "PERCENT"}, "old_self_sale_uv_placed_rate": {"indicatorAlias": "老客户访问下单转化率", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户访问下单转化率：统计时间内，店铺老客户下单人数 / 老客户访问人数。", "displayFormat": "PERCENT"}, "new_self_sale_placed_paid_rate": {"indicatorAlias": "新客户下单支付转化率", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新客户下单支付转化率：统计时间内，店铺新客户支付人数 / 新客户下单人数。", "displayFormat": "PERCENT"}, "old_self_sale_placed_paid_rate": {"indicatorAlias": "老客户下单支付转化率", "indicatorDefinition": "老客户：过去2年有购买行为的客户。\n老客户下单支付转化率：统计时间内，店铺老客户支付人数 / 老客户下单人数。", "displayFormat": "PERCENT"}, "new_uv_paid_rate": {"indicatorAlias": "新客访问-支付转化率", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。\n新访客访问下单转化率：统计时间内，通过该渠道进店的新客户下单人数 / 新客户访客数。", "displayFormat": "PERCENT"}, "total_churned_customer_uv": {"indicatorAlias": "累计流失用户数", "indicatorDefinition": "流失用户：最近购买时间再在1年-2年内有购买行为的用户", "displayFormat": "NUMBER"}, "total_churn_risk_customer_uv": {"indicatorAlias": "累计预流失用户数", "indicatorDefinition": "预流失用户：最近购买时间在181-365天的客户数", "displayFormat": "NUMBER"}, "total_dormant_customer_uv": {"indicatorAlias": "累计沉睡用户数", "indicatorDefinition": "沉睡用户：最近购买时间在91-180天的客户数", "displayFormat": "NUMBER"}, "total_loyal_customer_uv": {"indicatorAlias": "累计忠诚用户数", "indicatorDefinition": "忠诚用户：近90天下单且支付了1单及以上的客户数，且91天-2年内有购买行为的用户", "displayFormat": "NUMBER"}, "total_active_customer_uv": {"indicatorAlias": "累计活跃用户数", "indicatorDefinition": "活跃用户：近90天下单且支付了2单及以上的客户数，且91天-2年内无购买行为的用户", "displayFormat": "NUMBER"}, "total_one_order_new_customer_uv": {"indicatorAlias": "累计一单新客户数", "indicatorDefinition": "一单新客：近90天下单且支付了一单的客户数，且91天-2年内无购买行为的用户", "displayFormat": "NUMBER"}, "total_potential_customer_uv": {"indicatorAlias": "累计潜在用户数", "indicatorDefinition": "潜在用户：近90天进店访问，且近2年内未支付的客户数", "displayFormat": "NUMBER"}, "goods_detail_uv_ds": {"indicatorAlias": "商品访客数", "indicatorDefinition": "统计时间内，进入商品详情页的访客数，一人多次访问被计为一人。", "displayFormat": "NUMBER"}, "on_shelf_goods_cnt": {"indicatorAlias": "在架商品数", "indicatorDefinition": "统计时间内，在架商品的数量。", "displayFormat": "NUMBER"}, "old_uv_paid_rate": {"indicatorAlias": "老客访问-支付转化率", "indicatorDefinition": "老客户：过去2年有购买行为的客户。老客户访问支付转化率：统统计时间内，店铺老客户支付人数 / 老客户访客数。", "displayFormat": "PERCENT"}, "pv": {"indicatorAlias": "浏览量", "indicatorDefinition": "统计时间内，商品详情页被访问的次数，一个人在统计时间内访问多次记为多次。", "displayFormat": "NUMBER"}, "add_cart_uv": {"indicatorAlias": "加购人数", "indicatorDefinition": "统计时间内，添加该商品进入购物车的去重人数，一个人在统计时间内多次添加商品到购物车只记为一人（删除购物车，人数不会删除）", "displayFormat": "NUMBER"}, "add_cart_sku_cnt": {"indicatorAlias": "加购商品件数", "indicatorDefinition": "统计时间内，添加该商品进入购物车的件数（以第一次加入购物车的数据为准，删除或者更新加件数据不会变）", "displayFormat": "NUMBER"}, "repurchase_avg_period": {"indicatorAlias": "平均复购周期", "indicatorDefinition": "统计时间内，每个用户（最近一次支付时间-最早一次支付时间）/（支付次数-1）的平均数", "displayFormat": "NUMBER"}, "momRate": {"indicatorAlias": "增幅率", "indicatorDefinition": "增幅率是环比增幅率，环比增幅率=（统计周期支付金额-环比周期支付金额）/环比周期支付金额。环比周期是上一个周期（昨天环比前天、上周环比上上周、上个月环比上上月）。", "displayFormat": "PERCENT"}, "contributionRate": {"indicatorAlias": "变化贡献度", "indicatorDefinition": "变化贡献率=各因素波动值/总波动值，是某一因素对整体指标影响大小的度量，单一维度下，各项因素的贡献率之和为100%", "displayFormat": "PERCENT"}, "diffValue": {"indicatorAlias": "变化量", "indicatorDefinition": "变化量是环比变化量，环比变化量=统计周期支付金额-环比周期支付金额。环比周期是上一个周期（例如：昨天环比前天、上周环比上上周、上个月环比上上月）。", "displayFormat": "NUMBER"}, "is_new_customer": {"indicatorAlias": "新客", "indicatorDefinition": "新客户：过去2年没有购买行为的客户。", "displayFormat": "NUMBER"}, "is_old_customer": {"indicatorAlias": "老客", "indicatorDefinition": "老客户：过去2年有购买行为的客户。", "displayFormat": "NUMBER"}, "is_not_customer": {"indicatorAlias": "非客户", "indicatorDefinition": "非客户：同一天内浏览店铺但是未登录的访客", "displayFormat": "NUMBER"}, "self_sale_refunded_order_amt": {"indicatorAlias": "成功退款金额", "indicatorDefinition": "统计时间内，成功退款的金额。以成功退款时间点为准。单位：元。", "displayFormat": "MONEY"}, "self_sale_refunded_cnt": {"indicatorAlias": "成功退款订单数", "indicatorDefinition": "统计时间内，成功退款的订单数。以成功退款时间点为准。", "displayFormat": "NUMBER"}, "new_fans_uv": {"indicatorAlias": "新增粉丝数", "indicatorDefinition": "筛选时间内，新关注的粉丝去重人数", "displayFormat": "NUMBER"}, "fans_paid_order_uv": {"indicatorAlias": "成交粉丝数", "indicatorDefinition": "筛选时间内，付款成功的微信粉丝数，一人多次付款成功记为一人", "displayFormat": "NUMBER"}, "new_memb_uv": {"indicatorAlias": "新增会员数", "indicatorDefinition": "筛选时间内，通过领取会员卡，新成为会员的客户数量", "displayFormat": "NUMBER"}, "memb_paid_order_uv": {"indicatorAlias": "成交会员数", "indicatorDefinition": "筛选时间内，付款成功的会员数，一人多次付款成功记为一人", "displayFormat": "NUMBER"}}