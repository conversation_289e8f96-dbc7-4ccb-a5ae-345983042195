{"1": {"name": "经营分析报告", "moduleMap": {"0": {"name": "概况", "data": [{"type": "indicator", "data": [{"key": "self_sale_paid_order_amt", "getter": "reportData.self_sale_paid_order_amt"}, {"key": "uv", "getter": "reportData.uv"}, {"key": "self_sale_paid_order_cnt", "getter": "reportData.self_sale_paid_order_cnt"}, {"key": "self_sale_uv_paid_rate", "getter": "reportData.self_sale_uv_paid_rate"}, {"key": "new_paid_order_uv_2year", "getter": "reportData.new_paid_order_uv_2year"}, {"key": "repurchase_uv_ratio", "getter": "reportData.repurchase_uv_ratio"}, {"key": "ump_self_sale_paid_order_amt", "getter": "reportData.ump_self_sale_paid_order_amt"}, {"key": "self_sale_paid_order_uv", "getter": "reportData.self_sale_paid_order_uv", "hidden": true}, {"key": "self_sale_refunded_cnt", "getter": "reportData.self_sale_refunded_cnt"}, {"key": "self_sale_refunded_order_amt", "getter": "reportData.self_sale_refunded_order_amt"}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "1": {"name": "转化率", "data": [{"type": "funnel", "requiredKey": ["uv", "self_sale_placed_order_uv", "self_sale_paid_order_uv", "self_sale_uv_paid_rate", "self_sale_uv_placed_rate", "self_sale_placed_paid_rate"], "data": [{"key": "uv", "getter": "reportData.uv"}, {"key": "self_sale_placed_order_uv", "getter": "reportData.self_sale_placed_order_uv"}, {"key": "self_sale_paid_order_uv", "getter": "reportData.self_sale_paid_order_uv"}, {"key": "self_sale_uv_paid_rate", "getter": "reportData.self_sale_uv_paid_rate"}, {"key": "self_sale_uv_placed_rate", "getter": "reportData.self_sale_uv_placed_rate"}, {"key": "self_sale_placed_paid_rate", "getter": "reportData.self_sale_placed_paid_rate"}], "rule": {"top": {"key": "uv"}, "center": {"key": "self_sale_placed_order_uv"}, "bottom": {"key": "self_sale_paid_order_uv"}}, "name": {"topCenter": "访问下单转化率", "centerBottom": "下单支付转化率", "topBottom": "访问支付转化率"}}, {"type": "indicator", "disabledValue": true, "data": [{"key": "uv", "getter": "reportData.uv"}, {"key": "self_sale_placed_order_uv", "getter": "reportData.self_sale_placed_order_uv"}, {"key": "self_sale_paid_order_uv", "getter": "reportData.self_sale_paid_order_uv"}, {"key": "self_sale_uv_paid_rate", "getter": "reportData.self_sale_uv_paid_rate"}, {"key": "self_sale_uv_placed_rate", "getter": "reportData.self_sale_uv_placed_rate"}, {"key": "self_sale_placed_paid_rate", "getter": "reportData.self_sale_placed_paid_rate"}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "2": {"name": "客单价", "data": [{"type": "indicator", "data": [{"key": "customer_price", "getter": "reportData.customer_price"}, {"key": "unit_price", "getter": "reportData.unit_price"}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "3": {"name": "客户分析", "data": [{"type": "indicator", "data": [{"key": "repurchase_uv_ratio", "getter": "reportData.repurchase_uv_ratio"}, {"key": "orders_per_capita", "getter": "reportData.orders_per_capita"}, {"key": "new_fans_uv", "getter": "reportData.new_fans_uv"}, {"key": "fans_paid_order_uv", "getter": "reportData.fans_paid_order_uv"}, {"key": "new_memb_uv", "getter": "reportData.new_memb_uv"}, {"key": "memb_paid_order_uv", "getter": "reportData.memb_paid_order_uv"}]}, {"type": "compareIndicator", "name": "支付金额", "requiredKey": ["new_paid_order_amt_2year", "old_paid_order_amt_2year"], "data": [{"key": "self_sale_paid_order_amt", "getter": "reportData.self_sale_paid_order_amt"}, {"key": "new_paid_order_amt_2year", "getter": "reportData.new_paid_order_amt_2year"}, {"key": "old_paid_order_amt_2year", "getter": "reportData.old_paid_order_amt_2year"}], "rule": {"base": {"key": "new_paid_order_amt_2year", "name": "新客户"}, "target": {"key": "old_paid_order_amt_2year", "name": "老客户"}}}, {"type": "compareIndicator", "requiredKey": ["visit_new_customer_uv", "visit_old_customer_uv"], "name": "访客数", "data": [{"key": "visit_new_customer_uv", "getter": "reportData.visit_new_customer_uv"}, {"key": "visit_old_customer_uv", "getter": "reportData.visit_old_customer_uv"}], "rule": {"base": {"key": "visit_new_customer_uv", "name": "新客户"}, "target": {"key": "visit_old_customer_uv", "name": "老客户"}}}, {"type": "compareIndicator", "name": "支付人数", "requiredKey": ["new_paid_order_uv_2year", "old_paid_order_uv_2year"], "data": [{"key": "self_sale_paid_order_uv", "getter": "reportData.self_sale_paid_order_uv"}, {"key": "new_paid_order_uv_2year", "getter": "reportData.new_paid_order_uv_2year"}, {"key": "old_paid_order_uv_2year", "getter": "reportData.old_paid_order_uv_2year"}], "rule": {"base": {"key": "new_paid_order_uv_2year", "name": "新客户"}, "target": {"key": "old_paid_order_uv_2year", "name": "老客户"}}}, {"type": "compareIndicatorLine", "name": "客单价", "requiredKey": ["new_customer_price", "old_customer_price"], "data": [{"key": "new_customer_price", "getter": "reportData.new_customer_price"}, {"key": "old_customer_price", "getter": "reportData.old_customer_price"}], "rule": {"base": {"key": "new_customer_price", "name": "新客户"}, "target": {"key": "old_customer_price", "name": "老客户"}}}, {"type": "compareIndicatorLine", "name": "访问支付转化率", "requiredKey": ["new_self_sale_uv_paid_rate", "old_self_sale_uv_paid_rate"], "data": [{"key": "new_self_sale_uv_paid_rate", "getter": "reportData.new_self_sale_uv_paid_rate"}, {"key": "old_self_sale_uv_paid_rate", "getter": "reportData.old_self_sale_uv_paid_rate"}], "rule": {"base": {"key": "new_self_sale_uv_paid_rate", "name": "新客户"}, "target": {"key": "old_self_sale_uv_paid_rate", "name": "老客户"}}}, {"type": "compareIndicatorLine", "name": "访问下单转化率", "requiredKey": ["new_self_sale_uv_placed_rate", "old_self_sale_uv_placed_rate"], "data": [{"key": "new_self_sale_uv_placed_rate", "getter": "reportData.new_self_sale_uv_placed_rate"}, {"key": "old_self_sale_uv_placed_rate", "getter": "reportData.old_self_sale_uv_placed_rate"}], "rule": {"base": {"key": "new_self_sale_uv_placed_rate", "name": "新客户"}, "target": {"key": "old_self_sale_uv_placed_rate", "name": "老客户"}}}, {"type": "compareIndicatorLine", "name": "下单支付转化率", "requiredKey": ["new_self_sale_placed_paid_rate", "old_self_sale_placed_paid_rate"], "data": [{"key": "new_self_sale_placed_paid_rate", "getter": "reportData.new_self_sale_placed_paid_rate"}, {"key": "old_self_sale_placed_paid_rate", "getter": "reportData.old_self_sale_placed_paid_rate"}], "rule": {"base": {"key": "new_self_sale_placed_paid_rate", "name": "新客户"}, "target": {"key": "old_self_sale_placed_paid_rate", "name": "老客户"}}}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "4": {"name": "商品分析", "data": [{"type": "indicator", "data": [{"key": "self_sale_paid_sku_cnt", "getter": "reportData.self_sale_paid_sku_cnt"}, {"key": "sold_goods_cnt", "getter": "reportData.sold_goods_cnt"}]}, {"type": "goodsList", "requiredKey": ["goods_rank", "gmv"], "data": [{"key": "goods_rank", "getter": "reportData.goods_rank"}, {"key": "gmv", "getter": "reportData.top_10_goods_gmv_proportion"}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "5": {"name": "营销分析", "data": [{"type": "indicator", "data": [{"key": "ump_roi", "getter": "reportData.ump_roi"}, {"key": "ump_discount_amt", "getter": "reportData.ump_discount_amt"}]}, {"type": "compareIndicator", "name": "支付金额", "requiredKey": ["ump_self_sale_paid_order_amt", "not_ump_self_sale_paid_order_amt"], "fillInCompare": true, "data": [{"key": "self_sale_paid_order_amt", "getter": "reportData.self_sale_paid_order_amt"}, {"key": "ump_self_sale_paid_order_amt", "getter": "reportData.ump_self_sale_paid_order_amt"}, {"key": "not_ump_self_sale_paid_order_amt", "getter": "reportData.not_ump_self_sale_paid_order_amt"}], "rule": {"base": {"key": "ump_self_sale_paid_order_amt", "name": "营销"}, "target": {"key": "not_ump_self_sale_paid_order_amt", "name": "非营销"}}}, {"type": "compareIndicator", "name": "支付订单数", "requiredKey": ["ump_self_sale_paid_order_cnt", "not_ump_self_sale_paid_order_cnt"], "fillInCompare": true, "data": [{"key": "self_sale_paid_order_cnt", "getter": "reportData.self_sale_paid_order_cnt"}, {"key": "ump_self_sale_paid_order_cnt", "getter": "reportData.ump_self_sale_paid_order_cnt"}, {"key": "not_ump_self_sale_paid_order_cnt", "getter": "reportData.not_ump_self_sale_paid_order_cnt"}], "rule": {"base": {"key": "ump_self_sale_paid_order_cnt", "name": "营销"}, "target": {"key": "not_ump_self_sale_paid_order_cnt", "name": "非营销"}}}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}, {"type": "compareIndicator", "name": "营销支付金额", "requiredKey": ["ump_new_self_sale_paid_order_amt", "ump_old_self_sale_paid_order_amt"], "data": [{"key": "ump_self_sale_paid_order_amt", "getter": "reportData.ump_self_sale_paid_order_amt"}, {"key": "ump_new_self_sale_paid_order_amt", "getter": "reportData.ump_new_self_sale_paid_order_amt"}, {"key": "ump_old_self_sale_paid_order_amt", "getter": "reportData.ump_old_self_sale_paid_order_amt"}], "rule": {"base": {"key": "ump_new_self_sale_paid_order_amt", "name": "新客户"}, "target": {"key": "ump_old_self_sale_paid_order_amt", "name": "老客户"}}}, {"type": "compareIndicator", "name": "营销访客数", "requiredKey": ["ump_new_visit_uv", "ump_old_visit_uv"], "data": [{"key": "ump_new_visit_uv", "getter": "reportData.ump_new_visit_uv"}, {"key": "ump_old_visit_uv", "getter": "reportData.ump_old_visit_uv"}], "rule": {"base": {"key": "ump_new_visit_uv", "name": "新客户"}, "target": {"key": "ump_old_visit_uv", "name": "老客户"}}}, {"type": "compareIndicatorLine", "name": "营销客单价", "requiredKey": ["ump_new_customer_price", "ump_old_customer_price"], "data": [{"key": "ump_new_customer_price", "getter": "reportData.ump_new_customer_price"}, {"key": "ump_old_customer_price", "getter": "reportData.ump_old_customer_price"}], "rule": {"base": {"key": "ump_new_customer_price", "name": "新客户"}, "target": {"key": "ump_old_customer_price", "name": "老客户"}}}, {"type": "compareIndicatorLine", "name": "营销访问支付转化率", "requiredKey": ["ump_new_uv_paid_rate", "ump_old_uv_paid_rate"], "data": [{"key": "ump_new_uv_paid_rate", "getter": "reportData.ump_new_uv_paid_rate"}, {"key": "ump_old_uv_paid_rate", "getter": "reportData.ump_old_uv_paid_rate"}], "rule": {"base": {"key": "ump_new_uv_paid_rate", "name": "新客户"}, "target": {"key": "ump_old_uv_paid_rate", "name": "老客户"}}}, {"type": "text", "data": [{"key": "2", "getter": "suggests['2']"}]}]}, "6": {"name": "退款分析", "data": [{"type": "indicator", "data": [{"key": "self_sale_paid_refunded_rate", "getter": "reportData.self_sale_paid_refunded_rate"}, {"key": "self_sale_refunded_cnt", "getter": "reportData.self_sale_refunded_cnt"}, {"key": "self_sale_refunded_order_amt", "getter": "reportData.self_sale_refunded_order_amt"}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}}}, "2": {"name": "商品销售数据报告", "moduleMap": {"0": {"name": "商品销售数据报告", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "retailTable", "data": [{"key": "retailTable", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}}}, "3": {"name": "GMV渠道影响波动分析", "moduleMap": {"0": {"name": "GMV波动分析报告", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "GmvAnalysisLine", "data": [{"key": "GmvAnalysisLine", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "data.suggests"}]}]}, "1": {"name": "GMV渠道影响波动分析", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "ChannelAnalysisTree", "data": [{"key": "ChannelAnalysisTree", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "data.suggests"}]}]}, "2": {"name": "GMV渠道影响商品波动分析报告", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "ChannelAnalysisTree", "data": [{"key": "ChannelAnalysisTree", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "data.suggests"}]}]}}}, "4": {"name": "推广分析", "moduleMap": {"0": {"name": "推广效果分析", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "promote", "data": [{"key": "1", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "1": {"name": "推广效果分析", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "promote", "data": [{"key": "1", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "2": {"name": "推广效果分析", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "promote", "data": [{"key": "1", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "3": {"name": "推广效果分析", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "promote", "data": [{"key": "1", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}, "4": {"name": "推广效果分析", "icon": "https://img01.yzcdn.cn/upload_files/2023/07/12/FiJQRDDZ7M8zhxG61XPFJQdgxkSv.png", "data": [{"type": "promote", "data": [{"key": "1", "getter": ""}]}, {"type": "text", "data": [{"key": "1", "getter": "suggests['1']"}]}]}}}}