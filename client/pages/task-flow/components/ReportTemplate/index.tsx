import React, { useMemo, useState, useEffect, useRef } from 'react';
import { cloneDeep, get, uniq, set, transform } from 'lodash';
import { BlockLoading, Button, DateRangePicker, Notify, Pop } from 'zent';
import style from './style.m.scss';
import templateLocalData from './localTemplateData/template.json';
import indicatorLocalData from './localTemplateData/indicator.json';
import { IReport, IComponentInfo } from '../../constants/reportTemplate';
import { CancelIcon, CrossIcon, Info16pxIcon, DoneIcon, NomiIcon } from '../../../../svg/index';
import Indicator from './components/Indicator';
import Funnel from './components/Funnel';
import TextComponent from './components/Text';
import GoodsList from './components/GoodsList';
import IndicatorCompare from './components/IndicatorCompare';
import IndicatorCompareLine from './components/IndicatorCompareLine';
import ChannelAnalysisTree from './components/ChannelAnalysisTree';
import GmvAnalysisLine from './components/GmvAnalysisLine';
import GoodsAnalysis from './components/GoodsAnalysis/index';
import CustomIndexAnalysis from './components/CustomIndexAnalysis/index';
import RetailTable from './components/RetailTable';
import PromoteAnalysis from './components/PromoteAnalysis';
import { toPng } from 'html-to-image';
// import { collectSession } from '../../api/report';
// import { messagesAtom } from '../../atoms/chat';
import classNames from 'classnames';
import { TopRight } from '../../../../fns/pop';
import { formatDisplayTime } from '../../../../fns/time';
import TabSelect from '../TabSelect';
import {
  completeCallback,
  exportGoodsSalesReport,
  getBatchPromotionReportData,
  getReportData,
  pollReportData,
  pollRetailReportData,
} from '../../api';
import './zent.scss';
import { useRequest } from 'ahooks';
import { IProgressStatusEnum } from '../../constants/reportTemplate';
import { ReportTemplateTypeWriter } from '../../../../components/TypeWriter';
import { canScroll, isScrolledToBottom } from '../../../../fns/scroll';
import { scrollToBottom } from '../../../../fns/scrollTo';
import { useThrottleFn } from 'ahooks';
import CollectDownloadIcon from 'svg/CollectDownload';
import EditIcon from 'svg/Edit';
import LikeIcon from 'svg/Like';
import UnLikeIcon from 'svg/UnLike';
import { updateCollectState } from 'pages/collect/api';

interface ReportTemplateProps {
  // 在这里定义组件需要的props类型
  data: IReport;
  loggerParams?: object;
  hideEdit?: boolean;
  onReportChanged?: (data: any) => Promise<any>;
  hideHeader?: boolean;
  elementKey?: string;
  contentPartsElemId?: string;
  dataModelElemId?: string;
  messageId?: string;

  sendText?: (params: any) => void;
  onClose?: () => void;
  setContentVisible: (boo: boolean) => void;
  type?: string;
  collectMode?: boolean; // 收藏页模式
  collectInfo?: any; // 收藏上下文信息
  onItemUpdate?: () => void;
  skillName?: string;
}

const getReportTemplateData = (reportId, moduleId) => {
  // @ts-ignore
  const templateMap = __DEBUG__
    ? templateLocalData
    : JSON.parse(localStorage.getItem('reportTemplateData') || '{}');
  return get(templateMap, `${reportId}.moduleMap.${moduleId}.data`);
};

const getIndicatorByKey = key => {
  // @ts-ignore
  const indicatorMap = __DEBUG__
    ? indicatorLocalData
    : JSON.parse(localStorage.getItem('indicatorLocalData') || '{}');
  return indicatorMap?.[key];
};

const formateSuggestsWithTips = (
  suggests: string,
  recommendShortcuts: string[],
  isEdit: boolean,
): string => {
  if (isEdit || !recommendShortcuts?.length) {
    return suggests;
  }
  let formatText = suggests;
  recommendShortcuts.forEach(title => {
    formatText = formatText.replace(
      new RegExp(`${title}(?!span>)`),
      `<span data-shortcut="${title}">${title}</span>`,
    );
  });
  return formatText;
};

const ReportTemplate: React.FC<ReportTemplateProps> = ({
  data = {} as any,
  collectMode = false,
  collectInfo,
  onItemUpdate,
  loggerParams,
  hideEdit = false,
  onReportChanged = () => Promise.resolve(),
  hideHeader = false,
  onClose,
  elementKey,
  contentPartsElemId,
  dataModelElemId,
  messageId,
  setContentVisible,
  type,
  sendText,
  skillName,
}) => {
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [backup, setBackup] = useState<IReport>(cloneDeep(data));
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [subPageCmp, setSubPageCmp] = useState<React.ReactNode>();
  const [chartList, setChartList] = useState([]);
  const [suggestsContent, setSuggestsContent] = useState<string>('');
  const [recommendShortcuts, setRecommendShortcuts] = useState<string[]>([]);
  const [useFlow, setUseFlow] = useState<boolean>(true);
  const [scrollMaskVisible, setScrollMaskVisible] = useState<boolean>(false);

  const [curReportPosData, setCurReportPosData] = useState<any>({});
  const [reportData, setReportData] = useState(data);
  // const [messageList, setMessageList] = useAtom(messagesAtom);
  const [tabs, setTabs] = useState(data.menus || []);
  const [currentModuleId, setCurrentModuleId] = useState(data?.menus?.[0]?.moduleId ?? '');
  const [reportLoading, setReportLoading] = useState(!data.reportData ? true : false);
  const [currentModuleName, setCurrentModuleName] = useState(data?.moduleName ?? '');
  const [isPolling, setIsPolling] = useState<boolean>(false);

  const [reportDataCacheMap, setReportDataCacheMap] = useState({});
  const [currentReportKey, setCurrentReportKey] = useState<string>(
    data.reportId + '-' + data.moduleId,
  );
  const [isLike, setIsLike] = useState<boolean>(collectMode);

  const [currentRangeItem, setCurrentRangeItem] = useState<any>([]);

  const { currentTimeRange } = data;

  const retailTableRef = React.useRef<{ exportExcel: () => Promise<any> }>();

  const { run: pollReport, cancel: cancelPollReport } = useRequest(pollReportData, {
    manual: true,
    pollingInterval: 1000,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 1,
    // @ts-ignore
    onSuccess: ({ result, status }) => {
      if (!data) return;
      /* if (status === IProgressStatusEnum.failed) {
        stopPollReport(1);
      } */
      if (status === IProgressStatusEnum.doing || status === IProgressStatusEnum.init) {
        setIsPolling(true);
        if (result) {
          handleData(result);
          setReportLoading(false);
        }
      }
      if (status === IProgressStatusEnum.done) {
        setIsPolling(false);
        stopPollReport(2);
        handleData(result);
        // 在这里做一个缓存
        setCacheMap(result);
      }
    },
    onError: e => {
      stopPollReport(3);
      Notify.error(e);
    },
  });

  const stopPollReport = e => {
    setReportLoading(false);
    setIsPolling(false);
    cancelPollReport();
  };

  useEffect(() => {
    setContentVisible(!!suggestsContent);
  }, [suggestsContent]);

  useEffect(() => {
    const { currentTimeRange } = data;

    if (!currentTimeRange) {
      return;
    }

    const convertDateFormat = dateArray => {
      return dateArray.map(date => {
        // 将数字转换为字符串并格式化为 YYYY-MM-DD
        const dateStr = date.toString();
        return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;
      });
    };
    const currentRange = convertDateFormat([currentTimeRange.startDay, currentTimeRange.endDay]);

    setCurrentRangeItem(currentRange);
  }, []);

  // 重置最原始的suggestsContent，用于初始化、切换tab获取、取消编辑时设置最原始的建议数据
  const resetSuggestsContent = reportData => {
    const suggestsData = reportTemplateData
      .map(item => {
        const { data } = item;
        return {
          ...item,
          data: data
            .filter(dataItem => {
              return dataItem.getter.includes('suggest');
            })
            .map(dataItem => {
              let value = get(reportData, dataItem.getter, messageData);
              if (value?.isDeleted || value?.value?.isDeleted) value = null;
              return {
                ...dataItem,
                value,
              };
            }),
        };
      })
      .filter(({ data = [], requiredKey = [] } = {}) => {
        const result: Record<string, any> = {};
        (data || []).forEach(({ key, ...res }: { key: string; [key: string]: any }) => {
          result[key] = res;
        });
        return requiredKey.every(key => result[key]?.value) && data.length;
      });
    setSuggestsContent(suggestsData?.[0]?.data[0]?.value);
    const { recommendShortcuts } = reportData?.payload || {};
    setRecommendShortcuts(recommendShortcuts || []);
  };

  const handleData = res => {
    setReportData(res || {});

    // 将报告数据回写到消息内，用于补充上下文
    const { suggests, reportName, moduleName, currentTimeRange, ...rest } = res;
    const { menus } = data;
    /* completeCallback({
      sessionId: curReportPosData.sessionId,
      messageId,
      callbackKey: elementKey,
      payload: {
        shopType: 0,
        contentPartsElemId,
        dataModelElemId,
        suggests: {
          suggests,
          reportName,
          moduleName,
          currentTimeRange,
          menus,
        },
      },
    }); */

    resetSuggestsContent(res);
  };

  const setCacheMap = reportData => {
    setReportDataCacheMap(prev => {
      const cacheMap = cloneDeep(prev);
      cacheMap[currentReportKey] = reportData;
      return cacheMap;
    });
  };

  // 处理tabs
  useEffect(() => {
    const { menus = [], reportId, menuParam } = data;
    const resultMenus = [...menus];

    if (+reportId === 4) {
      resultMenus.push({
        reportId: 4,
        menuName: '报告说明',
        moduleId: 999,
      });
    }

    setTabs(
      resultMenus.map(item => ({
        ...item,
        value: {
          reportId: item.reportId,
          moduleId: item.moduleId,
        },
      })),
    );

    // reportData不存在时表明是周报场景，需要请求数据
    // 当 type 为推广分析报告的时候，需要动态建议，也需要加载数据
    if (!data.data || type === 'promotion_report') {
      // 先去缓存map里取
      if (reportDataCacheMap[data.reportId + '-' + data.moduleId]) {
        const res = reportDataCacheMap[data.reportId + '-' + data.moduleId];
        handleData(res);
        setReportLoading(false);
        return;
      }

      const handler =
        type === 'promotion_report'
          ? getBatchPromotionReportData(menuParam.find(item => item.moduleId === data.moduleId))
          : getReportData({
              reportId: data.reportId,
              moduleId: data.moduleId,
              ...data.param.timeParam,
              dateType: data.param?.dateType,
              id: data?.param?.id,
            });

      handler
        .then(res => {
          const { status, responseId, result } = res;

          /* if (status === IProgressStatusEnum.failed) {
            stopPollReport(4);
          } else */
          if (status === IProgressStatusEnum.done) {
            if (result) {
              setUseFlow(false);
              handleData(result);
              setReportLoading(false);
              setIsPolling(false);
            }
          } else if (status === IProgressStatusEnum.doing || status === IProgressStatusEnum.init) {
            setUseFlow(true);
            setIsPolling(true);
            pollReport({ responseId });
          }
          return;
        })
        .catch(err => {
          setReportLoading(false);
          Notify.error(err);
        });
    } else if (isExcelDownload()) {
      const { requestParams } = data;
      pollRetailReportData({
        ...requestParams,
        salesKdtIds: JSON.stringify(requestParams.salesKdtIds || []),
        orderSources: JSON.stringify(requestParams.orderSources || []),
      }).then(res => {
        const { status, responseId, result } = res;
        if (status === IProgressStatusEnum.done) {
          if (result) {
            handleData(result);
            setReportLoading(false);
            setIsPolling(false);
          }
        } else if (status === IProgressStatusEnum.doing || status === IProgressStatusEnum.init) {
          setIsPolling(true);
          pollReport({ responseId });
        }
        return;
      });
      setReportLoading(false);
      handleData(data);
    } else {
      setReportLoading(false);
      handleData(data);
    }
  }, []);

  useEffect(() => {
    const downloadReportDom = document.querySelector('#report-content-download') as HTMLElement;
    // 将其宽度设置为style.reportWrapper的宽度
    if (downloadReportDom) {
      // 获取style.reportWrapper的宽度
      const reportWrapperDom = document.querySelector('.' + style.reportWrapper) as HTMLElement;
      const width = reportWrapperDom?.offsetWidth + 20 + 323;
      downloadReportDom.style.width = width + 'px';
      downloadReportDom.style.height = 'auto';
    }
  }, [isDownloading]);

  useEffect(() => {
    setIsFavorite(curReportPosData.isFavorite === '1');
    setIsEdit(false);
    // setBackup(cloneDeep(reportData));
  }, [curReportPosData]);

  const momTimeRangeInfo = '环比';
  const yoyTimeRangeInfo = '同比';

  const reportTemplateData: any[] = useMemo(() => {
    return getReportTemplateData(reportData?.reportId, reportData?.moduleId) || [];
  }, [reportData]);

  const messageData = useMemo(() => {
    return isEdit ? backup : reportData!;
  }, [isEdit, reportData, backup]);
  const reportDataList = useMemo(() => {
    const reportDataList = reportTemplateData
      .map(item => {
        const { data } = item;
        return {
          ...item,
          data: data
            .filter(dataItem => {
              return !dataItem.getter.includes('suggest');
            })
            .map(dataItem => {
              let value = get(messageData, dataItem.getter, messageData);
              if (value?.isDeleted || value?.value?.isDeleted) value = null;
              return {
                ...dataItem,
                ...getIndicatorByKey(dataItem.key),
                value,
              };
            }),
        };
      })
      .filter(({ data = [], requiredKey = [] } = {}) => {
        const result: Record<string, any> = {};
        (data || []).forEach(({ key, ...res }: { key: string; [key: string]: any }) => {
          result[key] = res;
        });
        return requiredKey.every(key => result[key]?.value) && data.length;
      });
    return reportDataList;
  }, [reportTemplateData, messageData]);
  // 建议的数据
  const suggestsData = useMemo(() => {
    const suggestsData = reportTemplateData
      ?.map(item => {
        const { data } = item;
        return {
          ...item,
          data: data
            .filter(dataItem => {
              return dataItem.getter.includes('suggest');
            })
            .map(dataItem => {
              let value = get(messageData, dataItem.getter, messageData);
              if (value?.isDeleted || value?.value?.isDeleted) value = null;
              return {
                ...dataItem,
                value,
              };
            }),
        };
      })
      .filter(({ data = [], requiredKey = [] } = {}) => {
        const result: Record<string, any> = {};
        (data || []).forEach(({ key, ...res }: { key: string; [key: string]: any }) => {
          result[key] = res;
        });
        return requiredKey.every(key => result[key]?.value) && data.length;
      });
    return suggestsData;
  }, [reportTemplateData, messageData]);

  const rangeInfo = useMemo(() => {
    if (data.timeRange) {
      data.currentTimeRange = data.timeRange;
    }
    if (!data.currentTimeRange) {
      return [];
    }

    const { currentTimeRange = {} } = data;
    const { momTimeRange = {}, yoyTimeRange = {} } = reportData || {};

    let result = [
      {
        label: '统计',
        value: uniq(
          [currentTimeRange.startDay, currentTimeRange.endDay].map(item =>
            formatDisplayTime(item, currentTimeRange.dateTypeCode),
          ),
        ),
      },
    ];

    if (momTimeRange.startDay && momTimeRange.endDay) {
      result.push({
        label: '环比',
        value: uniq(
          [momTimeRange.startDay, momTimeRange.endDay].map(item =>
            formatDisplayTime(item, momTimeRange.dateTypeCode),
          ),
        ),
      });
    }
    if (yoyTimeRange.startDay && yoyTimeRange.endDay) {
      result.push({
        label: '同比',
        value: uniq(
          [yoyTimeRange.startDay, yoyTimeRange.endDay].map(item =>
            formatDisplayTime(item, yoyTimeRange.dateTypeCode),
          ),
        ),
      });
    }
    return result;
  }, [data, reportData]);

  const downLoadReport = async () => {
    setIsDownloading(true);
    if (subPageCmp) {
      const heightDiv = document.querySelector('#scrollDiv');
      const footerDiv = document.querySelector('#footer-div');
      const reportEle = document.querySelector('#report-body');
      if (reportEle) {
        // @ts-ignore
        heightDiv.style.height = 'auto';
        // @ts-ignore
        footerDiv.style.display = 'none';
        await toPng(reportEle as HTMLElement, { pixelRatio: 3 }).then(imgData => {
          // 将imgData存储到本地
          const a = document.createElement('a');
          a.href = imgData;
          // 下载文件名称
          const reportName = `${data.reportName} （${currentModuleName || data?.moduleName}）`;
          const timeRange = data.currentTimeRange || {};
          const timeStr = uniq(
            [timeRange.startDay, timeRange.endDay].map(item =>
              formatDisplayTime(item, timeRange.dateTypeCode),
            ),
          );
          a.download = `${reportName}（${timeStr.join(' - ')}）.png`;
          a.click();
          setIsDownloading(false);

          // @ts-ignore
          heightDiv.style.height = `calc(720px - 116px)`;
          // @ts-ignore
          footerDiv.style.display = 'block';
        });
      }
      return;
    }
    setTimeout(async () => {
      const reportEle = document.querySelector('#report-content-download');
      const reportBodyEle = reportEle?.querySelector('.' + style['report-body']);
      if (reportEle) {
        // @ts-ignore
        reportBodyEle.style.overflow = 'hidden';

        await toPng(reportEle as HTMLElement, { pixelRatio: 3 }).then(imgData => {
          // 将imgData存储到本地
          const a = document.createElement('a');
          a.href = imgData;
          // 下载文件名称
          const reportName = `${data.reportName} （${currentModuleName || data?.moduleName}）`;
          const timeRange = data.currentTimeRange || {};
          const timeStr = uniq(
            [timeRange.startDay, timeRange.endDay].map(item =>
              formatDisplayTime(item, timeRange.dateTypeCode),
            ),
          );
          a.download = `${reportName}（${timeStr.join(' - ')}）.png`;
          a.click();
          setIsDownloading(false);

          // @ts-ignore
          reportBodyEle.style.overflow = 'visible';
        });
      }
    });
  };

  const downloadExcel = () => {
    retailTableRef.current
      ?.exportExcel()
      .then(res => {
        if (res) {
          Notify.success('导出成功');
          // 跳转页面
          window.open('https://store.youzan.com/erp/report?type=goodsSalesAnalyze');
        }
      })
      .catch(err => {
        Notify.error(err || '导出失败');
      });
  };

  const onClickLike = (collected: boolean): void => {
    // TODO
    const { index, ...restParams } = curReportPosData;
    const { contentPartsElemId, dataModelElemId, isFavorite: newFavorite = '0' } = curReportPosData;
    // collectSession({ msgFavorite: { ...restParams, isFavorite: newFavorite === '1' ? '0' : '1' } })
    //  .then(res => {
    //    const newMsgList = cloneDeep(messageList);
    //    if (index) {
    //      // @ts-ignore
    //      /* newMsgList[index].contentParts[contentPartsElemId].dataModels![dataModelElemId].isFavorite = !!+isFavorite;
    //    newMsgList[index].contentParts[contentPartsElemId!].dataModels![
    //      dataModelElemId!
    //    ].favoriteId = res.contentParts![contentPartsElemId!].dataModels[dataModelElemId!].favoriteId; */
    //      newMsgList[index].contentParts = res.contentParts;
    //      setMessageList(newMsgList);
    //      setIsFavorite(!(newFavorite === '1'));
    //      setCurReportPosData({ ...curReportPosData, isFavorite: newFavorite === '1' ? '0' : '1' });
    //    }
    //  })
    //  .catch(err => {
    //    console.log('err is ', err);
    //  });

    if (collectMode) {
      updateCollectState({
        collected,
        ...collectInfo,
      }).then(() => {
        setIsLike(collected);
        Notify.success(collected ? '收藏成功' : '取消收藏');
        onItemUpdate && onItemUpdate();
      });
    } else {
      // FIXME: 报告没有一个唯一 id 所以只好先给个时间戳代替，带来的问题就是：无法取消收藏
      updateCollectState({
        collected,
        content: JSON.stringify(data),
        type: 3,
        skillName,
        itemId: new Date().getTime(),
      }).then(() => {
        setIsLike(collected);
      });
    }
  };

  const onDataChanged = ({ key, value }) => {
    set(backup, key, value);
    setBackup({ ...backup, key: backup.key ? backup.key + 1 : 1 });
  };
  const deleteComponent = (componentInfo: IComponentInfo) => {
    const { data, requiredKey } = componentInfo;
    const result: any = {};
    (data || []).forEach(({ key, ...res }) => {
      const { value, ...rest } = res;
      result[key] = {
        ...rest,
        ...value,
      };
    });
    requiredKey.forEach(key => {
      result[key] && (result[key].isDeleted = true);
    });
    const newMessageData = cloneDeep(messageData);
    requiredKey.forEach(key => {
      newMessageData.reportData![key] = result[key];
    });
    setBackup(newMessageData);
  };

  const onConfirmReportChange = () => {
    const diffResult = {
      suggests: {},
      reportData: {},
    };
    Object.entries(backup?.reportData || {}).forEach(([itemKey, value]) => {
      const currentKey = `${'reportData'}.${itemKey}`;
      // 验证字段下的isDeleted是否一致
      if (value?.isDeleted !== get(data, currentKey)?.isDeleted) {
        set(diffResult, currentKey, {
          isDeleted: value?.isDeleted,
        });
      }
    });
    Object.entries(backup?.suggests || {}).forEach(([itemKey, value]) => {
      const currentKey = `${'suggests'}['${itemKey}']`;
      // 验证建议是否被编辑
      if (value !== get(data, currentKey)) {
        set(diffResult, currentKey, value);
      }
    });

    const { dataModelElemId, contentPartsElemId, messageId } = curReportPosData;

    return onReportChanged({
      data: { ...backup, diff: diffResult },
      from: reportData,
      options: {
        contentPartsElemId,
        dataModelElemId,
        messageId,
      },
    }).then(() => {
      setReportData(backup);
      setTimeout(() => {
        setIsEdit(false);
      });
    });
    /* updateData({ ...backup, diff: diffResult }).then(() => {
      setReportData(backup);
      setTimeout(() => {
      setIsEdit(false);
      });
    }); */
  };

  const onCancelReportChange = () => {
    setIsEdit(false);
    setBackup(cloneDeep(data));
    // 重置建议数据，因为建议数据现在和报告数据是分开的，所以单独处理
    resetSuggestsContent(reportData);
  };

  const onClosePreview = () => {
    setCurReportPosData({});
    setReportData(null);

    onClose?.();
  };

  const onReportTypeChange = async value => {
    setIsPolling(false);
    cancelPollReport();
    onCancelReportChange();
    setSuggestsContent('');
    const { reportId, currentTimeRange } = reportData;
    const { menuParam = [] } = data;

    setCurrentReportKey(reportId + '-' + value.value);

    const isStaticPanel = +reportId === 4 && value.value === 999;

    setReportLoading(true);
    let res;
    if (reportDataCacheMap[reportId + '-' + value.value] || isStaticPanel) {
      res = reportDataCacheMap[reportId + '-' + value.value];

      setUseFlow(false);
      res && handleData(res);
      setReportLoading(false);
    } else {
      setUseFlow(true);
      const curTab = tabs.find(item => item.value.moduleId === value.value);
      const handler =
        type === 'promotion_report'
          ? getBatchPromotionReportData(menuParam.find(item => item.moduleId === value.value))
          : getReportData({
              reportId,
              moduleId: value.value,
              ...data.param.timeParam,
              dateType: data.param?.dateType,
              id: curTab?.id,
            });

      handler
        .then(res => {
          const { status, responseId, result } = res;

          /* if (status === IProgressStatusEnum.failed) {
            stopPollReport(4);
          } else */
          if (status === IProgressStatusEnum.done) {
            if (result) {
              setUseFlow(false);
              handleData(result);
              setReportLoading(false);
              setIsPolling(false);
            }
          } else if (status === IProgressStatusEnum.doing || status === IProgressStatusEnum.init) {
            setUseFlow(true);
            setIsPolling(true);
            pollReport({ responseId });
          }
          return;
        })
        .catch(err => {
          setReportLoading(false);
          Notify.error(err);
        });
    }
    setCurrentModuleName(value.label);
    setCurrentModuleId(value.value);
  };

  const getIndexAnalysisEvent = ({ reportId, moduleId }) => {
    // 自定义指标下钻
    if (reportId === 3 && moduleId === 1) {
      return openIndexAnalysis;
    }
    // 商品分析
    if (reportId === 3 && moduleId === 2) {
      return openGoodsAnalysis;
    }
  };

  // 打开自定义指标下钻二级页面
  const openIndexAnalysis = ({ params, channelChName }) => {
    /* previewRef.current?.addPage({
      width: 600,
      name: channelChName,
      component: <CustomIndexAnalysis params={params} />,
    }); */
    setSubPageCmp(<CustomIndexAnalysis params={params} />);
  };

  // 打开商品分析二级页面
  const openGoodsAnalysis = ({ params, channelChName }) => {
    /* previewRef.current?.addPage({
      width: 600,
      name: channelChName,
      component: <GoodsAnalysis params={params} />,
    }); */
    setSubPageCmp(<GoodsAnalysis params={params} />);
  };

  const onTreeClickIndex = ({ indexAnalysis, path, timeParam, dateType }) => {
    const params = { indexName: 'self_sale_paid_order_amt', timeParam, dateType };
    path.slice(1).forEach((item, index) => {
      params[`channelLv${index + 1}`] = item.name;
    });

    // 名称为 params中的channelLv1-channelLv2-channelLv3等等 以此类推直到没有
    let name = path[1].title;
    for (let i = 2; i <= 10; i++) {
      if (path[i]) {
        name += `-${path[i].title}`;
      } else {
        break;
      }
    }
    const renderSubPage = getIndexAnalysisEvent({
      reportId: reportData.reportId,
      moduleId: reportData.moduleId,
    });

    renderSubPage && renderSubPage({ params, channelChName: name });
  };

  const controlScrollMaskVisible = () => {
    const elem = document.querySelector('#suggestsScrollArea');
    if (elem) {
      const _canScroll = canScroll(elem);
      const _isScrolledToBottom = isScrolledToBottom(elem);
      setScrollMaskVisible(_canScroll && !_isScrolledToBottom);
    }
  };

  const onSuggestsScroll = e => {
    controlScrollMaskVisible();
  };

  const { run: handleScrollToBottom } = useThrottleFn(
    () => {
      const elem = document.querySelector('#suggestsScrollArea');
      scrollToBottom(elem);
    },
    { wait: 400 },
  );

  const isExcelDownload = () => {
    return reportData?.reportId === 2 && reportData?.moduleId === 0;
  };

  const renderSuggests = (
    props: { customStyle?: any; className?: string; needFlow?: boolean } = {},
  ) => {
    const { customStyle, className, needFlow } = props || {};

    return (
      <div className={`${style.suggestsWrapper} ${className}`} style={customStyle}>
        <div className={style.suggestsHeader}>
          <NomiIcon />
        </div>
        <div className={style.suggestsBody}>
          {/* <BlockLoading loading={reportLoading} className={style.suggestsBody} icon="circle"> */}
          {/* {suggestsData.map((componentItem, index) => { */}
          {/* return ( */}
          <div>
            <svg
              width="28"
              height="28"
              viewBox="0 0 28 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.1"
                d="M27.2051 22.7008H16.825V13.3129L27.2051 5.29883V22.7008ZM11.1007 22.7008H0.796875V13.3129L11.1007 5.29883V22.7008Z"
                fill="black"
              />
            </svg>
          </div>

          <div className={style.scrollArea} id="suggestsScrollArea" onScroll={onSuggestsScroll}>
            {suggestsContent && (
              <>
                {useFlow && needFlow ? (
                  <ReportTemplateTypeWriter
                    key={currentModuleId}
                    isPolling={isPolling}
                    text={typeof suggestsContent === 'string' ? suggestsContent : ''}
                    interval={100}
                    onFinished={controlScrollMaskVisible}
                    onNextChar={handleScrollToBottom}
                  >
                    <TextComponent
                      text={
                        typeof suggestsContent === 'string'
                          ? formateSuggestsWithTips(suggestsContent, recommendShortcuts, isEdit)
                          : ''
                      }
                      isEdit={isEdit}
                      onDataChanged={text => {
                        // 单独修改建议的数据
                        setSuggestsContent(text);
                        onDataChanged({ key: suggestsData?.[0]?.data?.[0]?.key, value: text });
                      }}
                    />
                  </ReportTemplateTypeWriter>
                ) : (
                  <TextComponent
                    text={
                      typeof suggestsContent === 'string'
                        ? formateSuggestsWithTips(suggestsContent, recommendShortcuts, isEdit)
                        : ''
                    }
                    isEdit={isEdit}
                    onDataChanged={text => {
                      // 单独修改建议的数据
                      setSuggestsContent(text);
                      onDataChanged({ key: suggestsData?.[0]?.data?.[0]?.key, value: text });
                    }}
                  />
                )}
              </>
            )}
          </div>
          <div style={{ textAlign: 'right' }}>
            <svg
              width="28"
              height="28"
              viewBox="0 0 28 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.1"
                d="M0.796875 5.29883H11.177V14.6867L0.796875 22.7008V5.29883ZM16.9013 5.29883H27.2051V14.6867L16.9013 22.7008V5.29883Z"
                fill="black"
              />
            </svg>
          </div>
          {/* </BlockLoading> */}

          {scrollMaskVisible && <div className={style.scrollMask}></div>}
        </div>
        <div className={style.suggestsFooter}>
          <p>- 小建议 -</p>
        </div>
      </div>
    );
  };

  const onChangeRange = value => {
    setCurrentRangeItem(value);
  };

  const changeTime = () => {
    sendText?.(`我要查看${currentRangeItem[0]}到${currentRangeItem[1]}的商品销售报告`);
  };

  const targetModuleId = currentModuleId || reportData?.moduleId;

  const isRetailReport = reportData?.reportId === 2 && reportData?.moduleId === 0;

  const renderRangeItem = rangeItem => {
    // 这里写巨恶心的门店报告的特殊逻辑  要求时间能修改，并且发消息到助手
    // 20240822 又tm不加这个逻辑了 草！
    /* if (isRetailReport) {
      return (
      // 时间选择器
      <>
        <DateRangePicker
        className="zent-datepicker-demo"
        value={currentRangeItem}
        onChange={onChangeRange}
        dateSpan={30}
        canClear={[false, true]}
        />
        <Button onClick={changeTime} type="primary" style={{ marginLeft: '4px' }}>
        切换时间
        </Button>
      </>
      );
    } */

    return `${rangeItem.label}${
      rangeItem.value.length === 1 ? '时间' : '周期'
    }:${rangeItem.value?.join(' - ') || ' -'}`;
  };

  // 在这里编写组件的逻辑和渲染内容
  return (
    <div style={{ boxSizing: 'border-box' }}>
      {renderSuggests({
        className: `${suggestsContent ? style.suggestsWrapperShow : style.suggestsWrapperHide}`,
        needFlow: true,
      })}
      <div className={style.reportWrapper}>
        <div
          id="report-body"
          className={classNames(style['report-content'], {
            [style['report-content-bg']]: !hideHeader,
          })}
        >
          {hideHeader ? (
            <div className={style.reportHeaderHide}>
              <div className={style.reportTitle}>
                {data?.reportName}
                {/* ({currentModuleName || data?.moduleName}) */}
              </div>
              <div
                className={`${style['report-range-container']} ${style['finer-border']} ${style['bottom']}`}
              >
                <div className={style['report-range-content']}>
                  {rangeInfo
                    .filter(item => item.label === '统计')
                    .map(rangeItem => (
                      <div key={rangeItem.label} className={style['report-range-item']}>
                        {renderRangeItem(rangeItem)}
                        {!isRetailReport && (
                          <Pop
                            trigger="hover"
                            position={TopRight}
                            content={
                              <div className="jarvis-tips-wrapper">
                                {rangeInfo.map((rangeItem, index) => (
                                  <div key={index} className="tips-time-wrapper">
                                    <div className="tips-time-title">
                                      {rangeItem.label}
                                      {rangeItem.value.length === 1 ? '时间' : '周期'}
                                    </div>
                                    <div className="tips-time-content">
                                      {rangeItem.value?.join(' - ') || ' -'}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            }
                          >
                            <div className={style.svgWrapper}>
                              <Info16pxIcon width="16" height="16" />
                            </div>
                          </Pop>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            </div>
          ) : (
            <div className={style.reportHeader}>
              {!isEdit && (
                <div className={style.closeBtn} onClick={onClosePreview}>
                  <CrossIcon />
                </div>
              )}
              <div className={style.reportTitle}>{data?.reportName}</div>
              <div
                className={`${style['report-range-container']} ${style['finer-border']} ${style['bottom']}`}
              >
                <div className={style['report-range-content']}>
                  {rangeInfo
                    .filter(item => item.label === '统计')
                    .map(rangeItem => (
                      <div key={rangeItem.label} className={style['report-range-item']}>
                        {renderRangeItem(rangeItem)}
                        {!isRetailReport && (
                          <Pop
                            trigger="hover"
                            position={TopRight}
                            content={
                              <div className="jarvis-tips-wrapper">
                                {rangeInfo.map((rangeItem, index) => (
                                  <div key={index} className="tips-time-wrapper">
                                    <div className="tips-time-title">
                                      {rangeItem.label}
                                      {rangeItem.value.length === 1 ? '时间' : '周期'}
                                    </div>
                                    <div className="tips-time-content">
                                      {rangeItem.value?.join(' - ') || ' -'}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            }
                          >
                            <div className={style.svgWrapper}>
                              <Info16pxIcon width="16" height="16" />
                            </div>
                          </Pop>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            </div>
          )}
          {!hideHeader && <div className={style.borderLine}></div>}
          <div
            className={style.reportScroll}
            id={'scrollDiv'}
            style={{
              paddingTop: hideHeader ? '20px' : '12px',
              height: window.innerHeight > 800 ? `calc(720px - 116px)` : `calc(580px - 116px)`,
            }}
          >
            <BlockLoading
              loading={reportLoading}
              className={style.reportBodyContainer}
              icon="circle"
            >
              {tabs.length ? (
                <div className={style.type}>
                  <TabSelect
                    options={tabs.map(item => ({
                      label: item.menuName,
                      value: item.moduleId,
                    }))}
                    defaultValue={data.moduleId}
                    onChange={onReportTypeChange}
                  />
                </div>
              ) : null}
              {!subPageCmp && (
                <div className={style['report-body']}>
                  {reportDataList.map((componentItem, index) => (
                    <div
                      className={`${style['report-content-item']} ${
                        style[`${componentItem.type}-report`]
                      }`}
                      key={index}
                    >
                      {componentItem.type === 'indicator' && (
                        <Indicator
                          data={componentItem.data}
                          disabled-value={componentItem.disabledValue}
                          momTimeRangeInfo={momTimeRangeInfo}
                          yoyTimeRangeInfo={yoyTimeRangeInfo}
                          isEdit={isEdit}
                          onDataChanged={onDataChanged}
                        />
                      )}
                      {componentItem.type === 'funnel' && (
                        <Funnel
                          data={componentItem.data}
                          rule={componentItem.rule}
                          isEdit={isEdit}
                          name={componentItem.name}
                          onDelete={() => deleteComponent(componentItem)}
                        />
                      )}
                      {componentItem.type === 'text' && (
                        <div className={style['report-text-container']}>
                          <TextComponent
                            text={
                              typeof componentItem.data[0].value === 'string'
                                ? componentItem.data[0].value
                                : ''
                            }
                            isEdit={isEdit}
                            onDataChanged={text =>
                              onDataChanged({ key: componentItem.data[0]?.getter, value: text })
                            }
                          />
                        </div>
                      )}
                      {componentItem.type === 'goodsList' && (
                        <GoodsList
                          data={componentItem.data}
                          isEdit={isEdit}
                          onDelete={() => deleteComponent(componentItem)}
                        />
                      )}
                      {componentItem.type === 'compareIndicator' && (
                        <IndicatorCompare
                          data={componentItem.data}
                          rule={componentItem.rule}
                          name={componentItem.name}
                          isEdit={isEdit}
                          fill-in-compare={componentItem.fillInCompare}
                          momTimeRangeInfo={momTimeRangeInfo}
                          yoyTimeRangeInfo={yoyTimeRangeInfo}
                          onDelete={() => deleteComponent(componentItem)}
                        />
                      )}
                      {componentItem.type === 'compareIndicatorLine' && (
                        <IndicatorCompareLine
                          name={componentItem.name}
                          data={componentItem.data}
                          rule={componentItem.rule}
                          isEdit={isEdit}
                          momTimeRangeInfo={momTimeRangeInfo}
                          yoyTimeRangeInfo={yoyTimeRangeInfo}
                          onDelete={() => deleteComponent(componentItem)}
                        />
                      )}
                      {componentItem.type === 'GmvAnalysisLine' && (
                        <GmvAnalysisLine data={componentItem.data} />
                      )}
                      {componentItem.type === 'ChannelAnalysisTree' && (
                        <ChannelAnalysisTree
                          data={componentItem.data}
                          onClickIndex={onTreeClickIndex}
                        />
                      )}
                      {componentItem.type === 'retailTable' && (
                        <RetailTable
                          ref={retailTableRef}
                          currentTimeRange={currentTimeRange}
                          data={componentItem.data}
                        />
                      )}
                      {componentItem.type === 'promote' && (
                        <PromoteAnalysis
                          moduleId={targetModuleId}
                          data={componentItem.data}
                          reportData={data}
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}
              {subPageCmp && <div className={style.subPage}>{subPageCmp}</div>}
            </BlockLoading>

            {/* 推广分析报告不展示下载按钮 */}
            {type === 'promotion_report' ? null : (
              <>
                {isEdit && !isDownloading ? (
                  <div className={style['report-content-footer-edit']}>
                    <div
                      className={style['report-content-footer-right-item']}
                      onClick={onCancelReportChange}
                    >
                      <CancelIcon fill="#4a4a4a" width={'24'} height={'24'} />
                    </div>
                    <div
                      className={style['report-content-footer-right-item']}
                      onClick={() => {
                        onConfirmReportChange().then(() => {
                          downLoadReport();
                        });
                      }}
                    >
                      <CollectDownloadIcon width={18} height={18} />
                    </div>
                  </div>
                ) : (
                  <div className={style['report-content-footer']} id="footer-div">
                    <div className={style['report-content-footer-left']}></div>
                    <div className={style['report-content-footer-right']}>
                      {isExcelDownload() ? (
                        <div className={style.editPopWrapper}>
                          <p
                            className={style['report-content-footer-right-item']}
                            onClick={downloadExcel}
                          >
                            <CollectDownloadIcon />
                          </p>
                        </div>
                      ) : (
                        <div className={style['report-content-footer-right-item']}>
                          <span
                            onClick={() => {
                              setIsEdit(!isEdit);
                              setBackup(cloneDeep(reportData));
                            }}
                          >
                            <EditIcon />
                          </span>
                          {isLike ? (
                            <span onClick={() => onClickLike(false)}>
                              <LikeIcon className={style.likeFillIcon} />
                            </span>
                          ) : (
                            <span onClick={() => onClickLike(true)}>
                              <UnLikeIcon />
                            </span>
                          )}
                          <span onClick={downLoadReport}>
                            <CollectDownloadIcon />
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* 不在页面展示，用于下载图片的元素 */}
        {/* 先改为必定展示了，不然新的树图无法渲染 */}
        <div id="report-content-download" className={style.reportContentDownload}>
          <div
            className={classNames(
              style['report-content'],
              style['report-content-bg'],
              style['report-content-hide'],
            )}
            id="report-content"
          >
            <div className={style.reportHeader}>
              <div className={style.reportTitle}>
                {data?.reportName}({currentModuleName || data?.moduleName})
              </div>
              <div
                className={`${style['report-range-container']} ${style['finer-border']} ${style['bottom']}`}
              >
                <div className={style['report-range-content']}>
                  {rangeInfo
                    .filter(item => item.label === '统计')
                    .map(rangeItem => (
                      <div className={style['report-range-item']} key={rangeItem.label}>
                        {rangeItem.label}
                        {rangeItem.value.length === 1 ? '时间' : '周期'}:
                        {rangeItem.value?.join(' - ') || ' -'}
                        <div className={style.svgWrapper}>
                          <Info16pxIcon width="16" height="16" />
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
            <div className={style.borderLine}></div>
            {!subPageCmp && (
              <div className={style['report-body']}>
                {reportDataList.map((componentItem, index) => (
                  <div
                    className={`${style['report-content-item']} ${
                      style[`${componentItem.type}-report`]
                    }`}
                    key={index}
                  >
                    {componentItem.type === 'indicator' && (
                      <Indicator
                        data={componentItem.data}
                        disabled-value={componentItem.disabledValue}
                        momTimeRangeInfo={momTimeRangeInfo}
                        yoyTimeRangeInfo={yoyTimeRangeInfo}
                        isEdit={isEdit}
                        onDataChanged={onDataChanged}
                      />
                    )}
                    {componentItem.type === 'funnel' && (
                      <Funnel
                        data={componentItem.data}
                        rule={componentItem.rule}
                        isEdit={isEdit}
                        name={componentItem.name}
                        onDelete={() => deleteComponent(componentItem)}
                      />
                    )}
                    {componentItem.type === 'text' && (
                      <div className={style['report-text-container']}>
                        <TextComponent
                          text={
                            typeof componentItem.data[0].value === 'string'
                              ? componentItem.data[0].value
                              : ''
                          }
                          isEdit={isEdit}
                          onDataChanged={text =>
                            onDataChanged({ key: componentItem.data[0]?.getter, value: text })
                          }
                        />
                      </div>
                    )}
                    {componentItem.type === 'goodsList' && (
                      <GoodsList
                        data={componentItem.data}
                        isEdit={isEdit}
                        onDelete={() => deleteComponent(componentItem)}
                      />
                    )}
                    {componentItem.type === 'compareIndicator' && (
                      <IndicatorCompare
                        data={componentItem.data}
                        rule={componentItem.rule}
                        name={componentItem.name}
                        isEdit={isEdit}
                        fill-in-compare={componentItem.fillInCompare}
                        momTimeRangeInfo={momTimeRangeInfo}
                        yoyTimeRangeInfo={yoyTimeRangeInfo}
                        onDelete={() => deleteComponent(componentItem)}
                      />
                    )}
                    {componentItem.type === 'compareIndicatorLine' && (
                      <IndicatorCompareLine
                        name={componentItem.name}
                        data={componentItem.data}
                        rule={componentItem.rule}
                        isEdit={isEdit}
                        momTimeRangeInfo={momTimeRangeInfo}
                        yoyTimeRangeInfo={yoyTimeRangeInfo}
                        onDelete={() => deleteComponent(componentItem)}
                      />
                    )}
                    {componentItem.type === 'GmvAnalysisLine' && (
                      <GmvAnalysisLine data={componentItem.data} />
                    )}
                    {componentItem.type === 'ChannelAnalysisTree' && (
                      <ChannelAnalysisTree
                        data={componentItem.data}
                        onClickIndex={onTreeClickIndex}
                        showComplete={true}
                      />
                    )}
                  </div>
                ))}
              </div>
            )}
            {subPageCmp && <div className={style.subPage}>{subPageCmp}</div>}
          </div>
          {renderSuggests({
            customStyle: { right: 0, left: 'unset' },
            className: `${style.suggestsWrapperDownload}`,
          })}
        </div>
      </div>
    </div>
  );
};

export default ReportTemplate;
