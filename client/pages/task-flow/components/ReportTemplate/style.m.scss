.report-wrapper {
  position: relative;
  overflow: hidden;
  padding: 0 8px;
}

.report-text-container {
  padding: 8px 0;
}

.report-header,
.report-header-hide {
  // border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  position: relative;
  background-color: #fff;
  padding: 4px 8px 16px;

  .report-title {
    color: #333;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: PingFang SC;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    /* 140% */
    letter-spacing: 0.4px;
    margin-bottom: 4px;
  }

  .close-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    cursor: pointer;

    path {
      fill: #999;
    }

    &:hover {
      path {
        fill: #4a4a4a;
      }
    }
  }
}

.report-header-hide {
  background-color: transparent;

  .report-title {
    text-align: center;
  }

  .report-range-item {
    justify-content: center;
  }

  .report-range-content {
    justify-content: center;
  }
}

.border-line {
  width: calc(100% - 16px);
  height: 1px;
  background-color: #eee;
  margin: 0 auto;
}

.collapsed-icon {
  position: absolute;
  right: 0;
  top: 0px;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;

  &.expanded {
    transform: rotate(180deg);
  }
}

.report-range-container {
  position: relative;

  color: #999;
  font-size: 12px;
  font-family: PingFang SC;
  line-height: 20px;

  &.isEdit {
    margin-bottom: 16px;
  }

  .report-range-content {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  & .collapsed {
    height: 20px;
    overflow: hidden;
  }
}

.report-range-item {
  min-width: calc(50% - 5px);
  display: flex;
  align-items: center;
}

.report-scroll {
  background-color: #fff;
  border-radius: 16px;
  display: flex;
  // height: calc(720px - 116px);

  padding-top: 12px;
  padding-left: 8px;
}

.report-body-container {
  display: flex;
  // flex: 1;
  width: 100%;
}

.suggests-wrapper {
  position: absolute;
  top: 0;
  // left: calc(100% + 20px);
  right: 0;
  background-color: #fff;

  box-sizing: border-box;

  transform: translateX(0);
  transition: transform ease-in 0.2s;
  visibility: hidden;

  padding: 20px;
  border-radius: 16px;
  width: 323px;
  // min-height: 720px;
  height: 100%;

  display: flex;
  flex-direction: column;

  .suggests-header {
    display: flex;
    justify-content: center;
  }

  .suggests-body {
    margin-top: 16px;
    // overflow-y: auto;
    height: calc(100% - 24px - 28px - 32px) !important; // 这里因为是给blockLoading的样式，需要强制给高度
    width: 100%;
    display: flex;
    flex-direction: column;

    flex-grow: 1;
    position: relative;

    .scroll-area {
      // height: 508px;
      max-height: calc(100% - 66px);
      // flex: 1;
      overflow-y: auto;
      margin: 11px 0;

    }

    .scroll-mask {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 44px;
      height: 4em;
      background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    }
  }

  .suggests-footer {
    margin-top: 16px;

    p {
      font-size: 14px;
      line-height: 24px;
      color: #999;
      text-align: center;
    }
  }
}

.suggests-wrapper-show {
  animation: show cubic-bezier(0.33, 0.66, 0.66, 1) 0.2s forwards;
}

.suggests-wrapper-hide {
  animation: hide cubic-bezier(0.33, 0.66, 0.66, 1) 0.2s forwards;
}

@keyframes show {
  0% {
    transform: translateX(0);
    visibility: visible;
  }

  100% {
    visibility: visible;
    transform: translateX(343px);
  }
}

/* 定义隐藏动画 */
@keyframes hide {
  0% {
    visibility: visible;
    transform: translateX(343px);
  }

  99% {
    visibility: visible;
    transform: translateX(0);
  }

  100% {
    visibility: hidden;
  }
}

.suggests-wrapper-download {
  position: unset;
  margin-left: 16px;
  min-height: 720px;
  height: auto;
  visibility: visible;

  .suggests-body {
    height: auto !important;

    .scroll-area {
      overflow: visible;
      overflow-y: visible;
      // min-height: 508px;
      height: auto;
    }

    .scroll-mask {
      visibility: hidden;
    }
  }
}

.report-body {
  padding: 0px 8px 36px;
  // padding-top: -12px;
  border-radius: 16px;
  background-color: #fff;
  // max-height: calc(100vh - 40px - 72px - 16px - 24px);
  // height: calc(100% - 80px);
  overflow-y: auto;
  flex: 1;

  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }

  .svg-wrapper {
    display: flex;
    margin-left: 2px;
    cursor: pointer;
    // transform: scale(0.9);
  }
}

.report-body-stright {
  max-height: unset;
  overflow-y: unset;
}

.sub-page {
  // position: absolute;
  width: 100%;
  background: #fff;
  overflow: auto;
  // min-height: 300px;
  // min-height: calc(100% - 80px);
  padding-bottom: 64px;
}

.report-content-download {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  min-height: 720px;

  display: flex;
  height: auto;
}

.report-content {
  // background-color: #fff;
  border-radius: 8px;
  // padding: 24px;
  height: 100%;

  .indicator {
    width: 50%;
    min-width: 155px;
    margin-bottom: 24px;

    &.full-indicator-item {
      width: 100%;
    }

    .title {
      color: #999;
      font-size: 14px;
      margin-bottom: 4px;
      display: inline-block;
    }

    .big-value {
      font-size: 32px;
      line-height: 42px;
      font-weight: bolder;
      display: inline-block;
    }

    .value {
      font-size: 24px;
      line-height: 32px;
      color: #323233;
      font-weight: 700;
      display: inline-block;
    }
  }
}

.report-content-bg {
  background-color: #fff;
  padding-top: 20px;
  padding-bottom: 20px;
  border-radius: 16px;

  width: 100%;
}

.report-content-hide {
  /* position: absolute;
  top: 0;
  left: 0;
  z-index: -1; */
  width: 616px !important;
  min-height: 720px;
  overflow: hidden;
  height: auto;
  // height: 1400px !important;
}

.report-content-footer,
.report-content-footer-edit {
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  background-color: #fff;
  margin-top: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  // border-radius: 16px;
  box-sizing: border-box;

  .report-content-footer-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .report-content-footer-right {
    height: 32px;
    display: flex;
    justify-content: flex-end;

    .report-content-footer-right-item {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 16px;
      gap: 16px;
      cursor: pointer;

      .report-content-footer-right-item-icon {
        width: 24px;
        height: 24px;
        margin-right: 4px;

        svg {
          width: 100%;
          height: 100%;
        }
      }

      &:not(.like) {
        path {
          fill: #999;
        }
      }

      .report-content-footer-right-item-text {
        font-size: 14px;
        color: #999;
      }

      svg {
        padding: 3px;

        &:hover {
          path {
            fill: #4a4a4a;
          }
        }

      }

      .like-fill-icon {

        &:hover {
          path {
            fill: #db2424;
          }
        }

        path {
          fill: #db2424;
        }
      }
    }
  }
}

.report-content-footer-edit {
  justify-content: center;

  .report-content-footer-right-item {
    margin: 0 16px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    text-align: center;

    svg {
      vertical-align: middle;

      path {
        fill: #4a4a4a;
      }
    }

    &:hover {
      background-color: #f0f0f0;
      border-radius: 2px;
    }
  }
}

.report-content-item+.report-content-item {
  margin-top: 16px;
}

.svg-wrapper {
  display: flex;
  margin-left: 2px;
  cursor: pointer;
  // transform: scale(0.9);
}

.edit-pop {
  padding: 0;

  .zent-pop-v2-inner {
    padding: 0;
  }

  .edit-pop-wrapper {
    padding: 4px;

    p {
      width: 78px;
      line-height: 32px;
      border-radius: 2px;
      cursor: pointer;
      text-align: center;

      &:hover {
        background-color: #f8f8f8;
      }
    }
  }
}