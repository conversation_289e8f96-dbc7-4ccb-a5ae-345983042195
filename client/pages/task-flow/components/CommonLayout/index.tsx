import React from 'react';
import styles from './style.m.scss';
import TaskFlowHeader from '../TaskFlowHeader';
import SkillsList from '../SkillsList';
import DataOverview from '../DataOverview';
import LogsGrid from '../LogsGrid';
import Description from '../Description';
import { isWechatMiniStoreAgent } from './tools';
import WxxdAgent from '../WxxdAgent';
import { isEmpty } from 'lodash';

const CommonLayout: React.FC<{ dataOverview: any; agentInfo: any; agentId: number }> = ({
  dataOverview,
  agentId,
  agentInfo,
}) => {
  // const { description, name, type, id, enable } = agentInfo;
  const isWechatXdAgent = isWechatMiniStoreAgent(agentInfo);

  const renderDashboard = ({ tips }: { tips?: React.ReactNode } = {}) => {
    return (
      <div className={styles.taskFlow}>
        <TaskFlowHeader agentInfo={agentInfo} />

        <div className={styles.taskFlowContent}>
          <div className={styles.taskFlowContentLeft}>
            {/* 描述 */}
            <Description agentInfo={agentInfo} tips={tips} />

            <div className={styles.splitLine}></div>

            <SkillsList agentInfo={agentInfo} agentId={agentId} />
          </div>

          <div className={styles.taskFlowContentRight}>
            <div className={styles.taskFlowContentRightTop}>
              <DataOverview data={dataOverview} />
            </div>
            <div className={styles.taskFlowContentRightBottom}>
              <LogsGrid agentId={agentId} agentInfo={agentInfo} isWechatXdAgent={isWechatXdAgent} />
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isEmpty(agentInfo)) {
    return null;
  }

  return isWechatXdAgent ? (
    <WxxdAgent agentId={agentId} dashboard={renderDashboard} />
  ) : (
    renderDashboard()
  );
};

export default CommonLayout;
