@use '../../common.scss' as *;

.task-flow {
    min-height: calc(100vh - 68px - 48px - 28px - 6px);
    display: flex;
    flex-direction: column;

    .task-flow-content {
        margin-top: 28px;
        display: flex;
        justify-content: space-between;
        gap: 12px;
        flex-grow: 1;

        .task-flow-content-left {
            @extend .commonCardTemp;
            width: 52%;
            display: flex;
            flex-direction: column;
            gap: 20px;


            .split-line {
                background-color: #eee;
                width: 100%;
                height: 1px;
            }
        }

        .task-flow-content-right {
            width: 100%;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;

            .task-flow-content-right-top {
                @extend .commonCardTemp;
                padding: 40px;
            }

            .task-flow-content-right-bottom {
                @extend .commonCardTemp;
                flex-grow: 1;
            }
        }
    }
}