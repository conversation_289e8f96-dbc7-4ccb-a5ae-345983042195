import React, { useEffect, useMemo, useState, useRef } from 'react';
import { Button, Drawer, Notify } from 'zent';
import styles from './styles.m.scss';
import { ISkill } from 'pages/task-flow/constants';
import {
  EValueSource,
  IAction,
  IFlow,
  ITask,
  ScheduledTaskField,
  TriggerType,
} from 'pages/work-flow/constant';
import matchMaxWidth from 'fns/matchMaxWidth';
import { useAtom } from 'jotai';
import { saveSkill } from 'pages/task-flow/api/agent';
import { editingActionAtom, editingSeriesActionsAtom, flowAtom } from 'pages/task-flow/atoms';
// TODO: 为什么只有 condition 要从 work-flow 的 atom 中引入呢？ 因为不知道缺了什么，导致迁移过来的 condition 逻辑缺了一块，没有初始值。来不及排查了先这样……
import { editingConditionAtom } from 'pages/work-flow/atoms';
import DrawerContent from '../SubDrawer/DrawerContent';
import ConditionContent from '../ConditionContent';
import ActionSelector from '../ActionSelector';
import ScheduleTaskConfig, { defaultWeekValue } from '../ScheduledTaskConfig';
import { formatCondition, parseJSON } from 'pages/work-flow/utils/flow';
import { pick } from 'lodash';
import TagSelect, { TagSelectField } from '../tagSelect';
import { skillCustomEditComponents } from './SkillCustomEdit';

interface EditSkillDrawerProps {
  visible: boolean;
  onClose: () => void;
  skill: ISkill | null;
  agentId: number;
  refreshSkills?: () => void;
  onlyEdit?: boolean;
  onChange?: (data: any) => void;
}

const DEFAULT_SUBJECT_DETAIL = {
  repeatRule: {
    startTime: '',
    cycleRule: '',
    endTime: '',
    endType: 1 as 0 | 1,
    weekDays: defaultWeekValue,
  },
};

const EditSkillDrawer: React.FC<EditSkillDrawerProps> = ({
  visible,
  onClose,
  skill,
  agentId,
  refreshSkills,
  onlyEdit = false,
  onChange,
}) => {
  // FIXME: 这里巨傻逼 因为 condition 和 action 的逻辑写的太过依赖 atom  导致很难复用，只好接着用 atom
  const [flowData, setFlowData] = useAtom(flowAtom);
  const [editingAction, setEditingAction] = useAtom(editingActionAtom);
  const [editingCondition, setEditingCondition] = useAtom(editingConditionAtom);
  const [editingSeriesActions, setEditingSeriesActions] = useAtom(editingSeriesActionsAtom);

  // 添加一个ref来跟踪drawer是否是刚打开的状态
  const drawerVisibleRef = useRef(false);

  const [customerTags, setCustomerTags] = useState<number[]>([]);

  const [subjectDetail, setSubjectDetail] = useState<ScheduledTaskField>(DEFAULT_SUBJECT_DETAIL);

  const { config, initialized } = skill ?? {};
  const {
    flowTemplate,
    flowRule,
    containsCondition,
    actionSelectable,
    actionList,
    fields,
    values,
  } = config ?? {};

  const flowRuleData = initialized ? flowRule : flowTemplate;
  const { triggerDefine: { name: triggerName } = { name: '' } } = flowTemplate ?? {};
  const { chainRules, triggerDefine } = flowRuleData ?? {};
  const { action = null, seriesActions = [], conditions = null } = chainRules?.[0] ?? {};

  // 使用自定义组件渲染
  const useCustomEdit = skill && skillCustomEditComponents[skill.skillTemplateId];
  const [customFlowRuleData, setCustomFlowRuleData] = useState<any>(flowRuleData);

  useEffect(() => {
    if (!customFlowRuleData) {
      setCustomFlowRuleData(flowRuleData);
    }
  }, [flowRuleData]);

  // 当 drawer 的可见性改变时，记录当前的可见状态
  useEffect(() => {
    // 如果drawer打开，标记为需要初始化
    if (visible) {
      drawerVisibleRef.current = true;
    }
  }, [visible]);

  // 初始化状态
  useEffect(() => {
    // 只有当drawer是刚打开的状态且有数据时才进行初始化
    if (drawerVisibleRef.current) {
      // 初始化seriesActions
      if (JSON.stringify(editingSeriesActions) !== JSON.stringify(seriesActions)) {
        if (seriesActions?.length) {
          setEditingSeriesActions(seriesActions);
        } else {
          setEditingSeriesActions([]);
        }
      }

      // 初始化action
      if (JSON.stringify(editingAction) !== JSON.stringify(action)) {
        setEditingAction(action);
      }

      // 初始化conditions
      if (JSON.stringify(editingCondition) !== JSON.stringify(conditions)) {
        setEditingCondition(conditions);
      }

      // 完成初始化后，重置标志位
      drawerVisibleRef.current = false;
    }
  }, [action, seriesActions, conditions, visible]);

  // 提取公共逻辑为独立函数
  const getFilteredVariables = (actionData: any) => {
    if (!actionData?.variables) return [];

    const hiddenVariables = actionData.variables
      ?.filter(variable => variable.valueSource === EValueSource.RADIO)
      .map(variable => JSON.parse(variable.dynamicEndpointConfig || '{}'))
      .flatMap(config => config.group);

    return actionData.variables.filter(item => !hiddenVariables.includes(item.id));
  };

  // 这段是从 xujiazheng 的表单联动的逻辑挪过来的 client/pages/work-flow/components/CustomDrawer/components/SubDrawer/index.tsx
  useEffect(() => {
    if (!editingAction) {
      return;
    }
    const variables = editingAction?.variables || [];
    variables.forEach(item => {
      const dynamicEndpointConfigObj = parseJSON(item.dynamicEndpointConfig);
      const { dependenciesVariales = [] } = dynamicEndpointConfigObj;
      dependenciesVariales.forEach(verCode => {
        const findVarVo = editingAction.variables.find(item => item.code === verCode);
        if (findVarVo) {
          const findDynamicEndpointConfigObj = parseJSON(findVarVo.dynamicEndpointConfig);
          findDynamicEndpointConfigObj.effectVarialeCodeList = [
            ...(findDynamicEndpointConfigObj.effectVarialeCodeList || []),
            pick(item, ['code', 'valueSource']),
          ];
          findVarVo.dynamicEndpointConfig = JSON.stringify(findDynamicEndpointConfigObj);
        }
      });
    });
  }, [editingAction]);

  const filteredVariableList = useMemo(() => {
    return getFilteredVariables(action);
  }, [action]);

  const filteredSeriesActionVariableList = useMemo(() => {
    if (!seriesActions?.length) return [];
    return seriesActions.map(action => getFilteredVariables(action));
  }, [seriesActions]);

  const handleRadioDefaultValue = (
    variables: any[],
    action: any,
    editingAction: any,
    setEditingAction: (action: any) => void,
    actionIndex = -1,
  ) => {
    // 找到valueSource为5的多选组，给默认值
    const radioVariableList = variables?.filter(item => item.valueSource === EValueSource.RADIO);

    if (radioVariableList && radioVariableList.length) {
      const actionFieldValueMaps4Execute = JSON.parse(
        editingAction?.actionFieldValueMaps4Execute || '{}',
      ) as any;
      const actionFieldValueMaps4Display = JSON.parse(
        editingAction?.actionFieldValueMaps4Display || '{}',
      ) as any;

      radioVariableList.forEach(item => {
        const { group } = JSON.parse(item.dynamicEndpointConfig || '{}');
        const radioGroupVariables = action?.variables.filter(item => group.includes(item.id));

        const defaultValue =
          +JSON.parse(editingAction?.actionFieldValueMaps4Display || '{}')?.[item.code!]?.name ||
          radioGroupVariables?.[0].id;

        const { code } = item;

        actionFieldValueMaps4Execute[code!] = defaultValue;
        actionFieldValueMaps4Display[code!] = { name: defaultValue };
      });

      setEditingAction(prev => {
        return {
          ...prev,
          actionFieldValueMaps4Execute: JSON.stringify(actionFieldValueMaps4Execute),
          actionFieldValueMaps4Display: JSON.stringify(actionFieldValueMaps4Display),
        };
      });
    }
  };

  const onConfirmTagSelect = (selectedTags: number[]) => {
    setCustomerTags(selectedTags);
  };

  useEffect(() => {
    // 使用 Array.from() 将 NodeListOf<Element> 转换为 HTMLElement[]
    const divsArray: HTMLElement[] = Array.from(document.querySelectorAll('.matchMaxWidthDiv'));
    matchMaxWidth(divsArray, 120);

    // 处理 action 的默认值
    handleRadioDefaultValue(filteredVariableList ?? [], action, editingAction, setEditingAction);
    // 处理 seriesActions 的默认值
    handleRadioDefaultValue(
      filteredSeriesActionVariableList ?? [],
      seriesActions,
      editingSeriesActions,
      setEditingSeriesActions,
    );
  }, [filteredVariableList, filteredSeriesActionVariableList]);

  const handleActionChange = (value: number) => {
    setEditingAction(prev => {
      return {
        ...prev,
        ...actionList?.find(item => item.id === value),
      };
    });
  };

  const parseSubjectDetail = (detail: string | undefined): ScheduledTaskField => {
    if (!detail) return DEFAULT_SUBJECT_DETAIL;
    try {
      return JSON.parse(detail) as ScheduledTaskField;
    } catch (error) {
      // 这里的逻辑是  定时任务会有一个可以被解析的 detail，如果解析不出来，就直接给默认值
      return DEFAULT_SUBJECT_DETAIL;
    }
  };

  const renderFields = (fields: any, customerTags: number[]) => {
    if (!fields) return null;
    return fields.map((field: any) => {
      return (
        <>
          <div key={field.id}>{field.name}</div>
          <TagSelectField onConfirm={onConfirmTagSelect} selectedTags={customerTags} />
        </>
      );
    });
  };

  const showActionDrawer = () => {
    if (!action) return false;

    return !(actionSelectable && actionList && !!action?.variables.length);
  };

  const renderFlowForm = (chainRules: ITask[]) => {
    const parsedSubjectDetail = parseSubjectDetail(triggerDefine?.subjectDetail);

    return (
      <>
        {triggerName && (
          <div className={styles.formBlock}>
            <div className={styles.formTitle}>业务场景</div>
            <div className={styles.formContent}>
              <div className={styles.formItemValue}>{triggerName}</div>
            </div>
          </div>
        )}
        {triggerDefine?.code === 'scheduled' && (
          <div className={styles.formBlock}>
            <div className={styles.formTitle}>定时任务</div>
            <ScheduleTaskConfig
              value={parsedSubjectDetail}
              onChange={value => {
                setSubjectDetail(value);
              }}
            />
          </div>
        )}

        {containsCondition && (
          <div className={styles.formBlock}>
            <div className={styles.formTitle}>执行条件</div>
            {/* condition */}
            <ConditionContent />
          </div>
        )}
        {console.log('actionSelectable', actionSelectable, actionList)}
        {actionSelectable && actionList && (
          <div className={styles.formBlock}>
            <div className={styles.formTitle}>选择操作</div>
            <ActionSelector
              actionList={actionList}
              onChange={handleActionChange}
              value={editingAction?.id || action?.id || 0}
            />
          </div>
        )}

        {showActionDrawer() && (
          <div className={styles.formBlock}>
            <div className={styles.formTitle}>执行操作</div>
            {/* action */}
            <DrawerContent
              data={editingAction}
              filteredVariableList={filteredVariableList}
              selectedData={editingAction}
              editingAction={editingAction}
              setEditingAction={setEditingAction}
            />
          </div>
        )}
        {!(actionSelectable && actionList) && !!seriesActions?.length && (
          <div className={styles.formBlock}>
            <div className={styles.formTitle}>执行操作</div>
            {seriesActions.map((seriesAction, index) => {
              return (
                <DrawerContent
                  key={seriesAction.id}
                  data={seriesAction}
                  filteredVariableList={filteredSeriesActionVariableList[index] || []}
                  selectedData={editingSeriesActions[index]}
                  editingAction={editingSeriesActions[index]}
                  setEditingAction={editedSeriesAction => {
                    const newSeriesActions = [...editingSeriesActions];
                    newSeriesActions[index] = editedSeriesAction;
                    setEditingSeriesActions(newSeriesActions);
                  }}
                />
              );
            })}
          </div>
        )}
      </>
    );
  };

  const renderCustomFields = (skill: ISkill | null) => {
    if (skill && useCustomEdit) {
      return useCustomEdit({
        skill,
        customFlowRuleData: customFlowRuleData,
        setCustomFlowRuleData,
      });
    }
  };

  const handleSave = () => {
    const filteredCondition = editingCondition?.map(item => {
      return {
        ...item,
        expressions: item.expressions.filter(expression => {
          return expression.variable.name;
        }),
      };
    });
    const { flowTemplate, actionList, ...rest } = config ?? {};
    let newConfig: any = {};
    if (useCustomEdit) {
      newConfig = {
        ...rest,
        flowRule: customFlowRuleData,
      };
    } else {
      if (fields) {
        newConfig = {
          fields,
          values: {
            customerTags,
          },
        };
      } else {
        newConfig = {
          ...rest,
          flowRule: {
            chainRules: [
              {
                action: editingAction!,
                seriesActions: editingSeriesActions!,
                conditions: filteredCondition ?? [],
              },
            ],
          },
        };
        if (triggerDefine?.code === 'scheduled') {
          newConfig.flowRule.triggerDefine = {
            ...triggerDefine,
            subjectDetail: JSON.stringify(subjectDetail),
          };
        }
      }
    }

    if (newConfig.flowRule) {
      newConfig.flowRule.chainRules = formatCondition(newConfig?.flowRule?.chainRules);
    }

    const params = {
      skillId: skill?.id,
      agentId,
      enable: true,
      config: JSON.stringify(newConfig),
    };

    if (onlyEdit) {
      onChange?.({
        apiData: params,
        skill: {
          ...skill,
          config: newConfig,
        },
      });
      onClose();
    } else {
      saveSkill(params)
        .then(res => {
          Notify.success('保存成功');
          onClose();
        })
        .then(() => {
          refreshSkills?.();
        })
        .catch(error => {
          Notify.error(error);
        });
    }
  };

  return (
    <Drawer
      title={skill?.name}
      visible={visible}
      width={692}
      placement="right"
      onClose={onClose}
      maskClosable
      footer={
        <div className={styles.editSkillDrawerFooter}>
          <Button type="primary" onClick={handleSave}>
            保存并启用
          </Button>
          <Button onClick={onClose}>取消</Button>
        </div>
      }
    >
      <div className={styles.formContainer}>
        {/* 20250327 微信小店托管使用自定义组件渲染 */}
        {useCustomEdit ? (
          renderCustomFields(skill)
        ) : (
          <>
            {/* 20241217 小店托管新增的不通过自动任务的 chainRules 结构来渲染的选择 */}
            {renderFields(fields, values?.customerTags)}
            {renderFlowForm(chainRules!)}
          </>
        )}
      </div>
    </Drawer>
  );
};

export default EditSkillDrawer;
