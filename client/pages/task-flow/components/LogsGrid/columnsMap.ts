import { EAgentType, ILogColumn } from "pages/task-flow/constants";

export const columnsMap: Record<string, ILogColumn[]> = {
    [EAgentType.TASK_FLOW]: [
        {
            title: '执行时间',
            width: 250,
            name: 'executeTime',
        },
        {
            title: '执行状态',
            width: 150,
            name: 'executeStatus',
        },
        {
            title: '执行内容',
            width: 200,
            name: 'executeContent',
        },
        {
            title: '执行结果',
            name: 'executeResult',
        },
    ],
    [EAgentType.BUSINESS_REPORT]: [
        {
            title: '报告',
            name: 'reportTitle',
        },
        {
            title: '报告类型',
            name: 'reportType',
        },
        {
            title: '报告概要',
            name: 'reportSummary',
        },
        {
            title: '操作',
            name: 'operation',
        }
    ],
}
