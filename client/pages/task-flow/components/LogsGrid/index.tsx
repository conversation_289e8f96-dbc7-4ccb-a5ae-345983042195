import React, { useEffect, useState } from 'react';
import { Grid, IGridOnChangeConfig, openDialog } from 'zent';
import { EAgentType, ILog, ILogColumn } from '../../constants';
import styles from './styles.m.scss';
import ReportTemplate from '../ReportTemplate';
import { useReportDialog } from '../../hooks/useReportDialog';
import { pageExecuteRecords } from 'pages/task-flow/api/agent';
import { columnsMap } from './columnsMap';
import OpenWeeklyReportButton from '../WxxdAgent/components/WeeklyReportDialog/OpenWeeklyReportButton';
import { SkillTemplateIdMap } from '../WxxdAgent/constant';

interface LogsGridProps {
  agentId: number;
  agentInfo: { type: EAgentType; name: string };
  isWechatXdAgent: boolean;
}

interface IPageInfo {
  page: number;
  pageSize: number;
  totalCount: number;
}

const LogsGrid: React.FC<LogsGridProps> = ({ agentId, agentInfo, isWechatXdAgent }) => {
  // const [columns, setColumns] = useState<ILogColumn[]>([]);
  const { type, name: skillName } = agentInfo;
  const columns = columnsMap[type];
  const [logs, setLogs] = useState<ILog[]>([]);
  const [pageInfo, setPageInfo] = useState<IPageInfo>({
    page: 1,
    pageSize: 10,
    totalCount: 0,
  });

  const { openReportDialog } = useReportDialog(false, skillName);

  useEffect(() => {
    pageExecuteRecords({
      myAgentId: agentId,
      page: pageInfo.page,
      pageSize: pageInfo.pageSize,
    }).then(res => {
      console.log(res);
      const { data, page, totalCount } = res;
      setLogs(
        data.map((item: any) => ({
          ...item,
          executeStatus: isWechatXdAgent ? '已执行' : item.executeStatus,
          extra: item.extra ? JSON.parse(item.extra) : {},
        })),
      );
      setPageInfo({
        page,
        pageSize: pageInfo.pageSize,
        totalCount,
      });
    });
  }, [pageInfo.page]);

  const formattedColumns = columns?.map(column => {
    if (column.name === 'operation') {
      // @ts-ignore
      column.bodyRender = (value, record) => {
        const { type, params } = value.operation || {};
        if (type === 'business_report') {
          return (
            <div>
              <a
                onClick={() => {
                  openReportDialog(params);
                }}
              >
                查看报告
              </a>
            </div>
          );
        }
        return '';
      };
    } else if (isWechatXdAgent && column.name === 'executeResult') {
      // @ts-ignore
      column.bodyRender = (value, record) => {
        const { weekStr, skillTemplateId } = value.extra || {};
        if (skillTemplateId === SkillTemplateIdMap.WeeklyReport) {
          return <OpenWeeklyReportButton weekStr={weekStr} executeTime={value.executeTime} />;
        }
        return value.executeResult;
      };
    }
    return column;
  });

  const onChange = (conf: IGridOnChangeConfig) => {
    setPageInfo({
      page: conf.current || 1,
      pageSize: conf.pageSize || 10,
      totalCount: pageInfo.totalCount,
    });
  };

  return (
    <div className={styles.logs}>
      <Grid
        columns={formattedColumns}
        datasets={logs}
        onChange={onChange}
        pageInfo={{
          current: pageInfo.page,
          pageSize: pageInfo.pageSize,
          total: pageInfo.totalCount,
        }}
      />
    </div>
  );
};

export default LogsGrid;
