import { IActionItem } from "pages/work-flow/constant";
import { Pop, Radio, RadioGroup } from "zent";
import styles from './styles.m.scss';

interface ActionSelectorProps {
    actionList: IActionItem[];
    onChange: (value: any) => void;
    value: number;
}

const ActionSelector: React.FC<ActionSelectorProps> = ({ actionList, onChange, value }) => {
    return <RadioGroup 
        onChange={(e) => onChange(e.target.value)} 
        value={value}
        className={styles.actionSelector}
    >
        {actionList.map(action => {
            if (action.canUse) {
                return (
                    <Radio 
                        key={action.id}
                        value={action.id}
                    >
                        {action.name}
                    </Radio>
                );
            }

            return (
                <Pop
                    key={action.id}
                    trigger="hover" 
                    content={action.unusableReason}
                >
                    <Radio 
                        value={action.id}
                        disabled
                    >
                        {action.name}
                    </Radio>
                </Pop>
            );
        })}
    </RadioGroup>
}

export default ActionSelector;