import { closeDialog, openDialog } from 'zent';
import ReportTemplate from '../components/ReportTemplate';
import styles from '../components/LogsGrid/styles.m.scss';

const DIALOG_ID = 'report-dialog';

export const useReportDialog = (collectMode = false, skillName = '') => {
  console.log('skillName', skillName);
  const openReportDialog = (data: any, collectInfo?: any, onItemUpdate?: () => void) => {
    openDialog({
      closeBtn: false,
      dialogId: DIALOG_ID,
      style: {
        width: 648,
        // height: 720
      },
      className: styles['report-dialog'],
      children: (
        <ReportTemplate
          collectMode={collectMode}
          collectInfo={collectInfo}
          onItemUpdate={onItemUpdate}
          data={data}
          setContentVisible={() => {}}
          skillName={skillName}
          onClose={() => {
            closeDialog(DIALOG_ID);
          }}
        />
      ),
    });
  };

  return {
    openReportDialog,
  };
};
