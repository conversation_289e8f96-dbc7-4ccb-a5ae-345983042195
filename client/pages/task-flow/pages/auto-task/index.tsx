import { getAgentDetail, querySkills } from 'pages/task-flow/api/agent';
import CommonLayout from 'pages/task-flow/components/CommonLayout';
import { mockData } from 'pages/task-flow/mockData';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

const AutoTask: React.FC = () => {
    const { agentId } = useParams();

    const [dataOverview, setDataOverview] = useState<{ title: string; content: string }[]>([]);
    const [agentInfo, setAgentInfo] = useState<any>({});


    useEffect(() => {

        getAgentDetail({ myAgentId: Number(agentId) }).then(res => {
            const { agentInfo, dataItems } = res;
            setDataOverview(dataItems);
            setAgentInfo(agentInfo);
        });


    }, []);

    return (
        <div>
            <CommonLayout agentInfo={agentInfo} dataOverview={dataOverview} agentId={Number(agentId)} />
        </div>
    );
};

export default AutoTask;
