import ajax from '@youzan/pc-ajax';
import { yzLocation } from '@youzan/url-utils';
import { combineRequestAndParams } from '../../../fns/combineRequest';
import min from 'lodash/min';

export * from './analysis';
export * from './help';
export * from './indexV2';
export * from './session';
export * from './text-generation';

import { apiPrefix } from '../../../constants/common';
import { Notify } from 'zent';

// 预发环境下所有请求追加上当前url上的get请求参数
// @ts-ignore
window?._global?.nodeEnv === 'pre' || __DEBUG__;
// @ts-ignore
ajax.interceptors.request.use(
  config => {
    const search = window.location.search;
    const currentPageParamsIterator = new URLSearchParams(search);
    const currentPageParams = {};
    for (const [key, value] of currentPageParamsIterator) {
      currentPageParams[key] = value;
    }
    const isDebug = currentPageParams['debug'];
    const isGet = config.method === 'get';
    const isFromJarvis = config?.url?.includes?.('/api/zan-ai');
    if (isDebug && isGet && isFromJarvis) {
      const rawParams = config.params;
      // 合并参数
      const totalParams = Object.assign({}, rawParams, currentPageParams);

      config.params = totalParams;
    } else if (isDebug && !isGet) {
      // Notify.warn('debug模式只允许Get请求！');
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

export const getHistory = (params: any = {}, prefix = '') => {
  const size = min([99, params?.size || 10]);
  return ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/chatHistory.json`,
    method: 'get',
    contentType: 'application/json',
    data: {
      ...params,
      size,
    },
    withCredentials: true,
  }).catch(() => {});
};

export const fetchRecommendData = (params: any = {}) => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/getHelperConfig.json',
    method: 'get',
    contentType: 'application/json',
    data: {
      ...params,
    },
    withCredentials: true,
  });
};

const doQueryByIdList = function(idList: number[]): Promise<any[]> {
  return ajax({
    method: 'GET',
    url: '/v4/statcenter/analytics-search/getIndicatorData.json',
    contentType: 'application/json',
    data: { idList: JSON.stringify(idList) },
    withCredentials: true,
  });
};

export const getIndicatorList = combineRequestAndParams(doQueryByIdList, 'id');

export const feedback = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/feedback.json',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

export const feedbackV2 = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/feedbackV2.json',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

export const getChatState = data =>
  ajax({
    url: apiPrefix + '/zan-ai/ai-helper/getChatState',
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

export const cacheChatState = data =>
  ajax({
    url: apiPrefix + '/zan-ai/ai-helper/cacheChatState',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

// 创建代理session
export const proxyChat = data =>
  ajax({
    url: apiPrefix + '/zan-ai/ai-helper/proxy/chat',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

// 轮训接口
export const poll = (data, prefix = '') =>
  ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/poll`,
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

export const chatAsync = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/chatAsync',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

export const getSkillTrees = data =>
  ajax({
    url: apiPrefix + '/zan-ai/ai-helper/getSkillTrees',
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

export const getNavSpace = (data, prefix = '') =>
  ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/getNavSpace`,
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

// 公用选择器搜索接口 searchData
export const searchData = async (params: any) => {
  return ajax({
    url: apiPrefix + '/zan-ai/flow/searchData',
    method: 'get',
    data: params,
    contentType: 'application/json',
    withCredentials: true,
  });
};
