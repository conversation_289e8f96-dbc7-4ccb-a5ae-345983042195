import ajax from '@youzan/pc-ajax';
import { apiPrefix } from '../../../constants/common';

export const getGoodsList = data =>
  ajax({
    url: apiPrefix + '/zan-ai/getGoodsList.json',
    method: 'get',
    data,
    withCredentials: true,
  });

// 查询微页面列表
export const queryPageList = data =>
  ajax({
    url: apiPrefix + '/zan-ai/queryPageList.json',
    method: 'get',
    data,
    withCredentials: true,
  });

export const queryPageDetail = data =>
  ajax({
    url: apiPrefix + '/zan-ai/queryPageDetail.json',
    method: 'get',
    data,
    withCredentials: true,
  });

export const queryPageDetailByAlias = data =>
  ajax({
    url: apiPrefix + '/zan-ai/queryPageDetailByAlias.json',
    method: 'get',
    data,
    withCredentials: true,
  });

export const queryOnlineShopGoodsList = data =>
  ajax({
    url: apiPrefix + '/zan-ai/queryOnlineShopGoodsList.json',
    method: 'get',
    data,
    withCredentials: true,
  });

// 获取商品的类目是否符合条件
export const getGoodsCategoryIsRegular = (data: { goodsId: number; goodsTitle?: string }) =>
  ajax({
    url: apiPrefix + '/zan-ai/getGoodsCategoryIsRegular.json',
    method: 'get',
    data,
    withCredentials: true,
  });

export const fillResultForMessage = data =>
  ajax({
    url: apiPrefix + '/zan-ai/ai-helper/fillResultForMessage',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });

export const getTextSceneConfig = data =>
  ajax({
    url: apiPrefix + '/zan-ai/ai-helper/getTextSceneConfig',
    method: 'get',
    data,
    withCredentials: true,
  });

export const editContent = data =>
  ajax({
    url: apiPrefix + '/zan-ai/editContent.json',
    method: 'post',
    data,
    withCredentials: true,
  });

export const getGoodsDetail = data =>
  ajax({
    url: apiPrefix + '/zan-ai/queryGoodsDetail.json',
    method: 'get',
    data,
    withCredentials: true,
  });
