import ajax from '@youzan/pc-ajax';

import { apiPrefix } from '../../../constants/common';

/** NOTE: 创建新会话 */
export const createSession = (data, prefix = '') => {
  return ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/session/createSession`,
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 查询历史会话列表 */
export const getSessionHistoryList = (data, prefix = '') => {
  return ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/session/sessionHistory`,
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 获取当前会话的未读消息 */
export const getSessionUnreadMsg = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/getUnreadMsg',
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 刷新当前会话的已读时间戳 */
export const flushSessionReadAt = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/flushSessionReadAt',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 指定会话置顶 */
export const toTopSession = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/toTop',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 指定会话置顶 */
export const cancelTopSession = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/cancelTop',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 指定会话删除 */
export const removeSession = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/doDel',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 修改会话标题 */
export const updateSessionTitle = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/updateTitle',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 助手未读消息 */
export const getGlobalUnreadCnt = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/session/getGlobalUnreadCnt',
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

/** NOTE: 获取当前店铺下的最近一条 session */
export const getLastSession = (data, prefix = '') => {
  return ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/session/getLastSession`,
    method: 'get',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};
