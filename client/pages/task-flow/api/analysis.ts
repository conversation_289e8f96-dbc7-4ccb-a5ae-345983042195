import ajax from '@youzan/pc-ajax';
import { apiPrefix } from '../../../constants/common';

export const userAnalysisIndex = params => {
  return ajax({
    url: `${apiPrefix}/zan-ai/chat-analysis/userAnalysisIndex.json`,
    method: 'POST', // 明确指定 POST 方法
    data: params,
    withCredentials: true,
  });
};

export const goodsAnalysis = params => {
  return ajax({
    url: `${apiPrefix}/zan-ai/chat-analysis/goodsAnalysis.json`,
    method: 'POST', // 明确指定 POST 方法
    data: params,
    withCredentials: true,
  });
};

export const getReportTemlateData = () => {
  return ajax({
    url: `${apiPrefix}/zan-ai/ai-helper/getTemplateData`,
    withCredentials: true,
  });
};

interface IExportData {
  excelData: string;
}

// 导出excel
export const exportExcel = (data: IExportData) => {
  return ajax({
    url: `${apiPrefix}/zan-ai/ai-helper/exportExcel`,
    method: 'GET',
    withCredentials: true,
    data,
  });
};
