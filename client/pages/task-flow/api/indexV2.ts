import ajax from '@youzan/pc-ajax';
import { apiPrefix } from '../../../constants/common';

export const chatAsyncTemp = (data, prefix = '') => {
  return ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/chatAsyncTemp`,
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

export const reGenerate = (data, prefix = '') => {
  return ajax({
    url: apiPrefix + `/zan-ai/ai-helper${prefix}/reGenerate.json`,
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

export const completeCallback = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/completeCallback',
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

// 因为要支持报告数据的流式传输，所以需要把请求和轮询拆出来
export const getReportData = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/getReportDataNew',
    method: 'get',
    data,
    withCredentials: true,
  });
};

// 因为要支持报告数据的流式传输，所以需要把请求和轮询拆出来
export const getBatchPromotionReportData = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/ai-helper/getBatchPromotionReportData',
    method: 'get',
    data: {
      params: JSON.stringify(data),
    },
    withCredentials: true,
  });
};

// 单独拆除轮询接口
export const pollReportData = data => {
  return ajax({
    url: apiPrefix + '/zan-ai/getBatchResponse.json',
    method: 'get',
    data,
    withCredentials: true,
  });
};

// 门店报告表格数据
export const getRetailReportTableData = data => {
  return ajax({
    url: `${apiPrefix}/zan-ai/ai-helper/getReportTableData`,
    method: 'get',
    data,
    withCredentials: true,
  });
};

// 导出门店表格excel
export const exportGoodsSalesReport = data => {
  return ajax({
    url: `${apiPrefix}/zan-ai/ai-helper/exportGoodsSalesReport`,
    method: 'post',
    data,
    contentType: 'application/json',
    withCredentials: true,
  });
};

// 获取表格的经营建议
export const pollRetailReportData = data => {
  return ajax({
    url: `${apiPrefix}/zan-ai/ai-helper/getBatchRetailReportData`,
    method: 'get',
    data,
    withCredentials: true,
  });
};
