import { IFlow } from 'pages/work-flow/constant';
import ajax from 'zan-pc-ajax';

const apiPrefix = '/v4/jiawo/api/task-flow';

interface ISaveSkillParams {
  skillId: number;
  agentId: number;
  enable: boolean;
  config: {
    flowTemplate: IFlow;
  };
}

export const querySkills = data => ajax(`${apiPrefix}/querySkills`, { data, method: 'get' });

export const getAgentDetail = data =>
  ajax(`${apiPrefix}/getMyAgentDetail`, { data, method: 'get' });

export const pageExecuteRecords = data =>
  ajax(`${apiPrefix}/pageExecuteRecords`, { data, method: 'get' });

export const saveSkill = data => ajax(`${apiPrefix}/saveSkill`, { data, method: 'post' });
