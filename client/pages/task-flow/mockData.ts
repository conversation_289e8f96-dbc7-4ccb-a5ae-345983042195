export const mockData = {
    skills: [
        {
            description: '当实物商品商品库存小于10时，发送消息提醒给小黑',
            name: '商品库存监控',
            enable: true,
            type: 'inventory',
            id: '1',
        },
        {
            description: '当收到新订单时,自动进行订单审核和发货处理', 
            name: '订单自动处理',
            enable: false,
            type: 'order',
            id: '2',
        },
        {
            description: '自动回复用户常见问题,提高客服效率',
            name: '客服消息回复', 
            enable: true,
            type: 'customer_service',
            id: '3',
        }
    ],
    data: [
        {
            title: '运行时长',
            value: '12小时',
        },
        {
            title: '执行次数',
            value: '128次',
        },
        {
            title: '成功率',
            value: '99.2%',
        }
    ],
    logColumns: [
        {
            title: '执行时间',
            name: 'executeTime',
            width: 180,
        },
        {
            title: '执行状态',
            name: 'status',
            width: 100,
        },
        {
            title: '执行内容',
            name: 'content',
            width: 200,
        },
        {
            title: '执行结果',
            name: 'result',
            width: 150,
        },
        {
            title: '操作',
            name: 'operation',
            width: 100,
        }
    ],
    logs: [
        {
            executeTime: '2024-01-10 12:00:00',
            status: '成功',
            content: '检测商品库存',
            result: '库存充足'
        },
        {
            executeTime: '2024-01-10 11:30:00',
            status: '成功', 
            content: '自动回复客服消息',
            result: '已回复5条消息'
        },
        {
            executeTime: '2024-01-10 11:00:00',
            status: '失败',
            content: '订单自动处理',
            result: '系统异常'
        },
        {
            executeTime: '2024-01-10 10:30:00',
            status: '成功',
            content: '检测商品库存',
            result: '库存不足,已通知'
        }
    ]
};