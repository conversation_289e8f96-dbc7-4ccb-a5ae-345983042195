{
  "compilerOptions": {
    "target": "es6",
    "module": "esnext",
    "lib": ["dom", "es2017", "es2018.promise"],
    "jsx": "preserve",
    "sourceMap": true,
    "strict": true,
    "noImplicitAny": false,
    "allowJs": true,
    "checkJs": false,
    "allowSyntheticDefaultImports": true,
    "alwaysStrict": true,
    "forceConsistentCasingInFileNames": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "noEmitHelpers": true,
    "importHelpers": true,
    "experimentalDecorators": true,
    "baseUrl": "./",
    "skipLibCheck": true,
    "paths": {
      "fns/*": ["./fns/*"],
      "utils/*": ["./utils/*"],
      "pages/*": ["./pages/*"],
      "components/*": ["./components/*"],
      "constants/*": ["./constants/*"],
      "shared/*": ["./shared/*"],
      "definitions/*": ["../definitions/*"],
      "common/*": ["./common/*"],
      "api/*": ["./api/*"],
    },
    "outDir": "../static",
    "types": ["node", "sass"]
  },
  "exclude": ["./webpack", "./sass", "./.cache-loader", "./node_modules", "./dist", "./shared"],
  "include": [
    ".",
    "../app/global.d.ts",
    "../node_modules/@qima-inc/wsc-pc-base/index.d.ts",
    "./node_modules/zan-utils/typings/index.d.ts",
    "./node_modules/zent/typings/index.d.ts",
    "../definitions"
  ]
}
