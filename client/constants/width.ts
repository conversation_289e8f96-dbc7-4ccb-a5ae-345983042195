import isNumber from 'lodash/isNumber';

const defaultWidth = 320;
/** NOTE: 助手宽度 */
export const getPageWidth = (cssWidth?: string) => {
  const stringWidth =
    cssWidth || getComputedStyle(document.documentElement).getPropertyValue('--layout-right-width') || '320px';
  const integerWidth = parseInt(stringWidth, 10);
  return isNumber(integerWidth) ? integerWidth : defaultWidth;
};

export const getClampTextWidth = (pageWidth = getPageWidth()) => pageWidth - 100;
export const getChartWidth = (pageWidth = getPageWidth()) => pageWidth - 150;
