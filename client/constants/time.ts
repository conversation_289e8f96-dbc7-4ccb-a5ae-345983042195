/**
 * 时间展示类型枚举
 */
export enum DisplayTimeEnum {
    realtime = 0, // 实时
    day = 1, // 自然日形式
    week = 2, // 自然周形式
    month = 3, // 自然月形式
    quarter = 7, // 季度形式
  }
  
  /**
   * 后端时间类型枚举
   */
  export enum DateTypeEnum {
    REAL_TIME = '0', // 今日实时
    NATURAL_DAY = '1', // 自然日
    NATURAL_WEEK = '2', // 自然周
    NATURAL_MONTH = '3', // 自然月
    LAST_SEVEN_DAY = '4', // 最近 7 天
    LAST_THIRTY_DAY = '5', // 最近 30 天
    SELF_DEFINE = '6', // 自定义
    NATURAL_QUARTER = '7', // 自然季度
    NATURAL_DAY_WITH_HOUR = '8', // 自然天，按照小时统计
    NATURAL_YEAR = '9', // 年份
  }
  
  /**
   * 环比文案枚举
   */
  export const ChainRatioDisplayTimeMoM = {
    [DateTypeEnum.NATURAL_DAY]: '前一天', // 自然日形式
    [DateTypeEnum.NATURAL_WEEK]: '上一周', // 自然周形式
    [DateTypeEnum.NATURAL_MONTH]: '上个月', // 自然月形式
    [DateTypeEnum.NATURAL_QUARTER]: '前一季度', // 季度形式
    [DateTypeEnum.LAST_SEVEN_DAY]: '前一周期', // 近7天
    [DateTypeEnum.LAST_THIRTY_DAY]: '前一周期', // 近30天
  };
  