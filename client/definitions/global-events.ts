// 唤起智能助手 历史已经定义过了，所以是小写
export const TOGGLE_JARVIS_ASSISTANTS = 'toggle_page_jarvis';
// 唤起智能助手成功
export const TOGGLE_PAGE_JARVIS_SUCCESS_EVENT = 'TOGGLE_PAGE_JARVIS_SUCCESS_EVENT';
// 发送消息
export const SEND_ASSISTANT_MESSAGE_EVENT = 'SEND_ASSISTANT_MESSAGE_EVENT';
// 消息处理失败
export const SEND_ASSISTANT_MESSAGE_FAIL_EVENT = 'SEND_ASSISTANT_MESSAGE_FAIL_EVENT';
// jarvis执行完成
export const JARVIS_EXECUTE_FINISHED_EVENT = 'JARVIS_EXECUTE_FINISHED_EVENT';
// 智能助手关闭
export const JARVIS_CLOSE_EVENT = 'JARVIS_CLOSE_EVENT';
// iframe 执行弹窗关闭
export const IFRAME_EXECUTE_MODAL_CLOSE = 'IFRAME_EXECUTE_MODAL_CLOSE';
// 确认助手是否可用
export const CHECK_ASSISTANT_AVAILABLE = 'CHECK_ASSISTANT_AVAILABLE';
// 确认proxy助手是否可用
export const CHECK_ASSISTANT_AVAILABLE_FOR_PROXY = 'CHECK_ASSISTANT_AVAILABLE_FOR_PROXY';
// 助手回复确认
export const ASSISTANT_AVAILABLE_CONFIRM = 'ASSISTANT_AVAILABLE_CONFIRM';
// 每次返回消息
export const ON_ASSISTANT_MESSAGE = 'ON_ASSISTANT_MESSAGE';

// 助手回答loading事件
export const ASSISTANT_ANSWER_LOADING = 'ASSISTANT_ANSWER_LOADING';

// 使用文案
export const ASSISTANT_AVAILABLE_CONFIRM_TEXT = 'ASSISTANT_AVAILABLE_CONFIRM_TEXT';

// 使用图片
export const ASSISTANT_AVAILABLE_CONFIRM_IMAGE = 'ASSISTANT_AVAILABLE_CONFIRM_IMAGE';
