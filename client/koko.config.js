const path = require('path');
const { isDev } = require('@kokojs/shared');

module.exports = {
  name: 'wsc-pc-jiawo',
  presets: ['pc'],
  configureWebpack: {
    output: {
      libraryTarget: 'window',
      globalObject: 'window',
    },
    // 解决模块解析问题
    resolve: {
      // 扩展名自动解析
      extensions: ['.tsx', 'ts', '.js', '.jsx'],
      // 自动解析的目录
      modules: [path.resolve(__dirname, 'src'), 'node_modules'],
    },
    devtool: isDev() ? 'eval-source-map' : false,
  },
  alias: {
    'zent/css/datetimepicker.css': '@zent/compat/css/datetimepicker.css',
    'zent/css/table.css': '@zent/compat/css/table.css',
    components: path.resolve(__dirname, './components'),
    constants: path.resolve(__dirname, './constants'),
    shared: path.resolve(__dirname, './shared'),
    fns: path.resolve(__dirname, './fns'),
    pages: path.resolve(__dirname, './pages'),
    utils: path.resolve(__dirname, './utils'),
    hooks: path.resolve(__dirname, './hooks'),
    definitions: path.resolve(__dirname, './definitions'),
    svg: path.resolve(__dirname, './svg'),
    api: path.resolve(__dirname, './api'),
    common: path.resolve(__dirname, './common'),
    sass: path.resolve(__dirname, './sass'),
  },
  plugins: {
    style: {
      loaderOptions: {
        css: {
          modules: {
            // 马勒戈壁的 啥比koko css-loader exportLocalsConvention 默认值还是asIs 不支持小驼峰自动转
            exportLocalsConvention: 'camelCase',
            /**
             * 如果只传递exportLocalsConvention
             * 会他妈导致覆盖普通css style的配置 => 默认以cssmodules的方式处理了（样式失效
             * 因为modules传进去了，所以只能用auto来强制处理，艹TMD 抄vue-cli为什么不抄好点
             * 我就好奇为什么其他项目能用
             */
            auto: /\.m\.\w+$/i,
          },
        },
      },
    },
    // 都2024了 还es-guard个锤子
    'es-guard': {
      enable: false,
    },
  },
  baseScripts: [],
};
