import React from 'react';

const Image = props => {
    return (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M25.9309 3.78564L26.2308 5.05664C26.7203 7.13139 28.3402 8.75136 30.4151 9.24094L31.6861 9.54085L30.4151 9.84076C28.3402 10.3303 26.7203 11.9503 26.2308 14.025L25.9309 15.2961L25.6309 14.025C25.1414 11.9503 23.5215 10.3303 21.4466 9.84076L20.1757 9.54085L21.4466 9.24094C23.5215 8.75136 25.1414 7.13139 25.6309 5.05664L25.9309 3.78564Z" fill="#3FC0D4" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M17.0906 8.76562C17.5877 8.76562 17.9906 9.16857 17.9906 9.66563C17.9906 10.1627 17.5877 10.5656 17.0906 10.5656H3.11396C2.56168 10.5656 2.11396 11.0133 2.11396 11.5656V19.4164C2.11396 20.3073 3.1911 20.7535 3.82106 20.1235L6.3855 17.5591C6.77602 17.1686 7.40918 17.1686 7.79971 17.5592L13.3334 23.0929C13.7239 23.4834 14.3571 23.4834 14.7476 23.0929L16.9713 20.8692C17.3619 20.4787 17.995 20.4787 18.3855 20.8692L23.6691 26.1527C23.8366 26.3203 24.0638 26.4144 24.3007 26.4144C24.7939 26.4144 25.1938 26.0145 25.1938 25.5212V18.2218C25.1938 17.7247 25.5968 17.3218 26.0938 17.3218C26.5909 17.3218 26.9938 17.7247 26.9938 18.2218V27.2144C26.9938 27.7666 26.5461 28.2144 25.9938 28.2144H1.31396C0.761681 28.2144 0.313965 27.7666 0.313965 27.2144V9.76562C0.313965 9.21334 0.761681 8.76562 1.31396 8.76562H17.0906ZM16.9713 23.4148C17.3619 23.0243 17.995 23.0243 18.3855 23.4148L19.678 24.7072C20.308 25.3372 19.8618 26.4144 18.9709 26.4144H17.0691C16.8039 26.4144 16.5496 26.309 16.362 26.1215L16.0204 25.7799C15.6299 25.3893 15.6299 24.7562 16.0204 24.3657L16.9713 23.4148ZM2.40686 24.0833C2.21932 24.2708 2.11396 24.5252 2.11396 24.7904V25.4144C2.11396 25.9666 2.56168 26.4144 3.11396 26.4144H11.6951C12.586 26.4144 13.0322 25.3372 12.4022 24.7072L7.79969 20.1047C7.40917 19.7142 6.77601 19.7142 6.38549 20.1047L2.40686 24.0833Z" fill="#3FC0D4" />
            <path d="M15.5596 16.6597C16.377 16.6597 17.0396 15.9971 17.0396 15.1797C17.0396 14.3623 16.377 13.6997 15.5596 13.6997C14.7422 13.6997 14.0796 14.3623 14.0796 15.1797C14.0796 15.9971 14.7422 16.6597 15.5596 16.6597Z" fill="#3FC0D4" />
        </svg>


    );
};

export default Image;
