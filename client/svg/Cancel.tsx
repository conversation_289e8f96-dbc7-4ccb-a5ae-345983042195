import React from 'react';

interface Props {
  color?: string;
  fill?: string;
  width?: string;
  height?: string;
}

const CancelIcon: React.FC<Props> = ({ width = '20', height = '20', color = '#5E697D', fill = 'none' }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={width} viewBox="0 0 20 20" fill="none">
      <path
        d="M15.4965 5.51364C15.6639 5.34628 15.6639 5.07492 15.4965 4.90755L15.0924 4.50349C14.9251 4.33612 14.6537 4.33612 14.4863 4.50349L9.99999 8.98984L5.51361 4.50346C5.34624 4.33609 5.07488 4.33609 4.90752 4.50346L4.50346 4.90752C4.33609 5.07488 4.33609 5.34624 4.50346 5.51361L8.98984 9.99999L4.50346 14.4864C4.3361 14.6537 4.3361 14.9251 4.50346 15.0925L4.90752 15.4965C5.07489 15.6639 5.34625 15.6639 5.51362 15.4965L9.99999 11.0101L14.4863 15.4965C14.6537 15.6639 14.9251 15.6639 15.0924 15.4965L15.4965 15.0924C15.6639 14.9251 15.6639 14.6537 15.4965 14.4863L11.0101 9.99999L15.4965 5.51364Z"
        fill={fill}
      />
    </svg>
  );
};

export default CancelIcon;
