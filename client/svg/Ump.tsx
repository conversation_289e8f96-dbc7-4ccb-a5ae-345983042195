import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const UmpIcon: FC<ICommonSvg> = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.94755 17.6452C6.65968 17.6452 6.38586 17.4767 6.26651 17.1924C5.95407 16.4517 5.73642 15.6688 5.61706 14.8614C5.23442 12.2496 5.93301 9.66587 7.53029 7.77722C7.79358 7.46478 8.26047 7.42617 8.57291 7.68946C8.88534 7.95274 8.92396 8.41964 8.66067 8.73208C7.33721 10.2978 6.76149 12.4532 7.08094 14.6473C7.18275 15.3318 7.36529 15.9918 7.62858 16.6167C7.78656 16.9923 7.61103 17.4276 7.23541 17.5856C7.14413 17.6277 7.04584 17.6452 6.94755 17.6452Z"
        fill="#C346B8"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1074 30.4233V27.0497H9.52432C9.2821 27.0497 9.05743 26.9339 8.92052 26.7373C8.7801 26.5407 8.7485 26.288 8.82924 26.0633L9.55364 24.0345C5.35147 22.8106 2.23987 18.5272 2.23987 13.4465C2.23987 7.4155 6.62449 2.50781 12.0131 2.50781C15.3408 2.50781 18.2856 4.37941 20.0514 7.23136C20.1676 7.2254 20.2839 7.22242 20.3997 7.22242C24.6298 7.22944 28.0771 11.077 28.0771 15.8091C28.0771 19.7742 25.6532 23.1198 22.3743 24.0968L22.9202 25.6315C22.9869 25.8176 22.9589 26.0247 22.843 26.1862C22.7377 26.3477 22.5516 26.4424 22.3551 26.4424H21.0527V29.2192C21.0527 29.5527 20.7824 29.8266 20.4453 29.8266C20.1083 29.8266 19.838 29.5527 19.838 29.2192V26.4424H18.5356C18.339 26.4424 18.153 26.3477 18.0371 26.1862C17.9248 26.0247 17.8932 25.8176 17.9599 25.6315L18.4971 24.1215C17.7859 23.9198 17.1037 23.6044 16.4682 23.182C15.7517 23.5943 14.9839 23.9104 14.1787 24.1149L14.8743 26.0633C14.9551 26.2915 14.92 26.5407 14.7831 26.7373C14.6356 26.9339 14.4109 27.0497 14.1687 27.0497H12.5888V30.4233C12.5853 30.8306 12.2553 31.1641 11.8481 31.1641C11.4409 31.1641 11.1074 30.8341 11.1074 30.4233ZM10.9952 24.3853L10.574 25.5683H13.1191L12.6978 24.3853H10.9952ZM20.3961 24.3853C20.1667 24.3853 19.9386 24.374 19.7123 24.3518L19.3992 25.2278H21.4915L21.1747 24.3415C20.9186 24.3705 20.6589 24.3853 20.3961 24.3853ZM12.0131 22.9074C7.43892 22.9074 3.71779 18.6632 3.71779 13.4465C3.71779 8.22993 7.43892 3.98573 12.0131 3.98573C14.9106 3.98573 17.4658 5.68872 18.9497 8.2631C18.9776 8.34069 19.0179 8.41167 19.0679 8.47405C19.8541 9.91978 20.3084 11.6238 20.3084 13.4465C20.3084 18.6632 16.5873 22.9074 12.0131 22.9074ZM20.8277 8.72409C24.048 8.97662 26.5992 12.0561 26.5992 15.8056C26.5992 19.7198 23.8189 22.9038 20.3997 22.9038C19.4949 22.9038 18.613 22.6811 17.8044 22.2539C20.2185 20.2602 21.7864 17.0544 21.7864 13.4465C21.7864 11.7564 21.442 10.1545 20.8277 8.72409Z"
        fill="#C346B8"
      />
      <path
        d="M26.6917 0.835938L26.8516 1.51357C27.1126 2.61971 27.9763 3.48338 29.0824 3.7444L29.7601 3.9043L29.0824 4.0642C27.9763 4.32521 27.1126 5.18888 26.8516 6.29502L26.6917 6.97266L26.5318 6.29502C26.2708 5.18888 25.4071 4.32521 24.301 4.0642L23.6233 3.9043L24.301 3.7444C25.4071 3.48338 26.2708 2.61971 26.5318 1.51357L26.6917 0.835938Z"
        fill="#C346B8"
      />
    </svg>
  );
};

export default UmpIcon;
