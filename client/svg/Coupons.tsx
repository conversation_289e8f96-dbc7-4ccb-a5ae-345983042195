import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const CouponsIcon: FC<ICommonSvg> = ({ width = 20, height = 20, mainColor = '#A552E0' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        opacity="0.1"
        d="M0 2.85732C0 1.27927 1.27927 0 2.85732 0H17.1434C18.7215 0 20.0007 1.27927 20.0007 2.85732V17.1434C20.0007 18.7215 18.7215 20.0007 17.1434 20.0007H2.85732C1.27927 20.0007 0 18.7215 0 17.1434V2.85732Z"
        fill={mainColor}
      />
      <path
        d="M3.03125 5.7118C3.03125 5.11967 3.51127 4.63965 4.1034 4.63965H15.897C16.4891 4.63965 16.9692 5.11967 16.9692 5.7118V14.289C16.9692 14.8811 16.4891 15.3611 15.897 15.3611H4.1034C3.51127 15.3611 3.03125 14.8811 3.03125 14.289V12.7567C3.03125 11.8266 4.1034 10.9305 4.1034 10.0004C4.1034 9.07023 3.03125 8.1742 3.03125 7.24404V5.7118Z"
        fill={mainColor}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.7529 4.63965H14.289V6.24787H13.7529V4.63965ZM13.7529 7.85616H14.289V9.46438H13.7529V7.85616ZM14.289 11.0725H13.7529V12.6807H14.289V11.0725ZM13.7529 14.289H14.289V15.3612H13.7529V14.289ZM6.95903 11.5036H8.26002V12.461H9.24541V11.5036H10.5639V10.7286H9.24541V10.3078H10.5639V9.53285H9.63466L10.7393 7.3201H9.65921L8.94384 8.81397C8.86201 8.9893 8.79889 9.15412 8.75447 9.30842C8.71707 9.16581 8.65395 9.00099 8.56511 8.81397L7.86376 7.3201H6.78369L7.87428 9.53285H6.95903V10.3078H8.26002V10.7286H6.95903V11.5036Z"
        fill="#F7F7F7"
      />
    </svg>
  );
};

export default CouponsIcon;
