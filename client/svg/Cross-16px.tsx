import React from 'react';

interface Props {
  width?: number;
  height?: number;
  fill?: string;
}

const CrossIcon16px: React.FC<Props> = ({ width = 16, height = 16, fill = '#4A4A4A' }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect opacity="0.7" width="16" height="16" rx="2" fill="white" />
      <g opacity="0.5">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.5355 11.5356C11.3402 11.7308 11.0236 11.7308 10.8284 11.5356L4.4644 5.17162C4.26914 4.97636 4.26914 4.65978 4.4644 4.46452V4.46452C4.65966 4.26925 4.97625 4.26925 5.17151 4.46452L11.5355 10.8285C11.7307 11.0237 11.7307 11.3403 11.5355 11.5356V11.5356Z"
          fill={fill}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.5355 4.46452C11.7307 4.65979 11.7307 4.97637 11.5355 5.17163L5.1715 11.5356C4.97624 11.7309 4.65966 11.7309 4.46439 11.5356V11.5356C4.26913 11.3403 4.26913 11.0237 4.46439 10.8285L10.8284 4.46452C11.0236 4.26926 11.3402 4.26926 11.5355 4.46452V4.46452Z"
          fill={fill}
        />
      </g>{' '}
    </svg>
  );
};

export default CrossIcon16px;
