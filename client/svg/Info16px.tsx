import React from 'react';

interface Props {
  className?: string;
  width?: string;
  height?: string;
}

const Info16px: React.FC<Props> = ({ className, width = '20', height = '20' }) => {
  return (
    <svg
      className={className}
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="16" height="16" fill="transparent" />
      <path
        d="M9.28564 9.27945C9.28564 9.09168 9.43787 8.93945 9.62564 8.93945H10.3756C10.5634 8.93945 10.7156 9.09168 10.7156 9.27945V13.0075C10.7156 13.1953 10.5634 13.3475 10.3756 13.3475H9.62564C9.43787 13.3475 9.28564 13.1953 9.28564 13.0075V9.27945Z"
        fill="#999999"
      />
      <path
        d="M9.62564 6.65283C9.43787 6.65283 9.28564 6.80506 9.28564 6.99283V7.74212C9.28564 7.9299 9.43787 8.08212 9.62564 8.08212H10.3756C10.5634 8.08212 10.7156 7.9299 10.7156 7.74212V6.99283C10.7156 6.80506 10.5634 6.65283 10.3756 6.65283H9.62564Z"
        fill="#999999"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.0005 17.184C13.9681 17.184 17.1845 13.9676 17.1845 9.99997C17.1845 6.03233 13.9681 2.81592 10.0005 2.81592C6.03282 2.81592 2.81641 6.03233 2.81641 9.99997C2.81641 13.9676 6.03282 17.184 10.0005 17.184ZM10.0005 15.754C13.1783 15.754 15.7545 13.1778 15.7545 9.99997C15.7545 6.82209 13.1783 4.24592 10.0005 4.24592C6.82258 4.24592 4.24641 6.82209 4.24641 9.99997C4.24641 13.1778 6.82258 15.754 10.0005 15.754Z"
        fill="#999999"
      />
    </svg>
  );
};

export default Info16px;
