import React from 'react';

interface ArrowDownProps {
  width?: number;
  height?: number;
  fill?: string;
}

const ArrowDown: React.FC<ArrowDownProps> = ({ width = 12, height = 12, fill = '#45A110' }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.73488 4.66697C8.93374 4.40342 8.74573 4.02605 8.41558 4.02605L3.5893 4.02605C3.25773 4.02605 3.07012 4.40628 3.27185 4.66942L5.70422 7.84213C5.86511 8.05198 6.18171 8.05076 6.34097 7.83968L8.73488 4.66697Z"
        fill={fill}
      />{' '}
    </svg>
  );
};

export default ArrowDown;
