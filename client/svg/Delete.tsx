import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const Delete: FC<ICommonSvg> = ({ width = 16, height = 16, mainColor = '#D42F15' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_1793_7965)">
        <circle cx="7.99954" cy="8.00003" r="7.99954" fill={mainColor} />
        <rect
          x="11.4912"
          y="7.20923"
          width="1.58154"
          height="6.98242"
          rx="0.34"
          transform="rotate(90 11.4912 7.20923)"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1793_7965">
          <rect width="16" height="16" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Delete;
