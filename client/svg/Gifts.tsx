import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const GiftsIcon: FC<ICommonSvg> = ({
  width = 20,
  height = 20,
  mainColor = '#2DB26F',
  secondaryColor,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        opacity="0.1"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 3C0 1.34315 1.34315 0 3 0H17C18.6569 0 20 1.34315 20 3V17C20 18.6569 18.6569 20 17 20H3C1.34315 20 0 18.6569 0 17V3Z"
        fill={mainColor}
      />
      <rect
        x="12.5"
        y="3.23242"
        width="1"
        height="4"
        transform="rotate(45 12.5 3.23242)"
        fill={mainColor}
      />
      <rect
        x="6.99994"
        y="3.93945"
        width="1"
        height="4"
        transform="rotate(-45 6.99994 3.93945)"
        fill={mainColor}
      />
      <path
        d="M3.5 8.00098C3.5 6.89641 4.39543 6.00098 5.5 6.00098H14.5C15.6046 6.00098 16.5 6.89641 16.5 8.00098V13.501C16.5 14.6055 15.6046 15.501 14.5 15.501H5.5C4.39543 15.501 3.5 14.6055 3.5 13.501V8.00098Z"
        fill={mainColor}
      />
      <rect x="9.49994" y="6.00098" width="1" height="4" fill="#F7F7F7" />
    </svg>
  );
};

export default GiftsIcon;
