import React from 'react';

interface DoneIconProps {
  width?: number;
  height?: number;
  fill?: string;
}

const DoneIcon: React.FC<DoneIconProps> = ({ width = 28, height = 28, fill = '#4A4A4A' }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 28 28" fill={fill}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.2829 7.1794C24.5172 7.41372 24.5172 7.79362 24.2829 8.02793L10.9243 21.3865C10.69 21.6208 10.3101 21.6208 10.0758 21.3865L3.71723 15.0279C3.48292 14.7936 3.48292 14.4137 3.71723 14.1794L4.28292 13.6137C4.51723 13.3794 4.89713 13.3794 5.13145 13.6137L10.5001 18.9823L22.8687 6.61372C23.103 6.3794 23.4829 6.3794 23.7172 6.61372L24.2829 7.1794Z"
      />{' '}
    </svg>
  );
};

export default DoneIcon;
