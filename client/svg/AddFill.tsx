import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const AddFill: FC<ICommonSvg> = ({ width = 20, height = 20, mainColor = '#4a4a4a' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
    >
      <mask id="path-1-inside-1_226_7959" fill="white">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.184 9.99997C17.184 13.9676 13.9676 17.184 9.99997 17.184C6.03233 17.184 2.81592 13.9676 2.81592 9.99997C2.81592 6.03233 6.03233 2.81592 9.99997 2.81592C13.9676 2.81592 17.184 6.03233 17.184 9.99997ZM12.9403 9.28497C13.128 9.28497 13.2803 9.4372 13.2803 9.62497V10.375C13.2803 10.5627 13.128 10.715 12.9403 10.715H10.7151V12.934C10.7151 13.1218 10.5629 13.274 10.3751 13.274H9.62509C9.43731 13.274 9.28509 13.1218 9.28509 12.934V10.715H7.05985C6.87207 10.715 6.71985 10.5627 6.71985 10.375V9.62497C6.71985 9.4372 6.87207 9.28497 7.05985 9.28497H9.28509V7.06589C9.28509 6.87811 9.43731 6.72589 9.62509 6.72589H10.3751C10.5629 6.72589 10.7151 6.87811 10.7151 7.06589V9.28497H12.9403Z"
        />
      </mask>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.184 9.99997C17.184 13.9676 13.9676 17.184 9.99997 17.184C6.03233 17.184 2.81592 13.9676 2.81592 9.99997C2.81592 6.03233 6.03233 2.81592 9.99997 2.81592C13.9676 2.81592 17.184 6.03233 17.184 9.99997ZM12.9403 9.28497C13.128 9.28497 13.2803 9.4372 13.2803 9.62497V10.375C13.2803 10.5627 13.128 10.715 12.9403 10.715H10.7151V12.934C10.7151 13.1218 10.5629 13.274 10.3751 13.274H9.62509C9.43731 13.274 9.28509 13.1218 9.28509 12.934V10.715H7.05985C6.87207 10.715 6.71985 10.5627 6.71985 10.375V9.62497C6.71985 9.4372 6.87207 9.28497 7.05985 9.28497H9.28509V7.06589C9.28509 6.87811 9.43731 6.72589 9.62509 6.72589H10.3751C10.5629 6.72589 10.7151 6.87811 10.7151 7.06589V9.28497H12.9403Z"
        fill={mainColor}
      />
      <path
        d="M10.7151 10.715V9.28497H9.28509V10.715H10.7151ZM9.28509 10.715H10.7151V9.28497H9.28509V10.715ZM9.28509 9.28497V10.715H10.7151V9.28497H9.28509ZM10.7151 9.28497H9.28509V10.715H10.7151V9.28497ZM9.99997 18.614C14.7574 18.614 18.614 14.7574 18.614 9.99997H15.754C15.754 13.1778 13.1778 15.754 9.99997 15.754V18.614ZM1.38592 9.99997C1.38592 14.7574 5.24256 18.614 9.99997 18.614V15.754C6.82209 15.754 4.24592 13.1778 4.24592 9.99997H1.38592ZM9.99997 1.38592C5.24256 1.38592 1.38592 5.24256 1.38592 9.99997H4.24592C4.24592 6.82209 6.82209 4.24592 9.99997 4.24592V1.38592ZM18.614 9.99997C18.614 5.24256 14.7574 1.38592 9.99997 1.38592V4.24592C13.1778 4.24592 15.754 6.82209 15.754 9.99997H18.614ZM14.7103 9.62497C14.7103 8.64743 13.9178 7.85497 12.9403 7.85497V10.715C12.3383 10.715 11.8503 10.227 11.8503 9.62497H14.7103ZM14.7103 10.375V9.62497H11.8503V10.375H14.7103ZM12.9403 12.145C13.9178 12.145 14.7103 11.3525 14.7103 10.375H11.8503C11.8503 9.77298 12.3383 9.28497 12.9403 9.28497V12.145ZM10.7151 12.145H12.9403V9.28497H10.7151V12.145ZM9.28509 10.715V12.934H12.1451V10.715H9.28509ZM9.28509 12.934C9.28509 12.3321 9.7731 11.844 10.3751 11.844V14.704C11.3526 14.704 12.1451 13.9116 12.1451 12.934H9.28509ZM10.3751 11.844H9.62509V14.704H10.3751V11.844ZM9.62509 11.844C10.2271 11.844 10.7151 12.3321 10.7151 12.934H7.85509C7.85509 13.9116 8.64754 14.704 9.62509 14.704V11.844ZM10.7151 12.934V10.715H7.85509V12.934H10.7151ZM7.05985 12.145H9.28509V9.28497H7.05985V12.145ZM5.28985 10.375C5.28985 11.3525 6.0823 12.145 7.05985 12.145V9.28497C7.66184 9.28497 8.14985 9.77298 8.14985 10.375H5.28985ZM5.28985 9.62497V10.375H8.14985V9.62497H5.28985ZM7.05985 7.85497C6.0823 7.85497 5.28985 8.64743 5.28985 9.62497H8.14985C8.14985 10.227 7.66184 10.715 7.05985 10.715V7.85497ZM9.28509 7.85497H7.05985V10.715H9.28509V7.85497ZM10.7151 9.28497V7.06589H7.85509V9.28497H10.7151ZM10.7151 7.06589C10.7151 7.66788 10.2271 8.15589 9.62509 8.15589V5.29589C8.64754 5.29589 7.85509 6.08835 7.85509 7.06589H10.7151ZM9.62509 8.15589H10.3751V5.29589H9.62509V8.15589ZM10.3751 8.15589C9.7731 8.15589 9.28509 7.66788 9.28509 7.06589H12.1451C12.1451 6.08835 11.3526 5.29589 10.3751 5.29589V8.15589ZM9.28509 7.06589V9.28497H12.1451V7.06589H9.28509ZM12.9403 7.85497H10.7151V10.715H12.9403V7.85497Z"
        fill="#4A4A4A"
        mask="url(#path-1-inside-1_226_7959)"
      />
    </svg>
  );
};

export default AddFill;
