import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const Add: FC<ICommonSvg> = ({ width = 20, height = 20, mainColor = '#4a4a4a' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
    >
      <circle cx="9.99997" cy="9.99997" r="6.46905" stroke="#4A4A4A" strokeWidth="1.43" />
      <rect
        x="12.6304"
        y="9.28491"
        width="1.43"
        height="5.26062"
        rx="0.34"
        transform="rotate(90 12.6304 9.28491)"
        fill={mainColor}
      />
      <rect
        x="10.7151"
        y="12.6304"
        width="1.43"
        height="5.26062"
        rx="0.34"
        transform="rotate(-180 10.7151 12.6304)"
        fill={mainColor}
      />
    </svg>
  );
};

export default Add;
