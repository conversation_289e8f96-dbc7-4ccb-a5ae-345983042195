import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const GoodsIcon: FC<ICommonSvg> = ({
  width = 20,
  height = 20,
  mainColor = '#A552E0',
  secondaryColor,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        opacity="0.1"
        d="M0 2.85732C0 1.27927 1.27927 0 2.85732 0H17.1434C18.7215 0 20.0007 1.27927 20.0007 2.85732V17.1434C20.0007 18.7215 18.7215 20.0007 17.1434 20.0007H2.85732C1.27927 20.0007 0 18.7215 0 17.1434V2.85732Z"
        fill={mainColor}
      />
      <path
        d="M5.28093 5.33817C5.31623 4.57565 5.94473 3.97559 6.70806 3.97559H13.2925C14.0558 3.97559 14.6843 4.57565 14.7196 5.33818L15.1694 15.0528C15.2071 15.8669 14.5572 16.5475 13.7423 16.5475H6.25831C5.44335 16.5475 4.79349 15.8669 4.83118 15.0528L5.28093 5.33817Z"
        fill={mainColor}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.6195 6.0713C12.6195 7.51782 11.4468 8.69045 10.0003 8.69045C8.55381 8.69045 7.38118 7.51782 7.38118 6.0713C7.38118 6.07124 7.38118 6.07118 7.38118 6.07111H8.42883C8.42883 6.07127 8.42883 6.07142 8.42883 6.07157C8.42883 6.93948 9.13241 7.64306 10.0003 7.64306C10.8682 7.64306 11.5718 6.93948 11.5718 6.07157C11.5718 6.07142 11.5718 6.07127 11.5718 6.07111H12.6195C12.6195 6.07118 12.6195 6.07124 12.6195 6.0713ZM8.42883 3.97579H11.5718C11.1341 3.64699 10.59 3.45215 10.0003 3.45215C9.4107 3.45215 8.86658 3.64699 8.42883 3.97579Z"
        fill="#F7F7F7"
      />
    </svg>
  );
};

export default GoodsIcon;
