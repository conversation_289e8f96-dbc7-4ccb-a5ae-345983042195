import React, { CSSProperties } from 'react';

interface Props {
  width?: number;
  height?: number;
  fill?: string;
  style?: CSSProperties;
}

const CrossIcon: React.FC<Props> = ({ width = 28, height = 28, fill = '#4A4A4A', style }) => {
  return (
    <svg
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 28 28"
      fill={fill}
    >
      <path d="M15.3729 14L19.874 9.49899C20.042 9.33093 20.042 9.05845 19.874 8.89039L19.1096 8.12604C18.9415 7.95799 18.6691 7.95799 18.501 8.12604L14 12.6271L9.49899 8.12604C9.33093 7.95799 9.05845 7.95799 8.89039 8.12604L8.12604 8.89039C7.95799 9.05845 7.95799 9.33093 8.12604 9.49899L12.6271 14L8.12604 18.501C7.95799 18.6691 7.95799 18.9415 8.12604 19.1096L8.89039 19.874C9.05845 20.042 9.33093 20.042 9.49899 19.874L14 15.3729L18.501 19.874C18.6691 20.042 18.9415 20.042 19.1096 19.874L19.874 19.1096C20.042 18.9415 20.042 18.6691 19.874 18.501L15.3729 14Z" />{' '}
    </svg>
  );
};

export default CrossIcon;
