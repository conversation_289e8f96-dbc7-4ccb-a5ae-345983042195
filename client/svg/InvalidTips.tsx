import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const InvalidTips: FC<ICommonSvg> = ({ width = 16, height = 16, mainColor = '#D42F15' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_310_8610)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16Z"
          fill={mainColor}
        />
        <path
          d="M8.4176 9.1812C8.6267 9.1812 8.79621 9.01169 8.79621 8.80259L8.79621 4.65108C8.79621 4.44197 8.6267 4.27246 8.4176 4.27246H7.58241C7.37331 4.27246 7.2038 4.44197 7.2038 4.65108L7.2038 8.80259C7.2038 9.01169 7.37331 9.1812 7.58241 9.1812H8.4176Z"
          fill="white"
        />
        <path
          d="M8.4176 11.7281C8.6267 11.7281 8.79621 11.5586 8.79621 11.3495L8.79621 10.5151C8.79621 10.306 8.6267 10.1364 8.4176 10.1364H7.58241C7.37331 10.1364 7.2038 10.306 7.2038 10.5151V11.3495C7.2038 11.5586 7.37331 11.7281 7.58241 11.7281H8.4176Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_310_8610">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default InvalidTips;
