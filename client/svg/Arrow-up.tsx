import React from 'react';

interface ArrowUpProps {
    width?: number;
    height?: number;
    fill?: string;
}

const ArrowUp: React.FC<ArrowUpProps> = ({ width = 12, height = 12, fill = '#D42F15' }) => {
    return (
        <svg width={width} height={height} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.73488 7.35789C8.93374 7.62143 8.74573 7.99881 8.41558 7.99881L3.5893 7.99881C3.25773 7.99881 3.07012 7.61858 3.27185 7.35544L5.70422 4.18273C5.86511 3.97288 6.18171 3.97409 6.34097 4.18517L8.73488 7.35789Z"
                fill={fill}
            />        </svg>

    );
};

export default ArrowUp;
