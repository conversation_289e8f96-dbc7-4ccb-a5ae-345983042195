import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const QRIcon: FC<ICommonSvg> = ({ width = 20, height = 20, mainColor = '#4a4a4a' }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.79175 4.45581C3.79175 4.08906 4.08906 3.79175 4.45581 3.79175H5.00919C5.37594 3.79175 5.67324 4.08906 5.67324 4.45581V5.00919C5.67324 5.37594 5.37594 5.67324 5.00919 5.67324H4.45581C4.08906 5.67324 3.79175 5.37594 3.79175 5.00919V4.45581Z"
        fill="#4A4A4A"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.584961 2.79848C0.584961 1.57599 1.57599 0.584961 2.79848 0.584961H6.66646C7.88896 0.584961 8.87998 1.57599 8.87998 2.79848V6.66646C8.87998 7.88896 7.88896 8.87998 6.66646 8.87998H2.79848C1.57599 8.87998 0.584961 7.88896 0.584961 6.66646V2.79848ZM2.79848 2.46645H6.66646C6.84984 2.46645 6.99849 2.61511 6.99849 2.79848V6.66646C6.99849 6.84984 6.84984 6.99849 6.66646 6.99849H2.79848C2.61511 6.99849 2.46645 6.84984 2.46645 6.66646V2.79848C2.46645 2.61511 2.61511 2.46645 2.79848 2.46645Z"
        fill="#4A4A4A"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.584961 13.3338C0.584961 12.1113 1.57599 11.1203 2.79848 11.1203H6.66646C7.88896 11.1203 8.87998 12.1113 8.87998 13.3338V17.2018C8.87998 18.4242 7.88896 19.4153 6.66646 19.4153H2.79848C1.57599 19.4153 0.584961 18.4242 0.584961 17.2018V13.3338ZM2.79848 13.0017H6.66646C6.84984 13.0017 6.99849 13.1504 6.99849 13.3338V17.2018C6.99849 17.3851 6.84984 17.5338 6.66646 17.5338H2.79848C2.61511 17.5338 2.46645 17.3851 2.46645 17.2018V13.3338C2.46645 13.1504 2.61511 13.0017 2.79848 13.0017Z"
        fill="#4A4A4A"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M13.3338 0.584961C12.1113 0.584961 11.1203 1.57599 11.1203 2.79848V6.66646C11.1203 7.88896 12.1113 8.87998 13.3338 8.87998H17.2018C18.4242 8.87998 19.4153 7.88896 19.4153 6.66646V2.79848C19.4153 1.57599 18.4242 0.584961 17.2018 0.584961H13.3338ZM17.2018 2.46645H13.3338C13.1504 2.46645 13.0017 2.61511 13.0017 2.79848V6.66646C13.0017 6.84984 13.1504 6.99849 13.3338 6.99849H17.2018C17.3851 6.99849 17.5338 6.84984 17.5338 6.66646V2.79848C17.5338 2.61511 17.3851 2.46645 17.2018 2.46645Z"
        fill="#4A4A4A"
      />
      <path
        d="M12.9305 11.1203C12.5638 11.1203 12.2665 11.4176 12.2665 11.7843V15.6684C12.2665 16.0352 12.5638 16.3325 12.9305 16.3325H13.4839C13.8506 16.3325 14.148 16.0352 14.148 15.6684V11.7843C14.148 11.4176 13.8506 11.1203 13.4839 11.1203H12.9305Z"
        fill="#4A4A4A"
      />
      <path
        d="M16.3876 14.7722C16.3876 14.4055 16.6849 14.1082 17.0517 14.1082H17.6051C17.9718 14.1082 18.2691 14.4055 18.2691 14.7722V18.6563C18.2691 19.0231 17.9718 19.3204 17.6051 19.3204H17.0517C16.6849 19.3204 16.3876 19.0231 16.3876 18.6563V14.7722Z"
        fill="#4A4A4A"
      />
      <path
        d="M12.9305 17.5338C12.5638 17.5338 12.2665 17.8311 12.2665 18.1979V18.7513C12.2665 19.118 12.5638 19.4153 12.9305 19.4153H13.4839C13.8506 19.4153 14.148 19.118 14.148 18.7513V18.1979C14.148 17.8311 13.8506 17.5338 13.4839 17.5338H12.9305Z"
        fill="#4A4A4A"
      />
      <path
        d="M17.0517 11.1203C16.6849 11.1203 16.3876 11.4176 16.3876 11.7843V12.3377C16.3876 12.7044 16.6849 13.0017 17.0517 13.0017H17.6051C17.9718 13.0017 18.2691 12.7044 18.2691 12.3377V11.7843C18.2691 11.4176 17.9718 11.1203 17.6051 11.1203H17.0517Z"
        fill="#4A4A4A"
      />
    </svg>
  );
};

export default QRIcon;
