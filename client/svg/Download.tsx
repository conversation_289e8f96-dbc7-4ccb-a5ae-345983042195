import React from 'react';

interface Props {
  className?: string;
  width: number;
  height: number;
}

const DownloadIcon: React.FC<Props> = ({ className, width = 28, height = 28 }) => (
  <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width={width} height={height} fill="transparent" />
    <path
      d="M12.8588 3.89906C12.8588 3.61502 12.6286 3.38477 12.3445 3.38477H11.6588C11.3748 3.38477 11.1445 3.61502 11.1445 3.89906V13.5737L7.4789 9.90808C7.27806 9.70724 6.95243 9.70724 6.75159 9.90808L6.26672 10.393C6.06588 10.5938 6.06588 10.9194 6.26672 11.1203L11.6381 16.4916C11.8389 16.6925 12.1646 16.6925 12.3654 16.4916L17.7368 11.1203C17.9376 10.9194 17.9376 10.5938 17.7368 10.393L17.2519 9.90808C17.051 9.70724 16.7254 9.70724 16.5246 9.90808L12.8588 13.5738V3.89906Z"
      fill="#4A4A4A"
    />
    <path
      d="M19.316 19.4145C19.316 19.6985 19.0857 19.9287 18.8017 19.9287H5.20179C4.91775 19.9287 4.6875 19.6985 4.6875 19.4145V18.7287C4.6875 18.4447 4.91775 18.2145 5.20179 18.2145H18.8017C19.0857 18.2145 19.316 18.4447 19.316 18.7287V19.4145Z"
      fill="#4A4A4A"
    />
  </svg>
);

export default DownloadIcon;
