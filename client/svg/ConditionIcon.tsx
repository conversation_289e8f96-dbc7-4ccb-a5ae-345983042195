import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const ConditionIcon: FC<ICommonSvg> = ({ width = 16, height = 16 }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 16 17"
      fill="none"
    >
      <path
        d="M9.66943 9.90206C9.66943 9.38836 9.8671 8.89436 10.2214 8.52244L13.8731 4.68981C14.4797 4.05321 14.0284 3 13.1491 3H2.85089C1.97159 3 1.52035 4.05321 2.1269 4.68981L5.77805 8.52183C6.13241 8.89375 6.33008 9.38775 6.33008 9.90146V15.2723C6.33008 15.6268 6.75707 15.8061 7.01016 15.5579L9.06984 13.5379C9.45335 13.1618 9.66943 12.6472 9.66943 12.11V9.90206Z"
        fill="#2DB26F"
      />
    </svg>
  );
};

export default ConditionIcon;
