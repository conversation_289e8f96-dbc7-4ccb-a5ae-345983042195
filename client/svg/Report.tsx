import React from 'react';

const ReportIcon = () => {
  return (
    <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18.5718 9.64404C16.5994 9.64404 15.0005 11.243 15.0005 13.2154V66.7854C15.0005 68.7577 16.5994 70.3567 18.5718 70.3567H61.4278C63.4002 70.3567 64.9991 68.7577 64.9991 66.7854V23.9294L50.7138 9.64404H18.5718Z"
        fill="url(#paint0_linear_571_12945)"
      />
      <g filter="url(#filter0_dii_571_12945)">
        <path
          d="M65 23.9287L54.7147 23.9287C52.5055 23.9287 50.7147 22.1378 50.7147 19.9287L50.7147 9.64338L65 23.9287Z"
          fill="url(#paint1_linear_571_12945)"
        />
      </g>
      <g filter="url(#filter1_d_571_12945)">
        <path
          d="M28.6533 42.4184C28.6533 40.0281 29.3924 37.8105 30.6546 35.9815C30.8529 35.6941 31.2578 35.6597 31.5165 35.8942L34.4381 38.5424C34.6601 38.7437 34.6968 39.0763 34.5491 39.337C34.0337 40.2467 33.7395 41.2982 33.7395 42.4184C33.7395 45.876 36.5424 48.6788 39.9999 48.6788C41.0125 48.6788 41.9689 48.4384 42.8152 48.0116C43.092 47.872 43.4353 47.9334 43.6221 48.1808L45.9843 51.3096C46.1912 51.5835 46.1272 51.9762 45.8329 52.1529C44.1284 53.1765 42.1329 53.765 39.9999 53.765C33.7334 53.765 28.6533 48.685 28.6533 42.4184Z"
          fill="url(#paint2_linear_571_12945)"
          shape-rendering="crispEdges"
        />
        <path
          d="M47.3571 50.1643C47.572 50.4489 47.9873 50.4852 48.2327 50.2265C50.1625 48.1924 51.3465 45.4436 51.3465 42.4184C51.3465 38.5667 49.4272 35.1632 46.4929 33.1121C46.2043 32.9104 45.8095 33.0234 45.6518 33.3383L43.8804 36.8755C43.7485 37.1389 43.8295 37.4569 44.054 37.6478C45.4039 38.7961 46.2603 40.5073 46.2603 42.4184C46.2603 43.8461 45.7824 45.1622 44.9778 46.2154C44.8036 46.4435 44.7876 46.761 44.9605 46.99L47.3571 50.1643Z"
          fill="url(#paint3_linear_571_12945)"
          shape-rendering="crispEdges"
        />
        <path
          d="M36.4444 37.265C36.1998 37.434 35.8681 37.4286 35.6478 37.2289L32.7223 34.5771C32.4626 34.3417 32.4583 33.9335 32.7273 33.7087C34.3255 32.3727 36.297 31.4684 38.4626 31.1751C38.8092 31.1282 39.1075 31.4052 39.1075 31.7549V35.7007C39.1075 35.9993 38.887 36.2494 38.5959 36.3161C37.8125 36.4956 37.0851 36.8221 36.4444 37.265Z"
          fill="url(#paint4_linear_571_12945)"
          shape-rendering="crispEdges"
        />
        <path
          d="M44.0678 32.5135C44.2235 32.2025 44.0834 31.8231 43.7551 31.708C43.0456 31.4593 42.3039 31.279 41.538 31.1752C41.1914 31.1283 40.8932 31.4053 40.8932 31.7551V35.7009C40.8932 35.9995 41.1136 36.2495 41.4046 36.3162C41.4575 36.3284 41.5102 36.3412 41.5625 36.3546C41.8549 36.4297 42.1688 36.3054 42.3039 36.0356L44.0678 32.5135Z"
          fill="url(#paint5_linear_571_12945)"
          shape-rendering="crispEdges"
        />
      </g>
      <defs>
        <filter
          id="filter0_dii_571_12945"
          x="47.7148"
          y="7.64355"
          width="20.2852"
          height="20.2852"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1.5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.329412 0 0 0 0 0.368627 0 0 0 0 0.447059 0 0 0 0.56 0"
          />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_571_12945" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_571_12945"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="1" dy="1" />
          <feGaussianBlur stdDeviation="0.5" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.14 0" />
          <feBlend mode="normal" in2="shape" result="effect2_innerShadow_571_12945" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="-1" dy="-1" />
          <feGaussianBlur stdDeviation="0.5" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.337255 0 0 0 0 0.376471 0 0 0 0 0.45098 0 0 0 0.29 0"
          />
          <feBlend
            mode="normal"
            in2="effect2_innerShadow_571_12945"
            result="effect3_innerShadow_571_12945"
          />
        </filter>
        <filter
          id="filter1_d_571_12945"
          x="24.6533"
          y="29.1699"
          width="30.6934"
          height="30.5952"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.284601 0 0 0 0 0.327027 0 0 0 0 0.404167 0 0 0 1 0"
          />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_571_12945" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_571_12945"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_571_12945"
          x1="39.9998"
          y1="9.64404"
          x2="39.9998"
          y2="70.3567"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#89909E" />
          <stop offset="1" stop-color="#5E697D" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_571_12945"
          x1="57.8573"
          y1="23.9287"
          x2="57.8573"
          y2="9.64338"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6B768A" />
          <stop offset="1" stop-color="#9CA4B3" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_571_12945"
          x1="39.9999"
          y1="31.1064"
          x2="39.9999"
          y2="53.765"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0.35" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_571_12945"
          x1="39.9999"
          y1="31.1064"
          x2="39.9999"
          y2="53.765"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0.35" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_571_12945"
          x1="39.9999"
          y1="31.1064"
          x2="39.9999"
          y2="53.765"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0.35" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_571_12945"
          x1="39.9999"
          y1="31.1064"
          x2="39.9999"
          y2="53.765"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0.35" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default ReportIcon;
