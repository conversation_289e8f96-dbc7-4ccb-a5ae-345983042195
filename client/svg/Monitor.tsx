import React from 'react';

const Monitor = props => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M24.3996 4.57443L24.1266 3.41748L23.8536 4.57443C23.408 6.463 21.9334 7.9376 20.0448 8.38325L18.8878 8.65625L20.0448 8.92925C21.9334 9.3749 23.408 10.8495 23.8536 12.7381L24.1266 13.895L24.3996 12.7381C24.8452 10.8495 26.3198 9.3749 28.2085 8.92925L29.3654 8.65625L28.2085 8.38325C26.3198 7.9376 24.8452 6.463 24.3996 4.57443Z"
        fill="#7DD043"
      />
      <path
        d="M10.1035 6.48538C6.92157 6.48538 4.342 9.06488 4.342 12.2469C4.342 14.5395 5.68108 16.5211 7.62352 17.449C8.0489 17.6522 8.22918 18.1618 8.02598 18.5873C7.82277 19.0127 7.31298 19.1928 6.8876 18.9896C4.37409 17.7889 2.63464 15.2219 2.63464 12.2469C2.63464 8.12192 5.97849 4.77803 10.1035 4.77803C13.0934 4.77803 15.6713 6.53498 16.8643 9.06902C17.065 9.4956 16.882 10.0042 16.4554 10.205C16.029 10.4058 15.5202 10.2228 15.3195 9.79624C14.3977 7.83798 12.4077 6.48538 10.1035 6.48538Z"
        fill="#7DD043"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.01116 13.0348C8.55536 11.8927 9.67205 10.7525 10.8233 11.1842L25.5676 16.7134C26.7998 17.1754 26.7904 18.9214 25.5534 19.3701L20.3726 21.2494C19.8047 21.4554 19.3639 21.912 19.178 22.4868L17.5229 27.6024C17.1148 28.8637 15.3523 28.9226 14.8608 27.6914L9.01116 13.0348ZM10.8415 13.0145L16.1472 26.3083L17.5533 21.9613C17.9016 20.8851 18.7271 20.03 19.7905 19.6443L24.2283 18.0345L10.8415 13.0145Z"
        fill="#7DD043"
      />
    </svg>
  );
};

export default Monitor;
