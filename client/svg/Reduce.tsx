import React, { FC } from 'react';
import { ICommonSvg } from './constant';

const Reduce: FC<ICommonSvg> = ({ width = 20, height = 20, mainColor = '#4a4a4a' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 28 28"
      fill="none"
    >
      <path
        d="M16.6304 13.625C16.6304 13.4373 16.4781 13.285 16.2904 13.285H11.7098C11.522 13.285 11.3698 13.4373 11.3698 13.625V14.375C11.3698 14.5628 11.522 14.715 11.7098 14.715L16.2904 14.715C16.4781 14.715 16.6304 14.5628 16.6304 14.375V13.625Z"
        fill={mainColor}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.184 14C21.184 17.9676 17.9676 21.184 14 21.184C10.0323 21.184 6.81592 17.9676 6.81592 14C6.81592 10.0323 10.0323 6.81592 14 6.81592C17.9676 6.81592 21.184 10.0323 21.184 14ZM19.754 14C19.754 17.1778 17.1778 19.754 14 19.754C10.8221 19.754 8.24592 17.1778 8.24592 14C8.24592 10.8221 10.8221 8.24592 14 8.24592C17.1778 8.24592 19.754 10.8221 19.754 14Z"
        fill={mainColor}
      />
    </svg>
  );
};

export default Reduce;
