import React from 'react';

const AI = props => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M25.1287 4.69825L24.8288 3.42725L24.5289 4.69825C24.0393 6.77299 22.4194 8.39296 20.3446 8.88254L19.0736 9.18245L20.3446 9.48236C22.4194 9.97194 24.0393 11.5919 24.5289 13.6667L24.8288 14.9377L25.1287 13.6667C25.6183 11.5919 27.2382 9.97194 29.313 9.48236L30.584 9.18245L29.313 8.88254C27.2382 8.39296 25.6183 6.77299 25.1287 4.69825Z"
        fill="#E3AE52"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.8884 28.2657L12.9143 28.2877C12.9981 28.374 13.0995 28.4447 13.2142 28.4943L13.2314 28.5015C13.2522 28.5099 13.2731 28.5176 13.2939 28.5245C13.5087 28.5962 13.7466 28.5894 13.9627 28.4961C14.0859 28.4429 14.1937 28.3653 14.281 28.2704L14.2887 28.2639C14.3396 28.221 14.4204 28.1553 14.5301 28.0741C14.7499 27.9115 15.0823 27.6889 15.5155 27.4648C16.3824 27.0163 17.6396 26.5683 19.2046 26.5683C20.7695 26.5683 22.0268 27.0163 22.8936 27.4648C23.3268 27.6889 23.6593 27.9115 23.8788 28.0741C23.9885 28.1553 24.0696 28.221 24.1204 28.2639L24.1735 28.3099L24.1827 28.3181C24.4549 28.5728 24.8522 28.6421 25.1947 28.4943C25.5384 28.346 25.7611 28.0075 25.7611 27.6332V18.2928C25.7611 17.7748 25.3412 17.3549 24.8233 17.3549C24.3052 17.3549 23.8855 17.7748 23.8855 18.2928V25.8673L23.7554 25.7989C22.6866 25.246 21.1345 24.6926 19.2046 24.6926C17.2746 24.6926 15.7225 25.246 14.6537 25.7989L14.5292 25.8645V10.2615L14.5591 10.2286C14.7105 10.0659 14.9603 9.83347 15.328 9.59573C15.6582 9.38228 16.0916 9.15918 16.6466 8.97924C17.0787 8.83912 17.4013 8.45518 17.4013 8.00089C17.4013 7.41071 16.8696 6.95688 16.3032 7.12259C15.4867 7.36142 14.8286 7.68509 14.3096 8.02058C14.0257 8.20418 13.7862 8.38944 13.5874 8.56302C13.3892 8.38995 13.1503 8.20535 12.8673 8.02241C11.8359 7.3555 10.2542 6.7353 7.97259 6.7353C5.69075 6.7353 4.10907 7.3555 3.0776 8.02241C2.56609 8.35309 2.1993 8.68909 1.95408 8.95271C1.83135 9.08445 1.73908 9.19819 1.67429 9.28431C1.65139 9.3147 1.6317 9.34172 1.61544 9.3649L1.59667 9.39183L1.5724 9.42798L1.56393 9.44129L1.56049 9.44673L1.55889 9.4491L1.55751 9.45128C1.46501 9.59999 1.41602 9.77165 1.41602 9.94681V27.635C1.41602 28.0093 1.63857 28.3478 1.98224 28.4961C2.32568 28.6443 2.72316 28.5754 2.9954 28.319L3.00364 28.3117C3.01349 28.3028 3.03135 28.2871 3.05676 28.2657C3.10759 28.2228 3.18841 28.1571 3.29809 28.0759C3.51789 27.9133 3.85034 27.6908 4.28354 27.4666C5.15039 27.0181 6.40763 26.5701 7.97259 26.5701C9.53754 26.5701 10.7948 27.0181 11.6616 27.4666C12.0948 27.6908 12.4273 27.9133 12.6469 28.0759C12.7565 28.1571 12.8376 28.2228 12.8884 28.2657ZM3.29168 10.2693V25.8692L3.42173 25.8007C4.49052 25.2478 6.04266 24.6945 7.97259 24.6945C9.90251 24.6945 11.4546 25.2478 12.5234 25.8007L12.648 25.8663V10.2634L12.6178 10.2304C12.4664 10.0677 12.2169 9.83528 11.8491 9.59756C11.1222 9.12758 9.8945 8.61096 7.97259 8.61096C6.05067 8.61096 4.82298 9.12758 4.09602 9.59756C3.72831 9.83528 3.47851 10.0677 3.32717 10.2304L3.29168 10.2693Z"
        fill="#E3AE52"
      />
    </svg>
  );
};

export default AI;
