import React, { useEffect } from 'react';
import { openDialog, closeDialog, Button, Notify } from 'zent';
import { hasWorkFlowPermission } from 'api/workflow';
import { getPluginLifeCycle, isCustomerPro } from '../api/automation';

export const isValid = time => {
  if (!time) return false;
  // time时间戳，valid条件是time大于当前时间
  return time > new Date().getTime();
};

// 新增的权限检查函数
export const checkPermissions = async (): Promise<{ isCrm: boolean; isPro: boolean }> => {
  try {
    const permission = await getPluginLifeCycle();
    const isPro = await isCustomerPro({});
    const { crmPluginLifecycle, customerProPluginLifecycle } = permission;

    const isCrm = isValid(crmPluginLifecycle?.endTime);
    const isProValid = isValid(customerProPluginLifecycle?.endTime) || isPro;

    return {
      isCrm,
      isPro: isProValid,
    };
  } catch (error) {
    console.error('检查权限失败', error);
    return { isCrm: false, isPro: false };
  }
};

const usePermission = () => {
  const checkAutoMarketingPermission = async () => {
    try {
      // 判断是否有客户pro权限
      const dialogId = 'checkAutoMarketingPermission';
      const permission = await getPluginLifeCycle();
      const isPro = await isCustomerPro({});
      const { crmPluginLifecycle, customerProPluginLifecycle } = permission;
      // 都没有的时候提示开通客户pro
      if (
        !isValid(crmPluginLifecycle?.endTime) &&
        !isValid(customerProPluginLifecycle?.endTime) &&
        !isPro
      ) {
        openDialog({
          dialogId,
          title: '提示',
          children: <div>购买“客户Pro”或者“CRM”后可用</div>,
          footer: (
            <>
              <Button onClick={() => closeDialog(dialogId)}>取消</Button>
              <Button
                type="primary"
                href="https://qima.feishu.cn/wiki/QpwFwtoEpikmavk3s6zcN7yEnPg"
                target="_blank"
              >
                了解客户Pro
              </Button>
              <Button
                type="primary"
                href="https://www.xinlingshou.com/products/crm"
                target="_blank"
              >
                了解CRM
              </Button>
            </>
          ),
        });
      }
      if (
        isValid(customerProPluginLifecycle?.endTime) ||
        isValid(crmPluginLifecycle?.endTime) ||
        isPro
      ) {
        return Promise.resolve();
      }

      return Promise.reject();
    } catch (error) {
      Notify.error('检查权限失败');
      return Promise.reject();
    }
  };

  const checkWorkFlowPermission = async () => {
    const result = await hasWorkFlowPermission();
    const dialogId = 'checkWorkFlowPermission';
    if (result) {
      return Promise.resolve();
    }
    openDialog({
      dialogId,
      title: '提示',
      children: <div>当前微商城版本不支持，升级专业版或更高版本后可用</div>,
      footer: (
        <>
          <Button onClick={() => closeDialog('checkWorkFlowPermission')}>取消</Button>
          <Button type="primary" href="https://www.youzan.com/intro/wsc/price">
            了解详情
          </Button>
        </>
      ),
    });
    return Promise.reject();
  };

  return {
    checkAutoMarketingPermission,
    checkWorkFlowPermission,
  };
};

export default usePermission;
