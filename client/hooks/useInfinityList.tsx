import { useCallback, useEffect, useState } from 'react';

interface IUseInfinityList {
  fetchList: (params: any) => Promise<any>;
  defaultPageSize?: number;
  // 接口默认参数
  defaultParams?: any;
  filterParams?: any;
  skipFirstReq?: boolean;
  keywordKey?: string;
  defaultTopOption?: any;
}

// 配合infinityScroller的hook
export const useInfinityList = ({
  fetchList,
  defaultPageSize = 10,
  defaultParams = {},
  filterParams,
  skipFirstReq = false,
  keywordKey = 'keywords',
  defaultTopOption,
}: IUseInfinityList) => {
  const [list, setList] = useState<any>(defaultTopOption ? [defaultTopOption] : []);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [keywords, setKeywords] = useState('');
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const loadMore = useCallback(
    async (isSearch = false, refreshParams = {}, isUpdate = false) => {
      try {
        setLoading(true);
        const {
          items: newList,
          paginator: { page: realPage, totalCount: total },
        } = await fetchList({
          page: isSearch ? 1 : page,
          pageSize: defaultPageSize,
          [keywordKey]: keywords,
          ...defaultParams,
          ...filterParams,
          ...refreshParams,
        });
        const formattedList =
          realPage === 1
            ? defaultTopOption
              ? [defaultTopOption, ...newList]
              : newList
            : [...list, ...newList];
        setList(formattedList);
        const hasMore = formattedList.length < total;
        setHasMore(hasMore);
        setLoading(false);
        if (!isUpdate && hasMore) {
          setPage(realPage + 1);
        }
        setTotal(total);
        return formattedList;
      } catch (error) {
        setPage(1);
        setLoading(false);
        setHasMore(true);
      }
    },
    [page, keywords, hasMore, filterParams, list],
  );

  const refresh = (params = {}) => {
    loadMore(1, params);
  };

  // 仅重新执行当前分页下的数据加载
  const onItemUpdate = useCallback(() => {
    loadMore(false, {}, true);
  }, [page]);

  useEffect(() => {
    // 防抖loadMore
    const timer = setTimeout(() => {
      loadMore(true);
    }, 300);
    return () => {
      clearTimeout(timer);
    };
  }, [keywords, filterParams]);

  return {
    hasMore,
    list,
    loading,
    total,
    loadMore,
    keywords,
    setKeywords,
    refresh,
    onItemUpdate,
  };
};
