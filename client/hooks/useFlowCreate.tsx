import React, { useState } from 'react';
import jumpTo from '../fns/jumpTo';
import { Input, Notify, Sweetalert } from 'zent';
import { getTemplateDetail, saveDraft } from '../api/workflow';
import usePermission from './usePermission';

export const useFlowCreate = ({ templateId = '', flowName = '' }) => {
  const taskNameRef = React.useRef<string>();
  const { checkAutoMarketingPermission, checkWorkFlowPermission } = usePermission();
  const onConfirm = async () => {
    let taskName = taskNameRef.current || '';
    taskName = taskName.trim();
    if (!taskName && !templateId) {
      Notify.error('任务名称不能为空');
      return Promise.reject();
    }
    if (taskName.length > 20 && !templateId) {
      Notify.error('任务名称不能超过20个字');
      return Promise.reject();
    }
    try {
      if (templateId) {
        const detail = templateId
          ? await getTemplateDetail({
              id: templateId,
            })
          : {};
        const result = await saveDraft({
          ...detail,
          name: taskName || flowName,
          id: undefined,
        });
        // 创建草稿拿到flowId后跳转
        jumpTo({
          url: '/v4/jiawo/work-flow/graph',
          params: {
            flowId: result,
          },
          isAsync: true,
        });
      } else {
        jumpTo({
          url: '/v4/jiawo/work-flow/graph',
          params: {
            flowName: taskName || flowName,
          },
        });
      }
    } catch (error) {
      Notify.error(error || '服务端异常');
    } finally {
      // 由于hooks调用点可能是列表页，类似单例的场景，所以这里需要清空
      taskNameRef.current = '';
    }
  };

  const quickCreate = async (url, isWorkFlow) => {
    if (isWorkFlow) {
      // await checkWorkFlowPermission();
      // 先弹窗创建草稿
      if (!templateId) {
        Sweetalert.confirm({
          title: '新的自动任务',
          maskClosable: true,
          content: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>
                <span style={{ color: 'red' }}>* </span>任务名称：
              </span>
              <Input
                width={216}
                onChange={e => {
                  taskNameRef.current = e.target.value;
                }}
              ></Input>
            </div>
          ),
          onConfirm,
          onCancel: () => {
            taskNameRef.current = '';
          },
          onClose: () => {
            taskNameRef.current = '';
          },
          closeBtn: true,
        });
      } else {
        onConfirm();
      }
    } else {
      await checkAutoMarketingPermission();
      jumpTo({ url });
    }
  };

  return {
    quickCreate,
  };
};
