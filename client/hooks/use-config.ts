import { createContext, useContext } from 'react';

export interface IConfig {
  width?: number;
  getIndicatorList: (data: any) => Promise<any>;
  getHistory: (data: any, prefix?: string) => Promise<any>;
  getSkillTrees: (data: any) => Promise<any>;
  getNavSpace: (data: any) => Promise<any>;
  recommendData: Record<string, any>;
  [key: string]: any;
}

const Context = createContext<IConfig>({} as IConfig);

export const ConfigProvider = Context.Provider;

export const useConfig = () => {
  return useContext(Context);
};
