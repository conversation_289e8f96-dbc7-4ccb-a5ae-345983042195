import { useEffect } from 'react';

const useListenScrollToBottom = (ref, callback, offset = 100) => {
  useEffect(() => {
    const element = ref.current;

    if (element) {
      const handleScroll = () => {
        // 检查用户是否已经滚动到元素底部上方指定的offset内
        if (element.scrollHeight - element.scrollTop <= element.clientHeight + offset) {
          callback();
        }
      };

      // 绑定滚动事件
      element.addEventListener('scroll', handleScroll);

      // 清理函数
      return () => {
        element.removeEventListener('scroll', handleScroll);
      };
    }
  }, [ref, offset, callback]); // 依赖项包括ref，offset和callback

  // 如果你想要监视整个页面的滚动，而不仅仅是特定元素的滚动，
  // 你可以将element替换为window对象，并相应地调整scrollHeight、scrollTop和clientHeight的用法。
};

export default useListenScrollToBottom;
