import React, { useEffect, useMemo, useState } from 'react';

export interface CommonSelectProps {
  options: { label: string; value: string | number; icon?: React.ReactNode | string }[];
  value?: string | number;
  defaultValue?: string | number;
}

/**
 * 封装各种select组件用的hooks
 * @returns
 */
const useSelect = ({ defaultValue, options, value }: CommonSelectProps) => {
  const [currentValue, setCurrentValue] = useState(defaultValue);
  const currentLabel = useMemo(() => {
    return options.find(option => option.value === currentValue)?.label;
  }, [currentValue, options]);
  const currentSelectOption = useMemo(() => {
    return options.find(option => option.value === currentValue);
  }, [currentValue, options]);

  useEffect(() => {
    value && setCurrentValue(value);
  }, [value]);

  return {
    currentValue,
    currentLabel,
    currentSelectOption,
    setCurrentValue,
  };
};

export default useSelect;
