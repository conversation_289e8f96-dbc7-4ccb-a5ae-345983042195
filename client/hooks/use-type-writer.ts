import { useEffect, useRef, useState } from 'react';

interface IProps {
  text: string;
  interval: number;
  onFinished?: () => void;
  onNextChar?: () => void;
  disableTags?: string[];
  initialized?: boolean;
  wrapClassName?: string;
  blinkerClassName?: string;
}

export const useTypeWriter = (props: IProps) => {
  const {
    text,
    interval,
    onFinished,
    onNextChar,
    disableTags = [],
    initialized = true,
    wrapClassName,
    blinkerClassName,
  } = props;
  const [outputText, setOutputText] = useState('');
  const [oldText, setOldText] = useState('');
  const currentIndexRef = useRef(0);

  useEffect(() => {
    if (!text || !initialized) {
      return;
    }
    let timeoutId;

    if (!oldText) {
      setOldText(text);
      return;
    }

    const typeNextChar = () => {
      const nextChar = text[currentIndexRef.current];
      // 需要验证nextChar，是因为有时候流式传输内容会减少，导致取不到nextChar
      if (nextChar) {
        setOutputText(prev => prev + nextChar);
        currentIndexRef.current++;
      }

      // 兼容react18并发渲染,不然会没有光标
      setTimeout(() => {
        const elem = document.querySelectorAll(`.${wrapClassName}`);
        if (elem.length === 1) {
          renderBlinker(elem[0]);
        } else if (elem.length > 1) {
          const deepestElement = findDeepestElement(elem[elem.length - 2]);
          deepestElement.classList.remove(blinkerClassName);
          renderBlinker(elem[elem.length - 1]);
        }

        if (currentIndexRef.current < text.length - 1) {
          timeoutId = setTimeout(typeNextChar, interval);
          onNextChar && onNextChar();
        } else {
          onFinished && onFinished();
        }
      }, 0);
    };

    const typePreChar = index => {
      if (currentIndexRef.current > index) {
        const newText = outputText.slice(0, currentIndexRef.current - 1);
        setOutputText(newText);
        currentIndexRef.current--;
        timeoutId = setTimeout(() => typePreChar(index), 20);
      } else {
        setOldText(text);
        timeoutId = setTimeout(typeNextChar, interval);
      }
    };

    if (text.slice(0, currentIndexRef.current) !== oldText.slice(0, currentIndexRef.current)) {
      for (let i = 0; i < text.length; i++) {
        if (text[i] !== oldText[i]) {
          // 从这里开始出现分歧，取0 到 i下标的内容为基准内容，然后重新开始type动画
          clearTimeout(timeoutId);
          typePreChar(i);
          break;
        }
      }
    } else {
      setOldText(text);
      timeoutId = setTimeout(typeNextChar, interval);
    }

    return () => {
      clearTimeout(timeoutId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [text, interval, oldText, initialized]);

  // 找到最深层级的最后一个元素
  const findDeepestElement = element => {
    if (element.lastElementChild && !disableTags.includes(element.lastElementChild.tagName)) {
      return findDeepestElement(element.lastElementChild);
    } else {
      return element;
    }
  };

  // 渲染光标
  const renderBlinker = elem => {
    const deepestElement = findDeepestElement(elem);

    // 这一步是为了防止过程中渲染了A标签后面带光标
    if (deepestElement.tagName !== 'A') {
      // 去除已有的光标
      const activeElements = document.querySelectorAll(`.${blinkerClassName}`);
      activeElements.forEach(element => {
        element.classList.remove(blinkerClassName!);
      });
      deepestElement.classList.add(blinkerClassName);
    }
  };

  // 暂时没有看到作用
  // const onDeleteFinished = () => {
  //   console.log('删除动画走完');
  // };

  return outputText;
};
