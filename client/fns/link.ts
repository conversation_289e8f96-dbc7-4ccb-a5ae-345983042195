import { yzLocation } from '@youzan/url-utils';
import { getPageParams } from './getRouteParams';
import isString from 'lodash/isString';
import { navigateTo as microAppNavigateTo } from '@youzan/micro-app-react';
import { logger } from './logger';


/** NOTE: 是否需要在新页面打开链接 */
export const checkOpenUrlIsBlank = (targetHref: string) => {
  if (typeof targetHref === 'string' && targetHref.includes('help.allvalue')) {
    return true;
  }
  // TODO: 产品上线前临时要求改动，需要后端同步能力，所以前端先写死，后端能力同步完，下掉
  if (typeof targetHref === 'string' && targetHref === 'https://www.youzan.com/v4/materials/attachment#/image') {
    return false;
  }
  if (!window._global.url) return;
  const currentHost = new URL(window.location.href).host;
  const targetUrl = new URL(targetHref, window.location.href);
  const targetHost = targetUrl.host;

  // @ts-ignore
  const { base, domain, store, fenxiao, help, wsc } = _global.url || {};
  /** NOTE: 视为同一域名,base和domain会随着主域名变动 */
  const defaultList = [base, domain, store, wsc, 'crm.youzan.com', 'www.youzan.com'];
  /** NOTE: 当前域名是xxx.shangjia.youzan.com会使用defaultList */
  const currentSameDomainList =
    [defaultList, [fenxiao], [help], ['jarvis.youzan.com'], ['jiawo.youzan.com']].find((domainList: string[]) => {
      return domainList.find((domain = '') => {
        if (!domain) return false;
        return domainList.includes(currentHost);
      });
    }) ?? defaultList;
  /** NOTE: 判断同host */
  const sameHost = currentHost === targetHost;
  /** NOTE: 主要作用是用defaultList判断 */
  const isInSameDomain = sameHost || currentSameDomainList.find(item => item?.includes(targetHost));

  const notBlank = isInSameDomain;
  return !notBlank;
};

export const checkNeedReload = (targetUrl: string) => {
  // targetUrl 可能没有http:前缀 导致抛出错误，判断是否有http前缀
  const url = targetUrl.startsWith('https') ? targetUrl : `https://${targetUrl}`;
  const targetObj = new URL(url);
  if (targetObj?.pathname === window.location.pathname && targetObj?.hash) {
    setTimeout(() => {
      window.location.reload();
    }, 200);
  }
};

export const getHelpDocUrl = (data: any, helpData: any = {}, sceneType) => {
  // https://help.allvalue.com.cn/zh-CN/?q=%E5%A6%82%E4%BD%95%E8%AE%BE%E7%BD%AE%E4%BC%98%E6%83%A0%E5%88%B8
  if (sceneType === 'allvalue') {
    return {
      url: new URL(`/zh-CN/?q=${encodeURIComponent(data)}`, 'https://help.allvalue.com.cn'),
    };
  }

  const helpUrl = new URL(
    helpData?.helpUrl || 'https://help.youzan.com',
    /** NOTE: 企助没有接过帮助中心,特殊处理COPY帮助中心企助的首页URL */
    sceneType === 'WEASS' ? 'https://help.youzan.com/displaylist/list_26_42013_all' : yzLocation.href,
  );
  const queryData = getPageParams(
    ['/displaylist/detail_:id', '/displaylist/search_:idText', '/displaylist/list_:id', '/'],
    helpUrl.href,
  );
  let { id = 4 } = queryData;
  /** NOTE: id只取第一部分,兼容 CRM 导购 企微 群团团帮助中心*/
  if (isString(id)) {
    id = id.split('_').shift();
  }
  if (queryData.idText) {
    id = queryData.idText.split('_').shift();
  }

  const url = new URL(`/displaylist/search_${id}_${encodeURIComponent(data)}?page=1`, helpUrl);
  return { url, id };
};

// 官网跟UCD没有帮助中心
export const checkHelpUrlByEnvType = (sceneType: string) => {
  if (['OFFICIAL_SALE_BOT', 'UCD_BOT'].includes(sceneType)) {
    return false;
  }
  return true;
};

export const navigateTo = (url: string) => {
  try {
    microAppNavigateTo(url);
    logger &&
      logger.log({
        et: 'click', // 事件类型
        ei: 'micro_app_navigate', // 事件标识
        en: '微前端跳转', // 事件名称
        params: {
          url,
          component: 'jarvis_assistant',
        }, // 事件参数
      });
  } catch (error) {
    window.location.href = url;
  }
};
