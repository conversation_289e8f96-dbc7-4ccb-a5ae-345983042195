import { ASSISTANT_ANSWER_LOADING, TOGGLE_JARVIS_ASSISTANTS } from '../definitions/global-events';
import { getAppContext } from '@youzan/micro-app-react';
import {
  PAGE_JARVIS_VISIBLE_CHANGED,
  NAV_FOLD_STATUS_CHANGED,
  TOGGLE_NAV_FOLD_STATUS,
  GET_INIT_NAV_FOLD_STATUS,
  SET_JARVIS_ASSISTANTS_UNREAD_COUNT,
} from '../constants/events';

const TARGET_APP_MENU = '@pc-shared-service/menu';
const TARGET_APP_OPERATIONS = '@pc-shared-service/operations';
const CURRENT_APP = '@pc-shared-service/jarvis-assistants';

/** NOTE: 切换助手: 操作区域/JarvisPageSDK => 助手 */
export const toggleJarvisAssistants = (detail: any) => {
  try {
    const ctx = getAppContext(CURRENT_APP);
    ctx?.emit(TOGGLE_JARVIS_ASSISTANTS, { detail }, { targetApp: [CURRENT_APP] });
  } catch (error) {
    const event = new CustomEvent(TOGGLE_JARVIS_ASSISTANTS, { detail });
    window.dispatchEvent(event);
  }
};

/** NOTE: 助手监听事件: 切换助手 */
export const addEventListenerToggleJarvisAssistants = (callback: (event: any) => void) => {
  const _event = TOGGLE_JARVIS_ASSISTANTS;
  try {
    const menuCtx = getAppContext(TARGET_APP_MENU);
    menuCtx.listen(_event, callback);
    const operationsCtx = getAppContext(TARGET_APP_OPERATIONS);
    operationsCtx.listen(_event, callback);
    const jarvisAssistantsCtx = getAppContext(CURRENT_APP);
    jarvisAssistantsCtx.listen(_event, callback);
    /** NOTE: 兼容图文布点 */
    window.addEventListener(_event, callback);
  } catch (error) {
    window.addEventListener(_event, callback);
  }
};

/** NOTE: 助手取消监听事件: 切换助手 */
export const removeEventListenerToggleJarvisAssistants = (callback: (event: any) => void) => {
  const _event = TOGGLE_JARVIS_ASSISTANTS;
  try {
    const menuCtx = getAppContext(TARGET_APP_MENU);
    menuCtx.remove(_event, callback);
    const operationsCtx = getAppContext(TARGET_APP_MENU);
    operationsCtx.remove(_event, callback);
    const jarvisAssistantsCtx = getAppContext(CURRENT_APP);
    jarvisAssistantsCtx.remove(_event, callback);
    /** NOTE: 兼容图文布点 */
    window.removeEventListener(_event, callback);
  } catch (error) {
    window.removeEventListener(_event, callback);
  }
};

/** NOTE: 助手显隐变化: 助手 => 操作区域 */
export const toggleJarvisAssistantsVisibleChange = (detail: any) => {
  try {
    const ctx = getAppContext(CURRENT_APP);
    ctx.emit(PAGE_JARVIS_VISIBLE_CHANGED, { detail }, { targetApp: [TARGET_APP_OPERATIONS, CURRENT_APP] });
  } catch (error) {
    const event = new CustomEvent(PAGE_JARVIS_VISIBLE_CHANGED, { detail });
    window.dispatchEvent(event);
  }
};

/** NOTE: 切换菜单展开状态: 助手 => 菜单 */
export const toggleNavFoldStatus = (detail: any) => {
  try {
    const ctx = getAppContext(CURRENT_APP);
    ctx
      ?.invoke(TOGGLE_NAV_FOLD_STATUS, detail, {
        targetApp: TARGET_APP_MENU,
      })
      .then(res => console.log(res))
      .catch(err => console.error(err));
  } catch (error) {
    /** NOTE: 不需要兼容方案 */
  }
};

/** NOTE: 获取菜单展开状态: 助手 => 菜单 */
export const getInitNavFoldStatus = () => {
  return new Promise((resolve, reject) => {
    try {
      const ctx = getAppContext(CURRENT_APP);
      if (!ctx) return reject();
      ctx
        .invoke(GET_INIT_NAV_FOLD_STATUS, 1, {
          targetApp: TARGET_APP_MENU,
        })
        .then(res => {
          resolve(res);
        })
        .catch(() => {
          reject();
        });
    } catch (error) {
      reject();
    }
  });
};

/** NOTE: 助手监听事件: 菜单展开状态变化 */
export const addEventListenerNavFoldStatusChange = (callback: (event: any) => void) => {
  try {
    const ctx = getAppContext(CURRENT_APP);
    ctx.listen(NAV_FOLD_STATUS_CHANGED, callback);
  } catch (error) {
    /** NOTE: 不需要兼容方案 */
  }
};

/** NOTE: 助手取消监听事件: 菜单展开状态变化 */
export const removeEventListenerNavFoldStatusChange = (callback: (event: any) => void) => {
  try {
    const menuCtx = getAppContext(TARGET_APP_MENU);
    menuCtx.remove(NAV_FOLD_STATUS_CHANGED, callback);
  } catch (error) {
    /** NOTE: 不需要兼容方案 */
  }
};

/** NOTE: 通知 => 助手未读消息 */
export const setJarvisAssistantsUnreadCount = (detail: any) => {
  try {
    const ctx = getAppContext(CURRENT_APP);
    ctx.emit(SET_JARVIS_ASSISTANTS_UNREAD_COUNT, { detail }, { targetApp: [TARGET_APP_OPERATIONS] });
  } catch (error) {
    const event = new CustomEvent(SET_JARVIS_ASSISTANTS_UNREAD_COUNT, { detail });
    window.dispatchEvent(event);
  }
};

/** NOTE: 助手notify */
export const notifyJarvisAssistants = (detail: any) => {
  try {
    const ctx = getAppContext(CURRENT_APP);
    ctx?.emit(ASSISTANT_ANSWER_LOADING, { detail }, { targetApp: [CURRENT_APP] });
  } catch (error) {
    const event = new CustomEvent(ASSISTANT_ANSWER_LOADING, { detail });
    window.dispatchEvent(event);
  }
};

/** NOTE: 助手监听事件: 助手notify */
export const addEventListenerNotifyJarvisAssistants = (callback: (event: any) => void) => {
  const _event = ASSISTANT_ANSWER_LOADING;
  try {
    const menuCtx = getAppContext(TARGET_APP_MENU);
    menuCtx.listen(_event, callback);
    const operationsCtx = getAppContext(TARGET_APP_OPERATIONS);
    operationsCtx.listen(_event, callback);
    const jarvisAssistantsCtx = getAppContext(CURRENT_APP);
    jarvisAssistantsCtx.listen(_event, callback);
    /** NOTE: 兼容图文布点 */
    window.addEventListener(_event, callback);
  } catch (error) {
    window.addEventListener(_event, callback);
  }
};

/** NOTE: 助手取消监听事件: 切换助手 */
export const removeEventListenerNotifyJarvisAssistants = (callback: (event: any) => void) => {
  const _event = ASSISTANT_ANSWER_LOADING;
  try {
    const menuCtx = getAppContext(TARGET_APP_MENU);
    menuCtx.remove(_event, callback);
    const operationsCtx = getAppContext(TARGET_APP_MENU);
    operationsCtx.remove(_event, callback);
    const jarvisAssistantsCtx = getAppContext(CURRENT_APP);
    jarvisAssistantsCtx.remove(_event, callback);
    /** NOTE: 兼容图文布点 */
    window.removeEventListener(_event, callback);
  } catch (error) {
    window.removeEventListener(_event, callback);
  }
};
