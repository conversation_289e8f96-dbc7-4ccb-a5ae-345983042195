// 创建一个单例锁工厂
export class Locker {
  private static instance: Locker;

  private locks: Map<string, boolean> = new Map();

  //  private constructor() {}

  // sleep
  static sleep(time: number) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(true);
      }, time);
    });
  }

  static getInstance() {
    if (!Locker.instance) {
      Locker.instance = new Locker();
    }
    return Locker.instance;
  }

  isLocking(key: string) {
    return this.locks.get(key);
  }

  unLock(key: string) {
    this.locks.delete(key);
  }

  lock(key: string) {
    this.locks.set(key, true);
  }

  registe(key: string) {
    if (!this.locks.has(key)) {
      this.locks.set(key, false);
    }
    return {
      lock: () => this.lock(key),
      unLock: () => this.unLock(key),
      isLocking: () => this.isLocking(key),
    };
  }
}
