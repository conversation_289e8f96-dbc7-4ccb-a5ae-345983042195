import { yzLocation } from '@youzan/url-utils';

const escapeRegExp = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};
const doCompilePattern = (pattern: string) => {
  let regexpSource = '';
  const paramNames: any[] = [];
  const tokens: any[] = [];

  let match: any;
  let lastIndex = 0;
  const matcher = /:([a-zA-Z_$][a-zA-Z0-9_$]*)|\*\*|\*|\(|\)|\\\(|\\\)/g;
  // tslint:disable-next-line:no-conditional-assignment
  while ((match = matcher.exec(pattern))) {
    if (match.index !== lastIndex) {
      tokens.push(pattern.slice(lastIndex, match.index));
      regexpSource += escapeRegExp(pattern.slice(lastIndex, match.index));
    }

    if (match[1]) {
      regexpSource += '([^/]+)';
      paramNames.push(`${match[1]}`);
    } else if (match[0] === '**') {
      regexpSource += '(.*)';
      paramNames.push('splat');
    } else if (match[0] === '*') {
      regexpSource += '(.*?)';
      paramNames.push('splat');
    } else if (match[0] === '(') {
      regexpSource += '(?:';
    } else if (match[0] === ')') {
      regexpSource += ')?';
    } else if (match[0] === '\\(') {
      regexpSource += '\\(';
    } else if (match[0] === '\\)') {
      regexpSource += '\\)';
    }

    tokens.push(match[0]);

    lastIndex = matcher.lastIndex;
  }

  if (lastIndex !== pattern.length) {
    tokens.push(pattern.slice(lastIndex, pattern.length));
    regexpSource += escapeRegExp(pattern.slice(lastIndex, pattern.length));
  }

  return {
    pattern,
    regexpSource,
    paramNames,
    tokens,
  };
};

const CompiledPatternsCache = Object.create(null);

const compilePattern = (pattern: any) => {
  if (!CompiledPatternsCache[pattern]) {
    CompiledPatternsCache[pattern] = doCompilePattern(pattern);
  }

  return CompiledPatternsCache[pattern];
};

const getParamNames = (pattern: string) => {
  return compilePattern(pattern).paramNames;
};

export const applyRoute = (route = '', params: any = {}) => {
  let path = route;
  const paramList = getParamNames(route);
  paramList.forEach((item: string | number) => {
    if (!params[item]) {
      throw new Error(`key: ${item} was not found in route: ${route}, ${params}`);
    }
    path = path.replace(`:${item}`, params[item]);
  });
  const queryList = Object.entries(params).filter(([key, _]) => {
    return !paramList.includes(key);
  });

  const queryObj: any = queryList.length ? Object.fromEntries(queryList) : undefined;
  return queryObj ? `${path}?${new URLSearchParams(queryObj).toString()}` : path;
};

export const matchPattern = (pattern: string, pathname: string) => {
  // Ensure pattern starts with leading slash for consistency with pathname.
  if (pattern.charAt(0) !== '/') {
    pattern = '/' + pattern;
  }

  const doCompilePattern2 = compilePattern(pattern);
  let { regexpSource } = doCompilePattern2;
  const { paramNames } = doCompilePattern2;
  const { tokens } = doCompilePattern2;

  if (pattern.charAt(pattern.length - 1) !== '/') {
    regexpSource += '/?'; // Allow optional path separator at end.
  }

  // Special-case patterns like '*' for catch-all routes.
  if (tokens[tokens.length - 1] === '*') {
    regexpSource += '$';
  }

  const match = pathname.match(new RegExp('^' + regexpSource, 'i'));
  if (match === null) {
    return null;
  }

  const matchedPath = match[0];
  let remainingPathname = pathname.substr(matchedPath.length);

  if (remainingPathname) {
    // Require that the match ends at a path separator, if we didn't match
    // the full path, so any remaining pathname is a new path segment.
    if (matchedPath.charAt(matchedPath.length - 1) !== '/') {
      return null;
    }

    // If there is a remaining pathname, treat the path separator as part of
    // the remaining pathname for properly continuing the match.
    remainingPathname = '/' + remainingPathname;
  }

  return {
    remainingPathname,
    paramNames,
    paramValues: match.slice(1).map(function(v: string) {
      return v && decodeURIComponent(v);
    }),
  };
};

export function getParams(pattern: string, url: string) {
  const currentUrl = new URL(url, yzLocation.href);
  const query = Object.fromEntries(new URLSearchParams(yzLocation.search.substring(1)) as any);
  const match = matchPattern(pattern.replace(/\/\//g, '/'), currentUrl.pathname);
  if (!match) {
    return;
  }

  const { paramNames } = match;
  const { paramValues } = match;

  const params: any = {};

  paramNames.forEach(function(paramName: string | number, index: number) {
    params[paramName] = paramValues[index];
  });

  return {
    ...query,
    ...params,
  };
}

export const getPageParams = (patternList: string[], url = yzLocation.href) => {
  if (!Array.isArray(patternList)) {
    return getParams(patternList, url);
  }
  for (const pattern of patternList) {
    const result = getParams(pattern, url);

    if (result) {
      return result;
    }
  }
};
