import formatLargeNumber from '@youzan/utils/money/formatLargeNumber';
import isNil from 'lodash/isNil';
import toSafeInteger from 'lodash/toSafeInteger';

/**
 * 数字除 100 后的值
 * @param num 待展示的数字
 * @param prefix 前缀
 * @param suffix 后缀
 */
export const getNumberDivideHundredText = function getNumberDivideHundredText(
  num: number | string,
  prefix = '',
  suffix = '',
): string {
  const actualNum = Number(num);
  if (isNaN(actualNum)) {
    return '-';
  }
  return `${prefix}${formatLargeNumber(Number((actualNum / 100).toFixed(2)))}${suffix}`;
};

/** 检查是否为不可展示的非法数字 */
export const checkInValid = (num: number | string = '-', inValidNum?: number | number[]) => {
  const inValidNumList: Array<number | string> = inValidNum
    ? Array.isArray(inValidNum)
      ? inValidNum
      : [inValidNum]
    : [];
  if (isNil(num) || isNaN(Number(num))) {
    return true;
  }
  if (inValidNumList.indexOf(num) !== -1) {
    return true;
  }
  return false;
};

/**
 * 对值的转换，不做单位处理
 */
export const formatValues = {
  /**
   * 数字格式化，空值转为 '-'
   */
  number: (num: number | string = '-', inValidNum?: number | number[], fixedNum = 2, returnAbs?: boolean) => {
    if (checkInValid(num, inValidNum)) {
      return '-';
    }
    const actualNum = returnAbs ? Math.abs(Number(num)) : Number(num);
    return parseFloat(actualNum.toFixed(fixedNum));
  },
  /**
   * 百分比格式化，空值转为 '-'
   */
  percent: (num: number | string = '-', inValidNum?: number | number[], returnAbs?: boolean) => {
    if (checkInValid(num, inValidNum)) {
      return '-';
    }
    const actualNum = returnAbs ? Math.abs(Number(num)) : Number(num);
    return parseFloat((actualNum * 100).toFixed(2));
  },
  /**
   * 金钱格式化，空值转为 '-'
   */
  money: (num: number | string = '-', inValidNum?: number | number[], fixedNum = 2, returnAbs?: boolean) => {
    if (checkInValid(num, inValidNum)) {
      return '-';
    }

    const actualNum = returnAbs ? Math.abs(Number(num)) : Number(num);
    return parseFloat((actualNum / 100).toFixed(fixedNum));
  },
};

const bigFormatConfig = [
  {
    lenRequired: 11,
    unit: '亿',
    divide: 100000000,
  },
  {
    lenRequired: 7,
    unit: '万',
    divide: 10000,
  },
];

/**
 * 对大额数字 format（百万、百亿数据会被转化为以万、亿为单位的数字，并添加对应的单位后缀）
 */
const formatNumberWithUnitDivide = (num: number) => {
  const integerLen = toSafeInteger(num).toString().length;
  const formatConfig = bigFormatConfig.find(c => c.lenRequired <= integerLen);
  if (formatConfig) {
    return `${formatLargeNumber(formatValues.number(num / formatConfig.divide) as number)}${formatConfig.unit}`;
  }
  return formatLargeNumber(num);
};

export type IFormatOption<Extra = {}> =
  | number
  | ({
      inValidNum?: number | number[];
      withUnitDivide?: boolean;
      unit?: string;
      fixFloat?: boolean;
      fixedNum?: number;
      returnAbs?: boolean;
      withUnitPadding?: boolean;
    } & Extra);

export const formats = {
  /**
   * 数字 format
   * @param num 数字
   * @param option
   * @return
   */
  number: (
    num: string | number = '-',
    option: IFormatOption = {
      withUnitDivide: false,
      unit: '',
    },
  ) => {
    const inValidNum = typeof option === 'number' ? option : option.inValidNum;
    const withUnitDivide = typeof option === 'number' ? false : option.withUnitDivide;
    let unit = typeof option === 'number' ? '' : option.unit || '';
    const fixFloat = typeof option === 'number' ? false : option.fixFloat;
    const fixedNum = typeof option === 'number' ? 2 : option.fixedNum || 2;
    const returnAbs = typeof option === 'number' ? false : option.returnAbs;
    const res = formatValues.number(num, inValidNum, fixedNum, returnAbs);
    const withUnitPadding = typeof option === 'number' ? false : option.withUnitPadding;

    unit = withUnitPadding ? `<span class="unit-area">${unit}</span>` : unit;

    if (res === '-') {
      return res;
    }

    if (fixFloat) {
      return `${res.toFixed(fixedNum)}${unit}`;
    }

    if (withUnitDivide) {
      return `${formatNumberWithUnitDivide(res)}${unit}`;
    }
    return `${formatLargeNumber(res)}${unit}`;
  },

  /**
   * 将小数转化为 % 的形式
   * @param num 小数
   * @param option 无效数据
   * @return
   */
  percent: (
    num: string | number = '-',
    option: IFormatOption = {
      withUnitDivide: false,
      fixFloat: false,
    },
  ) => {
    const inValidNum = typeof option === 'number' ? option : option.inValidNum;
    const fixFloat = typeof option === 'number' ? false : option.fixFloat;
    const returnAbs = typeof option === 'number' ? false : option.returnAbs;
    const withUnitPadding = typeof option === 'number' ? false : option.withUnitPadding;
    const res = formatValues.percent(num, inValidNum, returnAbs);
    const unit = withUnitPadding ? '<span class="unit-area">%</span>' : '%';
    if (res !== '-') {
      if (withUnitPadding) return fixFloat ? `${res.toFixed(2)}${unit}` : `${res}${unit}`;
      return fixFloat ? `${res.toFixed(2)}%` : `${res}%`;
    }
    return res;
  },

  /**
   * 将金钱转化为 xx 的形式
   * @param num 数字
   * @param option
   * @return
   */
  money: (
    num: string | number = '-',
    option: IFormatOption = {
      withUnitDivide: false,
      unit: '',
    },
  ) => {
    const inValidNum = typeof option === 'number' ? option : option.inValidNum;
    const withUnitDivide = typeof option === 'number' ? false : option.withUnitDivide;
    let unit = typeof option === 'number' ? '' : option.unit || '';
    const fixFloat = typeof option === 'number' ? false : option.fixFloat;
    const fixedNum = typeof option === 'number' ? 2 : option.fixedNum || 2;
    const returnAbs = typeof option === 'number' ? false : option.returnAbs;
    const res = formatValues.money(num, inValidNum, fixedNum, returnAbs);

    const withUnitPadding = typeof option === 'number' ? false : option.withUnitPadding;

    unit = withUnitPadding ? `<span class="unit-area">${unit}</span>` : unit;

    if (res === '-') {
      return res;
    }

    if (fixFloat) {
      // @ts-ignore
      return `${formatLargeNumber(res.toFixed(fixedNum))}${unit}`;
    }

    if (withUnitDivide) {
      return `${formatNumberWithUnitDivide(res)}${unit}`;
    }
    return `${formatLargeNumber(res)}${unit}`;
  },
};

export type FormatTypes = keyof typeof formats;

/**
 * 格式化数据的统一调用方法
 */
export function simpleFormat(value: string | number, type?: FormatTypes, option?: IFormatOption) {
  return type ? formats[type](value, option) : value.toString();
}
