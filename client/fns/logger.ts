import { get } from 'lodash';

const loggerUrl = 'https://b.yzcdn.cn/client-log-sdk/web-logger-1.2.23-min.js';

// fx域名加载埋点sdk会与其他adm模块不兼容。。。
const ignoreDomainList = ['fx.youzan.com'];

// 检查当前域名是否在忽略列表中
const checkIgnore = () => {
  const { hostname } = window.location;
  return ignoreDomainList.includes(hostname);
};

export const loadLoggerSDK = async () => {
  console.log('未检测到全局埋点实例, jarvis-assistants开始加载埋点实例');
  const script = document.createElement('script');
  script.src = loggerUrl;

  return new Promise((resolve, reject) => {
    script.onload = resolve;
    script.onerror = reject;
    document.body.appendChild(script);
  });
};
// @ts-ignore
if (!window?.Logger && !window?.logger) {
  const si = window._global.kdtId;
  const li = window._global.userId || get(window, '_global.business.userInfo.userId');
  const m = window._global.mobile || get(window, '_global.business.userInfo.mobile');
  !checkIgnore() &&
    loadLoggerSDK().then(() => {
      try {
        // @ts-ignore
        // eslint-disable-next-line
      if (WebLogger) {
          try {
            let autoHistoryDisplay = false;
            /**
             * 可在config.xx.js中设置埋点的初始化配置
             * 例如在config.default.js中加入以下配置
             * track: {
             *    yai: '',
             * }
             */
            let trackInfo = {};
            // @ts-ignore
            let yai = trackInfo.yai || 'wsc_b';
            // @ts-ignore
            let trackHistoryDisplay = trackInfo.autoHistoryDisplay;
            if (false || trackHistoryDisplay) {
              autoHistoryDisplay = true;
            }
            if (window.Logger && typeof window.Logger.log === 'function') {
              window.Logger.setAutoHistoryDisplay(autoHistoryDisplay);
              return;
            }
            let params = {};
            params = {};
            let event = {
              si: si || '',
              params: params,
            };
            let Logger;
            // @ts-ignore
            // eslint-disable-next-line
          if (typeof WebLogger.getTracker === 'function') {
              // @ts-ignore
              // eslint-disable-next-line
            Logger = WebLogger.getTracker({
                requestUrl: 'https://tj1.youzanyun.com/v3/js/log',
                yai: yai,
                autoHashDisplay: false,
                autoHistoryDisplay: autoHistoryDisplay,
                user: {
                  li: li || '',
                  m: m || '',
                },
                event: event,
                autoNodeView: false,
              });
            } else {
              // @ts-ignore
              // eslint-disable-next-line
            Logger = new WebLogger({
                baseUrl: 'https://tj1.youzanyun.com/v3/js/log',
                plat: {
                  yai: yai,
                },
                autoHashDisplay: false,
                autoHistoryDisplay: autoHistoryDisplay,
                user: {
                  li: li || '',
                  m: m || '',
                },
                event: event,
                autoView: false,
              });
            }
            Logger.init();
            window.Logger = Logger;
          } catch (e) {
            console.log(e);
          } finally {
            if (!window.Logger || typeof window.Logger.log !== 'function') {
              window.Logger = {
                log: function() {},
              };
            }
          }
        }
      } catch (error) {
        console.log('兜底埋点实例初始化失败', error);
      }
    });
  // 埋点日志
}

// @ts-ignore
export const logger = window?.Logger || window?.logger;
