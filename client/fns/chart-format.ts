import { IFormatOption, formats } from './number';
import { DateTypeEnum, DisplayTimeEnum } from '../constants/time';
import cloneDeep from 'lodash/cloneDeep';
import { IFormatDisplayTimeOptions, getTimeStr, getWeekStr } from './time';
import { format, parse, getQuarter } from 'date-fns';

import isDate from 'lodash/isDate';

export const inValidNum = -999999.99;

export const formatOptions: Record<keyof typeof formats, Omit<IFormatOption, number>> = {
  number: { inValidNum },
  money: {
    fixFloat: true,
    inValidNum,
    unit: '元', // 只在图表的tooltip中需要展示金额单位
  },
  percent: {
    fixFloat: true,
    inValidNum,
  },
};

// 对象key转小写
const toLowerCaseKeys = obj => {
  return Object.keys(obj).reduce((newObj, key) => {
    newObj[key.toLowerCase()] = obj[key];
    return newObj;
  }, {});
};

export const convertKeysInArrayToLower = array => {
  return array.map(item => toLowerCaseKeys(item));
};

/**
 * 获得指定时间的对应年季度数
 */
const getQuarterStr = (time: string) => {
  const date = parse(time);
  const year = date.getFullYear();
  const quarter = getQuarter(date);
  return `${year}第${quarter}季度`;
};

const numberDateRe = /^(\d{4})(\d{2})(\d{2})$/;
/**
 * 将时间转换成 周、月 展示的形式
 * @param time 时间（如：2019-03-01）
 * @param displayType 要转化成的展示形式
 */
export function formatDisplayTime(
  time: string | number | Date = '',
  displayType: DisplayTimeEnum = DisplayTimeEnum.day,
  { formatNoDash = false, withWeekDateRange = false, showYear = false }: IFormatDisplayTimeOptions = {},
) {
  if (!time) {
    return '';
  }

  if (isDate(time)) {
    time = format(time, 'YYYY.MM.DD HH:mm:ss');
  }

  // 判断是否是 YYYYMMDD 这种类型的字符串（后端存储格式）
  const isNumberDate = String(time).match(numberDateRe);

  const timeStr =
    isNumberDate || formatNoDash
      ? // 如果后端传的是20190202这种值，做个转化
        String(time).replace(numberDateRe, '$1.$2.$3')
      : time.toString();

  let res = time;
  switch (+displayType) {
    case DisplayTimeEnum.realtime:
      res = format(new Date().setHours(parseInt(timeStr)), 'YYYY.MM.DD HH:mm');
      break;
    case DisplayTimeEnum.day:
      res = format(timeStr, showYear ? 'YYYY.MM.DD' : 'MM-DD');
      break;
    case DisplayTimeEnum.week:
      res = getWeekStr(timeStr, withWeekDateRange);
      break;
    case DisplayTimeEnum.month:
      res = format(parse(timeStr.replace(/-/g, '/')), 'YYYY.MM');
      break;
    case DisplayTimeEnum.quarter:
      res = getQuarterStr(timeStr);
      break;
    default:
      res = timeStr;
  }
  return res;
}

export const formatXAis = (currentDay, dateType: DateTypeEnum, yearChanged?: boolean) => {
  const resDateType = dateType?.toString();
  switch (resDateType) {
    case DateTypeEnum.REAL_TIME:
      return getTimeStr({ hour: currentDay });
    case DateTypeEnum.NATURAL_WEEK:
      return getWeekStr(currentDay);
    case DateTypeEnum.NATURAL_MONTH:
      return format(parse(currentDay.replace(/-/g, '/')), 'YYYY-MM');
    case DateTypeEnum.SELF_DEFINE:
      return formatDisplayTime(currentDay, DisplayTimeEnum.day, { showYear: yearChanged });
    default:
      return currentDay;
  }
};

export const getBreakpointContainerWith = (): any => {
  const dragDropWrapperHtml = document.querySelector('#cardListWrapper') as HTMLDivElement;
  if (!dragDropWrapperHtml) {
    return 'sm';
  }
  const width = dragDropWrapperHtml.offsetWidth;
  if (width < 500) {
    return 'sm';
  }
  if (width >= 500 && width < 1000) {
    return 'md';
  }
  if (width >= 1000 && width < 1800) {
    return 'lg';
  }
  if (width >= 1800) {
    return 'xl';
  }
  return 'lg';
};

/**
 * 转化为百分比
 * @param valRate 0-100
 */
export const formatRatioPercent = (valRate, options = {}) =>
  formats.percent(Math.abs(valRate), { ...formatOptions.percent, ...options });

export const formatMoney = (val, options = {}) => {
  return formats.money(val, { ...formatOptions.money, ...options });
};

/**
 * 转化为百分比（最小为0且允许负数）
 * @param valRate 0-100
 */
export const formatRatioPercentMinZero = valRate => formats.percent(Math.max(valRate, 0), formatOptions.percent);

export const HAS_FLOW_INTRO_STORAGE_KEY = 'dashboard-v2:editing:intro';

/**
 * 增加卡片后，输出完成二维坐标顺序
 * @param cardSite
 * @param selectedCardIds
 */
export function addQuotesToCardSite(cardSite, selectedCardIds) {
  const newCardSite = cloneDeep(cardSite);
  Object.keys(cardSite).forEach(key => {
    const lengths = cardSite[key].map(item => item.length);
    selectedCardIds.forEach(item => {
      const min = Math.min(...lengths);
      const minIndex = lengths.findIndex(l => l === min);
      newCardSite[key][minIndex] = [...(newCardSite[key][minIndex] || []), item];
      lengths[minIndex] = min + 1;
    });
  });
  return newCardSite;
}

export const formatValues = (data, formatType) => {
  if (!data && data !== 0) return '-';
  if (!formatType) return data.toFixed(2);
  if (formatType === 'MONEY') {
    return formatMoney(data);
  }
  if (formatType === 'PERCENT') {
    return formatRatioPercent(data);
  }
  return data.toFixed(2);
};
