import { differenceInCalendarWeeks, endOfWeek, format, getQuarter, parse } from 'date-fns';
import startOfWeek from 'date-fns/start_of_week';
import isDate from 'lodash/isDate';
import isNumber from 'lodash/isNumber';
import isString from 'lodash/isString';
import padStart from 'lodash/padStart';

import { DateTypeEnum, DisplayTimeEnum } from '../constants/time';

type IDateType = number | string | Date;


/**
 * 获得指定日期对应周的日期范围
 */
export const getDateRangeOfWeek = (time: string, withYear = false) => {
    const date = parse(time);
    const start = startOfWeek(date, { weekStartsOn: 1 });
    const end = endOfWeek(date, { weekStartsOn: 1 });
    const rangeStr = `${format(start, 'MM-dd')} ~ ${format(end, 'MM-dd')}`;
    if (withYear) {
        const year = end.getFullYear();
        return `${year}-${rangeStr}`;
    }
    return `${rangeStr}`;
};

/**
 * 获得指定时间的对应年周数
 */
export const getWeekStr = (time: string, withDateRange = false) => {
    const date = parse(time);
    const year = endOfWeek(date, { weekStartsOn: 1 }).getFullYear();
    const diff = differenceInCalendarWeeks(date, parse(`${year}/01/01`), { weekStartsOn: 1 }) + 1;
    const weekStr = `${year}第${diff}周`;
    return withDateRange ? `${weekStr}（${getDateRangeOfWeek(time)}）` : `${weekStr}`;
};

/**
 * 获得指定时间的对应年季度数
 */
const getQuarterStr = (time: string) => {
    const date = parse(time);
    const year = date.getFullYear();
    const quarter = getQuarter(date);
    return `${year}第${quarter}季度`;
};

interface ITimeOptions {
    hour: number | string;
    minute?: number | string;
}

const padTime = (v: number | string) => `${padStart(`${v}`, 2, '0')}`;

/**
 * 获取时间字符串 (01:00)
 */
export const getTimeStr = (options: ITimeOptions) => {
    const { hour, minute } = options;
    return `${padTime(hour)}:${padTime(minute || 0)}`;
};

interface ITimeRangeOptions {
    start: ITimeOptions;
    end?: ITimeOptions;
}

/**
 * 获取时间范围字符串 (01:00 ~ 02:00)
 */
export const getTimeRangeStr = (options: ITimeRangeOptions) => {
    const { start, end } = options;
    if (!end) {
        return getTimeStr(start);
    }
    return `${getTimeStr(start)} ~ ${getTimeStr(end)}`;
};

export interface IFormatDisplayTimeOptions {
    formatNoDash?: boolean; // 是否预格式化无横线的时间字符串（如 20190202）
    withWeekDateRange?: boolean; // 如果是自然周，是否展示日期区间
    showYear?: boolean;
}

const numberDateRe = /^(\d{4})(\d{2})(\d{2})$/;
/**
 * 将时间转换成 周、月 展示的形式
 * @param time 时间（如：2019-03-01）
 * @param displayType 要转化成的展示形式
 */
export function formatDisplayTime(
    time: string | number | Date = '',
    displayType: DisplayTimeEnum = DisplayTimeEnum.day,
    { formatNoDash = false, withWeekDateRange = false }: IFormatDisplayTimeOptions = {},
) {
    if (!time) {
        return '';
    }

    if (isDate(time)) {
        time = format(time, 'YYYY.MM.DD HH:mm:ss');
    }

    // 判断是否是 YYYYMMDD 这种类型的字符串（后端存储格式）
    const isNumberDate = String(time).match(numberDateRe);

    const timeStr =
        isNumberDate || formatNoDash
            ? // 如果后端传的是20190202这种值，做个转化
            String(time).replace(numberDateRe, '$1-$2-$3')
            : time.toString();

    let res = time;
    switch (+displayType) {
        case DisplayTimeEnum.realtime:
            res = format(new Date().setHours(parseInt(timeStr, 10)), 'YYYY.MM.DD HH:mm');
            break;
        case DisplayTimeEnum.day:
            res = format(new Date(timeStr), 'YYYY.MM.DD');
            break;
        case DisplayTimeEnum.week:
            res = getWeekStr(timeStr, withWeekDateRange);
            break;
        case DisplayTimeEnum.month:
            res = format(new Date(timeStr), 'YYYY.MM');
            break;
        case DisplayTimeEnum.quarter:
            res = getQuarterStr(timeStr);
            break;
        default:
            res = format(new Date(timeStr), 'YYYY.MM.DD');
    }
    return res;
}

/**
 * 将 date-filter 组件的值格式化为后端请求接收的 currentDay 值
 */
export function formatRequestCurrentDay(
    dateRange:
        | [string, string]
        | {
            startDay: string;
            endDay: string;
        }
        | string
        | number
        | Date,
    dateType: string | number = DateTypeEnum.NATURAL_DAY,
) {
    let formattedDateRange: [IDateType, IDateType];

    if (Array.isArray(dateRange)) {
        // 数组
        formattedDateRange = dateRange;
    } else if (isString(dateRange) || isNumber(dateRange) || isDate(dateRange)) {
        // 单个数据
        formattedDateRange = [dateRange, dateRange];
    } else {
        // 对象
        formattedDateRange = [dateRange.startDay, dateRange.endDay];
    }
    const [startDay, endDay] = formattedDateRange;
    switch (String(dateType)) {
        case DateTypeEnum.REAL_TIME:
        case DateTypeEnum.NATURAL_DAY:
        case DateTypeEnum.NATURAL_WEEK:
        case DateTypeEnum.NATURAL_MONTH:
        case DateTypeEnum.NATURAL_QUARTER:
        case DateTypeEnum.NATURAL_YEAR:
            return format(startDay, 'YYYYMMDD');
        case DateTypeEnum.LAST_SEVEN_DAY:
        case DateTypeEnum.LAST_THIRTY_DAY:
            return format(endDay, 'YYYYMMDD');
        default:
            throw new Error('还没有为该时间类型设置格式化方式');
    }
}

export function formatRequestTimeParams(dateRange: [string, string] | { startDay: string; endDay: string }) {
    let start: string;
    let end: string;
    if (Array.isArray(dateRange)) {
        [start, end] = dateRange;
    } else {
        start = dateRange.startDay;
        end = dateRange.endDay;
    }
    return {
        startDay: +format(start, 'YYYYMMDD'),
        endDay: +format(end, 'YYYYMMDD'),
    };
}
