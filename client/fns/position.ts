import { IPositionFunction, PopPositions } from 'zent';
import { getPageWidth } from '../constants/width';

// zent-popover-v2-position-arrow-top-right
export function prefix(name: string) {
  return `zent-popover-v2-${name}`;
}

export const MESSAGE_PADDING = 16;

export const CONTENT_RIGHT = 18;

export const PAGE_HEADER_HEIGHT = 57;

export const INPUT_HEIGHT = 64;

const getViewportSize = () => {
  const doc = document.documentElement;
  const scrollbarWidth = Math.abs(window.innerWidth - doc.clientWidth);
  const scrollbarHeight = Math.abs(window.innerHeight - doc.clientHeight);
  return {
    width: Math.max(doc.clientWidth, window.innerWidth) - scrollbarWidth,
    height: Math.max(doc.clientHeight, window.innerHeight) - scrollbarHeight,
  };
};
const positionHandler = ({ width = getPageWidth() } = {}) => {
  let position: PopPositions | IPositionFunction = 'auto-top-center';

  try {
    position = options => {
      const { cushion, contentRect, relativeRect, anchorRect } = options;
      const viewport = getViewportSize();
      // 限制左右上下间距，都以XY轴的坐标为准
      const limit = {
        left: viewport.width - width + MESSAGE_PADDING - CONTENT_RIGHT,
        right: viewport.width - MESSAGE_PADDING - CONTENT_RIGHT,
        top: PAGE_HEADER_HEIGHT,
        bottom: viewport.height - INPUT_HEIGHT,
      };

      let horizontal: 'left' | 'right' | 'center' = 'center';

      const defaultPopHorizonPotion = {
        left: (relativeRect.left + relativeRect.right - contentRect.width) / 2,
        right: (relativeRect.left + relativeRect.right + contentRect.width) / 2,
      };
      if (defaultPopHorizonPotion.left < limit.left) {
        horizontal = 'left';
      } else if (defaultPopHorizonPotion.right > limit.right) {
        horizontal = 'right';
      }

      let vertical: 'top' | 'bottom' = 'top';

      if (anchorRect.top - cushion - contentRect.height < limit.top) {
        vertical = 'bottom';
      }

      const positionData = {
        horizontal: {
          left: limit.left,
          right: limit.right - contentRect.width,
          center: defaultPopHorizonPotion.left,
        },
        vertical: {
          top: relativeRect.top - cushion - contentRect.height,
          bottom: relativeRect.bottom + cushion,
        },
      };

      return {
        style: {
          position: 'absolute',
          left: positionData.horizontal[horizontal],
          top: positionData.vertical[vertical],
        },

        className: prefix(`position-${vertical}-${horizontal}`),
      };
    };
  } catch (error) {
    position = 'auto-top-center';
  }

  return position;
};

export default positionHandler;
