import { windowOpen, yzLocation } from '@youzan/url-utils';
import { isEmpty } from 'lodash';

// 非spa路由内跳转
const jumpTo = ({ url, needNewWindow = true, params = {}, isAsync = false }) => {
  const newUrl = `${url}${isEmpty(params) ? '' : '?'}${Object.keys(params)
    .map(key => `${key}=${params[key]}`)
    .join('&')}`;

  // 兼容safari无法在异步请求中打开新窗口的问题
  if (isAsync) {
    const a = document.createElement('a');
    a.href = newUrl;
    needNewWindow && (a.target = '_blank');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    return;
  }
  if (needNewWindow) {
    windowOpen(newUrl);
    return;
  }
  yzLocation.href = newUrl;
};

export default jumpTo;
