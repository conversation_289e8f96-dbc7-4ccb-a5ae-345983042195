import uniq from 'lodash/uniq';

type requestHandler = (...args: any[]) => Promise<any>;

// 合并相同参数的请求为一个请求
export const combineRequest = (api: requestHandler) => {
  const slaveRequestMap = new Map<symbol, requestHandler[]>();
  return (...args: any[]) => {
    const symbol = Symbol.for(JSON.stringify(args));
    if (slaveRequestMap.get(symbol)) {
      const requestArray = slaveRequestMap.get(symbol) as requestHandler[];
      return new Promise((resolve: any) => {
        requestArray.push(resolve);
      });
    } else {
      slaveRequestMap.set(symbol, []);
      const request = api(...args);
      request.then(data => {
        const slaveRequest = slaveRequestMap.get(symbol) as requestHandler[];
        slaveRequest.map(resolve => resolve(data));
        slaveRequestMap.delete(symbol);
      });
      return request;
    }
  };
};

// 合并多个批量接口请求到一个请求，并做数据的缓存；
export const combineRequestAndParams = (api: (data: any) => Promise<any[]>, key: string = 'id'): any => {
  // 计时器
  let timer: any;
  // 缓存已经请求过的数据;
  const cacheDataMap: Record<string, any> = {};
  // 批量请求的参数集；
  const args: any[] = [];
  // 等待响应的请求集
  const requestMap: Record<string, requestHandler[]> = {};

  // 发起批量求情；
  const batchRequest = () => {
    const batchRequestParams = uniq(args);
    // 清空参数集;
    args.length = 0;

    const handleRequest = (singleArg: any, data: any[]) => {
      const requests = requestMap[singleArg];
      // 响应请求
      requests.forEach(resolve => resolve(data));
      // 清空请求集
      requests.length = 0;
    };

    const deleteRequestParam = (singleArg: any) => {
      // 删除已经返回结果的参数
      const index = batchRequestParams.indexOf(singleArg);
      index > -1 && batchRequestParams.splice(index, 1);
    };

    api(batchRequestParams).then(data => {
      data.forEach(dataItem => {
        // 找到和当前数据相关的请求集合；
        const singleArg = dataItem[key];

        // TODO 后面要引入 LRU 算法，防止内存爆掉。
        cacheDataMap[singleArg] = dataItem;

        handleRequest(singleArg, [dataItem]);

        deleteRequestParam(singleArg);
      });

      // 处理未返回结果的参数，返回默认值：[]
      batchRequestParams.forEach(param => {
        handleRequest(param, []);
        deleteRequestParam(param);
      });
    });
  };

  return ([...multiArgs]: [any]) => {
    // 检查缓存数据
    const cacheKeys = multiArgs.filter(singleArg => cacheDataMap[singleArg]);
    const unCacheKeys = multiArgs.filter(singleArg => !cacheDataMap[singleArg]);
    const cacheData = cacheKeys.map(singleArg => cacheDataMap[singleArg]);
    if (cacheKeys.length && cacheKeys.length === multiArgs.length) {
      return Promise.resolve(cacheData);
    }
    // 合并参数
    unCacheKeys.length && args.push(...unCacheKeys);
    // 将请求放入请求集，等待 resolve。
    const requestList: Array<Promise<any[]>> = multiArgs.map(singleArg => {
      return new Promise(resolve => {
        if (cacheDataMap[singleArg]) {
          resolve([cacheDataMap[singleArg]]);
          return;
        }
        requestMap[singleArg] || (requestMap[singleArg] = []);
        requestMap[singleArg].push(resolve as any);
      });
    });

    const requestHandler = Promise.all(requestList).then((dataList: any[][]) => {
      const result: any[] = [];
      dataList.forEach(item => {
        const resItem = Array.isArray(item) ? item : [item];
        result.push(...resItem);
      });
      return result;
    });
    // 在 event loop 中进行批量请求；
    clearTimeout(timer);
    timer = setTimeout(batchRequest, 0);

    return requestHandler;
  };
};
