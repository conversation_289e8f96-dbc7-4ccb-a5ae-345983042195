import { IFormatOption, formats } from './number';

export const inValidNum = -999999.99;
export const formatOptions: Record<keyof typeof formats, Omit<IFormatOption, number>> = {
  number: { inValidNum },
  money: {
    fixFloat: true,
    inValidNum,
    unit: '元', // 只在图表的tooltip中需要展示金额单位
  },
  percent: {
    fixFloat: true,
    inValidNum,
  },
};

/**
 * 转化为百分比
 * @param valRate 0-100
 */
export const formatRatioPercent = (valRate, options = {}) =>
  formats.percent(Math.abs(valRate), { ...formatOptions.percent, ...options });
