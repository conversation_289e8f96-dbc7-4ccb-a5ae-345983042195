import classNames from 'classnames';
import React, { useState } from 'react';
import { Icon } from 'zent';

import { getPageWidth } from '../../constants/width';
import ClampText from '../../pages/task-flow/components/ReportTemplate/components/ClampText';
import Link from '../../pages/task-flow/components/ReportTemplate/components/Link';
import { LoadingIcon, CompleteIcon } from './icon';
import { logger } from '../../fns/logger';

import style from './index.m.scss';

const CiteItem = ({ title, url, author, prefix = '', onClick }) => {
  return (
    <div className={style.item} onClick={onClick}>
      <Link href={url} className={style.title}>
        <ClampText text={prefix + title} width={getPageWidth() - 60} />
      </Link>
      {/* {author && <p className={style.desc}>作者: {author}</p>} */}
    </div>
  );
};

interface IProps {
  data: any[];
  extra?: any;
  className?: string;
  showLoading?: boolean;
}
export const Cite = ({ data = [], extra, className, showLoading }: IProps) => {
  const [active, setActive] = useState(false);
  const handleSwitchActive = () => {
    setActive(preActive => !preActive);
    logger &&
      logger.log({
        et: 'click', // 事件类型
        ei: 'jarvis_assistant_cite_card', // 事件标识
        en: '引用资料-下拉点击', // 事件名称
        params: {
          component: 'jarvis_assistant',
        }, // 事件参数
      });
  };
  return (
    <div
      className={classNames(style.wrap, className, {
        [style.active]: active,
      })}
    >
      <div className={style.btn} onClick={handleSwitchActive}>
        {showLoading ? (
          <>
            <LoadingIcon className={classNames(style.svgIcon, style.loading)} />
            <CompleteIcon className={classNames(style.svgIcon, style.complete)} />
          </>
        ) : (
          <CompleteIcon className={style.svgIcon} />
        )}

        <span className={style.btnTitle}>引用 {data.length} 篇资料作为参考</span>
        <Icon type="down" />
      </div>
      <div className={style.itemWrap}>
        {data?.map((item: any, idx: number) => (
          <CiteItem
            key={item.title}
            {...item}
            prefix={`${idx + 1}. `}
            onClick={() => {
              logger &&
                logger.log({
                  et: 'click', // 事件类型
                  ei: 'jarvis_assistant_cite_card_doc', // 事件标识
                  en: '引用资料-帮助中心文档点击', // 事件名称
                  params: {
                    component: 'jarvis_assistant',
                  }, // 事件参数
                });
            }}
          />
        ))}
        {extra && (
          <>
            <div className={style.divider}></div>
            <CiteItem
              {...extra}
              onClick={() => {
                logger &&
                  logger.log({
                    et: 'click', // 事件类型
                    ei: 'jarvis_assistant_cite_card_help_center', // 事件标识
                    en: '引用资料-前往帮助中心搜索点击', // 事件名称
                    params: {
                      component: 'jarvis_assistant',
                    }, // 事件参数
                  });
              }}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default Cite;
