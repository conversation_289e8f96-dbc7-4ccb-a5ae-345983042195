@use '../../sass/vars/index.scss' as *;

.wrap {
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  .btn {
    display: flex;
    align-items: center;
    font-size: 14px;
    width: 100%;
  }
  &.active {
    padding: 8px;
    background-color: $cite-bg;
    .btn {
      padding-top: 0;
      padding-left: 0;
      background-color: transparent;
    }
    // NOTE: 有点hack 1px 1ms的速度
    .item-wrap {
      transition: max-height 800ms cubic-bezier(0.33, 0.66, 0.66, 1);
      max-height: 500px;
      overflow-y: auto;
    }
  }
}

.btn {
  display: inline-block;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  background-color: $cite-bg;
}

.btn-title {
  margin-right: 4px;
}

.item-wrap {
  overflow: hidden;
  max-height: 0;
  a {
    margin: 0 !important;
    line-height: 18px !important;
  }
}

.item {
  display: flex;
  align-items: center;
  height: 18px;
  & + & {
    margin-top: 4px;
  }

  &:last-child {
    border-color: transparent;
  }
  .title {
    color: $primary-color;
    font-size: 12px;
    font-weight: normal;
  }
  .desc {
    color: $grey-color;
    margin-top: 4px;
  }
}

.divider {
  border-top: 1px dashed $cite-divider-color;
  margin: 8px 0;
}

.svg-icon {
  margin-right: 4px;
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
    display: none;
  }
}

.loading {
  animation: rotate 1s ease-in-out 0s 1 forwards;
}

@keyframes complete {
  0% {
    display: none;
  }
  99% {
    display: none;
  }
  100% {
    display: inline;
  }
}

.complete {
  animation: complete 1s ease-in-out 0s 1 forwards;
}
