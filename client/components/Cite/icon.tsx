import React from 'react';

export const LoadingIcon = props => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14 8.05705C14 9.40105 13.5488 10.7061 12.7186 11.7631C11.8885 12.8201 10.7275 13.5677 9.42175 13.8862C8.11602 14.2046 6.74118 14.0755 5.51761 13.5194C4.29404 12.9634 3.29259 12.0126 2.67379 10.8195C2.05499 9.62647 1.85468 8.26019 2.10497 6.9397C2.35525 5.61921 3.04164 4.42099 4.05411 3.53711"
      stroke="#155BD4"
      strokeWidth="1.4"
      stroke-linecap="round"
    />
  </svg>
);

export const CompleteIcon = props => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.9473 8C13.9473 11.2851 11.2841 13.9482 7.99902 13.9482C4.7139 13.9482 2.05078 11.2851 2.05078 8C2.05078 4.71488 4.7139 2.05176 7.99902 2.05176"
      stroke="#155BD4"
      strokeWidth="1.4"
      stroke-linecap="round"
    />
    <path
      d="M5.64844 6.8685L7.8619 9.08491L13.9458 3.00098"
      stroke="#155BD4"
      strokeWidth="1.4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
