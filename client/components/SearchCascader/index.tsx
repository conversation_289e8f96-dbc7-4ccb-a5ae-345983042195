import { MenuCascader, FormField, Input } from 'zent';
import { useInfinityList } from '../../hooks/useInfinityList';
import { useEffect, useMemo, useState } from 'react';
import styles from './index.m.scss';
import classnames from 'classnames';

const SearchCascader = ({
  fetchList,
  defaultParams,
  value,
  onChange,
  placeholder,
  keyValueMap = { label: 'name', value: 'id', children: 'secondGroups' },
  fixOptions,
  multiple,
  ...cascaderProps
}) => {
  const { hasMore, list, loadMore, keywords, total, setKeywords } = useInfinityList({
    fetchList,
    defaultParams,
    keywordKey: 'keyWord',
    defaultTopOption: fixOptions,
  });
  const [showInput, setShowInput] = useState(false);
  const [selectedValueNames, setSelectedValueNames] = useState([]);

  const formatList = list => {
    // 按照keyValueMap格式化数据, 可能包含嵌套结构
    return list.map(item => {
      const { label, value, children } = keyValueMap;
      return {
        label: item[label],
        value: item[value],
        children: item[children] ? formatList(item[children]) : [],
      };
    });
  };

  const formatedList = useMemo(() => formatList(list), [list]);

  const findName = (data, list) => {
    const [first, ...rest] = data;
    const target = list.find(item => item.id === first);
    if (rest.length) {
      return [target.name, ...findName(rest, target.secondGroups)];
    }
    return [target.name];
  };

  return (
    <div
      onClick={() => setShowInput(true)}
      onBlur={() => setShowInput(false)}
      className={styles.cascaderContainer}
      style={{ width: cascaderProps.width }}
    >
      {showInput && (
        <input
          className={styles.input}
          autoFocus
          value={keywords}
          onChange={e => setKeywords(e.target.value)}
          onFocus={() => setShowInput(true)}
        ></input>
      )}
      <MenuCascader
        placeholder={
          selectedValueNames.length ? selectedValueNames.join(' / ') : placeholder || '输入搜索'
        }
        className={classnames(selectedValueNames.length && styles.selected, styles.menuCascader)}
        onChange={data => {
          const names = findName(data, list);
          setSelectedValueNames(names);

          setShowInput(false);
          onChange && onChange(data);
        }}
        options={formatedList || []}
        // 不用value是因为menuCascader中有bug，value不存在于options中的话，搜索时候会报错
        // value={value}
        loadOptions={() => loadMore()}
        scrollable
        clearable
        changeOnSelect
        // multiple
        expandTrigger="hover"
        loadChildrenOnScroll={hasMore}
        {...cascaderProps}
      />
    </div>
  );
};

export default SearchCascader;

const renderCascader = (childProps, props) => {
  return <SearchCascader {...(props.props as any)} {...childProps} />;
};

export const FormSearchCascader = props => {
  return (
    <FormField {...props} defaultValue={props.defaultValue ?? (props.props?.multiple ? [] : null)}>
      {childProps => renderCascader(childProps, props)}
    </FormField>
  );
};
