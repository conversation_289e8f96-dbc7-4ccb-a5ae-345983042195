import React, { useEffect } from 'react';
import { Select, InfiniteScroller, <PERSON>Field } from 'zent';
import { useInfinityList } from '../../hooks/useInfinityList';
import styles from './index.m.scss';

interface ISearchSelectProps {
  fetchList: (params: any) => Promise<any>;
  searchKey: string;
  defaultParams?: any;
}

function SearchSelect({
  fetchList,
  defaultParams,
  value,
  onChange,
  placeholder,
  fixOptions,
  width,
  ...selectOptions
}) {
  const { hasMore, list, loadMore, keywords, setKeywords } = useInfinityList({
    fetchList,
    defaultParams,
    keywordKey: 'keyWord',
  });

  return (
    <Select
      placeholder={placeholder || '输入搜索'}
      clearable
      options={list.map((item: any) => ({ key: item.id, text: item.name }))}
      keyword={keywords}
      onKeywordChange={keyword => {
        setKeywords(keyword);
      }}
      width={width}
      onChange={onChange}
      value={value}
      renderOptionList={(optionList, renderOption) => {
        return (
          <InfiniteScroller
            hasMore={hasMore} // 当不在加载状态时，表示还有更多数据可加载
            loadMore={loadMore}
            skipLoadOnMount
            className={styles.optionList}
          >
            {fixOptions && renderOption(fixOptions, -1)}
            {optionList.map((item, index) => renderOption(item, index))}
          </InfiniteScroller>
        );
      }}
    />
  );
}

export default SearchSelect;

function renderSelect(childProps, props) {
  return <SearchSelect {...(props.props as any)} {...childProps} />;
}

export function FormSearchSelectField(props) {
  return (
    <FormField {...props} defaultValue={props.defaultValue ?? (props.props?.multiple ? [] : null)}>
      {/* @ts-ignore */}
      {childProps => renderSelect(childProps, props)}
    </FormField>
  );
}
