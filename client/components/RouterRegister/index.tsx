import React, { Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { BASE_NAME } from 'constants/router';

interface IRouterRegisterProps {
  routes: Array<{
    path: string;
    // 普通组件 | 懒加载组件
    component: React.ComponentType<any> | React.LazyExoticComponent<React.ComponentType<any>>;
  }>;
  bizName?: string;
}

const RouterRegister: React.FC<IRouterRegisterProps> = ({ routes, bizName = 'work-flow' }) => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Router basename={`${BASE_NAME}/${bizName}`}>
        <Routes>
          {routes.map(({ path, component: Component }, index) => {
            return <Route key={index} path={path} element={<Component />}></Route>;
          })}
        </Routes>
      </Router>
    </Suspense>
  );
};

export default RouterRegister;
