import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import get from 'lodash/get';
import omit from 'lodash/omit';
import React, { ReactNode, useEffect, useState } from 'react';

import Text from '../../atom-components/Text';
import Cite from '../../components/Cite';
import { useCiteText } from '../../hooks/use-cite-text';
import { useTypeWriter } from '../../hooks/use-type-writer';

import styles from './style.m.scss';

interface IProps {
  text: string;
  interval: number;
  onFinished?: () => void;
  onNextChar?: () => void;
  onStop?: (str: string) => void;
  event?: EventEmitter<void>;
  isHeavyText?: boolean;
  isPolling?: boolean;
  citeList?: any[];
  disableTags?: string[];
  children?: ReactNode;
}

enum TempStatus {
  DONE = -1,
  INITIAL,
  READY,
}

/** NOTE: 报告分析的流式输出 */
export const ReportTemplateTypeWriter = (props: IProps) => {
  const { text, interval, isHeavyText = true, isPolling, children, onFinished, onNextChar } = props;

  const [isFinished, setIsFinished] = useState<boolean>(false);
  const [isTempFinished, setIsTempFinished] = useState(TempStatus.INITIAL);

  const handleFinished = () => {
    if (!isPolling) {
      setIsFinished(true);
      onFinished && onFinished();
    } else if (isTempFinished === TempStatus.INITIAL) {
      setIsTempFinished(TempStatus.READY);
    }
  };

  useEffect(() => {
    setIsTempFinished(TempStatus.INITIAL);
  }, [text]);

  useEffect(() => {
    if (!isPolling && !isFinished && isTempFinished) {
      setIsTempFinished(TempStatus.DONE);
      setIsFinished(true);
      onFinished && onFinished();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPolling, isFinished, isTempFinished]);

  const typeWriterText = useTypeWriter({
    text,
    interval,
    onFinished: handleFinished,
    onNextChar,
    disableTags: ['P'],
    wrapClassName: styles.markdownBody,
    blinkerClassName: styles.blinker,
  });

  if (isFinished && children) {
    return <>{children}</>;
  }

  if (!isHeavyText) {
    return <div>{typeWriterText}</div>;
  }

  return <Text className={styles.markdownBody} data={typeWriterText} />;
};

/** NOTE: 引用的流式输出 */
export const CiteTypeWriter = (props: IProps) => {
  const { text, interval, event, onFinished, onNextChar, onStop, citeList = [], isHeavyText } = props;

  const [initialized, setInitialized] = useState(false);
  const typeWriterText = useTypeWriter({
    text,
    interval,
    onFinished,
    onNextChar,
    disableTags: ['STRONG', 'SPAN'],
    initialized,
    wrapClassName: styles.markdownBody,
    blinkerClassName: styles.blinker,
  });

  event?.useSubscription(() => {
    onStop && onStop(typeWriterText);
  });

  const prefixText = useCiteText(citeList);

  useEffect(() => {
    // 如果有引用,要先等css动画结再做流式输出,依赖后端返回内容
    if (prefixText) {
      setTimeout(() => {
        setInitialized(true);
        // 根据css动画来
      }, 1000);
      return;
    }
    setInitialized(true);
  }, [prefixText]);

  const prefixRender = citeList?.length > 0 && <Cite className="cite-wrap" data={citeList} showLoading />;

  const customerComponents = (defaultComponents: any) => {
    return {
      a(props: any) {
        // 脚注
        const isFootnote = get(props, 'aria-describedby') === 'footnote-label';
        if (isFootnote) {
          // 解析锚点的值,只考虑是数字吧,不然找不到list的i
          const childrenId = Number((props?.href ?? '').replace(/[^0-9]/gi, ''));
          return <span className={styles.footnoteItem}>{childrenId}</span>;
        }
        // 其他a标签 default
        return defaultComponents.a(props);
      },
      sup(props) {
        return <>{props.children}</>;
      },
      // 引用
      section(props) {
        const isFootnotes = get(props, 'className') === 'footnotes';
        if (isFootnotes) {
          // return <Cite className="cite-wrap" data={citeList} />;
          return null;
        }
        return <section {...omit(props, 'node')} />;
      },
    };
  };

  if (!isHeavyText) {
    return <div>{typeWriterText}</div>;
  }

  return (
    <Text
      data={typeWriterText}
      prefixRender={prefixRender}
      prefixText={prefixText}
      customerComponents={customerComponents}
      className={styles.markdownBody}
    />
  );
};
