@use '../../sass/vars/index.scss' as *;

// 这里的样式只能流式输出有关,其他都合并到.rc-markdown-body

.markdown-body {
  .blinker:after {
    content: '▌';
    animation: blinker 1s step-end infinite;
  }
  @keyframes blinker {
    0% {
      visibility: visible;
    }
    50% {
      visibility: hidden;
    }
    100% {
      visibility: visible;
    }
  }
}

.footnote-item {
  display: inline-block;
  height: 14px;
  margin: 0 2px;
  font-size: 10px;
  line-height: 14px;
  text-align: center;
  padding: 0 4px;
  color: $grey-color;
  background-color: $bg-color-100;
  border-radius: 8px;
  cursor: pointer;
}
