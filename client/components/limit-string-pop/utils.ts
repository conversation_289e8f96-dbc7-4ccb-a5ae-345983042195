const canvas = document.createElement('canvas');

const context = canvas.getContext('2d');

export const limitStringByWidth = (text: string, fontStyle, width: number) => {
  if (!context) {
    return;
  }
  context.font = fontStyle;
  const dimension = context.measureText(text);
  if (dimension.width < width) {
    return {
      enough: true,
      text,
    };
  }
  const dimensionPoint = context.measureText('...');
  const textWidth = width - dimensionPoint.width;
  let resText = '',
    i = 1;
  while (i++) {
    const currentText = text.substr(0, i);
    const currentDimension = context.measureText(currentText);
    if (currentDimension.width > textWidth) {
      break;
    }
    resText = currentText;
  }

  return {
    enough: false,
    text: `${resText}...`,
  };
};
