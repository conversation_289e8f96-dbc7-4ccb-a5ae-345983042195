import * as React from 'react';
import { useState, useEffect } from 'react';
import { Pop, IPopProps } from 'zent';
import { limitStringByWidth } from './utils';

const LimitStringWithPop: React.FC<{
  text: string;
  width: number;
  popProps?: Partial<IPopProps>;
}> = ({ text, width, popProps = {} }) => {
  const [font, setFont] = useState<string>('');
  const [stringData, setStringData] = useState<{ enough: boolean; text: string }>();
  // eslint-disable-next-line no-unused-vars
  const Content: any = Pop.withPop(_ => {
    return <div style={{ maxWidth: 400 }}>{text}</div>;
  });
  useEffect(() => {
    setStringData(limitStringByWidth(text, font, width));
  }, [font, text, width]);

  return (
    <span
      ref={ref => {
        if (!ref) {
          return;
        }
        const {
          fontVariant,
          fontStyle,
          fontSize,
          fontWeight,
          lineHeight,
          fontFamily,
        } = window.getComputedStyle(ref);
        const font = `${fontStyle} ${fontVariant} ${fontWeight} ${fontSize}/${lineHeight} ${fontFamily}`;
        setFont(font || '');
      }}
    >
      {stringData ? (
        stringData.enough ? (
          stringData.text
        ) : (
          <Pop
            position="top-center"
            className="limit-string-pop"
            {...popProps}
            trigger="hover"
            content={<Content />}
          >
            {stringData.text}
          </Pop>
        )
      ) : (
        undefined
      )}
    </span>
  );
};

export default LimitStringWithPop;
