import request from 'common/request';

export const createFlow = (params: any) =>
  request.post('/v4/jiawo/api/work-flow/createFlow', params);

export const editFlow = (params: any) => request.post('/v4/jiawo/api/work-flow/edit', params);

export const copyFlow = (params: any) => request.post('/v4/jiawo/api/work-flow/copy', params);

export const deleteFlow = (params: any) => request.post('/v4/jiawo/api/work-flow/delete', params);

export const enableFlow = (params: any, options?: any) =>
  request.post('/v4/jiawo/api/work-flow/enable', params, options);

export const disableFlow = (params: any) => request.post('/v4/jiawo/api/work-flow/disable', params);

export const getFlowRuntimeData = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/getFlowRuntimeData', params);

export const getFlowRuntimeRecord = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/getFlowRuntimeRecord', params);

export const saveDraft = (params: any) => request.post('/v4/jiawo/api/work-flow/saveDraft', params);

export const hasWorkFlowPermission = () =>
  request.get('/v4/jiawo/api/work-flow/checkWorkFlowPermission');

export const getTemplateDetail = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/getFlowTempById', params);

export const getFlowList = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/getFlowList', params);

export const searchData = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/searchData', params);

export const querySelectedData = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/querySelectedData', params);

export const querySelectedDataByTag = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/querySelectedDataByTag', params);

export const listFlowTemps = (params: any) =>
  request.get('/v4/jiawo/api/work-flow/listFlowTemps', params);

export const queryFlowTemps = async (params: any) => {
  return request.get('/v4/jiawo/api/work-flow/queryFlowTemps', params);
};

export const queryAllFlowTempGroup = async (params: any) => {
  return request.get('/v4/jiawo/api/work-flow/queryAllFlowTempGroup', params);
};

export const queryAllTriggerWithGroup = async (params: any) => {
  return request.get('/v4/jiawo/api/work-flow/queryAllTriggerWithGroup', params);
};

export const createDraftByTemplate = async (params: any) => {
  return request.post('/v4/jiawo/api/work-flow/createDraftByTemplate', params);
};
