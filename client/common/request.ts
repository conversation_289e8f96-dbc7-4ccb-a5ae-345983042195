import request from 'zan-pc-ajax';

class Request {
  public get<T = any>(url: string, data: null | object = {}, options?: any) {
    return request<T>({
      url,
      method: 'GET',
      ...options,
      data,
    });
  }

  public post<T = any>(url: string, data: null | object = {}, options?: any) {
    return request<T>({
      url,
      method: 'POST',
      contentType: 'application/json',
      ...options,
      data,
    });
  }
}

export default new Request();
