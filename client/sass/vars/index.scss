// scss定义变量
$gray-color: #f7f7f7;

// 选中的颜色
$selected-color: #155bd4;


/** NOTE: 变量 */
$page-width: var(--layout-right-width, 320px);
$page-right-border-width: var(--layout-right-border-width, 4px);
$page-x-padding: 10px;
$message-inner-padding: 10px;
/** NOTE: 去除各种padding之后撑满的宽度 */
$full-content-width: calc(#{$page-width} - #{$page-right-border-width} - #{$page-x-padding * 2 + $message-inner-padding * 2});

/** NOTE: 主题色 */
$gray-100: #fff !default;
$gray-200: #f7f7f7 !default;
$gray-300: #e0e0e0 !default;
$gray-400: #ccc !default;
$gray-500: #999 !default;
$gray-600: #333 !default;
$gray-700: #000 !default;

$green-base: #45a110 !default;
$orange-base: #ed6a18 !default;
$red-base: #d42f15 !default;

$star-color: #edd418 !default;
$shadow-color: $gray-700 !default;

/** NOTE: 主题色 */
$primary-color: var(--jarvis-primary-color, #155bd4) !default;
$title-color: var(--jarvis-title-color, #333) !default;
$grey-color: var(--jarvis-grey-color, #999) !default;
$disabled-color: var(--jarvis-disabled-color, #666) !default;
$bg-color-100: var(--jarvis-bg-color-100, #f8f8f8) !default;
$bg-color-200: var(--jarvis-bg-color-200, #fff) !default;
$bg-color-300: var(--jarvis-bg-color-300, #eee) !default;
$bg-color-400: var(--jarvis-bg-color-400, #f0f0f0) !default;

/** NOTE: 业务颜色 */
$avatar-display: var(--jarvis-avatar-display, 'block') !default;
$content-bg: var(--jarvis-content-bg, $bg-color-100) !default;
$input-bg: var(--jarvis-input-bg, $bg-color-200) !default;
$input-placeholder-color: var(--jarvis-input-placeholder-color, #ccc) !default;
$input-border-color: var(--jarvis-input-border-color, transparent) !default;
$input-border-active-color: var(--jarvis-input-border-active-color, transparent) !default;
$right-message-color: var(--jarvis-right-message-color, $bg-color-200) !default;
$right-message-bg: var(--jarvis-right-message-bg, $primary-color) !default;
$left-message-color: var(--jarvis-left-message-color, $title-color) !default;
$left-message-bg: var(--jarvis-left-message-bg, $bg-color-200) !default;
$message-item-bg: var(--jarvis-item-bg, $bg-color-100) !default;
$message-item-active-bg: var(--jarvis-item-active-bg, $bg-color-400) !default;
$icon-color: var(--jarvis-icon-color, $title-color) !default;
$icon-bg: var(--jarvis-icon-bg, $bg-color-200) !default;
$icon-active-color: var(--jarvis-icon-active-color, $bg-color-200) !default;
$icon-active-bg: var(--jarvis-icon-active-bg, $title-color) !default;
$operate-color: var(--jarvis-operate-color, $title-color) !default;
$operate-active-color: var(--jarvis-operate-active-color, $primary-color) !default;
$operate-bg: var(--jarvis-operate-bg, $bg-color-200) !default;
$operate-active-bg: var(--jarvis-operate-active-bg, $bg-color-200) !default;
$operate-border-color: var(--jarvis-operate-border-color, $bg-color-200) !default;
$tooltip-color: var(--jarvis-tooltip-color, $bg-color-200) !default;
$tooltip-bg: var(--jarvis-tooltip-bg, $title-color) !default;
$drawer-bg: var(--jarvis-drawer-bg, $bg-color-200) !default;
$grey-btn-bg: var(--jarvis-grey-btn-bg, rgba(0, 0, 0, 0.06)) !default;
$grey-btn-active-bg: var(--jarvis-grey-btn-active-bg, rgba(0, 0, 0, 0.03)) !default;
$quote-bg: var(--jarvis-quote-bg, $bg-color-100) !default;
$quote-active-bg: var(--jarvis-quote-active-bg, $bg-color-400) !default;
$quote-item-bg: var(--jarvis-quote-item-bg, $bg-color-100) !default;
$quote-item-border-color: var(--jarvis-quote-item-border-color, $bg-color-100) !default;
$quote-item-border-radius: var(--jarvis-quote-item-border-radius, 4px) !default;
$input-send-icon-color: var(--jarvis-input-send-icon-color, rgba(21, 91, 212, 0.5)) !default;
$input-send-icon-active-color: var(--jarvis-input-send-icon-active-color, $primary-color) !default;
$input-loading-icon-color: var(--jarvis-input-loading-icon-color, $primary-color) !default;
$input-loading-icon-action-color: var(--jarvis-input-loading-icon-action-color, $primary-color) !default;
$cite-bg: var(--jarvis-cite-bg, $bg-color-100) !default;
$cite-divider-color: var(--jarvis-cite-divider-color, #ccc) !default;
$send-icon-color: var(--jarvis-send-icon-color, $primary-color) !default;
$loading-color: var(--jarvis-loading-color, #5e697d) !default;
$md-pre-bg: var(--jarvis-md-pre-bg, $bg-color-200) !default;