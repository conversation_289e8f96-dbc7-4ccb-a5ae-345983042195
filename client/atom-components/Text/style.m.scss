@use '../../sass/vars/index.scss' as *;

.error-style {
  color: #ccc;

  p {
    color: #ccc!important;
  }

}

.footnote-item {
  display: inline-block;
  height: 14px;
  margin: 0 2px;
  font-size: 10px;
  line-height: 14px;
  text-align: center;
  padding: 0 4px;
  color: $grey-color;
  background-color: $bg-color-100;
  border-radius: 8px;
  cursor: pointer;
}


.pop {
  padding: 6px 8px;
  :global(.zent-pop-v2-inner) {
    padding: 0 !important;
  }
  a {
    color: $primary-color;
  }
}
