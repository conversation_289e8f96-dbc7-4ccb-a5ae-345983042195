import classNames from 'classnames';
import get from 'lodash/get';
import isInteger from 'lodash/isInteger';
import omit from 'lodash/omit';
import React, { ReactNode } from 'react';
import ReactMarkdown from 'react-markdown';
import { SpecialComponents } from 'react-markdown/lib/ast-to-react';
import { NormalComponents } from 'react-markdown/lib/complex-types';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { Pop } from 'zent';

import Cite from '../../components/Cite';
import Link from '../../pages/task-flow/components/ReportTemplate/components/Link';
import { FixedDefinition, InteractiveListProps } from '../../pages/task-flow/constants/interactiveList';
import { useCiteText } from '../../hooks/use-cite-text';

import style from './style.m.scss';

// 禁用引用的场景
const disabledCiteSceneTypeList = ['summary'];

interface ICommonProps {
  messageData: InteractiveListProps;
  onAction?: (definition: FixedDefinition, segmentId: string) => void;
  onCustomLinkClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  options?: {
    isBlank?: boolean;
    isPureText?: boolean;
  };
}

interface IOptions extends Omit<InteractiveListProps, 'data'> {
  isBlank?: boolean;
  isPureText?: boolean;
}

interface IBaseProps {
  data: string;
  options?: IOptions;
  onAction?: (definition: FixedDefinition, segmentId: string) => void;
  onCustomLinkClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  prefixRender?: ReactNode;
  suffixRender?: ReactNode;
  prefixText?: string;
  customerComponents?: (
    _: Partial<Omit<NormalComponents, keyof SpecialComponents> & SpecialComponents>,
  ) => Partial<Omit<NormalComponents, keyof SpecialComponents> & SpecialComponents>;
  className?: string;
}

interface ITextProps extends ICommonProps {
  prefixRender?: ReactNode;
  suffixRender?: ReactNode;
  prefixText?: string;
  customerComponents?: (
    _: Partial<Omit<NormalComponents, keyof SpecialComponents> & SpecialComponents>,
  ) => Partial<Omit<NormalComponents, keyof SpecialComponents> & SpecialComponents>;
}

interface IFootnoteTextProps extends ICommonProps {
  cite?: any[];
  extra?: any;
}

/** NOTE: 基础的 */
export const Text: React.FC<IBaseProps> = props => {
  const {
    data,
    options,
    onAction,
    onCustomLinkClick,
    prefixRender,
    suffixRender,
    prefixText = '',
    customerComponents,
    className,
  } = props;
  const { isBlank = true, isPureText = false } = options || {};

  const errorStyle = !!get(options, 'errorStyle', false);

  /** NOTE: a标签点击事件 */
  const handleLinkClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLElement;
    /** NOTE: 更高优先级的自定义事件处理 */
    onCustomLinkClick && onCustomLinkClick(event);
    // 如果他们执行了event.preventDefault()就不后续执行了
    if (event.defaultPrevented) {
      console.log(`-----📝📝📝默认事件被onCustomLinkClick阻止------`);
      return;
    }
    if (options) {
      const text = target.innerText;
      const { segmentId, interactDefinition: { fixedDefinitions = [] } = {} } = options;
      const definition = fixedDefinitions.find(item => item.elementKey === text);
      if (definition && onAction) {
        event.preventDefault();
        onAction(definition, segmentId);
        return;
      }
    }
  };

  const defaultComponents = {
    a(props) {
      // 其他a标签 default
      return <Link {...omit(props, 'node')} rel="noopener" isBlank={isBlank} onClick={handleLinkClick} />;
    },
  };

  if (isPureText) {
    return (
      <div className={classNames('rc-markdown-body', className, { [style.errorStyle]: errorStyle })}>
        <p>{data}</p>
      </div>
    );
  }

  return (
    <>
      {prefixRender && prefixRender}
      <ReactMarkdown
        className={classNames('rc-markdown-body', className, { [style.errorStyle]: errorStyle })}
        remarkPlugins={[remarkGfm]}
        // @ts-ignore
        rehypePlugins={[rehypeRaw]}
        components={customerComponents ? customerComponents(defaultComponents) : defaultComponents}
      >
        {prefixText + data}
      </ReactMarkdown>
    {console.log('我想看看 data 到底是什么？', data)}
      {suffixRender && suffixRender}
    </>
  );
};

/** NOTE: 消息的 */
export const MessageText: React.FC<ITextProps> = props => {
  const { messageData, options: propsOptions, ...restProps } = props;
  const { data, ...restMessageData } = messageData;
  const options = {
    ...restMessageData,
    ...propsOptions,
  };
  return <Text data={data as string} options={options} {...restProps} />;
};

/** NOTE: 带引用的消息的 */
export const CiteMessageText: React.FC<IFootnoteTextProps> = props => {
  const { messageData, cite: citeList = [], extra } = props;
  const sceneType = get(messageData, 'sceneType', '');

  const prefixRender = citeList?.length > 0 && !disabledCiteSceneTypeList.includes(sceneType) && (
    <Cite className="cite-wrap" data={citeList} extra={extra} />
  );

  const prefixText = useCiteText(citeList);

  const customerComponents = (defaultComponents: any) => {
    return {
      a(props: any) {
        // 脚注
        const isFootnote = get(props, 'aria-describedby') === 'footnote-label';
        if (isFootnote) {
          let idx = 0;
          // 解析锚点的值,只考虑是数字吧,不然找不到list的i
          const childrenId = Number((props?.href ?? '').replace(/[^0-9]/gi, ''));
          if (isInteger(childrenId) && childrenId >= 1) {
            idx = childrenId - 1;
          }
          const item = citeList?.[idx] || citeList?.[0];
          if (item) {
            return (
              <Pop
                className={style.pop}
                trigger="hover"
                position="auto-top-center"
                content={
                  <Link
                    rel="noopener"
                    href={item.url}
                    onClick={() => {

                    }}
                  >
                    {item.title}
                  </Link>
                }
              >
                <span className={style.footnoteItem}>{childrenId}</span>
              </Pop>
            );
          }
        }
        // 其他a标签 default
        return defaultComponents.a(props);
      },
      sup(props) {
        return <>{props.children}</>;
      },
      // 引用
      section(props) {
        const isFootnotes = get(props, 'className') === 'footnotes';
        if (isFootnotes) {
          // return <Cite className="cite-wrap" data={citeList} extra={extra} />;
          return null;
        }
        return <section {...omit(props, 'node')} />;
      },
    };
  };

  return (
    <MessageText
      {...props}
      prefixRender={prefixRender}
      prefixText={prefixText}
      customerComponents={customerComponents}
    />
  );
};

export default Text;
